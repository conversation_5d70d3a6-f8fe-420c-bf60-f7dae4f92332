# 🤖 Telegram Bot 交互功能使用指南

## 🚀 **启动Bot交互测试**

### 方法1: 使用测试脚本
```bash
python test_bot_interactive.py
```

### 方法2: 直接启动interactive模块
```bash
python -m interactive.main
```

## 📋 **主要功能**

### 🏠 **主菜单 (/start)**
用户发送 `/start` 命令后会看到：

```
👋 你好 [用户名]！

🤖 **欢迎使用智能助手Bot！**

✨ 我可以帮助您：
• 📊 获取实时信息
• 🔧 处理各种任务
• 💬 提供智能问答
• 📱 便捷的服务体验

请选择您需要的功能：
```

**菜单按钮**：
- 📋 服务介绍
- 💬 联系我们
- ❓ 帮助中心
- ⚙️ 设置
- 🌐 官方网站
- 📱 下载APP

### 📋 **服务介绍菜单**
展示bot的主要功能和服务内容：
- 🤖 智能助手功能
- 📊 数据服务
- 🔧 技术支持

### 💬 **联系我们菜单**
提供多种联系方式：
- 📧 邮箱联系
- 📞 电话支持
- 🌐 官网访问
- ⏰ 服务时间说明

### ❓ **帮助中心菜单**
丰富的帮助内容：
- ❓ 常见问题
- 💬 在线客服
- 📧 邮件支持
- 📱 电话热线
- 🌐 帮助文档
- 🎥 视频教程

### ⚙️ **设置菜单**
用户个性化设置：
- 🔔 通知设置
- 🌐 语言设置
- 👤 账户信息

## 🔧 **技术实现**

### **菜单系统**
- 使用JSON模板定义菜单结构
- 支持内联键盘按钮
- 支持回调数据和外部链接
- 动态菜单生成

### **消息处理**
- 命令处理器：处理 `/start`, `/help` 等命令
- 回调查询处理器：处理菜单按钮点击
- 文本消息处理器：处理普通文本
- 多媒体处理器：处理图片、文档等

### **错误处理**
- 优雅的错误处理和降级机制
- 详细的日志记录
- 用户友好的错误提示

## 🎯 **测试步骤**

### 1. **启动Bot**
```bash
python test_bot_interactive.py
```

### 2. **在Telegram中测试**
1. 找到你的Bot（使用BotFather创建的Bot）
2. 发送 `/start` 命令
3. 点击菜单按钮进行交互
4. 测试各种功能

### 3. **测试用例**
- ✅ 发送 `/start` 显示主菜单
- ✅ 点击 "服务介绍" 查看服务内容
- ✅ 点击 "联系我们" 查看联系方式
- ✅ 点击 "帮助中心" 查看帮助信息
- ✅ 点击 "设置" 查看用户设置
- ✅ 点击外部链接（官网、APP下载）
- ✅ 使用 "返回主菜单" 按钮导航
- ✅ 发送普通文本消息测试回复
- ✅ 发送图片、文档测试处理

## 📝 **配置说明**

### **Bot Token配置**
在 `config/config.yaml` 中设置：
```yaml
bot:
  token: "your-telegram-bot-token"
```

### **菜单模板**
菜单模板位于 `data/menus/` 目录：
- `main_menu.json` - 主菜单
- `help_center.json` - 帮助中心菜单

### **自定义菜单**
可以通过修改JSON模板来自定义菜单：
```json
{
  "title": "菜单标题",
  "description": "菜单描述文字",
  "buttons": [
    [
      {"text": "按钮文字", "callback_data": "回调数据"},
      {"text": "外部链接", "url": "https://example.com"}
    ]
  ]
}
```

## 🔍 **日志和调试**

### **查看日志**
Bot运行时会输出详细日志：
```
INFO: User 123456789 (username) started the bot
INFO: Main menu sent to user 123456789
INFO: User 123456789 clicked menu button: menu_services
```

### **调试模式**
在 `config/config.yaml` 中启用调试：
```yaml
debug: true
logging:
  level: DEBUG
```

## 🚀 **扩展功能**

### **添加新菜单**
1. 在 `data/menus/` 中创建新的JSON模板
2. 在 `message_handlers.py` 中添加处理函数
3. 在回调处理器中添加新的callback_data处理

### **添加新命令**
1. 在 `message_handlers.py` 中定义命令处理函数
2. 在 `get_handlers()` 中注册CommandHandler

### **自定义消息处理**
可以扩展 `handle_text_message` 函数来添加更多智能回复逻辑。

## ⚠️ **注意事项**

1. **Bot Token安全**：不要将Bot Token提交到代码仓库
2. **权限设置**：确保Bot有发送消息的权限
3. **错误处理**：添加适当的错误处理和用户提示
4. **性能考虑**：大量用户时考虑使用Webhook模式

现在你可以启动Bot并测试完整的交互菜单功能了！
