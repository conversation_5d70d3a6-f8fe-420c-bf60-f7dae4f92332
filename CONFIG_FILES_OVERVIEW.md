# 📁 配置文件总览

## 🗂️ **配置文件结构**

```
config/
├── config.yaml          # 主配置文件
├── menu_config.json     # 菜单配置文件
└── settings.py          # 配置管理模块
```

## 📋 **配置文件说明**

### **1. config/config.yaml - 主配置文件**

**用途**：Bot的基础配置，包括Token、API、数据库等设置

```yaml
# Bot配置
bot:
  token: "your-bot-token"

# 测试群组配置  
test:
  chat_id: -1002316158105

# 代理配置
proxy:
  enabled: true
  url: "http://127.0.0.1:7890"

# API服务配置
api:
  host: "0.0.0.0"
  port: 8000

# 数据库配置
database:
  host: "localhost"
  port: 3306
  user: "root"
  password: "password"
  name: "botdb"

# 菜单配置
menu:
  config_path: "config/menu_config.json"
```

---

### **2. config/menu_config.json - 菜单配置文件**

**用途**：Bot菜单的定制配置，支持Bot级别和群组级别的菜单定制

```json
{
  "menu_configuration": {
    "system_default": {
      "private_menu": "private_menu",
      "group_menu": "group_menu"
    },
    
    "bot_configs": {
      "your_bot_token": {
        "bot_name": "测试Bot",
        "description": "主要的测试机器人",
        "default_menus": {
          "private_menu": "private_menu",
          "group_menu": "group_menu"
        },
        "features": ["push_messages", "interactive_chat"]
      }
    },
    
    "group_custom_configs": {
      "your_bot_token": {
        "-1002316158105": {
          "group_name": "测试群组",
          "custom_menus": {
            "group_menu": "test_group_special"
          },
          "enabled_features": ["statistics", "activities"]
        }
      }
    }
  }
}
```

---

### **3. config/settings.py - 配置管理模块**

**用途**：统一的配置访问接口，支持环境变量和配置文件

**主要功能**：
- 从 `config.yaml` 加载配置
- 支持环境变量覆盖
- 提供类型安全的配置访问

## 🔧 **配置优先级**

### **主配置 (config.yaml)**
```
环境变量 > config.yaml > 默认值
```

### **菜单配置 (menu_config.json)**
```
群组定制 > Bot默认 > 系统默认
```

## 📝 **配置使用方法**

### **访问主配置**
```python
from config.settings import settings

# Bot Token
bot_token = settings.bot_token

# API配置
api_host = settings.api_host
api_port = settings.api_port

# 数据库配置
db_url = settings.database_url

# 菜单配置文件路径
menu_config_path = settings.menu_config_path
```

### **访问菜单配置**
```python
from pusher.menu.menu_config_manager import menu_config_manager

# 获取菜单模板
template = menu_config_manager.get_menu_template(bot_token, "private")

# 获取Bot信息
bot_info = menu_config_manager.get_bot_info(bot_token)

# 检查功能是否启用
is_enabled = menu_config_manager.is_feature_enabled(bot_token, "feature_name")
```

## 🎯 **配置示例**

### **多Bot配置示例**

**config/menu_config.json**:
```json
{
  "menu_configuration": {
    "bot_configs": {
      "bot_token_1": {
        "bot_name": "客服Bot",
        "default_menus": {
          "private_menu": "customer_service_private",
          "group_menu": "customer_service_group"
        }
      },
      
      "bot_token_2": {
        "bot_name": "管理Bot", 
        "default_menus": {
          "private_menu": "admin_private",
          "group_menu": "admin_group"
        }
      }
    }
  }
}
```

### **群组定制配置示例**

```json
{
  "group_custom_configs": {
    "bot_token_1": {
      "-1001234567890": {
        "group_name": "VIP客户群",
        "custom_menus": {
          "group_menu": "vip_customer_group"
        },
        "enabled_features": ["priority_support"]
      },
      
      "-1002316158105": {
        "group_name": "测试群组",
        "custom_menus": {
          "group_menu": "test_group_special"
        },
        "enabled_features": ["experimental_features"]
      }
    }
  }
}
```

## 🔒 **安全注意事项**

### **敏感信息处理**
1. **Bot Token**: 建议使用环境变量 `BOT_TOKEN`
2. **数据库密码**: 建议使用环境变量 `DATABASE_PASSWORD`
3. **API密钥**: 建议使用环境变量设置

### **环境变量示例**
```bash
# 设置Bot Token
export BOT_TOKEN="your-bot-token"

# 设置数据库密码
export DATABASE_PASSWORD="your-db-password"

# 设置Webhook URL
export WEBHOOK_URL="https://your-domain.com/webhook"
```

## 📂 **配置文件管理**

### **版本控制**
- ✅ **提交**: `config.yaml` (去除敏感信息)
- ✅ **提交**: `menu_config.json`
- ✅ **提交**: `settings.py`
- ❌ **不提交**: 包含真实Token的配置文件

### **部署建议**
1. **开发环境**: 使用本地配置文件
2. **测试环境**: 使用环境变量覆盖关键配置
3. **生产环境**: 全部使用环境变量

### **配置备份**
- 定期备份 `menu_config.json`
- 记录配置变更历史
- 测试配置变更后再应用到生产环境

## 🔄 **配置热重载**

### **重新加载主配置**
```python
from config.settings import settings
settings.load_config()
```

### **重新加载菜单配置**
```python
from pusher.menu.menu_config_manager import menu_config_manager
menu_config_manager.reload_config()
```

## 💡 **最佳实践**

1. **配置分离**: 主配置和菜单配置分开管理
2. **环境变量**: 敏感信息使用环境变量
3. **文档化**: 为每个配置项添加注释说明
4. **验证**: 启动时验证关键配置项
5. **备份**: 定期备份配置文件
6. **测试**: 配置变更后进行充分测试

现在所有配置文件都在 `config/` 目录下了！
