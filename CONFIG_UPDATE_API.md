# 🔧 统一配置更新API文档

## 📋 **接口概述**

提供一个统一的配置更新接口，支持三种更新类型，简化配置管理和维护。

### **接口地址**：
```
POST http://localhost:8000/api/config-update
```

## 🎯 **设计原则**

1. **单一接口** - 只提供一个更新接口，简化管理
2. **数据库为准** - 接口只确认DB已更新，然后查询DB获取最新数据
3. **减少错误** - 避免多信息源导致的数据不一致
4. **灵活控制** - 支持全量和指定ID的更新

## 📊 **更新类型说明**

### **1. `"all"` - 更新所有相关表**
- 更新Bot配置表缓存 (c_tbRobotConfig)
- 更新定时任务表缓存 (c_tgScheduledPushTasks)
- 适用场景：系统重启后、大批量配置变更

### **2. `"bot_config"` - 更新Bot配置**
- 只更新Bot配置表缓存 (c_tbRobotConfig)
- 支持指定商户号列表或全部更新
- 适用场景：Bot Token变更、商户配置修改

### **3. `"scheduled_tasks"` - 更新定时任务**
- 只更新定时任务表缓存 (c_tgScheduledPushTasks)
- 支持指定任务ID列表或全部更新
- 适用场景：定时任务配置修改、任务启用/禁用

## 🔧 **请求参数**

```json
{
  "update_type": "all | bot_config | scheduled_tasks",
  "target_ids": ["id1", "id2", ...] // 可选，为空则更新全部
}
```

### **参数说明**：
- **`update_type`** (必需): 更新类型
- **`target_ids`** (可选): 指定更新的ID列表
  - `bot_config` 类型时：商户号列表 (如: `["39bac42a", "40bac42b"]`)
  - `scheduled_tasks` 类型时：任务ID列表 (如: `[1, 2, 3]`)
  - `all` 类型时：忽略此参数

## 📝 **使用示例**

### **示例1: 更新所有配置**
```json
POST /api/config-update
{
  "update_type": "all"
}
```
**场景**: 系统重启、大批量配置变更

### **示例2: 更新所有Bot配置**
```json
POST /api/config-update
{
  "update_type": "bot_config"
}
```
**场景**: 批量更新所有商户的Bot配置

### **示例3: 更新指定商户的Bot配置**
```json
POST /config/update
{
  "update_type": "bot_config",
  "target_ids": ["39bac42a", "40bac42b"]
}
```
**场景**: 特定商户的Bot Token变更

### **示例4: 更新单个商户的Bot配置**
```json
POST /config/update
{
  "update_type": "bot_config",
  "target_ids": ["39bac42a"]
}
```
**场景**: 单个商户配置修改

### **示例5: 更新所有定时任务**
```json
POST /config/update
{
  "update_type": "scheduled_tasks"
}
```
**场景**: 批量更新所有定时任务配置

### **示例6: 更新指定定时任务**
```json
POST /config/update
{
  "update_type": "scheduled_tasks",
  "target_ids": [1, 2, 3]
}
```
**场景**: 特定任务的配置修改

## 📤 **响应格式**

### **成功响应**：
```json
{
  "success": true,
  "message": "开始更新Bot配置缓存 (商户: ['39bac42a'])",
  "data": {
    "update_type": "bot_config",
    "target_ids": ["39bac42a"],
    "target_count": 1
  }
}
```

### **错误响应**：
```json
{
  "detail": "不支持的更新类型: invalid_type。支持的类型: all, bot_config, scheduled_tasks"
}
```

## 🔄 **工作流程**

### **外部系统调用流程**：
```
1. 外部系统修改数据库 (c_tbRobotConfig 或 c_tgScheduledPushTasks)
2. 调用更新API通知配置变更
3. API后台异步从数据库重新加载最新配置
4. 调度器下次执行时使用新配置
```

### **内部处理流程**：
```
1. 接收更新请求
2. 验证参数格式
3. 后台异步执行更新任务
4. 立即返回响应 (不等待更新完成)
5. 后台从数据库查询最新数据更新缓存
```

## 🚀 **集成示例**

### **Shell脚本调用**：
```bash
#!/bin/bash
# 更新指定商户的Bot配置
curl -X POST http://localhost:8001/config/update \
  -H "Content-Type: application/json" \
  -d '{
    "update_type": "bot_config",
    "target_ids": ["39bac42a"]
  }'
```

### **Python调用**：
```python
import requests

def update_bot_config(business_nos):
    """更新指定商户的Bot配置"""
    url = "http://localhost:8001/config/update"
    data = {
        "update_type": "bot_config",
        "target_ids": business_nos
    }
    
    response = requests.post(url, json=data)
    return response.json()

# 使用示例
result = update_bot_config(["39bac42a", "40bac42b"])
print(result)
```

### **Java调用**：
```java
// 使用OkHttp或其他HTTP客户端
String json = """
{
  "update_type": "bot_config",
  "target_ids": ["39bac42a"]
}
""";

Request request = new Request.Builder()
    .url("http://localhost:8000/api/config-update")
    .post(RequestBody.create(json, MediaType.get("application/json")))
    .build();

Response response = client.newCall(request).execute();
```

## 📊 **监控和状态**

### **查看配置状态**：
```
GET http://localhost:8000/api/config-status
```

### **健康检查**：
```
GET http://localhost:8000/health
```

## 💡 **最佳实践**

1. **及时通知** - 数据库配置变更后立即调用API
2. **批量更新** - 多个相关配置变更时，使用批量更新减少调用次数
3. **错误处理** - 调用方应处理API调用失败的情况
4. **监控日志** - 关注API和调度器的日志，确保配置更新成功

## 🔧 **启动服务**

```bash
# 启动主API服务 (包含配置管理接口)
python -m uvicorn api.main:create_app --factory --host 0.0.0.0 --port 8000

# 启动调度器
python start_scheduler.py
```

这个统一接口设计简化了配置管理，提高了系统的可维护性！🎉
