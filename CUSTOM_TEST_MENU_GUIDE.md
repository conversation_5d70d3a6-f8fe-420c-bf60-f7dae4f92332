# 🎯 定制测试群组菜单使用指南

## ✅ **新菜单创建完成**

为你的Bot和群组组合创建了专门的定制菜单：
- **Bot**: `7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw`
- **群组**: `-1002316158105`
- **菜单**: `custom_test_group`

## 🎨 **菜单特色**

### **🖼️ 视觉设计**：
- **图片**: `https://picsum.photos/600/300?random=10`
- **标题**: 🎯 定制测试群组菜单
- **风格**: 功能测试导向

### **📝 菜单描述**：
```
🚀 **定制功能测试菜单**

🎯 这是专门为测试群组定制的功能菜单：
• 🌐 浏览器链接测试
• 📱 Telegram WebApp测试
• 👤 用户信息获取和显示
• 💬 私聊功能测试

✨ **特色功能**：
集成了多种交互方式，便于测试不同的Bot功能和用户体验

请选择测试功能：
```

## 🔘 **四个功能按钮**

### **第一行按钮**：

#### **🌐 打开Google**
- **类型**: 外部链接 (`url`)
- **功能**: 在浏览器中打开Google
- **链接**: `https://www.google.com`
- **行为**: 点击后在外部浏览器打开

#### **📱 Telegram WebApp**
- **类型**: WebApp (`web_app`)
- **功能**: 在Telegram内打开WebApp
- **链接**: `https://t.me/telegram`
- **行为**: 点击后在Telegram内打开

### **第二行按钮**：

#### **👤 获取我的信息**
- **类型**: 回调 (`callback_data`)
- **功能**: 获取用户TG信息并显示
- **回调**: `get_user_info`
- **行为**: 显示详细的用户和聊天信息

#### **💬 私聊我**
- **类型**: 回调 (`callback_data`)
- **功能**: 引导用户开始私聊
- **回调**: `start_private_chat`
- **行为**: 提供私聊链接和引导

## 🔧 **功能详解**

### **👤 获取用户信息功能**

点击后会显示：

```
👤 **用户信息详情**

🆔 **基本信息**
• 用户ID: 123456789
• 姓名: 张三 
• 用户名: @username
• 语言: zh
• 机器人: 否
• 高级用户: 是

💬 **聊天信息**
• 聊天ID: -1002316158105
• 聊天类型: supergroup
• 群组名称: 测试群组

⏰ **获取时间**: Bot 于刚刚获取

📊 **技术信息**
• Update ID: 123456
• Message ID: 789
```

**包含按钮**：
- 🔄 刷新信息
- 🏠 返回菜单

### **💬 私聊功能**

点击后会显示：

```
💬 **开始私聊**

👋 你好 [用户名]！

🎯 **私聊优势**：
• 🔐 更私密的交流环境
• 📱 个性化服务体验
• 🚀 更快的响应速度
• 🎨 专属的私聊菜单

📱 **开始私聊的方法**：
方法1: 点击下方按钮直接开始私聊
方法2: 手动搜索 @botusername
方法3: 复制链接

💡 **提示**: 私聊中发送 /start 可以看到专属的私聊菜单！
```

**包含按钮**：
- 💬 开始私聊
- 📋 复制链接
- ❓ 私聊帮助
- 🏠 返回菜单

## 🚀 **测试步骤**

### **步骤1: 重启Bot服务**
```bash
python start_bot_interactive.py
```

### **步骤2: 在测试群组中测试**
1. 在群组 `-1002316158105` 中发送 `/start`
2. 应该看到新的定制菜单（带图片）
3. 测试四个按钮的功能

### **步骤3: 功能测试清单**

#### **🌐 浏览器链接测试**：
- [ ] 点击 "🌐 打开Google"
- [ ] 确认在外部浏览器打开Google
- [ ] 检查链接是否正确

#### **📱 WebApp测试**：
- [ ] 点击 "📱 Telegram WebApp"
- [ ] 确认在Telegram内打开
- [ ] 检查WebApp功能

#### **👤 用户信息测试**：
- [ ] 点击 "👤 获取我的信息"
- [ ] 检查显示的用户信息是否正确
- [ ] 测试 "🔄 刷新信息" 按钮
- [ ] 测试 "🏠 返回菜单" 按钮

#### **💬 私聊测试**：
- [ ] 点击 "💬 私聊我"
- [ ] 检查私聊引导信息
- [ ] 点击 "💬 开始私聊" 按钮
- [ ] 确认能正确跳转到私聊

## 📊 **配置信息**

### **菜单配置路径**：
- **菜单文件**: `data/menus/custom_test_group.json`
- **配置文件**: `config/menu_config.json`

### **当前配置**：
```json
"-1002316158105": {
  "group_name": "定制测试群组",
  "description": "定制功能测试群组，包含浏览器、WebApp、用户信息、私聊等功能",
  "custom_menus": {
    "group_menu": "custom_test_group"
  },
  "enabled_features": ["browser_links", "webapp_links", "user_info", "private_chat"]
}
```

## 🔄 **切换回原菜单**

如果想切换回原来的测试菜单，修改配置：

```json
"custom_menus": {
  "group_menu": "test_group_special"  // 改回原来的菜单
}
```

## 💡 **扩展功能**

这个菜单展示了多种Bot交互方式：
- **外部链接**: 直接跳转到外部网站
- **WebApp**: 在Telegram内打开应用
- **回调处理**: 服务器端逻辑处理
- **用户信息**: 获取和显示用户数据
- **私聊引导**: 跨聊天类型的功能引导

可以基于这个模板创建更多定制功能！

## 🎉 **现在可以测试了**

重启Bot服务后，在你的测试群组中发送 `/start`，应该会看到全新的定制菜单，包含四个功能按钮和漂亮的图片！
