# 🚀 测试环境部署检查清单

## 📋 部署前检查

### ✅ **代码准备**
- [x] 所有功能开发完成
- [x] 定时任务支持新表结构
- [x] 即时推送API返回格式统一
- [x] URL验证功能已修复
- [x] 错误处理和日志完善

### ✅ **依赖检查**
- [x] requirements.txt已更新
- [x] 包含所有必要的生产依赖
- [x] 版本号已固定或指定范围

### ✅ **配置文件**
- [ ] 检查config/config.yaml配置
- [ ] 确认MongoDB连接信息
- [ ] 确认Bot Token配置
- [ ] 确认API端口配置

## 🔧 **部署步骤**

### **1. 环境准备**
```bash
# 创建Python虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows:
venv\Scripts\activate
# Linux/Mac:
source venv/bin/activate

# 升级pip
pip install --upgrade pip
```

### **2. 安装依赖**
```bash
# 安装所有依赖
pip install -r requirements.txt

# 验证关键包安装
python -c "import telegram; print('✅ python-telegram-bot')"
python -c "import fastapi; print('✅ fastapi')"
python -c "import pymongo; print('✅ pymongo')"
python -c "import uvicorn; print('✅ uvicorn')"
```

### **3. 配置检查**
```bash
# 检查配置文件
python -c "from config.settings import settings; print('✅ 配置加载成功')"

# 测试MongoDB连接
python test_mongodb_connection.py

# 检查Bot Token
python -c "from config.settings import settings; print(f'Bot Token: {settings.bot_token[:20]}...')"
```

### **4. 功能测试**
```bash
# 测试API服务
python -c "from api.main import create_app; print('✅ API应用创建成功')"

# 测试调度器
python -c "from scheduler.config_manager import config_manager; print('✅ 配置管理器正常')"

# 测试模板处理
python -c "from pusher.template_message_handler import template_handler; print('✅ 模板处理器正常')"
```

### **5. 启动服务**
```bash
# 启动统一服务器
python start_unified_server.py

# 或者使用uvicorn直接启动
uvicorn api.main:app --host 0.0.0.0 --port 8000
```

## 🔍 **部署验证**

### **API接口测试**
```bash
# 健康检查
curl http://localhost:8000/health

# 配置状态检查
curl http://localhost:8000/api/config-status

# 模板消息预览
curl -X POST "http://localhost:8000/api/realtime-push/template/preview" \
  -H "Content-Type: application/json" \
  -d '{
    "business_no": "39bac42a",
    "type": "18000",
    "params": ["test_user"]
  }'
```

### **定时任务验证**
- [ ] 检查定时任务是否正常加载
- [ ] 验证新表结构支持
- [ ] 确认优先级规则正确
- [ ] 测试多时间点执行

### **日志检查**
- [ ] 启动日志无错误
- [ ] MongoDB连接成功
- [ ] 配置加载正常
- [ ] 定时任务调度正常

## 📦 **生产环境建议**

### **进程管理**
```bash
# 使用systemd管理服务 (Linux)
sudo systemctl enable tgbot-service
sudo systemctl start tgbot-service

# 或使用supervisor
pip install supervisor
```

### **反向代理**
```nginx
# Nginx配置示例
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### **监控和日志**
```bash
# 安装监控工具
pip install sentry-sdk  # 错误监控
pip install structlog   # 结构化日志

# 日志轮转配置
# 在config/config.yaml中配置日志文件路径
```

## 🚨 **故障排除**

### **常见问题**
1. **MongoDB连接失败**
   - 检查网络连接
   - 验证用户名密码
   - 确认数据库名称

2. **Bot Token无效**
   - 检查Token格式
   - 验证Bot是否激活
   - 确认权限设置

3. **端口占用**
   - 检查端口是否被占用
   - 修改配置文件中的端口
   - 使用netstat查看端口状态

4. **依赖安装失败**
   - 升级pip版本
   - 检查Python版本兼容性
   - 使用国内镜像源

### **调试命令**
```bash
# 查看进程
ps aux | grep python

# 查看端口占用
netstat -tulpn | grep 8000

# 查看日志
tail -f logs/app.log

# 测试数据库连接
python test_mongodb_connection.py
```

## ✅ **部署完成确认**

- [ ] 服务正常启动
- [ ] API接口响应正常
- [ ] 定时任务运行正常
- [ ] 数据库连接稳定
- [ ] 日志输出正常
- [ ] 错误处理正确
- [ ] 性能表现良好

## 📞 **支持信息**

- **项目文档**: README.md
- **API文档**: http://localhost:8000/docs
- **配置说明**: config/config.yaml
- **数据库文档**: MONGODB_SETUP_GUIDE.md

---

🎉 **部署成功后，你的Telegram Bot服务就可以在测试环境中正常运行了！**
