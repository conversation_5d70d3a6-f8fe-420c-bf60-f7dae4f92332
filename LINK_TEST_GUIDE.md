# 链接功能测试指南

## 🔗 **功能说明**

新增的链接功能支持根据 `type` 参数智能选择使用哪个链接：

- **type = "browser"**: 使用 `link` 参数，在外部浏览器中打开
- **type = "webapp"**: 使用 `tg_short_link` 参数，在Telegram内打开

## 🎯 **设计理念**

提供双链接参数是为了防止调用方出错，确保 `type` 和具体的链接匹配：

```json
{
  "type": "browser",
  "link": "https://www.google.com",        // browser类型使用这个
  "tg_short_link": "https://t.me/telegram" // webapp类型使用这个
}
```

## 📋 **测试用例**

### 4.2 浏览器链接测试
- **type**: "browser"
- **预期**: 显示 "🌐 在浏览器中打开" 链接到 Google
- **实际链接**: `link` 参数的值

### 4.3 Telegram WebApp链接测试
- **type**: "webapp"  
- **预期**: 显示 "📱 在Telegram中打开" 链接到 Telegram
- **实际链接**: `tg_short_link` 参数的值

### 4.4 双链接完整测试
- **type**: "browser"
- **预期**: 显示 GitHub 链接（忽略 TG 链接）
- **验证**: 只显示一个链接，根据type选择

## 🔧 **技术实现**

### 链接选择逻辑
```python
if link_type == "webapp" and tg_short_link:
    # 使用 tg_short_link
elif link_type == "browser" and link:
    # 使用 link
else:
    # 降级处理：使用任何可用的链接
```

### 链接文字
- **browser**: "🌐 在浏览器中打开"
- **webapp**: "📱 在Telegram中打开"
- **降级**: "🔗 点击访问链接" 或 "📱 Telegram链接"

## 📊 **测试步骤**

1. **测试 4.2**：验证 browser 类型使用 link 参数
2. **测试 4.3**：验证 webapp 类型使用 tg_short_link 参数  
3. **测试 4.4**：验证双链接时正确选择
4. **检查日志**：确认使用了正确的链接

## 🔍 **预期日志**

```
INFO: 使用浏览器链接: https://www.google.com
INFO: 使用webapp链接: https://t.me/telegram
WARNING: 降级使用普通链接: https://github.com
```

## ✅ **验证要点**

- [ ] 消息包含图片
- [ ] 消息包含文字说明
- [ ] 消息包含正确的链接
- [ ] 链接文字符合预期
- [ ] 点击链接打开正确的URL
- [ ] 日志显示使用了正确的链接类型

## 🚀 **测试命令**

在VSCode中打开 `test_api.http`，依次点击：
1. 第4.2个请求的 "Send Request"
2. 第4.3个请求的 "Send Request"  
3. 第4.4个请求的 "Send Request"

每次测试后检查Telegram群组中的消息效果。
