# 🎨 菜单定制系统使用指南

## 🎯 **设计理念**

支持多层级的菜单定制，满足不同Bot和不同群组的个性化需求：

```
系统默认菜单 (fallback)
    ↓
Bot默认菜单 (bot_token → menu_template)
    ↓  
群组定制菜单 (bot_token + group_id → menu_template)
```

**优先级**：群组定制 > Bot默认 > 系统默认

## 📋 **配置文件结构**

### **主配置文件**: `config/menu_config.json`

```json
{
  "menu_configuration": {
    "system_default": {
      "private_menu": "private_menu",
      "group_menu": "group_menu"
    },
    
    "bot_configs": {
      "bot_token_1": {
        "bot_name": "客服Bot",
        "description": "专门用于客服的机器人",
        "default_menus": {
          "private_menu": "customer_service_private",
          "group_menu": "customer_service_group"
        },
        "features": ["customer_support", "ticket_system"]
      }
    },
    
    "group_custom_configs": {
      "bot_token_1": {
        "-1002316158105": {
          "group_name": "VIP客户群",
          "custom_menus": {
            "group_menu": "vip_customer_group"
          },
          "enabled_features": ["priority_support"]
        }
      }
    }
  }
}
```

## 🔧 **配置步骤**

### **步骤1: 配置Bot默认菜单**

在 `menu_config.json` 中添加Bot配置：

```json
"bot_configs": {
  "your_bot_token": {
    "bot_name": "你的Bot名称",
    "description": "Bot功能描述",
    "default_menus": {
      "private_menu": "your_private_template",
      "group_menu": "your_group_template"
    },
    "features": ["feature1", "feature2"]
  }
}
```

### **步骤2: 创建菜单模板文件**

在 `data/menus/` 目录下创建对应的JSON文件：

**私聊菜单**: `your_private_template.json`
```json
{
  "title": "🏠 私聊菜单",
  "description": "欢迎使用私聊服务...",
  "buttons": [
    [
      {"text": "功能1", "callback_data": "action1"},
      {"text": "功能2", "callback_data": "action2"}
    ]
  ]
}
```

**群聊菜单**: `your_group_template.json`
```json
{
  "title": "👥 群聊菜单", 
  "description": "欢迎使用群聊服务...",
  "buttons": [
    [
      {"text": "群组功能1", "callback_data": "group_action1"},
      {"text": "群组功能2", "callback_data": "group_action2"}
    ]
  ]
}
```

### **步骤3: 配置群组定制（可选）**

为特定群组配置专属菜单：

```json
"group_custom_configs": {
  "your_bot_token": {
    "-1001234567890": {
      "group_name": "特殊群组",
      "description": "这是一个特殊配置的群组",
      "custom_menus": {
        "group_menu": "special_group_template"
      },
      "enabled_features": ["special_feature"]
    }
  }
}
```

## 🎨 **菜单模板示例**

### **客服Bot私聊菜单**
```json
{
  "title": "🎧 客服私聊菜单",
  "description": "💬 **专业客服为您服务**\n\n🎯 我可以帮助您：\n• 📞 产品咨询和技术支持\n• 🎫 工单系统和问题跟踪",
  "buttons": [
    [
      {"text": "📞 产品咨询", "callback_data": "cs_product_inquiry"},
      {"text": "🔧 技术支持", "callback_data": "cs_tech_support"}
    ],
    [
      {"text": "🎫 提交工单", "callback_data": "cs_create_ticket"},
      {"text": "📋 查看工单", "callback_data": "cs_view_tickets"}
    ]
  ]
}
```

### **测试群组特殊菜单**
```json
{
  "title": "🧪 测试群组特殊菜单",
  "description": "🔬 **测试群组专属功能**\n\n🎯 这是为测试群组定制的特殊菜单",
  "buttons": [
    [
      {"text": "🧪 实验功能", "callback_data": "test_experimental"},
      {"text": "🔬 功能测试", "callback_data": "test_features"}
    ],
    [
      {"text": "📊 测试统计", "callback_data": "test_statistics"},
      {"text": "🔧 调试工具", "callback_data": "test_debug_tools"}
    ]
  ]
}
```

## 🔍 **使用场景示例**

### **场景1: 多功能Bot**
```
Bot A (通用Bot):
├── 私聊: 标准个人服务菜单
└── 群聊: 标准群组管理菜单

Bot B (客服Bot):
├── 私聊: 客服专用菜单 (工单、咨询)
└── 群聊: 客服群组菜单 (客户管理)

Bot C (管理Bot):
├── 私聊: 管理员菜单 (系统控制)
└── 群聊: 群组管理菜单 (权限、统计)
```

### **场景2: 群组定制**
```
同一个Bot在不同群组:
├── 普通群组: 标准群组菜单
├── VIP群组: VIP专属菜单 (特殊功能)
├── 测试群组: 测试菜单 (实验功能)
└── 管理群组: 管理菜单 (高级权限)
```

## 🚀 **实际应用**

### **当前配置示例**

你的测试Bot已经配置了：

1. **Bot默认配置**:
   - Bot Token: `7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw`
   - 私聊菜单: `private_menu` (标准个人服务)
   - 群聊菜单: `group_menu` (标准群组管理)

2. **群组定制配置**:
   - 群组ID: `-1002316158105`
   - 定制菜单: `test_group_special` (测试群组专属)

### **测试效果**

- **私聊Bot**: 显示标准私聊菜单
- **在测试群组**: 显示测试群组特殊菜单
- **在其他群组**: 显示标准群组菜单

## 🔧 **管理和维护**

### **重新加载配置**
```python
from pusher.menu.menu_config_manager import menu_config_manager
menu_config_manager.reload_config()
```

### **检查配置状态**
```python
# 获取Bot信息
bot_info = menu_config_manager.get_bot_info(bot_token)

# 获取群组信息  
group_info = menu_config_manager.get_group_info(bot_token, group_id)

# 检查功能是否启用
is_enabled = menu_config_manager.is_feature_enabled(bot_token, "feature_name", group_id)
```

### **获取菜单模板**
```python
# 获取上下文相关的菜单
menu_data = menu_builder.get_menu_for_context(bot_token, "private")
menu_data = menu_builder.get_menu_for_context(bot_token, "group", group_id)
```

## 💡 **最佳实践**

1. **命名规范**:
   - Bot配置: `{bot_purpose}_{bot_type}`
   - 菜单模板: `{purpose}_{chat_type}_{special}`
   - 例如: `customer_service_private`, `admin_group_vip`

2. **功能管理**:
   - 使用 `features` 字段控制Bot级别功能
   - 使用 `enabled_features` 控制群组级别功能
   - 在代码中检查功能是否启用

3. **配置维护**:
   - 定期备份配置文件
   - 使用版本控制管理配置变更
   - 测试新配置后再应用到生产环境

现在你可以为不同的Bot和群组配置个性化的菜单了！
