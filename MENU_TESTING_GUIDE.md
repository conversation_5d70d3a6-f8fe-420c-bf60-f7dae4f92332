# 🎨 菜单测试配置指南

## 📋 **创建的菜单模板总览**

### **🖼️ 所有菜单都带有图片**

| 菜单模板 | 图片 | 用途 | 特色 |
|---------|------|------|------|
| `group_menu` | 🖼️ random=1 | 默认群组菜单 | 标准群组功能 |
| `customer_service_group` | 🖼️ random=2 | 客服群组菜单 | 客服团队协作 |
| `admin_private` | 🖼️ random=3 | 管理员私聊菜单 | 系统管理控制台 |
| `admin_group` | 🖼️ random=4 | 管理群组菜单 | 群组管理控制台 |
| `vip_customer_group` | 🖼️ random=5 | VIP客户群组菜单 | 尊贵VIP服务 |
| `test_group_special` | 🖼️ random=6 | 测试群组特殊菜单 | 实验功能测试 |

## 🔧 **配置的Bot类型**

### **1. 测试Bot（当前使用）**
```json
"7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw": {
  "bot_name": "测试Bot",
  "default_menus": {
    "private_menu": "private_menu",
    "group_menu": "group_menu"  // 🖼️ 带图片的默认群组菜单
  }
}
```

### **2. 客服Bot**
```json
"customer_service_bot_token": {
  "bot_name": "客服Bot", 
  "default_menus": {
    "private_menu": "customer_service_private",
    "group_menu": "customer_service_group"  // 🖼️ 客服专用群组菜单
  }
}
```

### **3. 管理Bot**
```json
"admin_management_bot_token": {
  "bot_name": "管理Bot",
  "default_menus": {
    "private_menu": "admin_private",        // 🖼️ 管理员私聊菜单
    "group_menu": "admin_group"             // 🖼️ 管理群组菜单
  }
}
```

## 🎯 **群组定制配置**

### **1. 测试群组（当前配置）**
```json
"-1002316158105": {
  "group_name": "测试群组",
  "custom_menus": {
    "group_menu": "test_group_special"  // 🖼️ 测试专用菜单
  }
}
```

### **2. VIP客户群组**
```json
"-1001234567890": {
  "group_name": "VIP客户群",
  "custom_menus": {
    "group_menu": "vip_customer_group"  // 🖼️ VIP专属菜单
  }
}
```

### **3. 管理员群组**
```json
"-1002345678901": {
  "group_name": "管理员群组", 
  "custom_menus": {
    "group_menu": "admin_group"  // 🖼️ 管理专用菜单
  }
}
```

## 🚀 **测试方法**

### **方法1: 切换Bot Token测试**

#### **测试客服Bot菜单**：
1. 在 `config/menu_config.json` 中修改：
```json
"7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw": {
  "bot_name": "客服Bot测试",
  "default_menus": {
    "private_menu": "customer_service_private",
    "group_menu": "customer_service_group"
  }
}
```

2. 重启Bot服务：`python start_bot_interactive.py`
3. 在群组中发送 `/start` 查看客服群组菜单

#### **测试管理Bot菜单**：
1. 修改配置为：
```json
"default_menus": {
  "private_menu": "admin_private",
  "group_menu": "admin_group"
}
```

2. 重启服务测试管理员菜单

### **方法2: 切换群组定制测试**

#### **测试VIP群组菜单**：
1. 修改群组配置：
```json
"-1002316158105": {
  "group_name": "VIP测试群组",
  "custom_menus": {
    "group_menu": "vip_customer_group"
  }
}
```

2. 重启服务，在测试群组中查看VIP菜单

#### **测试默认群组菜单**：
1. 删除或注释群组定制配置：
```json
// "-1002316158105": { ... }  // 注释掉
```

2. 重启服务，查看默认群组菜单

## 📊 **菜单对比测试**

### **视觉对比要点**：

#### **1. 图片效果**：
- 每个菜单都有不同的随机图片
- 图片尺寸：600x300
- 图片来源：picsum.photos

#### **2. 菜单风格**：
- **默认群组**：🤖 标准群组助手风格
- **客服群组**：🎧 专业客服团队风格  
- **管理私聊**：🛡️ 系统管理员风格
- **管理群组**：👑 群组管理控制台风格
- **VIP群组**：💎 尊贵VIP服务风格
- **测试群组**：🧪 实验功能测试风格

#### **3. 功能按钮**：
- 每个菜单都有不同的功能按钮
- 按钮文字和图标都有针对性设计
- 回调数据都有对应的前缀区分

## 🔄 **快速切换配置**

### **配置模板**：

#### **切换到客服Bot**：
```json
"default_menus": {
  "private_menu": "customer_service_private",
  "group_menu": "customer_service_group"
}
```

#### **切换到管理Bot**：
```json
"default_menus": {
  "private_menu": "admin_private", 
  "group_menu": "admin_group"
}
```

#### **切换群组为VIP**：
```json
"custom_menus": {
  "group_menu": "vip_customer_group"
}
```

#### **切换群组为测试**：
```json
"custom_menus": {
  "group_menu": "test_group_special"
}
```

## 💡 **测试建议**

### **测试顺序**：
1. **默认配置**：先测试当前的默认群组菜单（带图片）
2. **Bot切换**：测试客服Bot和管理Bot的不同菜单
3. **群组定制**：测试VIP群组和测试群组的定制菜单
4. **私聊对比**：测试不同Bot的私聊菜单差异

### **观察要点**：
- ✅ 图片是否正确显示
- ✅ 菜单标题和描述是否符合预期
- ✅ 按钮布局和文字是否正确
- ✅ Bot名称是否正确显示
- ✅ 群组信息是否正确显示

### **配置重载**：
每次修改配置后，需要重启Bot服务：
```bash
# 停止当前服务 (Ctrl+C)
# 重新启动
python start_bot_interactive.py
```

## 🎨 **菜单特色预览**

### **🖼️ 图片效果**：
- `random=1`: 默认群组 - 清新自然风格
- `random=2`: 客服群组 - 专业商务风格  
- `random=3`: 管理私聊 - 科技感风格
- `random=4`: 管理群组 - 权威管理风格
- `random=5`: VIP群组 - 奢华尊贵风格
- `random=6`: 测试群组 - 创新实验风格

现在你可以通过修改配置文件来测试不同的菜单效果了！每个菜单都有独特的图片和风格设计。
