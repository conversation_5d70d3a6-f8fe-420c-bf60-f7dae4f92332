# 🍃 MongoDB配置和测试指南

## 📋 **配置完成**

已经为你的项目添加了完整的MongoDB支持：

### **1. 配置文件更新**
- ✅ `config/config.yaml` - 添加了MongoDB配置
- ✅ `config/settings.py` - 添加了MongoDB配置属性
- ✅ `requirements.txt` - 添加了MongoDB依赖

### **2. 新增文件**
- ✅ `database/mongodb_connection.py` - MongoDB连接管理器
- ✅ `test_mongodb_connection.py` - MongoDB连接测试脚本

## 🔧 **MongoDB配置详情**

### **config/config.yaml 配置**：
```yaml
database:
  mongodb:
    host: "*************"
    port: 27017
    username: "root"
    password: "cPOqO2hhxbLPe40f"
    database: "wingame_config"
    auth_source: "admin"
    connection_string: "***************************************************/wingame_config?authSource=admin"
    options:
      maxPoolSize: 100
      minPoolSize: 10
      connectTimeoutMS: 30000
      socketTimeoutMS: 360000
      retryWrites: true
      serverSelectionTimeoutMS: 5000
  
  type: "mongodb"  # 当前使用MongoDB
```

### **连接字符串**：
```
***************************************************/wingame_config?authSource=admin
```

## 🚀 **安装和测试步骤**

### **步骤1: 安装MongoDB依赖**
```bash
pip install pymongo>=4.6.0 motor>=3.3.0
```

或者安装所有依赖：
```bash
pip install -r requirements.txt
```

### **步骤2: 运行MongoDB连接测试**
```bash
python test_mongodb_connection.py
```

### **预期测试输出**：
```
🚀 MongoDB连接测试开始
⏰ 测试时间: 2024-01-XX XX:XX:XX

==================== MongoDB配置信息 ====================
📍 主机地址: *************
🔌 端口: 27017
👤 用户名: root
🔐 密码: ****************
🗄️ 数据库: wingame_config
🔑 认证源: admin
🔗 连接字符串: ***************************************************...

⚙️ 连接选项:
   • maxPoolSize: 100
   • minPoolSize: 10
   • connectTimeoutMS: 30000
   • socketTimeoutMS: 360000
   • retryWrites: True
   • serverSelectionTimeoutMS: 5000

==================== 基本连接测试 ====================
✅ MongoDB连接成功！

==================== 详细连接测试 ====================
✅ 详细连接测试成功！

🖥️ 服务器信息:
   • MongoDB版本: 6.0.x
   • Git版本: xxxxx
   • 平台: Linux

🗄️ 数据库信息:
   • 数据库名: wingame_config
   • 集合数量: X
   • 数据大小: XXXX bytes
   • 存储大小: XXXX bytes

📋 集合列表 (X 个):
   1. collection1
   2. collection2
   ...

==================== 数据库操作测试 ====================
📝 测试写入操作...
✅ 文档插入成功，ID: 507f1f77bcf86cd799439011
📖 测试读取操作...
✅ 文档查询成功: MongoDB连接测试
🗑️ 测试删除操作...
✅ 文档删除成功，删除数量: 1

==================== 集合访问测试 ====================
📋 发现 X 个集合
   • collection1: XXX 个文档
   • collection2: XXX 个文档
   ...

==================== 测试结果汇总 ====================
基本连接测试: ✅ 通过
详细连接测试: ✅ 通过
数据库操作测试: ✅ 通过
集合访问测试: ✅ 通过

📊 测试统计: 4/4 通过
🎉 所有测试通过！MongoDB连接正常。
```

## 💻 **代码使用示例**

### **基本连接使用**：
```python
from database.mongodb_connection import MongoDBConnection

# 方法1: 上下文管理器（推荐）
with MongoDBConnection() as mongo:
    if mongo.is_connected():
        collection = mongo.get_collection("your_collection")
        # 进行数据库操作
        result = collection.find_one({"key": "value"})

# 方法2: 手动管理
mongo = MongoDBConnection()
if mongo.connect():
    database = mongo.get_database()
    collection = database["your_collection"]
    # 进行数据库操作
    mongo.disconnect()
```

### **使用全局连接实例**：
```python
from database.mongodb_connection import get_mongodb_collection

# 获取集合
collection = get_mongodb_collection("your_collection")
if collection:
    # 进行数据库操作
    documents = collection.find({"status": "active"})
```

### **在Bot服务中使用**：
```python
from database.mongodb_connection import mongodb_connection

# 在服务启动时连接
if mongodb_connection.connect():
    print("MongoDB连接成功")
else:
    print("MongoDB连接失败")

# 在服务中使用
collection = mongodb_connection.get_collection("bot_data")
if collection:
    # 保存Bot数据
    collection.insert_one({
        "user_id": 123456,
        "action": "start_command",
        "timestamp": datetime.now()
    })
```

## 🔧 **配置管理**

### **通过settings访问配置**：
```python
from config.settings import settings

# 获取MongoDB配置
connection_string = settings.mongodb_connection_string
host = settings.mongodb_host
port = settings.mongodb_port
database = settings.mongodb_database
options = settings.mongodb_options
```

### **环境变量覆盖**：
```bash
# 设置环境变量覆盖配置
export MONGODB_HOST="your-host"
export MONGODB_PASSWORD="your-password"
export DATABASE_TYPE="mongodb"
```

## 🛠️ **故障排除**

### **常见问题**：

#### **1. 连接超时**
```
❌ MongoDB服务器选择超时
```
**解决方案**：
- 检查网络连接
- 确认服务器地址和端口正确
- 检查防火墙设置

#### **2. 认证失败**
```
❌ MongoDB操作失败: Authentication failed
```
**解决方案**：
- 检查用户名和密码
- 确认认证源（authSource）正确
- 检查用户权限

#### **3. 数据库不存在**
```
❌ 无法获取数据库
```
**解决方案**：
- 确认数据库名称正确
- 检查用户是否有访问该数据库的权限

### **调试模式**：
在 `config/config.yaml` 中启用调试：
```yaml
debug: true
logging:
  level: DEBUG
```

## 📊 **性能优化**

### **连接池配置**：
```yaml
mongodb:
  options:
    maxPoolSize: 100      # 最大连接数
    minPoolSize: 10       # 最小连接数
    connectTimeoutMS: 30000   # 连接超时
    socketTimeoutMS: 360000   # 套接字超时
```

### **最佳实践**：
1. **使用连接池**：避免频繁创建和关闭连接
2. **合理设置超时**：根据网络环境调整超时时间
3. **监控连接状态**：定期检查连接健康状态
4. **错误处理**：妥善处理连接异常

## 🔄 **集成到现有服务**

### **在API服务中使用**：
```python
# 在 start_api_service.py 中
from database.mongodb_connection import mongodb_connection

async def startup():
    if mongodb_connection.connect():
        logger.info("MongoDB连接成功")
    else:
        logger.error("MongoDB连接失败")

async def shutdown():
    mongodb_connection.disconnect()
```

### **在Bot交互服务中使用**：
```python
# 在 start_bot_interactive.py 中
from database.mongodb_connection import get_mongodb_collection

async def log_user_action(user_id, action):
    collection = get_mongodb_collection("user_actions")
    if collection:
        collection.insert_one({
            "user_id": user_id,
            "action": action,
            "timestamp": datetime.now()
        })
```

现在你可以运行 `python test_mongodb_connection.py` 来测试MongoDB连接了！
