# 周期性推送功能指南

## 📋 **功能概述**

周期性推送功能是一个新的推送类型，区别于即时推送和定时推送。它通过周期性查询游戏日志表，检测满足条件的获奖记录并自动发送推送通知。

### **核心特性**
- 🔄 **周期性监控**：每分钟轮询一次游戏日志表
- 🎯 **智能过滤**：基于游戏类型、平台、获奖倍数等条件筛选
- 📊 **多商户支持**：支持不同商户的独立配置
- 🚫 **避免重复**：使用ID追踪机制避免重复处理
- ⏰ **时间窗口**：查询create_time+5分钟后的数据确保游戏结束

## 🏗️ **系统架构**

### **数据库扩展**
- **c_tgNotify表**：新增 `types=300` 配置，支持以下新字段：
  - `gameType`: 游戏类型过滤
  - `platformId`: 平台ID过滤  
  - `gameId`: 游戏ID过滤
  - `gameWinMul`: 获奖倍数阈值

### **数据源**
- **游戏日志表**：`ea_platform_agent_game_log_YYYY-MM`
- **主键字段**：`number` (varchar)
- **时间字段**：`create_time` (Unix时间戳)
- **关键字段**：`gameType`, `platformId`, `gameId`, `betAmount`, `winAmount`

### **查询策略**
```sql
SELECT * FROM `ea_platform_agent_game_log_2025-07`
WHERE number > last_processed_number 
  AND create_time < NOW() - 4分钟
  AND create_time > NOW() - 1小时
ORDER BY number ASC
LIMIT 1000
```

## 🚀 **部署和配置**

### **1. 配置数据库**
运行配置脚本设置测试数据：
```bash
python tmp/setup_periodic_push_config.py
```

### **2. 配置文件更新**
在 `config/config.yaml` 中已添加周期性推送配置：
```yaml
periodic_push:
  check_interval: 60          # 检查间隔（秒）
  game_end_wait: 300         # 游戏结束等待时间（秒）
  query_time_range:
    min_ago: 240             # 查询4分钟前的数据
    max_ago: 3600            # 最多查询1小时前的数据
  max_records_per_query: 1000 # 每次查询最大记录数
```

### **3. 启动服务**
```bash
# 独立启动周期性推送服务
python start_periodic_push_service.py

# 或使用简化测试版本
python tmp/simple_start_periodic.py
```

## 📊 **配置示例**

### **MongoDB配置 (c_tgNotify)**
```javascript
{
  "merchantId": 1001,
  "groupId": -1002316158105,
  "templateId": "periodic_win_template_1",
  "language": 1,
  "types": 300,              // 周期性推送类型
  "gameType": 102,           // 限制游戏类型
  "platformId": 3,           // 限制平台ID
  "gameId": 300003,          // 限制游戏ID (null=不限制)
  "gameWinMul": 5.0,         // 获奖倍数阈值 >= 5倍
  "status": 1
}
```

### **模板配置 (c_tgTemplate)**
```javascript
{
  "templateId": "periodic_win_template_1",
  "language": 1,
  "title": "🎉 Big Win Alert!",
  "content": "🎊 Congratulations! Player {arg1} just won {arg2}x their bet amount of {arg3} in {arg4}! Total winnings: {arg5}! 🎊",
  "type": "periodic_win",
  "status": 1
}
```

## 🧪 **测试和验证**

### **1. 基本功能测试**
```bash
python tmp/test_periodic_push.py
```

### **2. 获奖条件测试**
```bash
python tmp/test_win_condition.py
```

### **3. 简化功能测试**
```bash
python tmp/simple_periodic_test.py
```

## 📈 **监控和日志**

### **日志级别**
- `INFO`: 正常运行状态、配置加载、推送成功
- `DEBUG`: 详细查询信息、条件检查过程
- `ERROR`: 连接失败、推送失败、异常情况

### **关键指标**
- 每分钟处理的记录数
- 满足条件的获奖记录数
- 推送成功/失败率
- 最后处理的number值

## 🔧 **故障排除**

### **常见问题**

1. **服务无法启动**
   - 检查数据库连接配置
   - 确认MongoDB和MySQL服务正常
   - 验证配置文件格式

2. **没有检测到获奖记录**
   - 检查获奖倍数阈值设置
   - 确认游戏类型/平台ID匹配
   - 验证时间窗口设置

3. **推送失败**
   - 检查Bot Token配置
   - 确认群组ID正确
   - 验证模板ID存在

### **调试命令**
```bash
# 检查配置加载
python -c "from services.periodic_push_service import periodic_push_service; import asyncio; asyncio.run(periodic_push_service.initialize())"

# 检查数据库连接
python tmp/simple_mysql_test.py

# 检查MongoDB配置
python tmp/setup_periodic_push_config.py
```

## 📝 **开发说明**

### **核心文件**
- `services/periodic_push_service.py`: 主服务类
- `start_periodic_push_service.py`: 独立启动脚本
- `config/config.yaml`: 配置文件
- `tmp/setup_periodic_push_config.py`: 配置初始化脚本

### **扩展点**
- 支持更多游戏条件过滤
- 添加推送频率限制
- 实现推送模板动态加载
- 支持多表监控

## 🎯 **使用场景**

1. **大奖提醒**：玩家获得高倍数奖金时自动推送
2. **活动监控**：特定游戏或平台的获奖情况
3. **商户通知**：不同商户的个性化获奖推送
4. **数据分析**：获奖趋势和频率统计

## 📞 **技术支持**

如需技术支持或功能扩展，请联系开发团队。

---

**版本**: 1.0.0  
**更新时间**: 2025-07-13  
**状态**: ✅ 已完成并测试通过
