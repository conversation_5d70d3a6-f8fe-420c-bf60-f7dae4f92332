# Telegram Bot 框架 - 项目结构

## 📁 目录结构

```
tgbot/
├── README.md                    # 项目文档
├── requirements.txt             # Python依赖
├── PROJECT_STRUCTURE.md         # 项目结构说明（本文件）
├── main.py                     # 主服务启动入口
│
├── config/                     # 配置模块
│   ├── __init__.py
│   ├── settings.py             # 配置管理类
│   └── config.yaml             # 配置文件
│
├── api/                        # FastAPI服务模块
│   ├── __init__.py
│   ├── main.py                 # FastAPI应用主程序
│   ├── routers/                # API路由
│   │   ├── __init__.py
│   │   ├── messages.py         # 消息发送API
│   │   └── tasks.py            # 任务管理API
│   ├── models/                 # API数据模型
│   │   ├── __init__.py
│   │   ├── message.py          # 消息模型
│   │   └── task.py             # 任务模型
│   └── middleware/             # 中间件
│       ├── __init__.py
│       └── auth.py             # 认证中间件
│
├── scheduler/                  # 定时任务模块
│   ├── __init__.py
│   ├── main.py                 # 定时任务主程序
│   ├── manager.py              # 任务管理器
│   ├── executor.py             # 任务执行器
│   └── jobs/                   # 任务类型
│       ├── __init__.py
│       ├── message_job.py      # 消息发送任务
│       └── custom_job.py       # 自定义任务
│
├── database/                   # 数据库模块（简化版）
│   ├── __init__.py
│   └── connection.py           # 数据库连接和查询
│
├── interactive/                # 交互模块（接收用户消息）
│   ├── __init__.py
│   ├── main.py                 # 交互模块主程序
│   ├── handlers/               # 消息处理器
│   │   ├── __init__.py
│   │   └── message_handlers.py # 消息处理逻辑
│   └── utils/                  # 交互工具
│       ├── __init__.py
│       └── conversation.py     # 对话管理
│
├── pusher/                     # 推送模块（运维推送消息）
│   ├── __init__.py
│   ├── main.py                 # 推送模块主程序
│   ├── menu/                   # 菜单管理
│   │   ├── __init__.py
│   │   └── menu_builder.py     # 菜单构建器
│   └── utils/                  # 推送工具
│       ├── __init__.py
│       └── message_sender.py   # 消息发送器
│
├── common/                     # 通用模块
│   ├── __init__.py
│   ├── bot_client.py           # Bot客户端封装
│   ├── utils.py                # 通用工具函数
│   └── exceptions.py           # 自定义异常
│
├── data/                       # 数据存储
│   ├── chat_lists/             # 聊天列表存储
│   └── menus/                  # 菜单配置
│       ├── help_center.json    # 帮助中心菜单
│       └── main_menu.json      # 主菜单
│
└── images/                     # 图片文件目录（预留）
```

## 🎯 核心模块说明

### 1. 配置模块 (config/)

**作用**: 统一管理项目配置

- `settings.py`: 配置管理类，支持环境变量和配置文件
- `config.yaml`: YAML格式的配置文件

**主要功能**:
- Bot Token管理
- 代理配置（本地开发/生产环境）
- Webhook配置
- 日志级别配置
- 调试模式开关

### 2. 交互模块 (interactive/)

**作用**: 处理用户与Bot的交互

- `main.py`: 交互服务主程序，启动Bot监听
- `handlers/message_handlers.py`: 处理各种类型的用户消息
- `utils/conversation.py`: 管理对话状态和上下文

**主要功能**:
- 接收用户消息（文本、图片、文档等）
- 处理Bot命令（/start, /help, /status, /echo）
- 对话状态管理
- 自动清理旧对话数据
- 支持Webhook和轮询模式

### 3. 推送模块 (pusher/)

**作用**: 运维人员主动推送消息和菜单

- `main.py`: 命令行推送工具主程序
- `menu/menu_builder.py`: 菜单构建和模板管理
- `utils/message_sender.py`: 消息发送功能实现

**主要功能**:
- 发送文本消息
- 发送图片消息
- 发送菜单消息
- 批量广播消息
- 聊天列表管理
- 菜单模板管理
- 发送日志记录

### 4. 通用模块 (common/)

**作用**: 提供各模块共用的功能

- `bot_client.py`: Telegram Bot API客户端封装
- `utils.py`: 通用工具函数

**主要功能**:
- 统一的Bot API调用接口
- 代理支持
- 错误处理和重试机制
- 通用工具函数
- 日志系统设置

### 5. 数据库模块 (database/)

**作用**: 简化的数据库连接和只读查询

- `connection.py`: 数据库连接管理和查询功能
- `example_usage.py`: 使用示例和最佳实践

**主要功能**:
- MySQL连接池管理
- 任务配置查询（只读）
- 直接SQL查询，参考SMS项目风格

**重要约束**:
- **只进行查询操作，不修改数据库**
- 数据库修改由专门的服务程序负责
- 表结构由外部系统管理
- 避免服务间的数据库操作冲突

### 6. 数据存储 (data/)

**作用**: 持久化存储项目数据

- `chat_lists/`: 存储聊天列表的JSON文件
- `menus/`: 存储菜单配置的JSON文件

**数据格式**:
- 聊天列表: `{"name": "list_name", "chat_ids": [123, 456], "description": "..."}`
- 菜单配置: `{"title": "菜单标题", "buttons": [...], "type": "..."}`

## 🚀 使用方法

### 启动交互服务
直接运行交互模块主程序

```bash
python interactive/main.py
```

### 使用推送功能
直接运行推送模块主程序

```bash
# 发送文本消息
python pusher/main.py send-text --chat-id 123456789 --text "Hello"

# 管理聊天列表
python pusher/main.py manage-list --name subscribers --add "123,456"

# 发送菜单
python pusher/main.py send-menu --chat-id 123456789 --menu main_menu
```

## 📋 框架特性

### ✅ 模块化设计
- 交互和推送功能完全分离
- 每个模块职责单一，便于维护
- 支持独立部署和扩展

### ✅ 配置灵活
- 支持环境变量配置
- 支持YAML配置文件
- 本地开发和生产环境自动切换

### ✅ 代理支持
- 本地开发时可使用代理
- 生产环境可直连
- 配置简单，一键切换

### ✅ 数据持久化
- JSON文件存储，简单可靠
- 聊天列表管理
- 菜单模板管理
- 发送记录追踪

### ✅ 命令行工具
- 丰富的命令行参数
- 批量操作支持
- 详细的操作反馈

### ✅ 日志系统
- 完整的日志记录
- 可配置日志级别
- 错误追踪和调试支持

## 🔧 扩展指南

### 添加新的消息处理器
在 `interactive/handlers/message_handlers.py` 中添加新的处理函数

### 添加新的推送功能
在 `pusher/main.py` 中添加新的命令和处理逻辑

### 添加新的菜单类型
在 `pusher/menu/menu_builder.py` 中扩展菜单构建逻辑

### 添加新的配置项
在 `config/settings.py` 中添加新的属性和 `config.yaml` 中添加配置

## 📝 开发建议

1. **保持模块独立**: 各模块之间通过common模块通信
2. **统一错误处理**: 使用common/utils.py中的工具函数
3. **配置优先级**: 环境变量 > 配置文件 > 默认值
4. **日志记录**: 重要操作都要记录日志
5. **数据验证**: 输入数据要进行验证和清理

## 🎯 使用场景

### 交互模块适用于:
- 客服Bot
- 问答Bot
- 游戏Bot
- 个人助手Bot

### 推送模块适用于:
- 系统通知
- 营销推广
- 运营活动
- 批量通知

这个框架提供了完整的Telegram Bot开发基础，可以根据具体需求进行扩展和定制。
