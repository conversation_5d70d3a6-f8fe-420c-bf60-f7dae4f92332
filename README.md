# Telegram Bot 框架

一个功能完整的 Telegram Bot 框架，包含交互模块和推送模块，支持代理配置。

## 🏗️ 项目结构

```
tgbot/
├── config/                 # 配置模块
│   ├── __init__.py
│   ├── settings.py         # 配置管理
│   └── config.yaml         # 配置文件
├── interactive/            # 交互模块
│   ├── __init__.py
│   ├── main.py            # 交互模块主程序
│   ├── handlers/          # 消息处理器
│   │   ├── __init__.py
│   │   └── message_handlers.py
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── conversation.py
├── pusher/                # 推送模块
│   ├── __init__.py
│   ├── main.py            # 推送模块主程序
│   ├── menu/              # 菜单管理
│   │   ├── __init__.py
│   │   └── menu_builder.py
│   └── utils/             # 工具函数
│       ├── __init__.py
│       └── message_sender.py
├── common/                # 通用工具和类
│   ├── __init__.py
│   ├── bot_client.py      # Telegram Bot客户端封装
│   └── utils.py           # 通用工具函数
├── data/                  # 数据目录
│   ├── chat_lists/        # 聊天列表存储
│   └── menus/             # 菜单配置存储
├── examples/              # 使用示例
├── requirements.txt       # Python依赖
└── README.md             # 项目说明
```

## 🚀 快速开始

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置 Bot Token

**方法一：环境变量（推荐）**
```bash
export BOT_TOKEN="your_bot_token_here"
```

**方法二：配置文件**
编辑 `config/config.yaml`：
```yaml
bot:
  token: "your_bot_token_here"
```

### 配置代理（可选）

**本地开发环境**
编辑 `config/config.yaml`：
```yaml
proxy:
  enabled: true
  url: "http://127.0.0.1:7890"  # 根据你的代理地址修改
```

**生产环境**
```yaml
proxy:
  enabled: false
```

## 🚀 使用方法

### 启动交互模块
```bash
python interactive/main.py
```

### 使用推送模块
```bash
# 发送文本消息
python pusher/main.py send-text --chat-id 123456789 --text "Hello World"

# 查看帮助
python pusher/main.py --help
```

## 📋 核心功能

### 交互模块 (interactive/)
- ✅ 接收和处理用户消息
- ✅ 支持多种消息类型（文本、图片、文档）
- ✅ 命令处理（/start, /help, /status, /echo）
- ✅ 对话状态管理
- ✅ 自动清理旧对话数据
- ✅ 支持 Webhook 和轮询模式

### 推送模块 (pusher/)
- ✅ 命令行界面
- ✅ 发送文本消息
- ✅ 发送图片消息
- ✅ 发送菜单消息
- ✅ 批量广播消息
- ✅ 聊天列表管理
- ✅ 菜单模板管理
- ✅ 发送日志记录

### 通用功能
- ✅ 统一的配置管理
- ✅ 代理支持（本地开发/生产环境切换）
- ✅ Bot 客户端封装
- ✅ 日志系统
- ✅ 工具函数库

## 🔧 配置说明

### 环境变量
| 变量名 | 说明 | 示例 |
|--------|------|------|
| `BOT_TOKEN` | Telegram Bot Token | `123456:ABC-DEF...` |
| `USE_PROXY` | 是否使用代理 | `true` / `false` |
| `PROXY_URL` | 代理地址 | `http://127.0.0.1:7890` |
| `WEBHOOK_URL` | Webhook URL（生产环境） | `https://your-domain.com/webhook` |
| `WEBHOOK_PORT` | Webhook 端口 | `8443` |
| `DEBUG` | 调试模式 | `true` / `false` |
| `LOG_LEVEL` | 日志级别 | `DEBUG` / `INFO` / `WARNING` / `ERROR` |

### 配置文件 (config/config.yaml)
```yaml
# Bot配置
bot:
  # token: "YOUR_BOT_TOKEN_HERE"  # 建议使用环境变量

# 代理配置
proxy:
  enabled: false  # 本地开发时设为true，生产环境设为false
  url: "http://127.0.0.1:7890"

# Webhook配置（生产环境使用）
webhook:
  # url: "https://your-domain.com/webhook"
  port: 8443

# 日志配置
logging:
  level: "INFO"

# 调试模式
debug: false
```

## 📱 使用示例

### 交互模块使用

启动交互模块后，用户可以：
- 发送 `/start` 开始使用
- 发送 `/help` 查看帮助
- 发送 `/status` 查看状态
- 发送 `/echo 消息` 回显消息
- 发送普通文本进行对话
- 发送图片和文档

### 推送模块使用

#### 1. 管理聊天列表
```bash
# 创建聊天列表
python pusher/main.py manage-list --name subscribers --add "123456789,987654321" --description "订阅用户列表"

# 查看聊天列表
python pusher/main.py manage-list --name subscribers --list

# 添加更多聊天
python pusher/main.py manage-list --name subscribers --add "111222333"

# 移除聊天
python pusher/main.py manage-list --name subscribers --remove "987654321"
```

#### 2. 发送消息
```bash
# 发送文本消息到单个聊天
python pusher/main.py send-text --chat-id 123456789 --text "系统维护通知"

# 发送文本消息到聊天列表
python pusher/main.py send-text --chat-list subscribers --text "新功能上线啦！"

# 发送菜单到聊天列表
python pusher/main.py send-menu --chat-list all_users --menu main_menu
```

#### 3. 创建和管理菜单
```bash
# 创建主菜单
python pusher/main.py create-menu --name main_menu --type main

# 创建服务菜单
python pusher/main.py create-menu --name services_menu --type services

# 创建联系菜单
python pusher/main.py create-menu --name contact_menu --type contact
```

## 🔒 安全注意事项

1. **Token 安全**：
   - 不要将 Bot Token 提交到版本控制系统
   - 使用环境变量或安全的配置管理系统
   - 定期轮换 Token

2. **代理配置**：
   - 生产环境建议直连，避免使用代理
   - 如需使用代理，确保代理服务器安全可靠

3. **权限控制**：
   - 推送功能应限制管理员使用
   - 实施适当的访问控制和审计

## 🐛 故障排除

### 常见问题

1. **Bot Token 错误**
   ```
   错误：Bot token not found
   解决：检查环境变量 BOT_TOKEN 或配置文件中的 token 设置
   ```

2. **代理连接失败**
   ```
   错误：Proxy connection failed
   解决：检查代理地址和端口，确保代理服务正常运行
   ```

3. **消息发送失败**
   ```
   错误：Failed to send message
   解决：检查聊天ID是否正确，用户是否已启动Bot
   ```

### 日志查看
程序运行时会在控制台输出详细的运行日志。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
