# 🚀 Telegram Bot 服务启动指南

## 📋 **服务组件说明**

当前项目包含三个主要服务组件：

### 1. **📡 API推送服务** (`main.py`)
- **功能**：提供HTTP API接口用于推送消息
- **端口**：8000 (默认)
- **用途**：外部系统调用推送消息到Telegram

### 2. **⏰ 定时任务调度器** (`scheduler`)
- **功能**：执行定时推送任务
- **用途**：根据数据库配置定时发送消息

### 3. **🤖 Bot交互监听** (`interactive`)
- **功能**：监听用户消息，处理 `/start` 等命令
- **用途**：用户与Bot的实时交互

## 🎯 **启动方式选择**

### **方案1: 完整服务启动（推荐）**

**一键启动所有服务**：
```bash
python start_full_bot_service.py
```

**包含功能**：
- ✅ API推送服务 (http://localhost:8000)
- ✅ 定时任务调度器
- ✅ Bot交互监听 (处理用户消息)

**适用场景**：
- 🎯 生产环境部署
- 🔧 完整功能测试
- 📱 用户交互 + API推送

---

### **方案2: 分别启动（推荐用于开发）**

#### **启动API推送服务**：
```bash
python start_api_service.py
```
- ✅ API服务器 (http://localhost:8000)
- ✅ 定时任务调度器
- ❌ 不包含Bot交互监听

#### **启动Bot交互服务**：
```bash
python start_bot_interactive.py
```
- ✅ Bot交互监听（处理用户消息）
- ✅ 菜单系统（私聊/群聊分离）
- ❌ 不包含API推送功能

**适用场景**：
- 🔧 功能模块单独调试
- 📊 性能测试和监控
- 🐛 问题排查和定位

---

### **方案3: 仅测试特定功能**

#### **仅测试API推送**：
```bash
python start_api_service.py
```
然后使用 `test_api.http` 测试推送功能

#### **仅测试Bot交互**：
```bash
python start_bot_interactive.py
```
然后在Telegram中发送 `/start` 测试菜单

## 📊 **服务状态检查**

### **API服务检查**：
```bash
curl http://localhost:8000/health
```

### **Bot交互检查**：
在Telegram中发送 `/start` 命令

### **完整服务检查**：
1. 访问 http://localhost:8000/health
2. 在Telegram中发送 `/start`
3. 使用API发送推送消息

## 🔧 **配置要求**

### **必需配置** (`config/config.yaml`)：
```yaml
bot:
  token: "your-telegram-bot-token"  # 必需

api:
  host: "0.0.0.0"
  port: 8000

database:
  url: "your-database-url"  # 定时任务需要
```

### **可选配置**：
```yaml
proxy:
  enabled: false
  url: "http://proxy:port"

webhook:
  url: ""  # 生产环境使用
  port: 8443
```

## 📝 **日志输出说明**

### **完整服务启动日志**：
```
🚀 启动完整的Telegram Bot服务
📡 API服务地址: http://0.0.0.0:8000
⏰ 定时任务: 已启用
🤖 Bot交互: 已启用
🔧 运行模式: Polling
```

### **服务状态日志**：
```
✅ Bot Token配置正确
🌐 代理未启用
INFO: FastAPI服务器启动...
INFO: 定时任务调度器启动...
INFO: Bot交互监听启动...
INFO: Bot正在运行中...
```

## 🚨 **常见问题解决**

### **1. Bot Token错误**
```
❌ 配置错误: Bot token not found
```
**解决**：检查 `config/config.yaml` 中的 `bot.token` 配置

### **2. 端口占用**
```
❌ FastAPI服务器启动失败: [Errno 48] Address already in use
```
**解决**：
- 修改 `config.yaml` 中的 `api.port`
- 或停止占用端口的进程

### **3. 网络连接问题**
```
❌ Bot交互监听启动失败: Network error
```
**解决**：
- 检查网络连接
- 启用代理配置（如需要）

### **4. 数据库连接问题**
```
❌ 定时任务调度器启动失败: Database connection failed
```
**解决**：
- 检查数据库配置
- 确保数据库服务运行正常

## 🎯 **推荐使用流程**

### **开发阶段**：
1. **功能开发**：使用方案2分别启动测试
2. **集成测试**：使用方案1完整启动测试
3. **问题调试**：使用方案2单独调试问题模块

### **生产部署**：
1. **配置检查**：确认所有配置正确
2. **完整启动**：使用方案1启动所有服务
3. **监控检查**：确认所有服务正常运行

### **日常维护**：
1. **功能测试**：定期测试API和Bot交互
2. **日志监控**：关注错误日志和性能指标
3. **配置更新**：根据需要调整配置参数

## 📱 **测试建议**

### **API推送测试**：
使用 `test_api.http` 文件中的测试用例

### **Bot交互测试**：
1. 私聊Bot发送 `/start`
2. 群组中发送 `/start`
3. 测试各种菜单功能

### **完整流程测试**：
1. 启动完整服务
2. 测试API推送功能
3. 测试Bot交互功能
4. 验证定时任务功能

选择适合你当前需求的启动方式开始使用吧！
