#!/usr/bin/env python3
"""
分析c_baseLanguage表结构
确定语言ID和语言代码的映射关系
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_language_table():
    """分析语言表结构"""
    print("🌐 分析c_baseLanguage表结构")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_baseLanguage")
        if collection is None:
            print("❌ 无法获取c_baseLanguage集合")
            return False
        
        # 获取所有语言记录
        languages = list(collection.find({}))
        
        if not languages:
            print("❌ 语言表中没有数据")
            return False
        
        print(f"📊 总语言数: {len(languages)}")
        
        # 分析字段结构
        print(f"\n📋 字段结构分析:")
        print("-" * 40)
        
        if languages:
            first_record = languages[0]
            for key, value in first_record.items():
                if key != '_id':
                    value_type = type(value).__name__
                    print(f"  {key:<20} {value_type:<10} = {value}")
        
        # 显示所有语言记录
        print(f"\n📋 所有语言记录:")
        print("-" * 60)
        print(f"{'ID':<5} {'代码':<10} {'名称':<20} {'状态':<10}")
        print("-" * 60)
        
        for lang in languages:
            lang_id = lang.get("id", "N/A")
            lang_code = lang.get("code", lang.get("languageCode", "N/A"))
            lang_name = lang.get("name", lang.get("languageName", "N/A"))
            status = lang.get("status", lang.get("enabled", "N/A"))
            
            print(f"{lang_id:<5} {lang_code:<10} {lang_name:<20} {status:<10}")
        
        # 生成语言映射
        print(f"\n🔧 生成语言映射:")
        print("-" * 40)
        
        language_mapping = {}
        for lang in languages:
            lang_id = lang.get("id")
            # 尝试多个可能的字段名
            lang_code = (lang.get("code") or 
                        lang.get("languageCode") or 
                        lang.get("lang_code") or
                        lang.get("locale"))
            
            if lang_id and lang_code:
                language_mapping[str(lang_id)] = lang_code
                print(f"  {lang_id} -> {lang_code}")
            else:
                print(f"  ⚠️ 语言 {lang_id} 缺少代码字段")
        
        print(f"\n📄 语言映射JSON:")
        print(json.dumps(language_mapping, indent=2, ensure_ascii=False))
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def suggest_language_mapping():
    """建议语言映射方案"""
    print(f"\n💡 语言映射建议")
    print("=" * 60)
    
    print("基于常见的语言ID和代码映射:")
    print()
    
    common_mappings = {
        "1": "en",      # 英文
        "2": "zh",      # 简体中文
        "3": "zh_tw",   # 繁体中文
        "4": "ja",      # 日文
        "5": "ko",      # 韩文
        "6": "th",      # 泰文
        "7": "vi",      # 越南文
        "8": "id",      # 印尼文
        "9": "ms",      # 马来文
        "10": "hi",     # 印地文
    }
    
    print("建议的映射关系:")
    for lang_id, lang_code in common_mappings.items():
        print(f"  {lang_id} -> {lang_code}")
    
    print(f"\n📝 如果c_baseLanguage表的字段名不是'code'，")
    print(f"请修改translation_manager.py中的字段名:")
    print(f"  lang_code = lang.get('你的字段名')")


def test_translation_manager():
    """测试翻译管理器的语言加载"""
    print(f"\n🧪 测试翻译管理器")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试加载语言映射
        success = translation_manager.load_language_mapping()
        
        if success:
            print("✅ 语言映射加载成功")
            print(f"📊 映射结果: {translation_manager.language_mapping}")
            
            # 测试语言代码获取
            test_ids = [1, 2, 3, 4, 5]
            print(f"\n🔤 语言代码测试:")
            for lang_id in test_ids:
                lang_code = translation_manager.get_language_code(lang_id)
                print(f"  ID {lang_id} -> {lang_code}")
        else:
            print("❌ 语言映射加载失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 c_baseLanguage表分析")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 分析语言表结构
    success1 = analyze_language_table()
    
    # 2. 建议映射方案
    suggest_language_mapping()
    
    # 3. 测试翻译管理器
    success2 = test_translation_manager()
    
    print(f"\n" + "=" * 60)
    print("📊 分析完成")
    print("=" * 60)
    
    if success1 and success2:
        print("✅ 语言表分析成功")
        print("💡 翻译管理器可以正常加载语言映射")
    elif success1:
        print("✅ 语言表分析成功")
        print("⚠️ 翻译管理器加载失败，请检查字段名")
    else:
        print("❌ 语言表分析失败")
        print("💡 请检查数据库连接和表结构")
    
    return 0 if (success1 and success2) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
