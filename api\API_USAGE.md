# Telegram Bot Service API 使用文档

## 📋 API接口列表

### 1. 发送消息接口

**接口**: `POST /api/send-message`

**描述**: 发送消息到指定的用户和群组，支持文本、图片、链接等多种内容

#### 请求参数

```json
{
  "bot_token": "your-telegram-bot-token",
  "chat_ids": [987654321, 123456789],
  "group_ids": [111111111, 222222222],
  "text": "这是一个推送文本，点击链接获取更多信息！",
  "banner_image_url": "https://example.com/banner.jpg",
  "link": "https://example.com",
  "tg_short_link": "https://t.me/shortlink",
  "type": "webapp"
}
```

#### 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| `bot_token` | string | ✅ | Telegram Bot Token |
| `chat_ids` | array[int] | ❌ | 用户的chat_id列表 |
| `group_ids` | array[int] | ❌ | 群组的group_id列表 |
| `text` | string | ✅ | 消息文本内容 |
| `banner_image_url` | string | ❌ | Banner图片URL |
| `link` | string | ❌ | 普通跳转链接 |
| `tg_short_link` | string | ❌ | TG短链接 |
| `type` | string | ❌ | 跳转方式: "webapp" 或 "browser"，默认"browser" |

#### 响应示例

```json
{
  "success": true,
  "message": "Message sent successfully to the specified users/groups",
  "targets": {
    "chat_ids": 2,
    "group_ids": 2,
    "total": 4
  }
}
```

#### 使用示例

```bash
# 发送纯文本消息
curl -X POST http://localhost:8000/api/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "bot_token": "your-bot-token",
    "chat_ids": [123456789],
    "text": "Hello, World!"
  }'

# 发送带图片和链接的消息
curl -X POST http://localhost:8000/api/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "bot_token": "your-bot-token",
    "chat_ids": [123456789],
    "group_ids": [111111111],
    "text": "查看我们的最新活动！",
    "banner_image_url": "https://example.com/banner.jpg",
    "link": "https://example.com/activity",
    "type": "webapp"
  }'
```

---

### 2. 任务重载接口

**接口**: `POST /api/task-reload`

**描述**: 从数据库重新加载定时任务配置

#### 请求参数

```json
{
  "task_id": "daily_report",
  "action": "reload"
}
```

#### 响应示例

```json
{
  "success": true,
  "message": "任务重载完成",
  "task_id": "daily_report",
  "result": {
    "task_id": "daily_report",
    "name": "每日报告",
    "schedule": "0 9 * * *",
    "enabled": true
  }
}
```

---

### 3. 调度器状态接口

**接口**: `GET /api/scheduler-status`

**描述**: 获取定时任务调度器的运行状态

#### 响应示例

```json
{
  "status": "running",
  "job_count": 5,
  "jobs": [
    {
      "id": "daily_report",
      "name": "每日报告",
      "next_run_time": "2024-01-02T09:00:00",
      "trigger": "cron"
    }
  ],
  "timestamp": "2024-01-01T12:00:00"
}
```

---

### 4. 健康检查接口

**接口**: `GET /health`

**描述**: 检查服务健康状态

#### 响应示例

```json
{
  "status": "healthy",
  "service": "telegram-bot-service",
  "version": "1.0.0",
  "timestamp": "2024-01-01T12:00:00"
}
```

---

## 🔧 接口特点

### ✅ 统一的消息发送

- **多目标支持**: 一次请求可以发送到多个用户和群组
- **多媒体支持**: 支持文本、图片、链接等内容
- **链接类型**: 支持普通链接和TG短链接
- **跳转方式**: 支持webapp和browser两种打开方式

### ✅ 灵活的配置

- **可选参数**: 除了bot_token和text，其他参数都是可选的
- **目标验证**: 必须指定chat_ids或group_ids中的至少一个
- **异步处理**: 消息发送在后台异步处理，接口快速响应

### ✅ 任务管理

- **只读操作**: 只从数据库读取配置，不修改数据库
- **动态重载**: 支持运行时重新加载任务配置
- **状态监控**: 提供调度器状态查询

---

## 🚀 快速开始

### 1. 启动服务

```bash
python main.py
```

### 2. 访问API文档

```
http://localhost:8000/docs
```

### 3. 测试接口

```bash
# 健康检查
curl http://localhost:8000/health

# 发送测试消息
curl -X POST http://localhost:8000/api/send-message \
  -H "Content-Type: application/json" \
  -d '{
    "bot_token": "your-bot-token",
    "chat_ids": [your-chat-id],
    "text": "API测试消息"
  }'
```

---

## ⚠️ 注意事项

1. **Bot Token**: 确保使用有效的Telegram Bot Token
2. **Chat ID**: 确保chat_id和group_id是正确的
3. **权限**: Bot需要有向目标用户/群组发送消息的权限
4. **图片URL**: banner_image_url必须是可访问的公网URL
5. **异步处理**: 消息发送是异步的，接口返回成功不代表消息已发送成功
