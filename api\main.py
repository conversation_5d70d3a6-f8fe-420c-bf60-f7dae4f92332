"""
FastAPI应用主程序
简化版本，所有API接口都在这个文件中
"""
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field
from typing import Dict, Any, List, Optional, Union
import logging
import asyncio
from datetime import datetime

from config.settings import settings
from pusher.utils.message_sender import message_sender
from pusher.template_message_handler import template_handler
from scheduler.main import scheduler_manager
from scheduler.config_manager import config_manager
from common.translation_manager import translation_manager
from middleware.api_logging import add_api_logging_middleware
from config.logging_config import app_logger

logger = app_logger


# 简化的数据模型
class SendMessageRequest(BaseModel):
    """发送消息请求"""
    bot_token: str = Field(..., description="Telegram Bot Token")
    chat_ids: Optional[List[int]] = Field(None, description="用户的chat_id列表")
    group_ids: Optional[List[int]] = Field(None, description="群组的group_id列表")
    text: str = Field(..., description="消息文本内容（必须）")
    banner_image_url: Optional[str] = Field(None, description="Banner图片URL（可选）")
    link: Optional[str] = Field(None, description="普通跳转链接（可选）")
    tg_short_link: Optional[str] = Field(None, description="TG短链接（可选）")
    link_text: Optional[str] = Field(None, description="链接显示文字（可选）")
    type: Optional[str] = Field("browser", description="跳转方式: webapp 或 browser")

    class Config:
        schema_extra = {
            "example": {
                "bot_token": "your-telegram-bot-token",
                "chat_ids": [987654321, 123456789],
                "group_ids": [111111111, 222222222],
                "text": "这是一个推送文本，点击链接获取更多信息！",
                "banner_image_url": "https://example.com/banner.jpg",
                "link": "https://example.com",
                "tg_short_link": "https://t.me/shortlink",
                "link_text": "🔗 点击查看详情",
                "type": "webapp"
            }
        }


class ConfigUpdateRequest(BaseModel):
    """统一配置更新请求"""
    update_type: str = Field(..., description="更新类型: all, robot_config, notify_config, scheduled_tasks")
    target_ids: Optional[List[str]] = Field(None, description="指定更新的ID列表，为空则更新全部")

    class Config:
        schema_extra = {
            "example": {
                "update_type": "robot_config",
                "target_ids": ["39bac42a", "40bac42b"]
            }
        }


class TemplateMessageRequest(BaseModel):
    """模板消息推送请求"""
    business_no: str = Field(..., description="商户号")
    type: int = Field(..., description="消息类型，对应notify表的types字段")
    params: List[Any] = Field(..., description="参数列表，支持嵌套数组")
    activity_id: Optional[int] = Field(None, description="活动ID，用于获取活动名称翻译")

    class Config:
        schema_extra = {
            "example": {
                "business_no": "39bac42a",
                "type": 18000,  # 支持int类型
                "params": [
                    "user_name_zhang",
                    ["game_a", "game_b"],
                    ["100", "50"],
                    "thank_you_message"
                ],
                "activity_id": 12  # 可选的活动ID
            }
        }


def create_app() -> FastAPI:
    """创建FastAPI应用"""

    app = FastAPI(
        title="Telegram Bot Service API",
        description="提供Bot消息发送和定时任务管理的API服务",
        version="1.0.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 添加API日志中间件
    add_api_logging_middleware(app)

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # 简化配置
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # API接口定义

    @app.post("/api/send-message")
    async def send_message(request: SendMessageRequest, background_tasks: BackgroundTasks):
        """发送消息到指定用户和群组"""
        try:
            # 验证请求参数
            if not request.chat_ids and not request.group_ids:
                raise HTTPException(status_code=400, detail="必须指定chat_ids或group_ids")

            total_targets = len(request.chat_ids or []) + len(request.group_ids or [])
            logger.info(f"收到发送消息请求: 目标数量={total_targets}, 文本长度={len(request.text)}")

            # 异步发送消息
            background_tasks.add_task(
                _send_message_task,
                request
            )

            return {
                "success": True,
                "message": "Message sent successfully to the specified users/groups",
                "targets": {
                    "chat_ids": len(request.chat_ids or []),
                    "group_ids": len(request.group_ids or []),
                    "total": total_targets
                }
            }

        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            raise HTTPException(status_code=500, detail=f"发送消息失败: {str(e)}")

    @app.post("/api/config-update")
    async def update_config(request: ConfigUpdateRequest, background_tasks: BackgroundTasks):
        """
        统一配置更新接口

        支持四种更新类型:
        - all: 更新所有三张表 (c_tbRobotConfig + c_tgNotify + c_tgScheduledPushTasks)
        - robot_config: 更新Bot配置表缓存 (c_tbRobotConfig)
        - notify_config: 更新通知配置表缓存 (c_tgNotify)
        - scheduled_tasks: 更新定时任务表缓存 (c_tgScheduledPushTasks)
        """
        try:
            update_type = request.update_type
            target_ids = request.target_ids

            logger.info(f"📢 收到配置更新请求: {update_type}, 目标: {target_ids or '全部'}")

            if update_type == "all":
                # 更新所有三张表
                background_tasks.add_task(_update_all_configs)
                message = "开始更新所有三表配置 (c_tbRobotConfig + c_tgNotify + c_tgScheduledPushTasks)"

            elif update_type == "robot_config":
                # 更新Bot配置表缓存
                background_tasks.add_task(_update_robot_configs, target_ids)
                target_desc = f"商户: {target_ids}" if target_ids else "全部商户"
                message = f"开始更新Bot配置缓存 ({target_desc})"

            elif update_type == "notify_config":
                # 更新通知配置表缓存
                background_tasks.add_task(_update_notify_configs, target_ids)
                target_desc = f"通知ID: {target_ids}" if target_ids else "全部通知"
                message = f"开始更新通知配置缓存 ({target_desc})"

            elif update_type == "scheduled_tasks":
                # 更新定时任务表缓存
                background_tasks.add_task(_update_scheduled_tasks, target_ids)
                target_desc = f"任务ID: {target_ids}" if target_ids else "全部任务"
                message = f"开始更新定时任务缓存 ({target_desc})"

            else:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的更新类型: {update_type}。支持的类型: all, robot_config, notify_config, scheduled_tasks"
                )

            return {
                "success": True,
                "message": message,
                "data": {
                    "update_type": update_type,
                    "target_ids": target_ids,
                    "target_count": len(target_ids) if target_ids else "全部"
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"❌ 处理配置更新请求失败: {e}")
            raise HTTPException(status_code=500, detail=f"处理配置更新请求失败: {str(e)}")

    @app.post("/api/realtime-push/template")
    async def send_template_message(request: TemplateMessageRequest):
        """发送模板消息"""
        try:
            logger.info("=" * 60)
            logger.info(f"📥 收到模板消息推送请求")
            logger.info(f"   商户号: {request.business_no}")
            logger.info(f"   消息类型: {request.type}")
            logger.info(f"   参数数量: {len(request.params)}")
            logger.info(f"   参数内容: {request.params}")
            logger.info(f"   活动ID: {request.activity_id}")
            logger.info("=" * 60)

            # 同步处理模板消息发送
            logger.info(f"🔄 开始同步处理模板消息...")
            result = await template_handler.send_template_message(
                business_no=request.business_no,
                message_type=request.type,
                params=request.params,
                activity_id=request.activity_id
            )

            if result["success"]:
                logger.info(f"✅ 模板消息发送成功: {result['message']}")
                details = result.get("details", {})

                return {
                    "status": "success",
                    "message": f"消息发送成功: {details.get('success_count', 0)}/{details.get('total_count', 0)} 个聊天",
                    "data": {
                        "business_no": request.business_no,
                        "type": request.type,
                        "params": request.params,
                        "rendered_message": details.get("rendered_message", ""),
                        "template_id": details.get("template_id"),
                        "template_name": details.get("template_name"),
                        "language": details.get("language"),
                        "target_chats": details.get("target_chats", []),
                        "success_count": details.get("success_count", 0),
                        "total_count": details.get("total_count", 0),
                        "results": details.get("results", [])
                    }
                }
            else:
                logger.error(f"❌ 模板消息发送失败: {result['message']}")
                return {
                    "status": "failed",
                    "message": result["message"],
                    "data": {
                        "business_no": request.business_no,
                        "type": request.type,
                        "params": request.params
                    }
                }

        except Exception as e:
            logger.error(f"❌ 处理模板消息推送请求失败: {e}")
            raise HTTPException(status_code=500, detail=f"处理模板消息推送请求失败: {str(e)}")

    return app


# 辅助函数

async def _send_message_task(request: SendMessageRequest):
    """异步发送消息任务"""
    try:
        # 收集所有目标ID
        all_targets = []
        if request.chat_ids:
            all_targets.extend([(chat_id, 'user') for chat_id in request.chat_ids])
        if request.group_ids:
            all_targets.extend([(group_id, 'group') for group_id in request.group_ids])

        logger.info(f"开始发送消息到 {len(all_targets)} 个目标")

        success_count = 0
        error_count = 0

        for target_id, target_type in all_targets:
            try:
                # 调用现有的message_sender发送消息
                if request.banner_image_url:
                    # 发送图片消息（先尝试下载，失败则直接发送URL）
                    success = await message_sender.send_photo_url_message(
                        chat_id=target_id,
                        photo_url=request.banner_image_url,
                        caption=request.text,
                        link=request.link,
                        tg_short_link=request.tg_short_link,
                        link_text=request.link_text,
                        link_type=request.type,
                        download_image=True  # 尝试下载图片
                    )
                else:
                    # 发送文本消息
                    success = await message_sender.send_text_message(
                        chat_id=target_id,
                        text=request.text,
                        link=request.link,
                        tg_short_link=request.tg_short_link,
                        link_text=request.link_text,
                        link_type=request.type
                    )

                if success:
                    success_count += 1
                    logger.debug(f"消息发送成功: {target_type} {target_id}")
                else:
                    error_count += 1
                    logger.error(f"发送到 {target_type} {target_id} 失败")

                # 添加发送间隔，避免触发限制
                await asyncio.sleep(0.1)

            except Exception as e:
                error_count += 1
                logger.error(f"发送到 {target_type} {target_id} 失败: {e}")

        logger.info(f"消息发送完成: 成功 {success_count}, 失败 {error_count}")

    except Exception as e:
        logger.error(f"异步发送消息任务失败: {e}")


# ========== 配置更新后台函数 ==========

async def _update_all_configs():
    """更新所有三表配置"""
    try:
        logger.info("🔄 开始更新所有三表配置")

        # 刷新所有三张表的缓存
        await config_manager.refresh_all_cache()

        logger.info("✅ 所有三表配置更新完成")

    except Exception as e:
        logger.error(f"❌ 更新所有配置失败: {e}")


async def _update_robot_configs(target_business_nos: Optional[List[str]] = None):
    """更新Bot配置缓存"""
    try:
        await config_manager.refresh_robot_config(target_business_nos)
        logger.info("✅ Bot配置更新完成")

    except Exception as e:
        logger.error(f"❌ 更新Bot配置失败: {e}")


async def _update_notify_configs(target_notify_ids: Optional[List[str]] = None):
    """更新通知配置缓存"""
    try:
        if target_notify_ids:
            # 转换字符串ID为整数
            notify_ids = [int(notify_id) for notify_id in target_notify_ids if notify_id.isdigit()]
            await config_manager.refresh_notify_config(notify_ids)
        else:
            await config_manager.refresh_notify_config()

        logger.info("✅ 通知配置更新完成")

    except Exception as e:
        logger.error(f"❌ 更新通知配置失败: {e}")


async def _update_scheduled_tasks(target_task_ids: Optional[List[str]] = None):
    """更新定时任务缓存"""
    try:
        if target_task_ids:
            # 转换字符串ID为整数
            task_ids = [int(task_id) for task_id in target_task_ids if task_id.isdigit()]
            await config_manager.refresh_scheduled_tasks(task_ids)
        else:
            await config_manager.refresh_scheduled_tasks()

        logger.info("✅ 定时任务缓存更新完成 (内存共享，立即生效)")

    except Exception as e:
        logger.error(f"❌ 更新定时任务失败: {e}")


async def _send_template_message_background(business_no: str, message_type: Union[int, str], params: List[Any]):
    """后台发送模板消息"""
    try:
        logger.info(" 开始后台处理模板消息")
        logger.info(f"   商户号: {business_no}")
        logger.info(f"   消息类型: {message_type}")
        logger.info(f"   参数: {params}")
        logger.info(f"   目标聊天: 从notify配置获取")

        result = await template_handler.send_template_message(
            business_no=business_no,
            message_type=message_type,
            params=params
        )

        if result["success"]:
            logger.info(f"✅ 后台模板消息处理成功: {result['message']}")
            details = result.get("details", {})
            if details:
                logger.info(f"   模板ID: {details.get('template_id')}")
                logger.info(f"   模板名称: {details.get('template_name')}")
                logger.info(f"   语言: {details.get('language')}")
                logger.info(f"   发送结果: {details.get('results', [])}")
        else:
            logger.error(f"❌ 后台模板消息处理失败: {result['message']}")

    except Exception as e:
        logger.error(f"❌ 后台发送模板消息异常: {e}")
        import traceback
        logger.error(f"异常堆栈: {traceback.format_exc()}")
