#!/usr/bin/env python3
"""
检查当前task表结构
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection

def check_task_table():
    """检查当前task表结构"""
    print("🔍 检查当前task表结构")
    print("=" * 50)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        if collection is None:
            print("❌ 无法获取c_tgScheduledPushTasks集合")
            return False
        
        # 获取所有记录
        records = list(collection.find({}))
        print(f"📊 表中记录数: {len(records)}")
        
        if records:
            print(f"\n📋 表结构分析:")
            
            # 分析第一条记录的字段
            first_record = records[0]
            print(f"📄 第一条记录的字段:")
            for key, value in first_record.items():
                value_type = type(value).__name__
                if key == '_id':
                    print(f"   {key}: {value} ({value_type})")
                else:
                    print(f"   {key}: {value} ({value_type})")
            
            print(f"\n📋 所有记录详情:")
            for i, record in enumerate(records, 1):
                print(f"\n记录 {i}:")
                for key, value in record.items():
                    if key != '_id':
                        print(f"   {key}: {value}")
        else:
            print("📋 表为空，无记录")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    check_task_table()
