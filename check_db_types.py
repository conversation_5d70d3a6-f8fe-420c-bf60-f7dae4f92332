#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection

mongo = MongoDBConnection()
mongo.connect()

# 检查notify表
collection = mongo.get_collection('c_tgNotify')
records = list(collection.find({}))
print(f"c_tgNotify records: {len(records)}")
for r in records:
    lang = r.get('language')
    print(f"ID={r.get('id')}, language={lang}, type={type(lang).__name__}")

mongo.disconnect()
