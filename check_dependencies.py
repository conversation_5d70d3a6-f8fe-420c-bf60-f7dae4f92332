#!/usr/bin/env python3
"""
检查项目依赖
"""
import os
import re
from pathlib import Path

def find_imports():
    """查找所有Python文件中的import语句"""
    imports = set()
    
    # 遍历所有Python文件
    for py_file in Path('.').rglob('*.py'):
        if '__pycache__' in str(py_file):
            continue
            
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找import语句
            import_lines = re.findall(r'^(?:import|from)\s+([a-zA-Z_][a-zA-Z0-9_]*)', content, re.MULTILINE)
            for imp in import_lines:
                # 排除本地模块
                if not imp.startswith(('config', 'api', 'database', 'common', 'pusher', 'scheduler', 'bot')):
                    imports.add(imp)
                    
        except Exception as e:
            print(f"读取文件 {py_file} 失败: {e}")
    
    return sorted(imports)

def check_requirements():
    """检查requirements.txt中的包"""
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取包名
        packages = set()
        for line in content.split('\n'):
            line = line.strip()
            if line and not line.startswith('#'):
                # 提取包名（去除版本号）
                package = re.split(r'[>=<]', line)[0].strip()
                if package:
                    packages.add(package)
        
        return sorted(packages)
    except FileNotFoundError:
        return []

def main():
    print("🔍 检查项目依赖")
    print("=" * 50)
    
    # 查找实际使用的import
    print("📋 项目中使用的第三方库:")
    used_imports = find_imports()
    for imp in used_imports:
        print(f"  • {imp}")
    
    print(f"\n📦 requirements.txt中的包:")
    required_packages = check_requirements()
    for pkg in required_packages:
        print(f"  • {pkg}")
    
    # 检查缺失的依赖
    print(f"\n🔍 依赖检查:")
    
    # 标准库模块（不需要安装）
    stdlib_modules = {
        'asyncio', 'datetime', 'json', 'logging', 'os', 'pathlib', 'sys', 
        'time', 'typing', 'uuid', 're', 'traceback', 'threading', 'collections',
        'functools', 'itertools', 'operator', 'copy', 'pickle', 'base64',
        'hashlib', 'hmac', 'urllib', 'http', 'email', 'html', 'xml', 'csv'
    }
    
    # 映射关系（import名 -> 包名）
    import_to_package = {
        'telegram': 'python-telegram-bot',
        'httpx': 'httpx',
        'yaml': 'PyYAML',
        'aiohttp': 'aiohttp',
        'dotenv': 'python-dotenv',
        'PIL': 'Pillow',
        'imageio': 'imageio',
        'fastapi': 'fastapi',
        'uvicorn': 'uvicorn',
        'pydantic': 'pydantic',
        'apscheduler': 'apscheduler',
        'aiomysql': 'aiomysql',
        'pymongo': 'pymongo',
        'motor': 'motor',
        'requests': 'requests',
        'jinja2': 'jinja2'
    }
    
    missing_packages = []
    for imp in used_imports:
        if imp not in stdlib_modules:
            package_name = import_to_package.get(imp, imp)
            if package_name not in required_packages:
                missing_packages.append(f"{imp} -> {package_name}")
    
    if missing_packages:
        print("❌ 缺失的依赖:")
        for missing in missing_packages:
            print(f"  • {missing}")
    else:
        print("✅ 所有依赖都已包含在requirements.txt中")
    
    # 检查可能多余的包
    used_package_names = set()
    for imp in used_imports:
        if imp not in stdlib_modules:
            package_name = import_to_package.get(imp, imp)
            used_package_names.add(package_name)
    
    unused_packages = []
    for pkg in required_packages:
        # 去除版本号和选项
        clean_pkg = re.split(r'[\[\]]', pkg)[0]
        if clean_pkg not in used_package_names and clean_pkg not in {
            'pytest', 'pytest-asyncio', 'black', 'flake8', 'mypy'  # 开发依赖
        }:
            unused_packages.append(pkg)
    
    if unused_packages:
        print(f"\n⚠️ 可能未使用的包:")
        for unused in unused_packages:
            print(f"  • {unused}")
    
    print(f"\n📊 统计:")
    print(f"  使用的第三方库: {len(used_imports)}")
    print(f"  requirements.txt中的包: {len(required_packages)}")
    print(f"  缺失的依赖: {len(missing_packages)}")

if __name__ == "__main__":
    main()
