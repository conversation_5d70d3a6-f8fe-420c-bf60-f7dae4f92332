#!/usr/bin/env python3
"""
检查MongoDB player表
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_mongo_player():
    """检查MongoDB player表"""
    print("🔍 检查MongoDB player表")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        print(f"✅ MongoDB连接成功")
        
        # 列出所有数据库
        print(f"\n📋 可用数据库:")
        db_names = mongo.client.list_database_names()
        for db_name in db_names:
            print(f"   - {db_name}")
        
        # 检查wingame_game库是否存在
        if "wingame_game" in db_names:
            print(f"\n✅ wingame_game库存在")
            
            # 切换到wingame_game库
            db = mongo.client["wingame_game"]
            
            # 列出所有集合
            print(f"\n📋 wingame_game库中的集合:")
            collection_names = db.list_collection_names()
            for collection_name in collection_names:
                print(f"   - {collection_name}")
            
            # 检查player集合是否存在
            if "player" in collection_names:
                print(f"\n✅ player集合存在")
                
                collection = db["player"]
                
                # 统计文档数量
                count = collection.count_documents({})
                print(f"   文档总数: {count}")
                
                if count > 0:
                    # 查看前几个文档的结构
                    print(f"\n📄 前3个文档结构:")
                    sample_docs = list(collection.find().limit(3))
                    
                    for i, doc in enumerate(sample_docs, 1):
                        print(f"\n   文档 {i}:")
                        for key, value in doc.items():
                            print(f"     {key}: {value} ({type(value).__name__})")
                    
                    # 检查是否有playerId字段
                    has_player_id = collection.count_documents({"playerId": {"$exists": True}})
                    print(f"\n📊 统计信息:")
                    print(f"   有playerId字段的文档: {has_player_id}")
                    
                    # 检查是否有playerName字段
                    has_player_name = collection.count_documents({"playerName": {"$exists": True}})
                    print(f"   有playerName字段的文档: {has_player_name}")
                    
                    # 查找一些具体的playerId
                    test_ids = [4743182, 2268779, 7731938]
                    print(f"\n🔍 查找测试playerId:")
                    for test_id in test_ids:
                        doc = collection.find_one({"playerId": test_id})
                        if doc:
                            player_name = doc.get("playerName", "未知")
                            print(f"   playerId={test_id} -> playerName='{player_name}' ✅")
                        else:
                            print(f"   playerId={test_id} -> 未找到 ❌")
                    
                    # 随机查看一些playerId范围
                    print(f"\n📊 playerId范围分析:")
                    pipeline = [
                        {"$match": {"playerId": {"$exists": True}}},
                        {"$group": {
                            "_id": None,
                            "min_id": {"$min": "$playerId"},
                            "max_id": {"$max": "$playerId"},
                            "count": {"$sum": 1}
                        }}
                    ]
                    
                    result = list(collection.aggregate(pipeline))
                    if result:
                        stats = result[0]
                        print(f"   最小playerId: {stats.get('min_id')}")
                        print(f"   最大playerId: {stats.get('max_id')}")
                        print(f"   总数: {stats.get('count')}")
                    
                    # 查看一些实际存在的playerId
                    print(f"\n📋 实际存在的playerId示例:")
                    sample_players = list(collection.find({"playerId": {"$exists": True}}).limit(5))
                    for player in sample_players:
                        player_id = player.get("playerId")
                        player_name = player.get("playerName", "未知")
                        print(f"   playerId={player_id} -> playerName='{player_name}'")
                
                else:
                    print(f"❌ player集合为空")
            else:
                print(f"❌ player集合不存在")
        else:
            print(f"❌ wingame_game库不存在")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 检查MongoDB player表结构和数据")
    print("=" * 80)
    
    success = check_mongo_player()
    
    if success:
        print(f"\n💡 检查完成")
        print(f"请根据上面的信息确认:")
        print(f"1. wingame_game库是否存在")
        print(f"2. player集合是否存在")
        print(f"3. playerId和playerName字段是否存在")
        print(f"4. 测试的playerId是否在数据库中")
    else:
        print(f"\n⚠️ 检查失败")

if __name__ == "__main__":
    main()
