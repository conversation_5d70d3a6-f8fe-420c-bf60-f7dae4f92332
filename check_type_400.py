#!/usr/bin/env python3
"""
检查type=400的配置，确认会发送几个TG消息
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import asyncio

async def check_type_400_config():
    """检查type=400的配置"""
    print("🔍 检查type=400的配置")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 获取所有notify配置
        all_configs = config_manager.get_all_notify_configs()
        
        # 查找包含type=400的配置
        configs_with_400 = []
        for config in all_configs:
            types = config.get("types", [])
            if 400 in types:
                configs_with_400.append(config)
        
        print(f"📋 包含type=400的配置数量: {len(configs_with_400)}")
        
        if not configs_with_400:
            print(f"❌ 未找到type=400的配置")
            print(f"💡 这意味着API调用会失败，不会发送任何消息")
            return 0
        
        total_messages = 0
        
        for i, config in enumerate(configs_with_400, 1):
            print(f"\n📋 配置 {i}:")
            print(f"   ID: {config.get('id')}")
            print(f"   名称: {config.get('tgName')}")
            print(f"   类型: {config.get('types')}")
            print(f"   通知类型: {config.get('notifyType')}")
            
            # 检查聊天ID
            chat_ids = config.get("chatIds", [])
            print(f"   聊天ID: {chat_ids}")
            print(f"   聊天数量: {len(chat_ids)}")
            
            # 检查模板
            template = config.get("template", "")
            if template:
                print(f"   模板: {template[:100]}...")
            else:
                print(f"   ⚠️ 没有模板")
            
            # 检查语言
            language = config.get("language", "")
            print(f"   语言: {language}")
            
            # 计算这个配置会发送的消息数
            if chat_ids and template:
                config_messages = len(chat_ids)
                print(f"   📤 此配置会发送: {config_messages} 条消息")
                total_messages += config_messages
            else:
                print(f"   ❌ 此配置无法发送消息（缺少聊天ID或模板）")
        
        print(f"\n" + "=" * 50)
        print(f"📊 总结:")
        print(f"   • 匹配的配置数: {len(configs_with_400)}")
        print(f"   • 总消息数: {total_messages}")
        
        if total_messages > 0:
            print(f"✅ API调用会发送 {total_messages} 条TG消息")
        else:
            print(f"❌ API调用不会发送任何TG消息")
        
        return total_messages
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return 0

async def simulate_api_call():
    """模拟API调用过程"""
    print(f"\n🎯 模拟API调用过程")
    print("=" * 50)
    
    try:
        from pusher.template_message_handler import TemplateMessageHandler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建处理器
        handler = TemplateMessageHandler()
        
        # 模拟查找配置
        print(f"📋 查找type=400的配置...")
        config = handler._find_notify_config_by_type(400)
        
        if config:
            print(f"✅ 找到配置: {config.get('tgName')}")
            
            # 检查必要字段
            chat_ids = config.get("chatIds", [])
            template = config.get("template", "")
            language = config.get("language", "")
            
            print(f"📋 配置详情:")
            print(f"   聊天ID数量: {len(chat_ids)}")
            print(f"   有模板: {'是' if template else '否'}")
            print(f"   语言: {language}")
            
            if chat_ids and template:
                print(f"✅ 配置完整，会发送 {len(chat_ids)} 条消息")
                return len(chat_ids)
            else:
                print(f"❌ 配置不完整，无法发送消息")
                return 0
        else:
            print(f"❌ 未找到type=400的配置")
            return 0
            
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        return 0

async def main():
    """主函数"""
    print("🚀 type=400消息数量分析")
    print("=" * 60)
    
    # 1. 检查配置
    config_messages = await check_type_400_config()
    
    # 2. 模拟API调用
    api_messages = await simulate_api_call()
    
    print(f"\n" + "=" * 60)
    print("📊 最终结论:")
    
    if config_messages == api_messages and config_messages > 0:
        print(f"🎉 API调用 type=400 会发送 {config_messages} 条TG消息")
    elif config_messages == 0:
        print(f"❌ 没有type=400的配置，API调用会失败")
    else:
        print(f"⚠️ 配置检查和API模拟结果不一致")
        print(f"   配置检查: {config_messages} 条")
        print(f"   API模拟: {api_messages} 条")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
