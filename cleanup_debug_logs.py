#!/usr/bin/env python3
"""
清理调试日志和print语句
"""
import re
from pathlib import Path
from config.logging_config import app_logger

def cleanup_debug_logs():
    """清理translation_manager.py中的调试日志"""
    file_path = Path("common/translation_manager.py")
    
    if not file_path.exists():
        app_logger.error(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 移除所有包含 [DEBUG] 的日志行
        lines = content.split('\n')
        cleaned_lines = []
        
        for line in lines:
            # 跳过包含 [DEBUG] 的行
            if '[DEBUG]' in line and ('logger.error' in line or 'logger.info' in line):
                app_logger.info(f"移除调试日志: {line.strip()}")
                continue
            cleaned_lines.append(line)
        
        # 写回文件
        cleaned_content = '\n'.join(cleaned_lines)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(cleaned_content)
        
        app_logger.info(f"✅ 清理完成: {file_path}")
        return True
        
    except Exception as e:
        app_logger.error(f"清理失败: {e}")
        return False

def remove_print_statements():
    """移除测试文件中的print语句"""
    test_files = [
        "test_activity_name.py",
        "test_activity_debug.py", 
        "test_activity_fix.py",
        "test_simplified_activity.py",
        "test_smart_activity_name.py",
        "test_fix.py",
        "simple_activity_test.py",
        "simple_smart_test.py",
        "test_language_match.py",
        "test_db_direct.py"
    ]
    
    for file_name in test_files:
        file_path = Path(file_name)
        if not file_path.exists():
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换print为logger
            lines = content.split('\n')
            modified_lines = []
            
            for line in lines:
                if line.strip().startswith('print('):
                    # 将print替换为logger.info
                    modified_line = line.replace('print(', 'app_logger.info(')
                    modified_lines.append(modified_line)
                    app_logger.info(f"替换print: {file_name} - {line.strip()}")
                else:
                    modified_lines.append(line)
            
            # 在文件开头添加logger导入
            if 'from config.logging_config import app_logger' not in content:
                # 找到第一个import行的位置
                import_index = 0
                for i, line in enumerate(modified_lines):
                    if line.startswith('import ') or line.startswith('from '):
                        import_index = i
                        break
                
                # 在import区域添加logger导入
                modified_lines.insert(import_index, 'from config.logging_config import app_logger')
            
            # 写回文件
            modified_content = '\n'.join(modified_lines)
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(modified_content)
            
            app_logger.info(f"✅ 处理完成: {file_path}")
            
        except Exception as e:
            app_logger.error(f"处理失败 {file_path}: {e}")

def main():
    """主函数"""
    app_logger.info("🔧 开始清理调试日志和print语句")
    app_logger.info("=" * 60)
    
    # 1. 清理调试日志
    app_logger.info("1. 清理translation_manager.py中的调试日志")
    cleanup_debug_logs()
    
    # 2. 处理测试文件中的print语句
    app_logger.info("\n2. 处理测试文件中的print语句")
    remove_print_statements()
    
    app_logger.info("\n🎉 清理完成！")
    app_logger.info("💡 现在所有日志都会保存到 logs/ 目录中")
    app_logger.info("📁 日志文件按日期分目录存储")

if __name__ == "__main__":
    main()
