"""
Telegram Bot客户端封装
提供统一的Bot客户端接口，支持代理配置
"""
import logging
import os
from typing import Optional, Dict, Any
from telegram import Bot
from telegram.ext import Application

from config.settings import settings


class TelegramBotClient:
    """Telegram Bot客户端封装类"""
    
    def __init__(self, token: Optional[str] = None):
        self.token = token or settings.bot_token
        self.logger = logging.getLogger(__name__)
        self._bot: Optional[Bot] = None
        self._application: Optional[Application] = None
        self._setup_proxy_env()
        
    def _setup_proxy_env(self):
        """设置代理环境变量"""
        if settings.use_proxy and settings.proxy_url:
            self.logger.info(f"Using proxy: {settings.proxy_url}")
            # 设置环境变量，这是最兼容的方式
            os.environ['HTTPS_PROXY'] = settings.proxy_url
            os.environ['HTTP_PROXY'] = settings.proxy_url
            # 为了兼容性，也设置小写版本
            os.environ['https_proxy'] = settings.proxy_url
            os.environ['http_proxy'] = settings.proxy_url
        else:
            # 如果不使用代理，清除代理环境变量
            for key in ['HTTPS_PROXY', 'HTTP_PROXY', 'https_proxy', 'http_proxy']:
                if key in os.environ:
                    del os.environ[key]
    
    @property
    def bot(self) -> Bot:
        """获取Bot实例"""
        if self._bot is None:
            # 使用默认配置创建Bot，代理通过环境变量设置
            self._bot = Bot(token=self.token)
        return self._bot
    
    def get_application(self) -> Application:
        """获取Application实例"""
        if self._application is None:
            # 使用默认配置创建Application，代理通过环境变量设置
            self._application = Application.builder().token(self.token).build()
        return self._application
    
    async def send_message(self, chat_id: int, text: str, **kwargs) -> Any:
        """发送消息"""
        try:
            return await self.bot.send_message(chat_id=chat_id, text=text, **kwargs)
        except Exception as e:
            self.logger.error(f"Failed to send message: {e}")
            raise
    
    async def send_photo(self, chat_id: int, photo, caption: Optional[str] = None, **kwargs) -> Any:
        """发送图片"""
        try:
            return await self.bot.send_photo(chat_id=chat_id, photo=photo, caption=caption, **kwargs)
        except Exception as e:
            self.logger.error(f"Failed to send photo: {e}")
            raise
    
    async def send_document(self, chat_id: int, document, caption: Optional[str] = None, **kwargs) -> Any:
        """发送文档"""
        try:
            return await self.bot.send_document(chat_id=chat_id, document=document, caption=caption, **kwargs)
        except Exception as e:
            self.logger.error(f"Failed to send document: {e}")
            raise

    async def send_animation(self, chat_id: int, animation, caption: Optional[str] = None, **kwargs) -> Any:
        """发送动图（GIF等）"""
        try:
            return await self.bot.send_animation(chat_id=chat_id, animation=animation, caption=caption, **kwargs)
        except Exception as e:
            self.logger.error(f"Failed to send animation: {e}")
            raise

    async def send_video(self, chat_id: int, video, caption: Optional[str] = None, **kwargs) -> Any:
        """发送视频"""
        try:
            return await self.bot.send_video(chat_id=chat_id, video=video, caption=caption, **kwargs)
        except Exception as e:
            self.logger.error(f"Failed to send video: {e}")
            raise


# 全局Bot客户端实例
bot_client = TelegramBotClient()
