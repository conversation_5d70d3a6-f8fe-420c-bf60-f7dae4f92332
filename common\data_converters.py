#!/usr/bin/env python3
"""
数据转换器 - 通用的数据转换函数
包含playerId->playerName, gameId->gameName等转换功能
"""
import logging
from typing import Optional, Dict, List
from database.mongodb_connection import MongoDBConnection

logger = logging.getLogger(__name__)


class DataConverter:
    """数据转换器类"""
    
    def __init__(self):
        self.mongo_conn = MongoDBConnection()
        # 缓存，避免重复查询
        self._player_name_cache = {}
        self._game_name_cache = {}
    
    async def get_player_name(self, player_id: int) -> str:
        """
        通过playerId获取playerName
        如果找不到玩家，返回 'WG' + str(player_id) (机器人玩家)
        
        Args:
            player_id: 玩家ID
            
        Returns:
            str: 玩家名称或机器人名称
        """
        try:
            # 检查缓存
            if player_id in self._player_name_cache:
                return self._player_name_cache[player_id]
            
            # 连接MongoDB wingame_game库
            if not self.mongo_conn.connect():
                logger.warning(f"⚠️ MongoDB连接失败，使用robot名称: WG{player_id}")
                robot_name = f'WG{player_id}'
                self._player_name_cache[player_id] = robot_name
                return robot_name

            # 切换到wingame_game库
            db = self.mongo_conn.client["wingame_game"]
            collection = db["player"]

            # 查询player记录
            player_doc = collection.find_one({"playerId": player_id})

            if player_doc and "playerName" in player_doc:
                player_name = str(player_doc["playerName"])
                logger.debug(f"🔍 查询到玩家名: playerId={player_id} -> playerName={player_name}")
                self._player_name_cache[player_id] = player_name
                return player_name
            else:
                robot_name = f'WG{player_id}'
                logger.debug(f"⚠️ 未找到玩家名: playerId={player_id}，使用robot名称: {robot_name}")
                self._player_name_cache[player_id] = robot_name
                return robot_name

        except Exception as e:
            robot_name = f'WG{player_id}'
            logger.warning(f"⚠️ 查询玩家名失败: playerId={player_id}, 错误: {e}，使用robot名称: {robot_name}")
            self._player_name_cache[player_id] = robot_name
            return robot_name
    
    async def batch_get_player_names(self, player_ids: List[int]) -> Dict[int, str]:
        """
        批量查询玩家名称
        
        Args:
            player_ids: 玩家ID列表
            
        Returns:
            Dict[int, str]: playerId -> playerName 的映射
        """
        try:
            # 检查缓存，过滤已缓存的ID
            result = {}
            uncached_ids = []
            
            for player_id in player_ids:
                if player_id in self._player_name_cache:
                    result[player_id] = self._player_name_cache[player_id]
                else:
                    uncached_ids.append(player_id)
            
            if not uncached_ids:
                return result
            
            # 连接MongoDB wingame_game库
            if not self.mongo_conn.connect():
                logger.warning(f"⚠️ MongoDB连接失败，使用robot名称")
                for player_id in uncached_ids:
                    robot_name = f'WG{player_id}'
                    result[player_id] = robot_name
                    self._player_name_cache[player_id] = robot_name
                return result

            # 切换到wingame_game库
            db = self.mongo_conn.client["wingame_game"]
            collection = db["player"]

            # 批量查询
            player_docs = collection.find({"playerId": {"$in": uncached_ids}})

            # 构建映射字典
            found_ids = set()

            for doc in player_docs:
                player_id = doc.get("playerId")
                player_name = str(doc.get("playerName", player_id))
                result[player_id] = player_name
                self._player_name_cache[player_id] = player_name
                found_ids.add(player_id)

            # 对于未找到的playerId，使用WG+playerId作为名称（robot用户）
            for player_id in uncached_ids:
                if player_id not in found_ids:
                    robot_name = f'WG{player_id}'
                    result[player_id] = robot_name
                    self._player_name_cache[player_id] = robot_name
                    logger.debug(f"⚠️ 未找到玩家名: playerId={player_id}，使用robot名称: {robot_name}")

            logger.debug(f"🔍 批量查询玩家名完成: {len(found_ids)}/{len(uncached_ids)} 找到")
            return result

        except Exception as e:
            logger.warning(f"⚠️ 批量查询玩家名失败，使用robot名称: {e}")
            for player_id in uncached_ids:
                robot_name = f'WG{player_id}'
                result[player_id] = robot_name
                self._player_name_cache[player_id] = robot_name
            return result
    
    async def get_game_name(self, game_id: int, language: int = 1) -> str:
        """
        通过gameId获取gameName
        
        Args:
            game_id: 游戏ID
            language: 语言ID (1=英语, 2=葡萄牙语等)，暂时忽略，直接使用gameName字段
            
        Returns:
            str: 游戏名称，如果找不到返回 str(game_id)
        """
        try:
            # 检查缓存
            cache_key = f"{game_id}_{language}"
            if cache_key in self._game_name_cache:
                return self._game_name_cache[cache_key]
            
            # 连接MongoDB wingame_config库
            if not self.mongo_conn.connect():
                logger.warning(f"⚠️ MongoDB连接失败，使用gameId: {game_id}")
                fallback_name = str(game_id)
                self._game_name_cache[cache_key] = fallback_name
                return fallback_name

            # 获取c_gameApi集合
            collection = self.mongo_conn.get_collection("c_gameApi")
            if collection is None:
                logger.warning(f"⚠️ 无法获取c_gameApi集合，使用gameId: {game_id}")
                fallback_name = str(game_id)
                self._game_name_cache[cache_key] = fallback_name
                return fallback_name

            # 查询游戏记录
            game_doc = collection.find_one({"gameId": game_id})

            if game_doc and "gameName" in game_doc:
                game_name = str(game_doc["gameName"])
                logger.debug(f"🔍 查询到游戏名: gameId={game_id} -> gameName={game_name}")
                self._game_name_cache[cache_key] = game_name
                return game_name
            else:
                logger.debug(f"⚠️ 未找到游戏名，使用gameId: {game_id}")
                fallback_name = str(game_id)
                self._game_name_cache[cache_key] = fallback_name
                return fallback_name

        except Exception as e:
            logger.warning(f"⚠️ 查询游戏名失败: gameId={game_id}, 错误: {e}，使用gameId")
            fallback_name = str(game_id)
            self._game_name_cache[cache_key] = fallback_name
            return fallback_name
    
    async def batch_get_game_names(self, game_ids: List[int], language: int = 1) -> Dict[int, str]:
        """
        批量查询游戏名称
        
        Args:
            game_ids: 游戏ID列表
            language: 语言ID，暂时忽略
            
        Returns:
            Dict[int, str]: gameId -> gameName 的映射
        """
        try:
            # 检查缓存，过滤已缓存的ID
            result = {}
            uncached_ids = []
            
            for game_id in game_ids:
                cache_key = f"{game_id}_{language}"
                if cache_key in self._game_name_cache:
                    result[game_id] = self._game_name_cache[cache_key]
                else:
                    uncached_ids.append(game_id)
            
            if not uncached_ids:
                return result
            
            # 连接MongoDB wingame_config库
            if not self.mongo_conn.connect():
                logger.warning(f"⚠️ MongoDB连接失败，使用gameId")
                for game_id in uncached_ids:
                    fallback_name = str(game_id)
                    result[game_id] = fallback_name
                    cache_key = f"{game_id}_{language}"
                    self._game_name_cache[cache_key] = fallback_name
                return result

            # 获取c_gameApi集合
            collection = self.mongo_conn.get_collection("c_gameApi")
            if collection is None:
                logger.warning(f"⚠️ 无法获取c_gameApi集合，使用gameId")
                for game_id in uncached_ids:
                    fallback_name = str(game_id)
                    result[game_id] = fallback_name
                    cache_key = f"{game_id}_{language}"
                    self._game_name_cache[cache_key] = fallback_name
                return result

            # 批量查询
            game_docs = collection.find({"gameId": {"$in": uncached_ids}})

            # 构建映射字典
            found_ids = set()

            for doc in game_docs:
                game_id = doc.get("gameId")
                game_name = str(doc.get("gameName", game_id))
                result[game_id] = game_name
                cache_key = f"{game_id}_{language}"
                self._game_name_cache[cache_key] = game_name
                found_ids.add(game_id)

            # 对于未找到的gameId，使用gameId作为名称
            for game_id in uncached_ids:
                if game_id not in found_ids:
                    fallback_name = str(game_id)
                    result[game_id] = fallback_name
                    cache_key = f"{game_id}_{language}"
                    self._game_name_cache[cache_key] = fallback_name
                    logger.debug(f"⚠️ 未找到游戏名: gameId={game_id}，使用gameId")

            logger.debug(f"🔍 批量查询游戏名完成: {len(found_ids)}/{len(uncached_ids)} 找到")
            return result

        except Exception as e:
            logger.warning(f"⚠️ 批量查询游戏名失败，使用gameId: {e}")
            for game_id in uncached_ids:
                fallback_name = str(game_id)
                result[game_id] = fallback_name
                cache_key = f"{game_id}_{language}"
                self._game_name_cache[cache_key] = fallback_name
            return result
    
    def clear_cache(self):
        """清空缓存"""
        self._player_name_cache.clear()
        self._game_name_cache.clear()
        logger.debug("🧹 数据转换器缓存已清空")
    
    def disconnect(self):
        """断开数据库连接"""
        if self.mongo_conn:
            self.mongo_conn.disconnect()


# 全局数据转换器实例
data_converter = DataConverter()
