#!/usr/bin/env python3
"""
翻译管理器
处理多语言文本翻译和模板渲染
"""
import json
import re
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from database.mongodb_connection import MongoDBConnection

logger = logging.getLogger(__name__)


class TranslationManager:
    """翻译管理器"""
    
    def __init__(self):
        self.translations = {}
        self.language_mapping = {}  # {language_id: language_name}
        self.default_language = "1"  # 默认使用ID 1 (English)
        self.config_file = Path(__file__).parent.parent / "config" / "translation_config.json"
        self.mongo_conn = MongoDBConnection()
        
    def load_translations(self):
        """加载翻译配置"""
        try:
            if not self.config_file.exists():
                logger.error(f"❌ 翻译配置文件不存在: {self.config_file}")
                return False

            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            self.translations = config.get("translations", {})
            self.language_mapping = config.get("language_mapping", {})
            self.default_language = config.get("default_language", "1")

            logger.info(f"✅ 翻译配置加载成功，支持 {len(self.language_mapping)} 种语言")
            return True

        except Exception as e:
            logger.error(f"❌ 加载翻译配置失败: {e}")
            return False

    def get_language_id(self, language_id) -> str:
        """根据语言ID获取语言ID字符串（保持一致性）

        Args:
            language_id: 可以是int或str类型的语言ID

        Returns:
            str: 标准化的语言ID字符串
        """
        # 统一转换为字符串类型
        language_id_str = str(language_id)

        # 检查是否在语言映射中
        if language_id_str in self.language_mapping:
            return language_id_str
        else:
            logger.warning(f"⚠️ 语言ID {language_id} (类型: {type(language_id).__name__}) 不在映射中，使用默认语言 {self.default_language}")
            return self.default_language
    
    def translate_text(self, text: str, language_id: str) -> str:
        """翻译单个文本"""
        # 如果是纯数字，不翻译
        if text.isdigit() or self._is_number(text):
            return text

        # 在所有分类中查找翻译
        for category, items in self.translations.items():
            if text in items:
                translation_dict = items[text]

                # 1. 尝试获取目标语言的翻译
                translation = translation_dict.get(language_id)
                if translation:
                    return translation

                # 2. 兜底方案：使用英文 (默认语言)
                fallback_translation = translation_dict.get(self.default_language)
                if fallback_translation:
                    logger.debug(f"🔄 使用英文兜底: {text} -> {language_id} (未找到) -> {self.default_language} (英文)")
                    return fallback_translation

                # 3. 如果英文也没有，返回原文
                logger.warning(f"⚠️ 英文兜底失败: {text} -> {language_id}")
                return text

        # 如果找不到翻译词条，返回原文
        logger.warning(f"⚠️ 未找到翻译词条: {text}")
        return text

    def translate_params(self, params: List[Any], language_id: str) -> List[Any]:
        """翻译参数列表，支持嵌套"""
        translated_params = []
        
        for param in params:
            if isinstance(param, list):
                # 递归处理嵌套列表
                translated_params.append(self.translate_params(param, language_id))
            elif isinstance(param, str):
                # 翻译字符串
                translated_params.append(self.translate_text(param, language_id))
            else:
                # 其他类型直接保留
                translated_params.append(param)
        
        return translated_params

    def get_activity_name_by_id(self, activity_id: int, language_id: str) -> Optional[str]:
        """通过activity_id从数据库获取活动名称"""
        try:
            logger.error(f"🔍🔍🔍 [DEBUG] 开始查询activity_id: {activity_id}, language_id: {language_id}")

            # 连接MongoDB
            if not self.mongo_conn.connect():
                logger.error(f"❌ MongoDB连接失败，无法获取活动名称")
                return None

            # 切换到wingame_config库
            db = self.mongo_conn.client["wingame_config"]
            collection = db["c_activity"]

            # 查询活动记录
            logger.error(f"🔍🔍🔍 [DEBUG] 查询数据库: wingame_config.c_activity, 条件: {{\"id\": {activity_id}}}")
            activity_doc = collection.find_one({"id": activity_id})

            if not activity_doc:
                logger.error(f"❌❌❌ [DEBUG] 未找到活动ID: {activity_id}")
                return None

            logger.error(f"✅✅✅ [DEBUG] 找到活动记录: id={activity_doc.get('id')}")

            # 获取activityData字段
            activity_data_raw = activity_doc.get("activityData")
            if not activity_data_raw:
                logger.error(f"❌ 活动ID {activity_id} 没有activityData字段")
                return None

            logger.error(f"📄📄📄 [DEBUG] 原始activityData类型: {type(activity_data_raw)}")
            logger.error(f"📄📄📄 [DEBUG] 原始activityData内容: {activity_data_raw}")

            # 处理activityData（可能是字符串或已解析的对象）
            try:
                if isinstance(activity_data_raw, str):
                    # 如果是字符串，需要JSON解析
                    logger.info(f"🔄 activityData是字符串，进行JSON解析")
                    activity_data = json.loads(activity_data_raw)
                elif isinstance(activity_data_raw, (list, dict)):
                    # 如果已经是对象，直接使用
                    logger.info(f"🔄 activityData已经是对象，直接使用")
                    activity_data = activity_data_raw
                else:
                    logger.error(f"❌ activityData类型不支持: {type(activity_data_raw)}")
                    return None

                logger.info(f"📄 最终activityData: {activity_data}")
            except json.JSONDecodeError as e:
                logger.error(f"❌ 活动ID {activity_id} 的activityData JSON解析失败: {e}")
                return None

            # 查找对应语言的活动名称
            if isinstance(activity_data, list):
                logger.info(f"🔍 在activityData中查找语言ID: {language_id}")
                for i, item in enumerate(activity_data):
                    logger.info(f"   项目{i}: {item}")
                    if isinstance(item, dict):
                        item_language = item.get("language")
                        item_activity_name = item.get("activityName")
                        logger.info(f"   语言: {item_language}, 活动名: {item_activity_name}")

                        # 详细调试语言匹配
                        logger.info(f"🔍 语言匹配调试:")
                        logger.info(f"   目标language_id: '{language_id}' (类型: {type(language_id)})")
                        logger.info(f"   数据item_language: '{item_language}' (类型: {type(item_language)})")
                        logger.info(f"   str比较: str({item_language}) == str({language_id}) = {str(item_language) == str(language_id)}")

                        try:
                            int_language_id = int(language_id)
                            logger.info(f"   int比较: {item_language} == int({language_id}) = {item_language == int_language_id}")
                        except ValueError:
                            logger.info(f"   int比较: language_id无法转换为int")

                        # 只匹配语言，不匹配ID（因为已经通过activity_id查到了正确的记录）
                        # 简化匹配逻辑：统一转换为字符串比较
                        language_match = str(item_language) == str(language_id)

                        logger.error(f"   🔍🔍🔍 [DEBUG] 匹配结果: str({item_language}) == str({language_id}) = {language_match}")

                        if language_match:
                            logger.info(f"🎯 找到匹配的语言: {item_language}")
                            if item_activity_name:
                                logger.info(f"✅ 通过activity_id找到活动名称: activityId={activity_id}, language={language_id} -> '{item_activity_name}'")
                                return item_activity_name
                            else:
                                logger.error(f"❌ 找到语言但activityName为空")
                        else:
                            logger.info(f"❌ 语言不匹配: 需要{language_id}, 实际{item_language}")
                    else:
                        logger.info(f"   项目{i}不是字典格式: {type(item)}")
            else:
                logger.error(f"❌ activityData不是列表格式: {type(activity_data)}")

            logger.error(f"❌ 未找到活动ID {activity_id} 在语言 {language_id} 下的名称")
            return None

        except Exception as e:
            logger.error(f"❌ 查询活动名称失败: activityId={activity_id}, error={e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return None



    def _replace_activity_name_in_params(self, params: List[Any], activity_name: str) -> List[Any]:
        """在参数中替换活动名称（旧方法，保留兼容性）"""
        def replace_in_item(item):
            if isinstance(item, str) and "活动名" in item:
                # 替换包含"活动名"的字符串
                return item.replace("活动名", activity_name)
            elif isinstance(item, list):
                # 递归处理列表
                return [replace_in_item(sub_item) for sub_item in item]
            else:
                # 其他类型保持不变
                return item

        return [replace_in_item(param) for param in params]

    def _override_activity_name_in_params(self, params: List[Any], activity_name: str) -> List[Any]:
        """无效化params中的活动名参数，直接使用查询到的活动名称"""
        def override_in_item(item):
            if isinstance(item, str) and "活动名" in item:
                # 无效化：不管原参数是什么，直接使用查询到的活动名称
                logger.debug(f"🔄 无效化活动名参数: '{item}' -> '{activity_name}'")
                return activity_name
            elif isinstance(item, list):
                # 递归处理列表
                return [override_in_item(sub_item) for sub_item in item]
            else:
                # 其他类型保持不变
                return item

        return [override_in_item(param) for param in params]

    def _smart_process_activity_name(self, template: str, params: List[Any], activity_id: Optional[int], message_type: Optional[int], language_id: str) -> List[Any]:
        """智能处理活动名占位符：优先activity_id，其次type，最后保持原逻辑"""
        try:
            import re

            # 解析模板中的占位符，找出活动名占位符的位置
            placeholder_pattern = r'\{([^}]+)\}'
            placeholders = re.findall(placeholder_pattern, template)

            processed_params = params.copy()

            logger.info(f"🔍 找到占位符: {placeholders}")
            logger.info(f"📋 原始参数: {processed_params}")

            # 遍历每个占位符，找出活动名占位符
            for i, placeholder in enumerate(placeholders):
                if i >= len(processed_params):
                    logger.debug(f"⚠️ 参数不足，跳过占位符 {i}: {placeholder}")
                    break

                # 检查是否是活动名占位符
                if "活动名" in placeholder:
                    logger.info(f"🎯 发现活动名占位符在位置 {i}: {placeholder}")

                    # 智能获取活动名称
                    activity_name = self._get_smart_activity_name(activity_id, message_type, processed_params[i], language_id)

                    # 替换参数
                    old_value = processed_params[i]
                    processed_params[i] = activity_name

                    logger.info(f"✅ 活动名参数替换: 位置{i} '{old_value}' -> '{activity_name}'")

            return processed_params

        except Exception as e:
            logger.warning(f"⚠️ 智能处理活动名失败: {e}")
            return params

    def _get_smart_activity_name(self, activity_id: Optional[int], message_type: Optional[int], original_param: Any, language_id: str) -> str:
        """智能获取活动名称：优先activity_id，其次type，最后使用现有映射"""
        try:
            logger.error(f"🎯🎯🎯 [DEBUG] 开始智能获取活动名称:")
            logger.error(f"   activity_id: {activity_id}")
            logger.error(f"   message_type: {message_type}")
            logger.error(f"   original_param: {original_param}")
            logger.error(f"   language_id: {language_id}")

            # 1. 优先使用activity_id查询数据库
            if activity_id is not None:
                logger.info(f"🔍 第一优先级：尝试通过activity_id={activity_id}查询数据库")
                activity_name = self.get_activity_name_by_id(activity_id, language_id)
                if activity_name:
                    logger.info(f"✅ 【ACTIVITY_ID替换】通过activity_id={activity_id}找到活动名称: '{activity_name}'")
                    logger.info(f"🎯 最终替换结果: '{original_param}' -> '{activity_name}' (来源: activity_id查询)")
                    return activity_name
                else:
                    logger.info(f"❌ activity_id={activity_id}未找到活动名称，尝试第二优先级")
            else:
                logger.info(f"⚠️ 没有activity_id，跳过数据库查询")

            # 2. 使用type查询现有映射
            if message_type is not None:
                logger.info(f"🔍 第二优先级：尝试通过type={message_type}查询配置映射")
                activity_name = self._get_activity_name_by_type(message_type, language_id)
                if activity_name:
                    logger.info(f"✅ 【TYPE替换】通过type={message_type}找到活动名称: '{activity_name}'")
                    logger.info(f"🎯 最终替换结果: '{original_param}' -> '{activity_name}' (来源: type映射)")
                    return activity_name
                else:
                    logger.info(f"❌ type={message_type}未找到活动名称，尝试第三优先级")
            else:
                logger.info(f"⚠️ 没有message_type，跳过type映射查询")

            # 3. 使用现有的特殊参数映射逻辑
            if isinstance(original_param, (int, str)):
                param_str = str(original_param)
                logger.info(f"🔍 第三优先级：尝试通过原始参数'{param_str}'查询现有映射")
                mapped_value = self._get_special_mapping("activity_name", param_str, language_id)
                if mapped_value and mapped_value != param_str:
                    logger.info(f"✅ 【现有映射替换】通过现有映射找到活动名称: '{param_str}' -> '{mapped_value}'")
                    logger.info(f"🎯 最终替换结果: '{original_param}' -> '{mapped_value}' (来源: 现有映射)")
                    return mapped_value
                else:
                    logger.info(f"❌ 现有映射未找到活动名称")

            # 4. 都没找到，返回空字符串
            logger.info(f"❌ 所有方式都未找到活动名称，使用空字符串")
            logger.info(f"🎯 最终替换结果: '{original_param}' -> '' (来源: 兜底空字符串)")
            return ""

        except Exception as e:
            logger.error(f"❌ 获取智能活动名称失败: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return str(original_param)  # 降级返回原参数

    def _get_activity_name_by_type(self, message_type: int, language_id: str) -> Optional[str]:
        """通过type从现有配置获取活动名称"""
        try:
            logger.info(f"🔍 通过type查询活动名称: type={message_type}, language_id={language_id}")
            # 使用现有的特殊参数映射
            result = self._get_special_mapping("activity_name", str(message_type), language_id)
            if result:
                logger.info(f"✅ type映射查询成功: type={message_type} -> '{result}'")
            else:
                logger.info(f"❌ type映射查询失败: type={message_type}")
            return result
        except Exception as e:
            logger.error(f"❌ 通过type查询活动名称失败: type={message_type}, error={e}")
            return None

    def render_template(self, template: str, params: List[Any], language_id: str, activity_id: Optional[int] = None, message_type: Optional[int] = None) -> str:
        """渲染模板"""
        try:
            logger.debug(f"🎨 开始渲染模板")
            logger.debug(f"   模板: {template}")
            logger.debug(f"   参数: {params}")
            logger.debug(f"   语言: {language_id}")
            logger.debug(f"   活动ID: {activity_id}")

            # 检查模板是否包含占位符
            import re
            has_placeholders = bool(re.search(r'\{[^}]+\}|\[repeat\d*\]', template))

            if not has_placeholders:
                logger.debug(f"📄 模板无占位符，直接返回原文")
                logger.debug(f"   忽略的参数: {params}")
                return template

            # 0. 智能处理活动名占位符（优先activity_id，其次type，最后现有逻辑）
            if "活动名" in template:
                logger.info(f"🎯 检测到活动名占位符，开始智能处理: activity_id={activity_id}, type={message_type}")
                params = self._smart_process_activity_name(template, params, activity_id, message_type, language_id)

            # 1. 翻译参数
            logger.debug(f"🌐 步骤1: 翻译参数...")
            translated_params = self.translate_params(params, language_id)
            logger.debug(f"   翻译后: {translated_params}")

            # 2. 特殊参数替换（活动名等关键字处理）
            logger.debug(f"🎯 步骤2: 特殊参数替换...")
            translated_params = self._process_special_params(template, translated_params, language_id)
            logger.debug(f"   特殊替换后: {translated_params}")

            # 3. 处理循环块（切割格式化拼接）
            logger.debug(f"🔄 步骤3: 处理循环块...")
            rendered_text, remaining_params = self._process_repeat_blocks(template, translated_params)
            logger.debug(f"   循环处理后: {rendered_text}")

            # 4. 替换普通变量（如果还有剩余的占位符）
            logger.debug(f"🔧 步骤4: 替换变量...")
            rendered_text = self._replace_variables(rendered_text, remaining_params)
            logger.debug(f"   最终结果: {rendered_text}")

            logger.debug(f"✅ 模板渲染完成")
            return rendered_text

        except Exception as e:
            logger.error(f"❌ 模板渲染失败: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
            return template

    def _process_special_params(self, template: str, params: List[Any], language_id: str) -> List[Any]:
        """处理特殊参数替换（如活动名等关键字）"""
        try:
            # 从配置文件获取特殊关键字映射
            from config.settings import settings
            special_keywords = settings.special_params_keywords

            if not special_keywords:
                logger.debug("⚠️ 未配置特殊参数关键字，跳过特殊参数处理")
                return params

            # 解析模板中的占位符，找出哪些位置需要特殊处理
            import re
            placeholder_pattern = r'\{([^}]+)\}'
            placeholders = re.findall(placeholder_pattern, template)

            processed_params = params.copy()

            logger.debug(f"🔍 找到占位符: {placeholders}")
            logger.debug(f"📋 原始参数: {processed_params}")

            # 遍历每个占位符，检查是否需要特殊处理
            for i, placeholder in enumerate(placeholders):
                if i >= len(processed_params):
                    logger.debug(f"⚠️ 参数不足，跳过占位符 {i}: {placeholder}")
                    break

                logger.debug(f"🔍 检查占位符 {i}: {placeholder}")

                # 检查占位符是否包含特殊关键字
                for keyword, mapping_type in special_keywords.items():
                    if keyword in placeholder:
                        param_value = processed_params[i]
                        logger.debug(f"🎯 发现特殊关键字: {keyword} 在位置 {i}, 原值: {param_value}")

                        # 如果参数是数字或数字字符串，尝试进行映射转换
                        if isinstance(param_value, (int, str)):
                            try:
                                # 转换为字符串进行查找
                                param_str = str(param_value)
                                mapped_value = self._get_special_mapping(mapping_type, param_str, language_id)

                                if mapped_value and mapped_value != param_str:
                                    logger.debug(f"✅ 特殊参数替换成功: {keyword} {param_value} -> {mapped_value}")
                                    processed_params[i] = mapped_value
                                else:
                                    logger.debug(f"⚠️ 未找到映射或映射相同: {keyword} {param_value}")

                            except Exception as e:
                                logger.warning(f"⚠️ 特殊参数处理失败: {keyword} {param_value}, 错误: {e}")

                        break  # 找到匹配的关键字后跳出循环

            logger.debug(f"📋 处理后参数: {processed_params}")

            return processed_params

        except Exception as e:
            logger.error(f"❌ 特殊参数处理失败: {e}")
            return params

    def _get_special_mapping(self, mapping_type: str, param_value: str, language_id: str) -> str:
        """获取特殊参数的映射值"""
        try:
            # 从配置文件获取映射表
            from config.settings import settings
            mappings = settings.special_params_mappings

            if mapping_type not in mappings:
                logger.debug(f"⚠️ 未找到映射类型: {mapping_type}")
                return None

            type_mappings = mappings[mapping_type]
            if param_value not in type_mappings:
                logger.debug(f"⚠️ 未找到参数值映射: {mapping_type}.{param_value}")
                return None

            param_mappings = type_mappings[param_value]
            if language_id not in param_mappings:
                logger.debug(f"⚠️ 未找到语言映射: {mapping_type}.{param_value}.{language_id}")
                return None

            mapped_value = param_mappings[language_id]
            logger.debug(f"✅ 找到映射: {mapping_type}.{param_value}.{language_id} -> {mapped_value}")
            return mapped_value

        except Exception as e:
            logger.error(f"❌ 获取特殊映射失败: {mapping_type} {param_value}, 错误: {e}")
            return None


    
    def _process_repeat_blocks(self, template: str, params: List[Any]) -> str:
        """处理循环块 - 切割分别格式化拼接思路"""
        import re

        # 1. 切割模板
        start_repeat = template.find("[repeat]")
        end_repeat = template.find("[/repeat]")

        if start_repeat == -1 or end_repeat == -1:
            # 没有repeat块，直接返回
            logger.debug(f"🔍 没有找到repeat块，直接返回")
            return template, params

        # 分割模板
        before_repeat = template[:start_repeat]
        repeat_content = template[start_repeat + len("[repeat]"):end_repeat]
        after_repeat = template[end_repeat + len("[/repeat]"):]

        logger.debug(f"🔪 模板切割:")
        logger.debug(f"   before: {repr(before_repeat)}")
        logger.debug(f"   repeat: {repr(repeat_content)}")
        logger.debug(f"   after: {repr(after_repeat)}")

        # 2. 切割参数
        before_list = []
        list_part = []
        after_list = []

        list_started = False
        for item in params:
            if isinstance(item, list) and len(item) > 0 and isinstance(item[0], list):
                # 这是嵌套数组
                list_started = True
                list_part = item
                logger.debug(f"🔍 找到嵌套数组: {item}")
            elif not list_started:
                before_list.append(item)
            else:
                after_list.append(item)

        logger.debug(f"🔪 参数切割:")
        logger.debug(f"   before_list: {before_list}")
        logger.debug(f"   list_part: {list_part}")
        logger.debug(f"   after_list: {after_list}")

        # 3. 重新编号占位符并格式化
        final_text = self._format_split_parts(before_repeat, repeat_content, after_repeat,
                                            before_list, list_part, after_list)

        return final_text, params

    def _format_split_parts(self, before_repeat: str, repeat_content: str, after_repeat: str,
                           before_list: List[Any], list_part: List[List], after_list: List[Any]) -> str:
        """格式化切割后的各部分"""
        import re

        def get_placeholders(text):
            """获取文本中的占位符 - 支持纯数字和带前缀的占位符"""
            # 匹配纯数字占位符 {1}, {2}, {3} 等
            numeric_placeholders = re.findall(r'\{(\d+)\}', text)
            # 匹配带前缀的占位符 {rank1}, {playid2} 等
            prefix_placeholders = re.findall(r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}', text)

            # 如果有纯数字占位符，优先使用
            if numeric_placeholders:
                logger.debug(f"🔍 找到纯数字占位符: {numeric_placeholders}")
                return [("", num) for num in numeric_placeholders]
            else:
                logger.debug(f"🔍 找到前缀占位符: {prefix_placeholders}")
                return prefix_placeholders

        def renumber_placeholders(text, placeholders):
            """重新编号占位符"""
            new_text = text
            for i, (prefix, old_num) in enumerate(placeholders):
                if prefix == "":
                    # 纯数字占位符
                    old_placeholder = f"{{{old_num}}}"
                else:
                    # 带前缀的占位符
                    old_placeholder = f"{{{prefix}{old_num}}}"

                new_placeholder = f"{{{i}}}"
                new_text = new_text.replace(old_placeholder, new_placeholder)
                logger.debug(f"🔄 重新编号: {old_placeholder} -> {new_placeholder}")
            return new_text

        # 1. 处理before部分
        before_placeholders = get_placeholders(before_repeat)
        before_renumbered = renumber_placeholders(before_repeat, before_placeholders)

        # 格式化before部分
        try:
            before_combined = before_renumbered.format(*before_list, *[""] * max(0, len(before_placeholders) - len(before_list)))
            logger.debug(f"✅ before格式化: {repr(before_combined)}")
        except Exception as e:
            logger.warning(f"⚠️ before格式化失败: {e}")
            before_combined = before_repeat

        # 2. 处理repeat部分
        repeat_placeholders = get_placeholders(repeat_content)
        repeat_renumbered = renumber_placeholders(repeat_content, repeat_placeholders)

        # 格式化repeat部分（循环）- 使用字典格式化方法
        repeat_results = []
        for sub_item in list_part:
            try:
                # 使用字典格式化方法，类似参考代码
                sub_item_dict = {}
                for idx, (prefix, old_num) in enumerate(repeat_placeholders):
                    if idx < len(sub_item):
                        if prefix == "":
                            # 纯数字占位符
                            placeholder_name = old_num
                        else:
                            # 带前缀的占位符
                            placeholder_name = f"{prefix}{old_num}"
                        sub_item_dict[placeholder_name] = sub_item[idx]
                    else:
                        # 如果数据不够，用空字符串填充
                        if prefix == "":
                            placeholder_name = old_num
                        else:
                            placeholder_name = f"{prefix}{old_num}"
                        sub_item_dict[placeholder_name] = ""

                # 使用字典格式化
                formatted_line = repeat_content.format(**sub_item_dict)
                repeat_results.append(formatted_line)
                logger.debug(f"✅ repeat字典格式化: {sub_item} -> {sub_item_dict} -> {repr(formatted_line)}")
            except Exception as e:
                logger.warning(f"⚠️ repeat格式化失败: {e}")
                # 降级到位置参数格式化
                try:
                    formatted_line = repeat_renumbered.format(*sub_item, *[""] * max(0, len(repeat_placeholders) - len(sub_item)))
                    repeat_results.append(formatted_line)
                    logger.debug(f"✅ repeat位置格式化: {sub_item} -> {repr(formatted_line)}")
                except Exception as e2:
                    logger.warning(f"⚠️ repeat位置格式化也失败: {e2}")
                    repeat_results.append(repeat_content)

        # 3. 处理after部分
        after_placeholders = get_placeholders(after_repeat)
        after_renumbered = renumber_placeholders(after_repeat, after_placeholders)

        # 格式化after部分
        try:
            after_combined = after_renumbered.format(*after_list, *[""] * max(0, len(after_placeholders) - len(after_list)))
            logger.debug(f"✅ after格式化: {repr(after_combined)}")
        except Exception as e:
            logger.warning(f"⚠️ after格式化失败: {e}")
            after_combined = after_repeat

        # 4. 最终拼接
        final_text = before_combined + '\n'.join(repeat_results) + after_combined
        logger.debug(f"🎯 最终拼接: {repr(final_text)}")

        return final_text

    def _fully_expand_params(self, params: List[Any]) -> List[Any]:
        """完全展开参数列表"""
        expanded = []

        for i, param in enumerate(params):
            if isinstance(param, list) and len(param) > 0:
                # 检查是否是嵌套数组
                if isinstance(param[0], list):
                    # 这是嵌套数组，需要展开
                    logger.debug(f"🔍 发现嵌套数组参数 {i+1}: {param}")

                    # 将嵌套数组的每个子数组展开
                    for sub_array in param:
                        if isinstance(sub_array, list):
                            # 将子数组的每个元素作为独立参数
                            for item in sub_array:
                                expanded.append(item)
                                logger.debug(f"  📋 展开: {item}")
                        else:
                            expanded.append(sub_array)
                else:
                    # 普通数组，直接添加
                    expanded.append(param)
            else:
                # 单值参数，直接添加
                expanded.append(param)

        logger.debug(f"🎯 完全展开结果: {expanded}")
        return expanded

    def _find_nested_array_for_repeat(self, repeat_content: str, params: List[Any]) -> tuple:
        """找到repeat块对应的嵌套数组参数"""
        # 查找第一个嵌套数组参数
        for i, param in enumerate(params):
            if isinstance(param, list) and len(param) > 0:
                if isinstance(param[0], list):
                    # 找到嵌套数组
                    logger.debug(f"🔍 找到嵌套数组参数 {i+1}: 长度={len(param)}")
                    return param, len(param)

        logger.warning(f"⚠️ 未找到嵌套数组参数")
        return None, 0

    def _create_sub_params_context(self, original_params: List[Any], nested_array: List[List], index: int) -> List[Any]:
        """为循环创建子参数上下文"""
        # 创建一个新的参数列表，其中嵌套数组被当前循环的子数组替换
        sub_params = []

        for i, param in enumerate(original_params):
            if param is nested_array:
                # 这是嵌套数组参数，用当前循环的子数组替换
                if 0 <= index < len(nested_array):
                    sub_params.append(nested_array[index])
                    logger.debug(f"🔄 循环 {index}: 参数{i+1} = {nested_array[index]}")
                else:
                    sub_params.append([])
            else:
                # 其他参数保持不变
                sub_params.append(param)

        return sub_params

    def _render_repeat_content(self, repeat_content: str, sub_params: List[Any]) -> str:
        """渲染repeat块内容"""
        import re

        # 匹配 {prefix数字} 格式，支持中文字符
        var_pattern = r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}'

        def replace_var(match):
            prefix = match.group(1)
            param_index = int(match.group(2)) - 1  # 转换为0基索引

            # 检查是否是特殊前缀（Amount, Currency），如果是，强制从嵌套数组获取
            special_prefixes = ["Amount", "Currency", "amount", "currency"]
            is_special_prefix = any(sp.lower() in prefix.lower() for sp in special_prefixes)

            if is_special_prefix:
                logger.debug(f"🎯 检测到特殊前缀 {prefix}，从嵌套数组获取")

                # 查找嵌套数组参数
                for i, param in enumerate(sub_params):
                    if isinstance(param, list) and len(param) > 0:
                        # 检查是否包含我们需要的数据
                        sub_index = self._get_sub_array_index(prefix, param_index + 1)
                        if 0 <= sub_index < len(param):
                            result = str(param[sub_index])
                            logger.debug(f"🎯 特殊前缀从嵌套数组获取: {prefix}{param_index+1} -> 参数{i+1}[{sub_index}] = {result}")
                            return result

                # 如果没找到嵌套数组，返回空字符串
                logger.debug(f"🔧 特殊前缀未找到嵌套数组: {match.group(0)} -> 使用空字符串填充")
                return ""

            # 非特殊前缀，按原逻辑处理
            if 0 <= param_index < len(sub_params):
                param = sub_params[param_index]

                if isinstance(param, list):
                    # 如果是数组，根据前缀确定索引
                    sub_index = self._get_sub_array_index(prefix, param_index + 1)
                    if 0 <= sub_index < len(param):
                        result = str(param[sub_index])
                        logger.debug(f"🎯 repeat内替换: {prefix}{param_index+1}[{sub_index}] = {result}")
                        return result
                    else:
                        logger.warning(f"⚠️ 子数组索引超出范围: {prefix}{param_index+1}[{sub_index}]")
                        return f"{{sub_index_out_of_range}}"
                else:
                    # 如果是单值，直接返回
                    result = str(param)
                    logger.debug(f"🎯 repeat内替换: {prefix}{param_index+1} = {result}")
                    return result

            # 如果指定参数不存在，尝试从嵌套数组中获取
            logger.debug(f"🔍 参数{param_index+1}不存在，尝试从嵌套数组获取 {prefix}")

            # 查找嵌套数组参数
            for i, param in enumerate(sub_params):
                if isinstance(param, list) and len(param) > 0:
                    # 检查是否包含我们需要的数据
                    sub_index = self._get_sub_array_index(prefix, param_index + 1)
                    if 0 <= sub_index < len(param):
                        result = str(param[sub_index])
                        logger.debug(f"🎯 从嵌套数组获取: {prefix}{param_index+1} -> 参数{i+1}[{sub_index}] = {result}")
                        return result

            # 如果都没找到，用空字符串填充
            logger.debug(f"🔧 repeat内未找到: {match.group(0)} -> 使用空字符串填充")
            return ""

        return re.sub(var_pattern, replace_var, repeat_content)

    def _expand_repeat_content(self, repeat_content: str, params: List[Any], nested_array: List[List], loop_index: int) -> str:
        """完全展开repeat内容 - 直接替换所有占位符"""
        import re

        # 获取当前循环的子数组
        if 0 <= loop_index < len(nested_array):
            current_sub_array = nested_array[loop_index]
        else:
            logger.warning(f"⚠️ 循环索引超出范围: {loop_index}")
            return repeat_content

        # 匹配所有占位符
        var_pattern = r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}'

        def replace_var(match):
            prefix = match.group(1)
            param_index = int(match.group(2)) - 1  # 转换为0基索引

            logger.debug(f"🔍 处理占位符: {prefix}{param_index+1}")

            # 根据占位符名称决定从子数组的哪个位置取值
            if prefix.lower() == "currency":
                # Currency 取金额（索引0）
                if 0 < len(current_sub_array):
                    result = str(current_sub_array[0])
                    logger.debug(f"🎯 Currency{param_index+1} -> 子数组[0] = {result}")
                    return result
            elif prefix.lower() == "amount":
                # Amount 取货币（索引1）
                if 1 < len(current_sub_array):
                    result = str(current_sub_array[1])
                    logger.debug(f"🎯 Amount{param_index+1} -> 子数组[1] = {result}")
                    return result
            else:
                # 其他占位符按正常逻辑处理
                if 0 <= param_index < len(params):
                    param = params[param_index]
                    if isinstance(param, list):
                        # 如果是数组，取第一个元素
                        if len(param) > 0:
                            result = str(param[0])
                            logger.debug(f"🎯 {prefix}{param_index+1} -> 参数{param_index+1}[0] = {result}")
                            return result
                    else:
                        # 如果是单值，直接返回
                        result = str(param)
                        logger.debug(f"🎯 {prefix}{param_index+1} -> 参数{param_index+1} = {result}")
                        return result

            # 如果都没找到，返回空字符串
            logger.debug(f"🔧 未找到值: {match.group(0)} -> 空字符串")
            return ""

        return re.sub(var_pattern, replace_var, repeat_content)

    def _determine_repeat_count(self, repeat_content: str, params: List[Any]) -> int:
        """确定循环次数（基于循环内容中引用的数组参数）"""
        import re

        # 匹配循环内容中的变量引用
        var_pattern = r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}'
        matches = re.findall(var_pattern, repeat_content)

        max_length = 0

        for prefix, param_num in matches:
            param_index = int(param_num) - 1  # 转换为0基索引

            if 0 <= param_index < len(params):
                param = params[param_index]

                if isinstance(param, list):
                    # 如果是数组参数，记录其长度
                    max_length = max(max_length, len(param))
                    logger.debug(f"🔍 找到数组参数 {param_num}: 长度={len(param)}")

        # 如果没有找到数组参数，默认循环1次
        if max_length == 0:
            logger.debug(f"⚠️ 循环内容中没有找到数组参数，默认循环1次")
            max_length = 1

        logger.debug(f"🔄 确定循环次数: {max_length}")
        return max_length

    def _replace_variables_for_index(self, text: str, params: List[Any], index: int) -> str:
        """为指定索引替换变量"""
        import re

        # 首先处理纯数字占位符 {1}, {2}, {3} 等
        def replace_simple_var(match):
            param_index = int(match.group(1)) - 1  # 转换为0基索引

            if 0 <= param_index < len(params):
                param = params[param_index]

                if isinstance(param, list):
                    # 检查是否是嵌套数组（双层数组）
                    if 0 <= index < len(param):
                        item = param[index]

                        if isinstance(item, list):
                            # 嵌套数组：返回第一个元素
                            return str(item[0]) if item else ""
                        else:
                            # 普通数组：直接返回值
                            return str(item)
                    else:
                        return ""
                else:
                    # 如果是单值，直接返回
                    return str(param)
            else:
                return ""

        # 先处理纯数字占位符
        simple_var_pattern = r'\{(\d+)\}'
        text = re.sub(simple_var_pattern, replace_simple_var, text)

        # 然后处理带前缀的占位符 {prefix1}, {prefix2} 等
        var_pattern = r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}'

        def replace_prefix_var(match):
            prefix = match.group(1)
            param_index = int(match.group(2)) - 1  # 转换为0基索引

            if 0 <= param_index < len(params):
                param = params[param_index]

                if isinstance(param, list):
                    # 检查是否是嵌套数组（双层数组）
                    if 0 <= index < len(param):
                        item = param[index]

                        if isinstance(item, list):
                            # 嵌套数组：根据占位符名称确定子数组索引
                            sub_index = self._get_sub_array_index(prefix)
                            if 0 <= sub_index < len(item):
                                logger.debug(f"🔄 嵌套数组: {prefix}{param_index+1}[{index}][{sub_index}] = {item[sub_index]}")
                                return str(item[sub_index])
                            else:
                                logger.warning(f"⚠️ 嵌套数组子索引超出范围: {prefix}{param_index+1}[{index}][{sub_index}]")
                                return f"{{sub_index_out_of_range}}"
                        else:
                            # 普通数组：直接返回值
                            logger.debug(f"🔄 普通数组: {prefix}{param_index+1}[{index}] = {item}")
                            return str(item)
                    else:
                        return f"{{list_index_out_of_range}}"
                else:
                    # 如果是单值，直接返回
                    return str(param)
            else:
                # 参数不足时，检查是否可以从其他参数获取嵌套数组
                nested_result = self._try_get_from_nested_array(prefix, param_index, params, index)
                if nested_result is not None:
                    return nested_result

                # 参数不足时，用空字符串填充
                logger.debug(f"🔧 循环中参数不足: {match.group(0)} -> 使用空字符串填充")
                return ""
        
        return re.sub(var_pattern, replace_var, text)

    def _get_sub_array_index(self, prefix: str, param_index: int = None) -> int:
        """根据占位符前缀和参数索引确定子数组索引"""
        # 根据你的需求，Currency2应该取Amount，Amount3应该取Currency
        # 这是为了支持 {Currency2}{Amount3} -> 10USDT 的顺序

        # 特殊映射规则：根据参数索引和前缀组合决定
        if prefix.lower() == "currency":
            if param_index == 2:  # Currency2
                logger.debug(f"🎯 特殊映射: Currency2 -> 子数组索引 0 (Amount)")
                return 0  # 取Amount
            elif param_index == 3:  # Currency3
                logger.debug(f"🎯 特殊映射: Currency3 -> 子数组索引 1 (Currency)")
                return 1  # 取Currency
        elif prefix.lower() == "amount":
            if param_index == 2:  # Amount2
                logger.debug(f"🎯 特殊映射: Amount2 -> 子数组索引 0 (Amount)")
                return 0  # 取Amount
            elif param_index == 3:  # Amount3
                logger.debug(f"🎯 特殊映射: Amount3 -> 子数组索引 1 (Currency)")
                return 1  # 取Currency

        # 默认映射规则
        prefix_mapping = {
            # 货币相关
            "Currency": 1,    # Currency -> 索引1 (Currency)
            "Amount": 0,      # Amount -> 索引0 (Amount)
            "currency": 1,    # currency -> 索引1
            "amount": 0,      # amount -> 索引0

            # 游戏相关
            "game": 0,        # game -> 索引0
            "score": 1,       # score -> 索引1

            # 奖励相关
            "reward": 0,      # reward -> 索引0
            "value": 1,       # value -> 索引1

            # 通用
            "item": 0,        # item -> 索引0
            "count": 1,       # count -> 索引1
            "type": 0,        # type -> 索引0
            "name": 0,        # name -> 索引0
        }

        # 查找匹配的前缀
        for key, index in prefix_mapping.items():
            if key.lower() in prefix.lower():
                logger.debug(f"🎯 默认映射: {prefix} -> 子数组索引 {index}")
                return index

        # 如果没有找到匹配，默认返回0
        logger.debug(f"⚠️ 未找到占位符映射: {prefix}，使用默认索引 0")
        return 0

    def _try_get_from_nested_array(self, prefix: str, param_index: int, params: List[Any], loop_index: int) -> str:
        """尝试从其他参数的嵌套数组中获取值"""
        # 查找包含嵌套数组的参数
        for i, param in enumerate(params):
            if isinstance(param, list) and len(param) > 0:
                # 检查是否是嵌套数组
                if isinstance(param[0], list):
                    # 这是一个嵌套数组，尝试从中获取值
                    if 0 <= loop_index < len(param):
                        item = param[loop_index]
                        if isinstance(item, list):
                            sub_index = self._get_sub_array_index(prefix)
                            if 0 <= sub_index < len(item):
                                logger.debug(f"🔄 从嵌套数组获取: {prefix}{param_index+1} -> 参数{i+1}[{loop_index}][{sub_index}] = {item[sub_index]}")
                                return str(item[sub_index])

        # 如果没有找到，返回None
        return None

    def _replace_variables(self, text: str, params: List[Any]) -> str:
        """替换普通变量（非循环内）"""
        import re

        # 首先处理纯数字占位符 {1}, {2}, {3} 等
        def replace_simple_var(match):
            param_index = int(match.group(1)) - 1  # 转换为0基索引

            if 0 <= param_index < len(params):
                param = params[param_index]

                if isinstance(param, list):
                    # 如果是列表，返回第一个元素
                    return str(param[0]) if param else ""
                else:
                    return str(param)
            else:
                # 参数不足时，用空字符串填充
                logger.debug(f"🔧 参数不足: {match.group(0)} -> 使用空字符串填充")
                return ""

        # 先处理纯数字占位符 {1}, {2}, {3} 等
        simple_var_pattern = r'\{(\d+)\}'
        text = re.sub(simple_var_pattern, replace_simple_var, text)

        # 然后处理带前缀的占位符 {prefix1}, {prefix2} 等
        var_pattern = r'\{([a-zA-Z_\u4e00-\u9fff]+)(\d+)\}'

        def replace_prefix_var(match):
            prefix = match.group(1)
            param_index = int(match.group(2)) - 1  # 转换为0基索引

            if 0 <= param_index < len(params):
                param = params[param_index]

                if isinstance(param, list):
                    # 如果是列表，在非循环上下文中可能需要特殊处理
                    # 这里简单返回第一个元素或列表字符串表示
                    return str(param[0]) if param else ""
                else:
                    return str(param)
            else:
                # 参数不足时，用空字符串填充
                logger.debug(f"🔧 参数不足: {match.group(0)} -> 使用空字符串填充")
                return ""

        text = re.sub(var_pattern, replace_prefix_var, text)
        return text
    
    def _is_number(self, text: str) -> bool:
        """检查是否为数字（包括小数）"""
        try:
            float(text)
            return True
        except ValueError:
            return False
    
    def add_translation(self, category: str, key: str, translations: Dict[str, str]):
        """添加新的翻译"""
        if category not in self.translations:
            self.translations[category] = {}
        
        self.translations[category][key] = translations
        logger.info(f"✅ 添加翻译: {category}.{key}")
    
    def save_translations(self):
        """保存翻译配置到文件"""
        try:
            config = {
                "translations": self.translations,
                "language_mapping": self.language_mapping,
                "default_language": self.default_language
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            logger.info("✅ 翻译配置保存成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ 保存翻译配置失败: {e}")
            return False
    
    def get_translation_stats(self) -> Dict:
        """获取翻译统计信息"""
        stats = {
            "categories": len(self.translations),
            "total_keys": 0,
            "languages": len(self.language_mapping),
            "language_codes": list(self.language_mapping.values())
        }
        
        for category, items in self.translations.items():
            stats["total_keys"] += len(items)
        
        return stats


# 全局翻译管理器实例
translation_manager = TranslationManager()
