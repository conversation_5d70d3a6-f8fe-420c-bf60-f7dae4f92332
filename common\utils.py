"""
共享工具函数
"""
import logging
import asyncio
from typing import Optional, List, Dict, Any
from pathlib import Path
import json
from datetime import datetime

from config.settings import settings


def setup_logging():
    """设置日志配置（已废弃，使用config.logging_config）"""
    # 这个函数保留兼容性，实际使用新的日志配置
    from config.logging_config import app_logger
    return app_logger


def load_json_file(file_path: str) -> Optional[Dict[str, Any]]:
    """加载JSON文件"""
    from config.logging_config import app_logger
    try:
        path = Path(file_path)
        if path.exists():
            with open(path, 'r', encoding='utf-8') as f:
                return json.load(f)
    except Exception as e:
        app_logger.error(f"Failed to load JSON file {file_path}: {e}")
    return None


def save_json_file(file_path: str, data: Dict[str, Any]) -> bool:
    """保存JSON文件"""
    from config.logging_config import app_logger
    try:
        path = Path(file_path)
        path.parent.mkdir(parents=True, exist_ok=True)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        app_logger.error(f"Failed to save JSON file {file_path}: {e}")
        return False


def format_message(template: str, **kwargs) -> str:
    """格式化消息模板"""
    from config.logging_config import app_logger
    try:
        return template.format(**kwargs)
    except KeyError as e:
        app_logger.error(f"Missing template variable: {e}")
        return template


def get_timestamp() -> str:
    """获取当前时间戳字符串"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")


def validate_chat_id(chat_id: str) -> Optional[int]:
    """验证并转换chat_id"""
    try:
        return int(chat_id)
    except (ValueError, TypeError):
        return None


def escape_markdown(text: str) -> str:
    """转义Markdown特殊字符"""
    escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
    for char in escape_chars:
        text = text.replace(char, f'\\{char}')
    return text


async def run_with_timeout(coro, timeout: float = 30.0):
    """运行协程并设置超时"""
    try:
        return await asyncio.wait_for(coro, timeout=timeout)
    except asyncio.TimeoutError:
        logging.error(f"Operation timed out after {timeout} seconds")
        raise
