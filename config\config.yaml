# Telegram Bot 配置文件
# 注意：敏感信息（如token）建议使用环境变量设置

# Bot配置
bot:
  token: "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw"  # Bot Token

# 测试群组配置
test:
  chat_id: -1002316158105  # 测试群组ID

# 代理配置
proxy:
  enabled: false  # 测试环境不需要代理
  url: "http://127.0.0.1:7890"  # 代理地址，根据实际情况修改

# Webhook配置（生产环境使用）
webhook:
  # url: "https://your-domain.com/webhook"  # 建议使用环境变量 WEBHOOK_URL
  port: 8443

# 日志配置
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR

# 调试模式
debug: false

# API服务配置
api:
  host: "0.0.0.0"
  port: 9005
  auth:
    enabled: false
    secret_key: "your-secret-key"

# 数据库配置
database:
  # MongoDB配置（生产环境）
  mongodb:
    host: "*************"
    port: 27017
    username: "root"
    password: "cPOqO2hhxbLPe40f"
    database: "wingame_config"
    auth_source: "admin"
    connection_string: "***********************************************************************************"
    options:
      maxPoolSize: 100
      minPoolSize: 10
      connectTimeoutMS: 30000
      socketTimeoutMS: 360000
      retryWrites: true
      serverSelectionTimeoutMS: 5000

  # MySQL数据库配置
  mysql:
    host: "*************"
    port: 3306
    user: "wingame"
    password: "ws82H4HRFbzjmNtD"
    name: "wingame"

  # SQLite配置（开发环境）
  sqlite:
    url: "sqlite:///bot.db"

  # 当前使用的数据库类型
  type: "mongodb"  # 可选: "sqlite", "mysql", "mongodb"

# 调度器配置
scheduler:
  timezone: "Asia/Shanghai"

# 周期性推送配置已移至 config/periodic_push_state.json
# 按 message_type 分组管理，支持不同类型的独立配置

# 菜单配置
menu:
  config_path: "config/menu_config.json"  # 菜单配置文件路径

# 特殊参数映射配置
special_params:
  # 关键字映射 (模板中的关键字 -> 映射类型)
  keywords:
    活动名: "activity_name"
    游戏名: "game_name"
    币种: "currency_type"
    等级: "level_name"
    道具名: "item_name"
    称号: "title_name"

  # 映射表配置
  mappings:
    # 活动名映射（根据活动类型的子类型名字）
    # 语言ID: 1=English, 2=Português, 12=繁體中文
    activity_name:
      "100":
        "1": "Deposit Success"
        "2": "Sucesso do Depósito"
        "12": "充值成功"
      "200":
        "1": "Withdrawal Success"
        "2": "Sucesso do Saque"
        "12": "提現成功"
      "300":
        "1": "Game Winning"
        "2": "Vitória no Jogo"
        "12": "遊戲獲獎"
      "400":
        "1": "Red Envelope Rain"
        "2": "Chuva de Envelopes Vermelhos"
        "12": "紅包雨"
      "500":
        "1": "Betting Cashback"
        "2": "Cashback de Apostas"
        "12": "投注返利"
      "18000":
        "1": "Betting Cashback"
        "2": "Cashback de Apostas"
        "12": "投注返利"
      "19000":
        "1": "First Deposit Bonus"
        "2": "Bônus de Primeiro Depósito"
        "12": "首充拉新"

    # 游戏名映射
    game_name:
      "1001":
        "1": "Slot Machine"
        "2": "Máquina Caça-níqueis"
        "12": "老虎機"
      "1002":
        "1": "Baccarat"
        "2": "Bacará"
        "12": "百家樂"
      "1003":
        "1": "Roulette"
        "2": "Roleta"
        "12": "輪盤"

    # 币种映射
    currency_type:
      "1":
        "1": "Gold"
        "2": "Ouro"
        "12": "金幣"
      "2":
        "1": "Diamond"
        "2": "Diamante"
        "12": "鑽石"
      "3":
        "1": "Points"
        "2": "Pontos"
        "12": "積分"

    # 等级映射
    level_name:
      "1":
        "1": "青铜"
        "2": "Bronze"
        "3": "青銅"
      "2":
        "1": "白银"
        "2": "Silver"
        "3": "白銀"
      "3":
        "1": "黄金"
        "2": "Gold"
        "3": "黃金"

    # 道具名映射
    item_name:
      "2001":
        "1": "超级武器"
        "2": "Super Weapon"
        "3": "超級武器"
      "2002":
        "1": "防护盾"
        "2": "Shield"
        "3": "防護盾"

    # 称号映射
    title_name:
      "3001":
        "1": "新手玩家"
        "2": "Newbie"
        "3": "新手玩家"
      "3002":
        "1": "资深玩家"
        "2": "Veteran"
        "3": "資深玩家"
