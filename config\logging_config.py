#!/usr/bin/env python3
"""
日志配置模块
"""
import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path

class LoggingConfig:
    """日志配置类"""

    def __init__(self):
        self.log_dir = Path("logs")
        self.setup_log_directory()

    def setup_log_directory(self):
        """创建日志目录"""
        self.log_dir.mkdir(exist_ok=True)
    
    def get_logger(self, name: str, level: str = "INFO") -> logging.Logger:
        """获取配置好的logger"""
        logger = logging.getLogger(name)

        # 避免重复添加handler
        if logger.handlers:
            return logger

        # 设置日志级别
        log_level = getattr(logging, level.upper(), logging.INFO)
        logger.setLevel(log_level)

        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # 当前日志文件
        current_log_file = self.log_dir / "log.log"

        # 使用TimedRotatingFileHandler实现按日期轮转
        # when='midnight': 每天午夜轮转
        # interval=1: 每1天轮转一次
        # backupCount=365: 保留365个备份文件
        # encoding='utf-8': 使用UTF-8编码
        file_handler = logging.handlers.TimedRotatingFileHandler(
            filename=current_log_file,
            when='midnight',
            interval=1,
            backupCount=365,
            encoding='utf-8'
        )

        # 设置归档文件名格式为 YYYY-MM-DD.log
        file_handler.suffix = "%Y-%m-%d.log"
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)

        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)

        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger
    
    def get_unified_logger(self, name: str = "app") -> logging.Logger:
        """获取统一的logger"""
        return self.get_logger(name, "INFO")

# 全局日志配置实例
logging_config = LoggingConfig()

# 统一的logger - 所有模块使用同一个logger，写入同一个文件
app_logger = logging_config.get_unified_logger("app")
api_logger = app_logger  # API日志使用同一个logger
db_logger = app_logger   # 数据库日志使用同一个logger
pusher_logger = app_logger  # 推送日志使用同一个logger
