#!/usr/bin/env python3
"""
日志配置模块
"""
import os
import logging
import logging.handlers
from datetime import datetime
from pathlib import Path

class LoggingConfig:
    """日志配置类"""
    
    def __init__(self):
        self.log_dir = Path("logs")
        self.setup_log_directory()
    
    def setup_log_directory(self):
        """创建日志目录"""
        self.log_dir.mkdir(exist_ok=True)
        
        # 按日期创建子目录
        today = datetime.now().strftime("%Y-%m-%d")
        self.today_log_dir = self.log_dir / today
        self.today_log_dir.mkdir(exist_ok=True)
    
    def get_logger(self, name: str, level: str = "INFO") -> logging.Logger:
        """获取配置好的logger"""
        logger = logging.getLogger(name)
        
        # 避免重复添加handler
        if logger.handlers:
            return logger
        
        # 设置日志级别
        log_level = getattr(logging, level.upper(), logging.INFO)
        logger.setLevel(log_level)
        
        # 创建格式器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 按日期创建日志文件
        today = datetime.now().strftime("%Y-%m-%d")
        log_file = self.today_log_dir / f"{name}_{today}.log"
        
        # 文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(log_level)
        file_handler.setFormatter(formatter)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level)
        console_handler.setFormatter(formatter)
        
        # 添加处理器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        return logger
    
    def get_api_logger(self) -> logging.Logger:
        """获取API专用logger"""
        return self.get_logger("api", "INFO")
    
    def get_app_logger(self) -> logging.Logger:
        """获取应用程序logger"""
        return self.get_logger("app", "INFO")
    
    def get_db_logger(self) -> logging.Logger:
        """获取数据库logger"""
        return self.get_logger("database", "INFO")
    
    def get_pusher_logger(self) -> logging.Logger:
        """获取推送logger"""
        return self.get_logger("pusher", "INFO")

# 全局日志配置实例
logging_config = LoggingConfig()

# 预定义的logger
api_logger = logging_config.get_api_logger()
app_logger = logging_config.get_app_logger()
db_logger = logging_config.get_db_logger()
pusher_logger = logging_config.get_pusher_logger()
