"""
配置管理模块
支持从环境变量和配置文件加载设置
"""
import os
import yaml
from typing import Optional, Dict, Any
from pathlib import Path


class Settings:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config/config.yaml"):
        self.config_file = Path(config_file)
        self._config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件"""
        if self.config_file.exists():
            with open(self.config_file, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f) or {}
        else:
            self._config = {}
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值，优先从环境变量获取"""
        # 首先尝试从环境变量获取
        env_value = os.getenv(key.upper())
        if env_value is not None:
            return env_value
        
        # 然后从配置文件获取
        keys = key.split('.')
        value = self._config
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        return value
    
    @property
    def bot_token(self) -> str:
        """获取Bot Token"""
        token = self.get('bot.token', self.get('BOT_TOKEN'))
        if not token:
            raise ValueError("Bot token not found. Please set BOT_TOKEN environment variable or configure in config.yaml")
        return token
    
    @property
    def use_proxy(self) -> bool:
        """是否使用代理"""
        value = self.get('proxy.enabled', self.get('USE_PROXY', 'false'))
        if isinstance(value, bool):
            return value
        return str(value).lower() in ('true', '1', 'yes')
    
    @property
    def proxy_url(self) -> Optional[str]:
        """代理URL"""
        if self.use_proxy:
            return self.get('proxy.url', self.get('PROXY_URL'))
        return None
    
    @property
    def webhook_url(self) -> Optional[str]:
        """Webhook URL（用于生产环境）"""
        return self.get('webhook.url', self.get('WEBHOOK_URL'))
    
    @property
    def webhook_port(self) -> int:
        """Webhook端口"""
        return int(self.get('webhook.port', self.get('WEBHOOK_PORT', 8443)))
    
    @property
    def debug(self) -> bool:
        """调试模式"""
        value = self.get('debug', self.get('DEBUG', 'false'))
        if isinstance(value, bool):
            return value
        return str(value).lower() in ('true', '1', 'yes')
    
    @property
    def log_level(self) -> str:
        """日志级别"""
        return self.get('logging.level', self.get('LOG_LEVEL', 'INFO')).upper()

    # API服务配置
    @property
    def api_host(self) -> str:
        """API服务主机"""
        return self.get('api.host', self.get('API_HOST', '0.0.0.0'))

    @property
    def api_port(self) -> int:
        """API服务端口"""
        port = self.get('api.port', self.get('API_PORT', '8000'))
        try:
            return int(port)
        except (ValueError, TypeError):
            return 8000

    # 数据库配置
    @property
    def database_url(self) -> str:
        """数据库连接URL（兼容性保留）"""
        return self.get('database.url', self.get('DATABASE_URL', 'mysql://user:password@localhost:3306/botdb'))

    @property
    def db_host(self) -> str:
        """数据库主机"""
        return self.get('database.mysql.host', self.get('DB_HOST', '*************'))

    @property
    def db_port(self) -> int:
        """数据库端口"""
        port = self.get('database.mysql.port', self.get('DB_PORT', '3306'))
        try:
            return int(port)
        except (ValueError, TypeError):
            return 3306

    @property
    def db_user(self) -> str:
        """数据库用户名"""
        return self.get('database.mysql.user', self.get('DB_USER', 'wingame'))

    @property
    def db_password(self) -> str:
        """数据库密码"""
        return self.get('database.mysql.password', self.get('DB_PASSWORD', 'ws82H4HRFbzjmNtD'))

    @property
    def db_name(self) -> str:
        """数据库名称"""
        return self.get('database.mysql.name', self.get('DB_NAME', 'wingame'))

    # 调度器配置
    @property
    def timezone(self) -> str:
        """时区"""
        return self.get('scheduler.timezone', self.get('TIMEZONE', 'Asia/Shanghai'))

    # API服务配置
    @property
    def api_host(self) -> str:
        """API服务主机"""
        return self.get('api.host', self.get('API_HOST', '0.0.0.0'))

    @property
    def api_port(self) -> int:
        """API服务端口"""
        port = self.get('api.port', self.get('API_PORT', '8000'))
        try:
            return int(port)
        except (ValueError, TypeError):
            return 8000

    @property
    def api_auth_enabled(self) -> bool:
        """API认证是否启用"""
        return self.get('api.auth.enabled', self.get('API_AUTH_ENABLED', 'false')).lower() == 'true'

    @property
    def api_secret_key(self) -> str:
        """API密钥"""
        return self.get('api.auth.secret_key', self.get('API_SECRET_KEY', 'your-secret-key'))

    @property
    def allowed_origins(self) -> list:
        """允许的CORS源"""
        origins = self.get('api.cors.allowed_origins', self.get('ALLOWED_ORIGINS', '*'))
        if origins == '*':
            return ["*"]
        return [origin.strip() for origin in origins.split(',')]

    # 数据库配置
    @property
    def database_url(self) -> str:
        """数据库连接URL"""
        return self.get('database.url', self.get('DATABASE_URL', 'sqlite:///./bot.db'))

    @property
    def database_echo(self) -> bool:
        """数据库SQL日志"""
        return self.get('database.echo', self.get('DATABASE_ECHO', 'false')).lower() == 'true'

    # 调度器配置
    @property
    def timezone(self) -> str:
        """时区"""
        return self.get('scheduler.timezone', self.get('TIMEZONE', 'Asia/Shanghai'))

    @property
    def test_chat_id(self) -> Optional[int]:
        """测试聊天ID"""
        chat_id = self.get('test.chat_id', self.get('CHAT_ID'))
        if chat_id:
            try:
                return int(chat_id)
            except (ValueError, TypeError):
                return None
        return None

    # 菜单配置
    @property
    def menu_config_path(self) -> str:
        """获取菜单配置文件路径"""
        return self.get('menu.config_path', 'config/menu_config.json')

    # MongoDB配置
    @property
    def mongodb_connection_string(self) -> str:
        """获取MongoDB连接字符串"""
        return self.get('database.mongodb.connection_string', '')

    @property
    def mongodb_host(self) -> str:
        """获取MongoDB主机"""
        return self.get('database.mongodb.host', 'localhost')

    @property
    def mongodb_port(self) -> int:
        """获取MongoDB端口"""
        return self.get('database.mongodb.port', 27017)

    @property
    def mongodb_username(self) -> str:
        """获取MongoDB用户名"""
        return self.get('database.mongodb.username', '')

    @property
    def mongodb_password(self) -> str:
        """获取MongoDB密码"""
        return self.get('database.mongodb.password', '')

    @property
    def mongodb_database(self) -> str:
        """获取MongoDB数据库名"""
        return self.get('database.mongodb.database', '')

    @property
    def mongodb_auth_source(self) -> str:
        """获取MongoDB认证源"""
        return self.get('database.mongodb.auth_source', 'admin')

    @property
    def mongodb_options(self) -> Dict[str, Any]:
        """获取MongoDB连接选项"""
        return self.get('database.mongodb.options', {})

    @property
    def database_type(self) -> str:
        """获取当前使用的数据库类型"""
        return self.get('database.type', 'sqlite')

    # 特殊参数配置
    @property
    def special_params_keywords(self) -> dict:
        """特殊参数关键字映射"""
        return self.get("special_params.keywords", {})

    @property
    def special_params_mappings(self) -> dict:
        """特殊参数映射表"""
        return self.get("special_params.mappings", {})


# 全局配置实例
settings = Settings()
