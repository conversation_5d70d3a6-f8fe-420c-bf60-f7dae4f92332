"""
数据库连接管理
参考SMS项目的简洁风格，直接使用连接池和原生SQL
"""
import asyncio
import logging
from typing import List, Dict, Any, Optional
import aiomysql
from config.settings import settings

logger = logging.getLogger(__name__)

# 全局连接池
_db_pool = None


async def init_mysql_pool():
    """初始化MySQL连接池"""
    global _db_pool
    try:
        # 解析数据库URL
        database_url = settings.database_url
        logger.info(f"连接数据库: {database_url}")

        # 简单解析URL (postgresql://user:password@host:port/database)
        # 这里假设你会配置为MySQL格式或者直接在settings中配置各个参数

        _db_pool = await aiomysql.create_pool(
            host=settings.db_host,
            port=settings.db_port,
            user=settings.db_user,
            password=settings.db_password,
            db=settings.db_name,
            autocommit=True,
            minsize=1,
            maxsize=10,
            charset='utf8mb4'
        )

        logger.info("MySQL连接池创建成功")

    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise


def get_db_pool():
    """获取数据库连接池"""
    if _db_pool is None:
        raise Exception("数据库连接池未初始化")
    return _db_pool


async def close_mysql_pool():
    """关闭数据库连接池"""
    global _db_pool
    if _db_pool:
        _db_pool.close()
        await _db_pool.wait_closed()
        _db_pool = None
        logger.info("数据库连接池已关闭")
# 业务查询函数 - 直接使用SQL，参考SMS项目风格

async def get_enabled_tasks() -> List[Dict[str, Any]]:
    """获取启用的定时任务列表"""
    try:
        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # TODO: 根据实际表结构调整SQL
                query = """
                    SELECT
                        id,
                        name,
                        description,
                        schedule,
                        task_type,
                        config,
                        enabled,
                        created_at,
                        updated_at
                    FROM scheduled_tasks
                    WHERE enabled = 1
                    ORDER BY created_at
                """
                await cursor.execute(query)
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]

    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        return []


async def get_task_config(task_id: str) -> Optional[Dict[str, Any]]:
    """获取任务配置"""
    try:
        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # TODO: 根据实际表结构调整SQL
                query = """
                    SELECT
                        id,
                        name,
                        description,
                        schedule,
                        task_type,
                        config,
                        enabled,
                        created_at,
                        updated_at
                    FROM scheduled_tasks
                    WHERE id = %s
                """
                await cursor.execute(query, [task_id])
                row = await cursor.fetchone()
                return dict(row) if row else None

    except Exception as e:
        logger.error(f"获取任务配置失败: {e}, task_id: {task_id}")
        return None





# 初始化和关闭函数
async def init_database():
    """初始化数据库连接"""
    await init_mysql_pool()


async def close_database():
    """关闭数据库连接"""
    await close_mysql_pool()
