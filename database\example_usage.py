"""
数据库使用示例
展示如何直接使用SQL查询，参考SMS项目风格
"""
import asyncio
import logging
from database.connection import get_db_pool
import aiomysql

logger = logging.getLogger(__name__)


async def example_direct_sql_usage():
    """直接使用SQL的示例"""
    try:
        # 获取连接池
        db_pool = get_db_pool()
        
        # 方式1: 查询任务列表
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                # 直接写SQL，根据实际表结构调整
                query = """
                    SELECT 
                        id,
                        name,
                        schedule,
                        task_type,
                        config,
                        enabled
                    FROM scheduled_tasks 
                    WHERE enabled = 1 
                    AND task_type = %s
                    ORDER BY created_at DESC
                    LIMIT 10
                """
                await cursor.execute(query, ['message'])
                tasks = await cursor.fetchall()
                
                print(f"找到 {len(tasks)} 个消息任务:")
                for task in tasks:
                    print(f"  - {task['name']}: {task['schedule']}")
        
        # 注意: 我们只进行查询操作，不修改数据库
        # 数据库的修改由专门的服务程序负责，避免混乱
        
        # 方式2: 复杂查询示例
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                query = """
                    SELECT 
                        t.id,
                        t.name,
                        t.schedule,
                        COUNT(l.id) as execution_count,
                        MAX(l.executed_at) as last_execution
                    FROM scheduled_tasks t
                    LEFT JOIN task_execution_log l ON t.id = l.task_id
                    WHERE t.enabled = 1
                    GROUP BY t.id, t.name, t.schedule
                    HAVING execution_count > 0
                    ORDER BY last_execution DESC
                """
                await cursor.execute(query)
                results = await cursor.fetchall()
                
                print(f"活跃任务统计:")
                for row in results:
                    print(f"  - {row['name']}: 执行{row['execution_count']}次, 最后执行: {row['last_execution']}")
                    
    except Exception as e:
        logger.error(f"数据库操作示例失败: {e}")


async def example_business_queries():
    """业务查询示例"""
    try:
        db_pool = get_db_pool()
        
        # 获取需要立即执行的任务
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                query = """
                    SELECT 
                        id,
                        name,
                        config,
                        task_type
                    FROM scheduled_tasks 
                    WHERE enabled = 1 
                    AND next_run_time <= NOW()
                    ORDER BY priority DESC, created_at ASC
                """
                await cursor.execute(query)
                pending_tasks = await cursor.fetchall()
                
                print(f"待执行任务: {len(pending_tasks)} 个")
                return pending_tasks
        
        # 获取错误频繁的任务
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                query = """
                    SELECT 
                        task_id,
                        COUNT(*) as error_count,
                        MAX(executed_at) as last_error
                    FROM task_execution_log 
                    WHERE status = 'error' 
                    AND executed_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY task_id
                    HAVING error_count >= 3
                    ORDER BY error_count DESC
                """
                await cursor.execute(query)
                error_tasks = await cursor.fetchall()
                
                print(f"高错误率任务: {len(error_tasks)} 个")
                for task in error_tasks:
                    print(f"  - 任务 {task['task_id']}: {task['error_count']} 次错误")
                
                return error_tasks
                
    except Exception as e:
        logger.error(f"业务查询失败: {e}")
        return []


# 这种方式的优势:
# 1. 直接使用SQL，性能更好
# 2. 灵活性高，可以写复杂查询
# 3. 代码简洁，没有多余的抽象层
# 4. 易于调试，SQL语句清晰可见
# 5. 参考SMS项目的成熟实践
# 6. 只查询不修改，避免服务间的数据库操作冲突
