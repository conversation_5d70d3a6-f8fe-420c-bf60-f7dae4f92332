"""
MongoDB连接管理模块
"""
import logging
import sys
from pathlib import Path
from typing import Optional, Dict, Any
from pymongo import MongoClient
from pymongo.errors import ConnectionFailure, ServerSelectionTimeoutError, OperationFailure

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import settings
from config.logging_config import db_logger

logger = db_logger


class MongoDBConnection:
    """MongoDB连接管理器"""
    
    def __init__(self):
        self.client: Optional[MongoClient] = None
        self.database = None
        self._is_connected = False
    
    def connect(self) -> bool:
        """
        连接到MongoDB
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 获取连接配置
            connection_string = settings.mongodb_connection_string
            
            if not connection_string:
                logger.error("MongoDB连接字符串未配置")
                return False
            
            # 获取连接选项
            options = settings.mongodb_options
            
            logger.info(f"正在连接MongoDB: {settings.mongodb_host}:{settings.mongodb_port}")
            logger.info(f"数据库: {settings.mongodb_database}")
            
            # 创建MongoDB客户端
            self.client = MongoClient(
                connection_string,
                **options
            )
            
            # 测试连接
            self.client.admin.command('ping')
            
            # 获取数据库
            self.database = self.client[settings.mongodb_database]
            
            self._is_connected = True
            logger.info("✅ MongoDB连接成功")
            return True
            
        except ConnectionFailure as e:
            logger.error(f"❌ MongoDB连接失败: {e}")
            return False
        except ServerSelectionTimeoutError as e:
            logger.error(f"❌ MongoDB服务器选择超时: {e}")
            return False
        except OperationFailure as e:
            logger.error(f"❌ MongoDB操作失败: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ MongoDB连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开MongoDB连接"""
        if self.client:
            try:
                self.client.close()
                self._is_connected = False
                logger.info("✅ MongoDB连接已关闭")
            except Exception as e:
                logger.error(f"❌ 关闭MongoDB连接失败: {e}")
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        if not self._is_connected or not self.client:
            return False
        
        try:
            # 发送ping命令测试连接
            self.client.admin.command('ping')
            return True
        except Exception:
            self._is_connected = False
            return False
    
    def get_database(self):
        """获取数据库对象"""
        if not self.is_connected():
            if not self.connect():
                return None
        return self.database
    
    def get_collection(self, collection_name: str):
        """获取集合对象"""
        database = self.get_database()
        if database is None:
            return None
        return database[collection_name]
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试连接并返回详细信息
        
        Returns:
            Dict: 连接测试结果
        """
        result = {
            "success": False,
            "error": None,
            "server_info": None,
            "database_info": None,
            "collections": []
        }
        
        try:
            if not self.connect():
                result["error"] = "连接失败"
                return result
            
            # 获取服务器信息
            server_info = self.client.server_info()
            result["server_info"] = {
                "version": server_info.get("version"),
                "git_version": server_info.get("gitVersion"),
                "platform": server_info.get("platform")
            }
            
            # 获取数据库信息
            db_stats = self.database.command("dbStats")
            result["database_info"] = {
                "name": self.database.name,
                "collections": db_stats.get("collections", 0),
                "data_size": db_stats.get("dataSize", 0),
                "storage_size": db_stats.get("storageSize", 0)
            }
            
            # 获取集合列表
            result["collections"] = self.database.list_collection_names()
            
            result["success"] = True
            logger.info("✅ MongoDB连接测试成功")
            
        except Exception as e:
            result["error"] = str(e)
            logger.error(f"❌ MongoDB连接测试失败: {e}")
        
        return result
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 创建全局MongoDB连接实例
mongodb_connection = MongoDBConnection()


def get_mongodb_client() -> Optional[MongoClient]:
    """获取MongoDB客户端"""
    if mongodb_connection.is_connected():
        return mongodb_connection.client
    
    if mongodb_connection.connect():
        return mongodb_connection.client
    
    return None


def get_mongodb_database():
    """获取MongoDB数据库"""
    return mongodb_connection.get_database()


def get_mongodb_collection(collection_name: str):
    """获取MongoDB集合"""
    return mongodb_connection.get_collection(collection_name)
