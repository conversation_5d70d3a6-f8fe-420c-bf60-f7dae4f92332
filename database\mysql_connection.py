#!/usr/bin/env python3
"""
MySQL数据库连接管理器
支持与MongoDB并存的双数据库架构
"""
import logging
import aiomysql
from typing import Optional, Dict, List, Any
from config.settings import settings

logger = logging.getLogger(__name__)

class MySQLConnection:
    """MySQL连接管理器"""
    
    def __init__(self):
        self.pool: Optional[aiomysql.Pool] = None
        self._is_connected = False
    
    async def connect(self) -> bool:
        """连接到MySQL数据库"""
        try:
            logger.info(f"正在连接MySQL: {settings.db_host}:{settings.db_port}")
            logger.info(f"数据库: {settings.db_name}")
            
            self.pool = await aiomysql.create_pool(
                host=settings.db_host,
                port=settings.db_port,
                user=settings.db_user,
                password=settings.db_password,
                db=settings.db_name,
                autocommit=True,
                minsize=1,
                maxsize=10,
                charset='utf8mb4'
            )
            
            # 测试连接
            async with self.pool.acquire() as conn:
                async with conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                    result = await cursor.fetchone()
                    if result[0] != 1:
                        raise Exception("MySQL连接测试失败")
            
            self._is_connected = True
            logger.info("✅ MySQL连接成功")
            return True
            
        except Exception as e:
            logger.error(f"❌ MySQL连接失败: {e}")
            self._is_connected = False
            return False
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        return self._is_connected and self.pool is not None
    
    async def disconnect(self):
        """断开连接"""
        if self.pool:
            self.pool.close()
            await self.pool.wait_closed()
            self.pool = None
            self._is_connected = False
            logger.info("MySQL连接已关闭")
    
    def get_pool(self) -> aiomysql.Pool:
        """获取连接池"""
        if not self.is_connected():
            raise Exception("MySQL连接池未初始化")
        return self.pool
    
    async def execute_query(self, query: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(query, params)
                    results = await cursor.fetchall()
                    return [dict(row) for row in results]
        except Exception as e:
            logger.error(f"MySQL查询失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {params}")
            raise
    
    async def execute_single_query(self, query: str, params: tuple = None) -> Optional[Dict[str, Any]]:
        """执行查询并返回单个结果"""
        try:
            async with self.pool.acquire() as conn:
                async with conn.cursor(aiomysql.DictCursor) as cursor:
                    await cursor.execute(query, params)
                    result = await cursor.fetchone()
                    return dict(result) if result else None
        except Exception as e:
            logger.error(f"MySQL单查询失败: {e}")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {params}")
            raise

# 创建全局MySQL连接实例
mysql_connection = MySQLConnection()

async def init_mysql():
    """初始化MySQL连接"""
    return await mysql_connection.connect()

async def close_mysql():
    """关闭MySQL连接"""
    await mysql_connection.disconnect()

def get_mysql_pool() -> aiomysql.Pool:
    """获取MySQL连接池"""
    return mysql_connection.get_pool()

def get_mysql_connection() -> MySQLConnection:
    """获取MySQL连接管理器"""
    return mysql_connection
