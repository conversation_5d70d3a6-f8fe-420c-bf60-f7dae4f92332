#!/usr/bin/env python3
"""
调试配置缓存问题
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def debug_config_cache():
    """调试配置缓存"""
    print("🔍 调试配置缓存问题")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        
        # 1. 检查配置管理器状态
        print("1. 检查配置管理器状态")
        print(f"   MongoDB连接: {config_manager.mongo.is_connected() if config_manager.mongo else '未连接'}")
        
        # 2. 强制重新加载notify配置
        print("\n2. 强制重新加载notify配置")
        success = await config_manager.load_notify_config()
        print(f"   加载结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 3. 检查缓存内容
        print("\n3. 检查缓存内容")
        all_configs = config_manager.get_all_notify_configs()
        print(f"   缓存中的配置数量: {len(all_configs)}")
        
        # 4. 详细检查每个配置
        print("\n4. 详细检查每个配置")
        for i, config in enumerate(all_configs, 1):
            config_id = config.get("id")
            config_name = config.get("tgName", "")
            config_types = config.get("types", [])
            business_no = config.get("business_no", "")
            
            print(f"   配置 {i}:")
            print(f"     ID: {config_id}")
            print(f"     名称: {config_name}")
            print(f"     types: {config_types} (类型: {type(config_types).__name__})")
            print(f"     business_no: {business_no}")
            print()
        
        # 5. 测试查找逻辑
        print("5. 测试查找逻辑")
        test_types = [18000, 19000, 100, 200, 400]
        
        for test_type in test_types:
            print(f"\n   查找类型 {test_type}:")
            
            # 手动查找
            found_config = None
            for config in all_configs:
                config_types = config.get("types", [])
                if test_type in config_types:
                    found_config = config
                    break
            
            if found_config:
                print(f"     ✅ 找到配置: {found_config.get('tgName')}")
                print(f"     配置ID: {found_config.get('id')}")
                print(f"     types: {found_config.get('types')}")
            else:
                print(f"     ❌ 未找到配置")
        
        # 6. 测试模板处理器的查找方法
        print(f"\n6. 测试模板处理器的查找方法")
        from pusher.template_message_handler import TemplateMessageHandler
        
        handler = TemplateMessageHandler()
        
        for test_type in [18000, 400]:
            print(f"\n   使用处理器查找类型 {test_type}:")
            config = handler._find_notify_config_by_type(test_type)
            
            if config:
                print(f"     ✅ 找到配置: {config.get('tgName')}")
                print(f"     配置ID: {config.get('id')}")
                print(f"     types: {config.get('types')}")
            else:
                print(f"     ❌ 未找到配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_direct_api():
    """直接测试API"""
    print(f"\n🌐 直接测试API")
    print("=" * 50)
    
    try:
        import aiohttp
        
        # 重新加载配置后测试
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        test_data = {
            "business_no": "39bac42a",
            "type": 18000,
            "params": [
                "测试用户",
                [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                "感谢参与"
            ]
        }
        
        print(f"📡 API地址: {api_url}")
        print(f"📋 测试数据: {test_data}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                status = response.status
                text = await response.text()
                
                print(f"\n📊 API响应:")
                print(f"   状态码: {status}")
                print(f"   响应: {text}")
                
                if status == 200:
                    try:
                        import json
                        response_data = json.loads(text)
                        if response_data.get("status") == "success":
                            print(f"🎉 推送成功！")
                            return True
                        else:
                            print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"⚠️ 响应格式异常")
                        return False
                else:
                    print(f"❌ API调用失败")
                    return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 调试定时推送配置问题")
    print("=" * 60)
    
    # 1. 调试配置缓存
    cache_ok = await debug_config_cache()
    
    # 2. 如果缓存正常，测试API
    api_ok = False
    if cache_ok:
        api_ok = await test_direct_api()
    
    print(f"\n" + "=" * 60)
    print(f"📊 调试结果:")
    print(f"   配置缓存: {'✅ 正常' if cache_ok else '❌ 异常'}")
    print(f"   API测试: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if cache_ok and api_ok:
        print(f"\n🎉 问题已解决！定时推送功能正常")
        print(f"💡 下一步:")
        print(f"   1. 定时任务将在00:55自动执行")
        print(f"   2. 监控Telegram频道确认推送结果")
    else:
        print(f"\n⚠️ 问题仍然存在，需要进一步排查")

if __name__ == "__main__":
    asyncio.run(main())
