#!/usr/bin/env python3
"""
调试数据源问题
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_query_today():
    """测试MySQL查询今天的数据"""
    print("🔍 测试MySQL查询今天的数据")
    print("=" * 60)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL
        await init_mysql()
        mysql_conn = get_mysql_connection()
        
        # 查询今天的数据
        business_no = "39bac42a"
        today = datetime.now().strftime("%Y-%m-%d")
        
        query = """
            SELECT playerId, ranking, currencyId, wagered 
            FROM ea_platform_wagered_rebate_rank_log 
            WHERE business_no = %s AND date = %s 
            ORDER BY ranking
        """
        
        print(f"查询SQL: {query}")
        print(f"参数: business_no={business_no}, date={today}")
        
        results = await mysql_conn.execute_query(query, (business_no, today))
        
        print(f"\n查询结果: {len(results)} 条记录")
        print(f"今天日期: {today}")
        
        if results:
            print(f"\n前5条记录:")
            for i, record in enumerate(results[:5], 1):
                ranking = record.get("ranking", 0)
                player_id = record.get("playerId", 0)
                currency_id = record.get("currencyId", 0)
                wagered = record.get("wagered", 0)
                
                print(f"  {i}. ranking={ranking}, playerId={player_id}, currencyId={currency_id}, wagered={wagered}")
                
                # 验证是否与您提供的数据一致
                if i == 1:
                    expected_player_id = 4743182
                    expected_ranking = 1
                    if player_id == expected_player_id and ranking == expected_ranking:
                        print(f"     ✅ 第1条记录与预期一致")
                    else:
                        print(f"     ❌ 第1条记录不一致，预期: playerId={expected_player_id}, ranking={expected_ranking}")
        else:
            print(f"❌ 没有查询到数据")
            
            # 尝试查询其他日期
            print(f"\n尝试查询其他日期:")
            date_query = """
                SELECT DISTINCT date, COUNT(*) as count
                FROM ea_platform_wagered_rebate_rank_log 
                WHERE business_no = %s
                GROUP BY date 
                ORDER BY date DESC 
                LIMIT 5
            """
            
            date_results = await mysql_conn.execute_query(date_query, (business_no,))
            for date_record in date_results:
                date_str = date_record.get("date", "")
                count = date_record.get("count", 0)
                print(f"   日期: {date_str}, 记录数: {count}")
        
        return results
        
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_handler_with_debug():
    """测试处理器并观察详细日志"""
    print(f"\n🎯 测试处理器并观察详细日志")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 调试数据源问题")
    print("=" * 80)
    
    # 1. 测试MySQL查询
    mysql_results = await test_mysql_query_today()
    
    # 2. 测试处理器
    handler_ok = await test_handler_with_debug()
    
    print(f"\n" + "=" * 80)
    print(f"📊 调试结果:")
    print(f"   MySQL查询: {len(mysql_results)} 条记录")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if mysql_results:
        print(f"\n💡 数据对比:")
        print(f"   数据库第1条: playerId={mysql_results[0].get('playerId')}, ranking={mysql_results[0].get('ranking')}")
        print(f"   您提供的第1条: playerId=4743182, ranking=1")
        
        if mysql_results[0].get('playerId') == 4743182:
            print(f"   ✅ 数据源一致")
        else:
            print(f"   ❌ 数据源不一致，可能是日期或查询条件问题")
    
    print(f"\n🚀 下一步:")
    print(f"   1. 观察02:45:00的详细日志")
    print(f"   2. 确认数据源和组装逻辑")
    print(f"   3. 修复数据不一致问题")

if __name__ == "__main__":
    asyncio.run(main())
