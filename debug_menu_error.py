#!/usr/bin/env python3
"""
调试菜单发送错误
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from pusher.menu.menu_config_manager import menu_config_manager
from pusher.menu.menu_builder import menu_builder


def test_menu_creation():
    """测试菜单创建过程"""
    print("🔍 测试菜单创建过程")
    print("=" * 60)
    
    bot_token = "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw"
    group_id = -1002316158105
    
    try:
        # 1. 测试菜单数据获取
        print("1. 测试菜单数据获取...")
        menu_data = menu_builder.get_menu_for_context(bot_token, "group", group_id)
        
        if not menu_data:
            print("   ❌ 菜单数据获取失败")
            return False
        
        print(f"   ✅ 菜单数据获取成功")
        print(f"   📋 标题: {menu_data.get('title')}")
        print(f"   🖼️ 图片: {menu_data.get('image')}")
        
        # 2. 测试按钮创建
        print("\n2. 测试按钮创建...")
        buttons = menu_data.get("buttons", [])
        
        if not buttons:
            print("   ❌ 没有按钮配置")
            return False
        
        print(f"   📊 按钮行数: {len(buttons)}")
        
        # 3. 测试内联键盘创建
        print("\n3. 测试内联键盘创建...")
        try:
            keyboard = menu_builder.create_inline_keyboard(buttons)
            print(f"   ✅ 内联键盘创建成功")
            print(f"   📊 键盘行数: {len(keyboard.inline_keyboard)}")
            
            # 检查每个按钮
            for i, row in enumerate(keyboard.inline_keyboard):
                print(f"   第{i+1}行: {len(row)}个按钮")
                for j, button in enumerate(row):
                    print(f"     按钮{j+1}: {button.text}")
                    if hasattr(button, 'url') and button.url:
                        print(f"       URL: {button.url}")
                    elif hasattr(button, 'web_app') and button.web_app:
                        print(f"       WebApp: {button.web_app.url}")
                    elif hasattr(button, 'callback_data') and button.callback_data:
                        print(f"       回调: {button.callback_data}")
            
        except Exception as e:
            print(f"   ❌ 内联键盘创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 4. 测试消息文本创建
        print("\n4. 测试消息文本创建...")
        try:
            bot_info = menu_data.get("_bot_info", {})
            group_info = menu_data.get("_group_info", {})
            bot_name = bot_info.get("bot_name", "智能助手")
            
            welcome_text = f"👋 你好 测试用户！\n\n"
            welcome_text += f"📍 群组：测试群组\n"
            welcome_text += f"🤖 **{bot_name}** 为群组服务\n\n"
            
            if group_info.get("description"):
                welcome_text += f"✨ {group_info['description']}\n\n"
            
            welcome_text += menu_data.get("description", "欢迎使用我们的群组服务！")
            
            print(f"   ✅ 消息文本创建成功")
            print(f"   📝 文本长度: {len(welcome_text)} 字符")
            
        except Exception as e:
            print(f"   ❌ 消息文本创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        # 5. 测试图片URL
        print("\n5. 测试图片URL...")
        image_url = menu_data.get("image")
        if image_url:
            print(f"   🖼️ 图片URL: {image_url}")
            
            # 简单测试URL格式
            if image_url.startswith(('http://', 'https://')):
                print(f"   ✅ 图片URL格式正确")
            else:
                print(f"   ⚠️ 图片URL格式可能有问题")
        else:
            print(f"   ℹ️ 没有配置图片")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_webapp_import():
    """测试WebApp导入"""
    print("\n🔍 测试WebApp导入")
    print("=" * 60)
    
    try:
        # 测试WebApp导入
        from pusher.menu.menu_builder import WebApp
        print(f"✅ WebApp导入成功: {WebApp}")
        
        # 测试WebApp创建
        webapp = WebApp(url="https://t.me/telegram")
        print(f"✅ WebApp创建成功: {webapp}")
        
        # 测试按钮创建
        from telegram import InlineKeyboardButton
        button = InlineKeyboardButton(text="测试", web_app=webapp)
        print(f"✅ WebApp按钮创建成功: {button.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ WebApp测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 菜单错误调试开始")
    
    # 测试1: 菜单创建
    result1 = test_menu_creation()
    
    # 测试2: WebApp导入
    result2 = test_webapp_import()
    
    print(f"\n" + "=" * 60)
    print("📊 调试结果汇总")
    print("=" * 60)
    
    if result1 and result2:
        print("✅ 所有测试通过，菜单应该可以正常工作")
        print("\n💡 如果仍然显示简单消息，可能的原因:")
        print("1. Bot服务没有重启")
        print("2. 代码中有其他异常没有被捕获")
        print("3. Telegram API限制或网络问题")
        
        print("\n🔧 建议操作:")
        print("1. 重启Bot服务")
        print("2. 检查Bot服务的日志输出")
        print("3. 在私聊中测试 /start 看是否正常")
        
    else:
        print("❌ 发现问题，需要修复")
        if not result1:
            print("   - 菜单创建有问题")
        if not result2:
            print("   - WebApp导入有问题")


if __name__ == "__main__":
    main()
