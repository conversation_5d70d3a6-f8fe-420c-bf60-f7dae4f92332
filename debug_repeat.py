#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🔍 调试repeat处理")
    
    translator = TranslationManager()
    
    # 简单测试
    template = "[repeat]pelo depósito – {Currency2}{Amount3}[/repeat]"
    params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    
    # 测试嵌套数组查找
    nested_array, count = translator._find_nested_array_for_repeat(template, params)
    print(f"找到嵌套数组: {nested_array}, 循环次数: {count}")
    
    # 测试子参数上下文创建
    if nested_array:
        for i in range(count):
            sub_params = translator._create_sub_params_context(params, nested_array, i)
            print(f"循环 {i}: 子参数 = {sub_params}")
            
            # 测试repeat内容渲染
            repeat_content = "pelo depósito – {Currency2}{Amount3}"
            rendered = translator._render_repeat_content(repeat_content, sub_params)
            print(f"循环 {i}: 渲染结果 = {rendered}")
    
    print(f"\n完整渲染:")
    result = translator.render_template(template, params, "2")
    print(f"结果: {result}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
