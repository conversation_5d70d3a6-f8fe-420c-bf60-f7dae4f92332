#!/usr/bin/env python3
"""
调试taskType=500任务配置
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def debug_task_500():
    """调试taskType=500任务"""
    print("🔍 调试taskType=500任务配置")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        from scheduler.timezone_manager import TimeZoneManager
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 创建时区管理器
        tz_manager = TimeZoneManager()
        await tz_manager.load_merchant_timezones(mongo)
        
        # 查询taskType=500的任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks_500 = list(collection.find({"taskType": 500}))
        
        print(f"1. taskType=500任务总数: {len(tasks_500)}")
        
        for i, task in enumerate(tasks_500, 1):
            print(f"\n任务 {i}:")
            print(f"  ID: {task.get('_id')}")
            print(f"  商户: {task.get('business_no')}")
            print(f"  taskType: {task.get('taskType')}")
            print(f"  notifyId: {task.get('notifyId')}")
            print(f"  启用: {task.get('enabled')}")
            print(f"  频率: {task.get('sendFrequency')}")
            print(f"  调度日期: {task.get('scheduleDays')}")
            print(f"  调度时间: {task.get('scheduleTimes')}")
            
            # 检查当前时间是否匹配
            current_utc = datetime.now(timezone.utc)
            business_no = task.get('business_no', '')
            local_time = tz_manager.get_merchant_local_time(business_no, current_utc)
            
            print(f"  商户时区: {tz_manager.get_merchant_timezone(business_no)}")
            print(f"  当前UTC: {current_utc.strftime('%H:%M:%S')}")
            print(f"  商户本地: {local_time.strftime('%H:%M:%S')}")
            
            # 检查日期匹配
            send_frequency = task.get("sendFrequency", "")
            schedule_days = task.get("scheduleDays", [])
            
            if send_frequency == "weekly":
                day_of_week = local_time.date().weekday() + 1  # 1=周一, 7=周日
                date_match = day_of_week in schedule_days
                print(f"  周任务检查: 今天={day_of_week}, 调度日={schedule_days}, 匹配={date_match}")
            elif send_frequency == "monthly":
                day_of_month = local_time.date().day
                date_match = day_of_month in schedule_days
                print(f"  月任务检查: 今天={day_of_month}, 调度日={schedule_days}, 匹配={date_match}")
            else:
                date_match = False
                print(f"  ❌ 未知频率: {send_frequency}")
            
            # 检查时间匹配
            schedule_times = task.get("scheduleTimes", [])
            current_time_str = local_time.strftime("%H:%M")
            
            time_matches = []
            for schedule_time in schedule_times:
                if len(schedule_time.split(':')) == 3:
                    schedule_time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    schedule_time_str = schedule_time
                
                time_match = current_time_str == schedule_time_str
                time_matches.append(time_match)
                print(f"  时间检查: 当前={current_time_str}, 调度={schedule_time_str}, 匹配={time_match}")
            
            # 总体匹配结果
            should_execute = date_match and any(time_matches)
            print(f"  🎯 应该执行: {should_execute}")
            
            # 使用时区管理器验证
            tz_result = tz_manager.should_execute_task(task, current_utc)
            print(f"  🔍 时区管理器结果: {tz_result}")
        
        # 查询所有任务的时间配置
        print(f"\n2. 所有任务的17:00时间配置:")
        all_tasks = list(collection.find({"enabled": True}))
        
        for task in all_tasks:
            schedule_times = task.get("scheduleTimes", [])
            task_type = task.get("taskType")
            
            for schedule_time in schedule_times:
                if schedule_time.startswith("17:"):
                    print(f"  taskType={task_type}: {schedule_time}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await debug_task_500()
    
    if success:
        print(f"\n💡 调试完成")
        print(f"请检查:")
        print(f"1. taskType=500任务的调度时间配置")
        print(f"2. 当前时间是否在调度时间范围内")
        print(f"3. 日期匹配逻辑是否正确")
    else:
        print(f"\n⚠️ 调试失败")

if __name__ == "__main__":
    asyncio.run(main())
