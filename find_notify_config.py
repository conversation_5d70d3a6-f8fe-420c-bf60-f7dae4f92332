#!/usr/bin/env python3
"""
查找可用的通知配置
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def find_notify_configs():
    """查找可用的通知配置"""
    print("🔍 查找可用的通知配置")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return []
        
        # 查询通知配置
        notify_collection = mongo.get_collection("c_tgNotify")
        configs = list(notify_collection.find({"business_no": "39bac42a"}).limit(10))
        
        print(f"📋 找到 {len(configs)} 个通知配置:")
        
        available_configs = []
        
        for config in configs:
            notify_id = config.get("notifyId", "N/A")
            text = config.get("text", "")
            language = config.get("language", "N/A")
            target_count = len(config.get("notifyTarget", []))
            
            print(f"\n   notifyId: {notify_id}")
            print(f"   文本: {text[:50]}{'...' if len(text) > 50 else ''}")
            print(f"   语言: {language}")
            print(f"   目标数: {target_count}")
            
            if text and target_count > 0:
                available_configs.append({
                    "notifyId": notify_id,
                    "text": text,
                    "language": language,
                    "target_count": target_count
                })
                print(f"   ✅ 可用于测试")
            else:
                print(f"   ❌ 不可用（缺少文本或目标）")
        
        mongo.disconnect()
        
        if available_configs:
            print(f"\n💡 推荐使用的配置:")
            best_config = available_configs[0]
            print(f"   notifyId: {best_config['notifyId']}")
            print(f"   可以用 type={best_config['notifyId']} 进行测试")
        
        return available_configs
        
    except Exception as e:
        print(f"❌ 查找失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    print("🔧 查找可用的通知配置")
    print("=" * 80)
    
    configs = find_notify_configs()
    
    if configs:
        print(f"\n🎯 HTML测试建议:")
        best_config = configs[0]
        print(f"   使用 type={best_config['notifyId']} 进行测试")
        print(f"   原始文本: {best_config['text']}")
        
        # 生成测试用的HTML版本
        html_text = f"<p><b>HTML测试</b></p>\n<p>原始: <i>{best_config['text'][:30]}...</i></p>\n<p>代码: <code>HTML_SUCCESS</code></p>"
        
        print(f"\n📝 建议的HTML测试文本:")
        print(f"   {html_text}")
        
        print(f"\n🚀 测试命令:")
        print(f"   修改 simple_html_test.py 中的 type 为 {best_config['notifyId']}")
        print(f"   然后运行测试")
    else:
        print(f"\n⚠️ 未找到可用的配置")

if __name__ == "__main__":
    main()
