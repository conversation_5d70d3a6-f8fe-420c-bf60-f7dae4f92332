#!/usr/bin/env python3
"""
修复通知配置问题
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def fix_notify_config():
    """修复通知配置"""
    print("🔧 修复通知配置问题")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取通知配置集合
        collection = mongo.get_collection("c_tgNotify")
        
        # 1. 查找问题配置
        print("1. 查找问题配置")
        problem_configs = list(collection.find({
            "$or": [
                {"id": 8},
                {"id": 9}
            ]
        }))
        
        print(f"   找到 {len(problem_configs)} 个配置")
        
        for config in problem_configs:
            print(f"\n   配置 {config.get('id')}:")
            print(f"     名称: {config.get('tgName')}")
            print(f"     图片: {config.get('image', 'N/A')}")
            print(f"     按钮: {config.get('buttons', [])}")
            
            # 检查问题
            image = config.get('image', '')
            buttons = config.get('buttons', [])
            
            problems = []
            if not image or len(image) < 10:
                problems.append("图片ID无效")
            
            for button in buttons:
                if isinstance(button, dict):
                    external = button.get('external', '')
                    internal = button.get('internal', '')
                    if external == 'a' or internal == 'b':
                        problems.append("按钮URL是占位符")
            
            if problems:
                print(f"     ❌ 问题: {', '.join(problems)}")
            else:
                print(f"     ✅ 配置正常")
        
        # 2. 修复配置
        print(f"\n2. 修复配置")
        
        # 修复配置8
        print(f"   修复配置8...")
        update_result_8 = collection.update_one(
            {"id": 8},
            {
                "$set": {
                    "image": "",  # 清空无效的图片ID
                    "buttons": []  # 清空无效的按钮
                }
            }
        )
        
        # 修复配置9
        print(f"   修复配置9...")
        update_result_9 = collection.update_one(
            {"id": 9},
            {
                "$set": {
                    "image": "",  # 清空无效的图片ID
                    "buttons": []  # 清空无效的按钮
                }
            }
        )
        
        print(f"   配置8修改: {update_result_8.modified_count} 条记录")
        print(f"   配置9修改: {update_result_9.modified_count} 条记录")
        
        # 3. 验证修复结果
        print(f"\n3. 验证修复结果")
        fixed_configs = list(collection.find({
            "$or": [
                {"id": 8},
                {"id": 9}
            ]
        }))
        
        for config in fixed_configs:
            print(f"\n   配置 {config.get('id')} (修复后):")
            print(f"     图片: '{config.get('image', '')}'")
            print(f"     按钮: {config.get('buttons', [])}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_config():
    """测试修复后的配置"""
    print(f"\n🧪 测试修复后的配置")
    print("=" * 50)
    
    try:
        import asyncio
        import aiohttp
        
        async def test_api():
            # 测试API端点
            api_url = "http://localhost:9005/api/realtime-push/template"
            
            # 使用修复后的配置测试
            test_data = {
                "business_no": "39bac42a",
                "type": 400,
                "params": [
                    "测试用户",
                    "400"  # 红包雨活动
                ]
            }
            
            print(f"📡 API地址: {api_url}")
            print(f"📋 测试数据: {test_data}")
            
            async with aiohttp.ClientSession() as session:
                try:
                    async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                        status = response.status
                        text = await response.text()
                        
                        print(f"\n📊 API响应:")
                        print(f"   状态码: {status}")
                        print(f"   响应: {text}")
                        
                        if status == 200:
                            try:
                                import json
                                response_data = json.loads(text)
                                if response_data.get("status") == "success":
                                    print(f"🎉 推送成功！")
                                    return True
                                else:
                                    print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                                    return False
                            except json.JSONDecodeError:
                                print(f"⚠️ 响应格式异常")
                                return False
                        else:
                            print(f"❌ API调用失败")
                            return False
                            
                except aiohttp.ClientConnectorError:
                    print(f"❌ 连接失败 - 服务可能未启动")
                    return False
                except Exception as e:
                    print(f"❌ 请求异常: {e}")
                    return False
        
        # 运行异步测试
        return asyncio.run(test_api())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复定时推送配置问题")
    print("=" * 60)
    
    # 1. 修复配置
    fix_ok = fix_notify_config()
    
    # 2. 测试修复结果
    test_ok = False
    if fix_ok:
        test_ok = test_fixed_config()
    
    print(f"\n" + "=" * 60)
    print(f"📊 修复结果:")
    print(f"   配置修复: {'✅ 成功' if fix_ok else '❌ 失败'}")
    print(f"   功能测试: {'✅ 正常' if test_ok else '❌ 异常'}")
    
    if fix_ok and test_ok:
        print(f"\n🎉 问题已修复！")
        print(f"💡 下一步:")
        print(f"   1. 等待下一个定时任务 (01:17)")
        print(f"   2. 观察是否推送成功")
        print(f"   3. 检查Telegram频道消息")
    else:
        print(f"\n⚠️ 问题仍然存在")
        print(f"💡 建议:")
        print(f"   1. 检查图片文件ID格式")
        print(f"   2. 确认按钮URL配置")
        print(f"   3. 查看详细错误日志")

if __name__ == "__main__":
    main()
