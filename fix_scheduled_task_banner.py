#!/usr/bin/env python3
"""
修复定时任务中的bannerUrl问题
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_and_fix_banner_url():
    """检查并修复定时任务中的bannerUrl"""
    print("🔍 检查定时任务中的bannerUrl问题")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取定时任务集合
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        
        # 1. 查找所有启用的任务
        print("1. 查找所有启用的任务")
        tasks = list(collection.find({"enabled": True}))
        print(f"   找到 {len(tasks)} 个启用的任务")
        
        # 2. 检查每个任务的bannerUrl
        print(f"\n2. 检查每个任务的bannerUrl")
        problem_tasks = []
        
        for task in tasks:
            task_id = task.get("_id")
            notify_id = task.get("notifyId")
            banner_url = task.get("bannerUrl", "")
            
            print(f"\n   任务 {task_id}:")
            print(f"     notifyId: {notify_id}")
            print(f"     bannerUrl: '{banner_url}'")
            print(f"     长度: {len(banner_url)}")
            
            # 检查是否有问题的bannerUrl
            if banner_url and len(banner_url) < 10:
                print(f"     ❌ 问题: bannerUrl太短，可能无效")
                problem_tasks.append(task_id)
            elif banner_url and not banner_url.startswith(('http', 'AgAC', 'BAA')):
                print(f"     ❌ 问题: bannerUrl格式不正确")
                problem_tasks.append(task_id)
            elif banner_url:
                print(f"     ⚠️ 有bannerUrl，可能导致sendPhoto调用")
            else:
                print(f"     ✅ 无bannerUrl，会发送文本消息")
        
        print(f"\n   有问题的任务数: {len(problem_tasks)}")
        
        # 3. 修复问题任务
        if problem_tasks:
            print(f"\n3. 修复问题任务")
            
            for task_id in problem_tasks:
                print(f"   修复任务 {task_id}...")
                
                # 清空bannerUrl
                update_result = collection.update_one(
                    {"_id": task_id},
                    {"$set": {"bannerUrl": ""}}
                )
                
                if update_result.modified_count > 0:
                    print(f"     ✅ 已清空bannerUrl")
                else:
                    print(f"     ❌ 修复失败")
        
        # 4. 验证修复结果
        print(f"\n4. 验证修复结果")
        updated_tasks = list(collection.find({"enabled": True}))
        
        for task in updated_tasks:
            task_id = task.get("_id")
            banner_url = task.get("bannerUrl", "")
            
            print(f"   任务 {task_id}: bannerUrl='{banner_url}'")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查修复失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_notify_config_images():
    """检查通知配置中的图片"""
    print(f"\n🖼️ 检查通知配置中的图片")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgNotify")
        
        # 查找ID为8和9的配置
        configs = list(collection.find({"id": {"$in": [8, 9]}}))
        
        for config in configs:
            config_id = config.get("id")
            image = config.get("image", "")
            
            print(f"   配置 {config_id}:")
            print(f"     image: '{image}'")
            print(f"     长度: {len(image)}")
            
            if image and len(image) < 10:
                print(f"     ❌ 图片ID太短，可能无效")
            elif not image:
                print(f"     ✅ 无图片，正常")
            else:
                print(f"     ⚠️ 有图片，需要验证格式")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 修复定时任务图片发送问题")
    print("=" * 60)
    
    # 1. 检查并修复定时任务的bannerUrl
    task_ok = check_and_fix_banner_url()
    
    # 2. 检查通知配置的图片
    config_ok = check_notify_config_images()
    
    print(f"\n" + "=" * 60)
    print(f"📊 检查修复结果:")
    print(f"   定时任务: {'✅ 修复完成' if task_ok else '❌ 修复失败'}")
    print(f"   通知配置: {'✅ 检查完成' if config_ok else '❌ 检查失败'}")
    
    if task_ok and config_ok:
        print(f"\n🎉 问题修复完成！")
        print(f"💡 修复内容:")
        print(f"   1. 清空了定时任务中无效的bannerUrl")
        print(f"   2. 现在定时任务会发送文本消息而不是图片消息")
        print(f"   3. 避免了'Wrong remote file identifier'错误")
        
        print(f"\n📅 下次测试:")
        print(f"   时间: 09:37 (约39分钟后)")
        print(f"   预期: 发送纯文本消息到Telegram频道")
        print(f"   频道: -1002316158105")
    else:
        print(f"\n⚠️ 修复过程中出现问题")

if __name__ == "__main__":
    main()
