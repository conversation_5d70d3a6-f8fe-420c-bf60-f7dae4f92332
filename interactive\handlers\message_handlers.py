"""
消息处理器
处理用户发送的各种类型消息
"""
import logging
from telegram import Update
from telegram.ext import ContextTypes, CommandHandler, MessageHandler, CallbackQueryHandler, filters
from pusher.menu.menu_builder import menu_builder

logger = logging.getLogger(__name__)


async def start_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /start 命令，根据聊天类型显示不同菜单"""
    user = update.effective_user
    chat = update.effective_chat

    # 检查是否有深度链接参数
    start_param = None
    if context.args and len(context.args) > 0:
        start_param = context.args[0]

    logger.info(f"User {user.id} ({user.username}) started the bot in {chat.type} chat {chat.id}, param: {start_param}")

    try:
        # 根据聊天类型发送不同菜单
        if chat.type == 'private':
            # 检查是否是从群组跳转过来的
            if start_param == "from_group":
                await send_private_menu_from_group(update, context)
            else:
                await send_private_menu(update, context)
        elif chat.type in ['group', 'supergroup']:
            await send_group_menu(update, context)
        else:
            # 频道或其他类型，使用私聊菜单
            await send_private_menu(update, context)

    except Exception as e:
        logger.error(f"Error in start command: {e}")
        # 降级到简单欢迎消息
        chat_type_text = "群组" if chat.type in ['group', 'supergroup'] else "私聊"
        welcome_message = f"""
👋 欢迎使用 Telegram Bot, {user.first_name}!

🤖 我是你的智能助手，在{chat_type_text}中可以帮助你：
• 回答问题
• 处理文件
• 提供信息查询
• 其他实用功能

💡 发送 /help 查看所有可用命令
        """
        await update.message.reply_text(welcome_message)


async def send_private_menu_from_group(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送从群组跳转过来的私聊菜单"""
    user = update.effective_user

    try:
        # 直接发送私聊菜单，不发送欢迎消息
        await send_private_menu(update, context)

        logger.info(f"从群组跳转的私聊菜单已发送给用户 {user.id}")

    except Exception as e:
        logger.error(f"发送从群组跳转的私聊菜单失败: {e}")
        # 降级到普通私聊菜单
        await send_private_menu(update, context)


async def send_private_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送私聊菜单"""
    try:
        user = update.effective_user
        bot_token = context.bot.token

        # 根据Bot配置加载私聊菜单模板
        menu_data = menu_builder.get_menu_for_context(bot_token, "private")

        if menu_data:
            # 个性化欢迎消息
            bot_info = menu_data.get("_bot_info", {})
            bot_name = bot_info.get("bot_name", "智能助手")

            welcome_text = f"👋 你好 {user.first_name}！\n\n"
            welcome_text += f"🤖 **{bot_name}** 为您服务\n\n"
            welcome_text += menu_data.get("description", "欢迎使用我们的私聊服务！")

            # 创建内联键盘
            keyboard = menu_builder.create_inline_keyboard(menu_data.get("buttons", []))

            # 检查是否有图片
            image_url = menu_data.get("image")

            # 发送菜单消息
            if update.message:
                if image_url:
                    # 发送带图片的菜单
                    await update.message.reply_photo(
                        photo=image_url,
                        caption=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
                else:
                    # 发送纯文字菜单
                    await update.message.reply_text(
                        text=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
            elif update.callback_query:
                if image_url:
                    # 回调查询时，先删除原消息，再发送新的带图片消息
                    await update.callback_query.delete_message()
                    await update.callback_query.message.reply_photo(
                        photo=image_url,
                        caption=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
                else:
                    # 编辑文字消息
                    await update.callback_query.edit_message_text(
                        text=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )

            logger.info(f"Private menu sent to user {user.id}")

        else:
            # 如果菜单模板加载失败，发送简单消息
            simple_message = f"👋 你好 {user.first_name}！\n\n欢迎使用我们的私聊服务！"
            if update.message:
                await update.message.reply_text(simple_message)
            elif update.callback_query:
                await update.callback_query.edit_message_text(simple_message)

    except Exception as e:
        logger.error(f"Error sending private menu: {e}")
        raise


async def send_group_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送群聊菜单"""
    try:
        user = update.effective_user
        chat = update.effective_chat
        bot_token = context.bot.token

        # 根据Bot和群组配置加载群聊菜单模板
        menu_data = menu_builder.get_menu_for_context(bot_token, "group", chat.id)

        if menu_data:
            # 群组欢迎消息
            bot_info = menu_data.get("_bot_info", {})
            group_info = menu_data.get("_group_info", {})
            bot_name = bot_info.get("bot_name", "智能助手")

            welcome_text = f"👋 你好 {user.first_name}！\n\n"
            welcome_text += f"📍 群组：{chat.title}\n"
            welcome_text += f"🤖 **{bot_name}** 为群组服务\n\n"

            # 如果有群组特殊配置，显示额外信息
            if group_info.get("description"):
                welcome_text += f"✨ {group_info['description']}\n\n"

            welcome_text += menu_data.get("description", "欢迎使用我们的群组服务！")

            # 创建内联键盘
            keyboard = menu_builder.create_inline_keyboard(menu_data.get("buttons", []))

            # 检查是否有图片
            image_url = menu_data.get("image")

            # 发送菜单消息
            if update.message:
                if image_url:
                    # 发送带图片的菜单
                    await update.message.reply_photo(
                        photo=image_url,
                        caption=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
                else:
                    # 发送纯文字菜单
                    await update.message.reply_text(
                        text=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
            elif update.callback_query:
                if image_url:
                    # 回调查询时，先删除原消息，再发送新的带图片消息
                    await update.callback_query.delete_message()
                    await update.callback_query.message.reply_photo(
                        photo=image_url,
                        caption=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
                else:
                    # 编辑文字消息
                    await update.callback_query.edit_message_text(
                        text=welcome_text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )

            logger.info(f"Group menu sent to user {user.id} in group {chat.id}")

        else:
            # 如果菜单模板加载失败，发送简单消息
            simple_message = f"👋 你好 {user.first_name}！\n\n欢迎在群组 {chat.title} 中使用我们的服务！"
            if update.message:
                await update.message.reply_text(simple_message)
            elif update.callback_query:
                await update.callback_query.edit_message_text(simple_message)

    except Exception as e:
        logger.error(f"Error sending group menu: {e}")
        raise


async def send_main_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """发送主菜单（兼容性保留，根据聊天类型自动选择）"""
    chat = update.effective_chat

    if chat.type == 'private':
        await send_private_menu(update, context)
    elif chat.type in ['group', 'supergroup']:
        await send_group_menu(update, context)
    else:
        await send_private_menu(update, context)


async def handle_menu_callback(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理菜单回调"""
    query = update.callback_query
    await query.answer()

    callback_data = query.data
    user = update.effective_user

    logger.info(f"User {user.id} clicked menu button: {callback_data}")

    try:
        if callback_data == "menu_services":
            await handle_services_menu(update, context)
        elif callback_data == "menu_contact":
            await handle_contact_menu(update, context)
        elif callback_data == "menu_help":
            await handle_help_menu(update, context)
        elif callback_data == "menu_settings":
            await handle_settings_menu(update, context)
        elif callback_data == "back_to_main":
            await send_main_menu(update, context)
        elif callback_data == "back_to_private":
            await send_private_menu(update, context)
        elif callback_data == "back_to_group":
            await send_group_menu(update, context)
        # 群聊特有功能
        elif callback_data == "group_stats":
            await handle_group_stats(update, context)
        elif callback_data == "group_members":
            await handle_group_members(update, context)
        elif callback_data == "group_notifications":
            await handle_group_notifications(update, context)
        elif callback_data == "group_activities":
            await handle_group_activities(update, context)
        elif callback_data == "group_admin_tools":
            await handle_group_admin_tools(update, context)
        elif callback_data == "group_rules":
            await handle_group_rules(update, context)
        elif callback_data == "group_help":
            await handle_group_help(update, context)
        elif callback_data == "group_settings":
            await handle_group_settings(update, context)
        # 私聊特有功能
        elif callback_data == "private_services":
            await handle_private_services(update, context)
        elif callback_data == "private_chat":
            await handle_private_chat(update, context)
        elif callback_data == "private_data":
            await handle_private_data(update, context)
        elif callback_data == "private_tasks":
            await handle_private_tasks(update, context)
        elif callback_data == "private_privacy":
            await handle_private_privacy(update, context)
        elif callback_data == "private_recommendations":
            await handle_private_recommendations(update, context)
        # 定制测试群组功能
        elif callback_data == "get_user_info":
            await handle_get_user_info(update, context)
        elif callback_data == "start_private_chat":
            await handle_start_private_chat(update, context)
        else:
            await query.edit_message_text(
                text=f"🔧 功能开发中...\n\n您点击了: {callback_data}",
                reply_markup=menu_builder.create_inline_keyboard([[
                    {"text": "🏠 返回主菜单", "callback_data": "back_to_main"}
                ]])
            )

    except Exception as e:
        logger.error(f"Error handling menu callback {callback_data}: {e}")
        await query.edit_message_text(
            text="❌ 处理请求时出现错误，请稍后重试。",
            reply_markup=menu_builder.create_inline_keyboard([[
                {"text": "🏠 返回主菜单", "callback_data": "back_to_main"}
            ]])
        )


async def handle_services_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理服务介绍菜单"""
    query = update.callback_query

    services_text = """
📋 **我们的服务**

🤖 **智能助手功能**
• 自动回复和问答
• 文件处理和转换
• 信息查询和搜索

📊 **数据服务**
• 实时数据推送
• 定制化报告
• 数据分析支持

🔧 **技术支持**
• 24/7 在线服务
• 专业技术团队
• 快速响应保障
    """

    keyboard = menu_builder.create_inline_keyboard([
        [{"text": "💬 联系客服", "callback_data": "menu_contact"}],
        [{"text": "🏠 返回主菜单", "callback_data": "back_to_main"}]
    ])

    await query.edit_message_text(
        text=services_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


# 群聊特有功能处理函数
async def handle_group_stats(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组统计"""
    query = update.callback_query
    chat = update.effective_chat

    stats_text = f"""
📊 **群组统计信息**

📍 **群组信息**
• 群组名称：{chat.title}
• 群组ID：{chat.id}
• 群组类型：{'超级群组' if chat.type == 'supergroup' else '普通群组'}

👥 **成员统计**
• 总成员数：正在统计...
• 活跃成员：正在统计...
• 管理员数：正在统计...

📈 **活动统计**
• 今日消息：正在统计...
• 本周活跃度：正在统计...
• 热门话题：正在分析...

💡 **提示**：完整统计功能需要管理员权限
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "👥 成员详情", "callback_data": "group_members"},
            {"text": "📈 活动分析", "callback_data": "group_activities"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=stats_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_members(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组成员管理"""
    query = update.callback_query

    members_text = """
👥 **成员管理**

🔧 **管理功能**
• 查看成员列表
• 成员权限管理
• 新成员欢迎
• 成员活跃度分析

⚠️ **权限说明**
部分功能需要Bot具有管理员权限

🛡️ **安全功能**
• 反垃圾信息
• 自动踢出违规用户
• 成员验证机制
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📋 成员列表", "callback_data": "group_member_list"},
            {"text": "🛡️ 安全设置", "callback_data": "group_security"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=members_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_notifications(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组通知设置"""
    query = update.callback_query

    notifications_text = """
🔔 **群组通知设置**

📢 **通知类型**
• 新成员加入：✅ 已开启
• 成员离开：❌ 已关闭
• 管理员操作：✅ 已开启
• 系统消息：✅ 已开启

⏰ **通知时间**
• 工作日：9:00 - 18:00
• 周末：10:00 - 16:00
• 静默时间：22:00 - 8:00

🎯 **智能通知**
• 重要消息优先
• 垃圾信息过滤
• 个性化推送
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "⚙️ 通知设置", "callback_data": "group_notification_settings"},
            {"text": "⏰ 时间设置", "callback_data": "group_time_settings"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=notifications_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_activities(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组活动"""
    query = update.callback_query

    activities_text = """
🎯 **群组活动**

🎉 **活动类型**
• 每日签到
• 话题讨论
• 投票活动
• 知识问答
• 游戏互动

📅 **活动安排**
• 周一：话题讨论
• 周三：知识问答
• 周五：投票活动
• 周末：自由互动

🏆 **积分系统**
• 参与活动获得积分
• 积分兑换奖励
• 排行榜展示
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🎉 创建活动", "callback_data": "group_create_activity"},
            {"text": "🏆 积分排行", "callback_data": "group_leaderboard"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=activities_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_admin_tools(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组管理工具"""
    query = update.callback_query
    user = update.effective_user

    admin_text = f"""
🛡️ **群组管理工具**

👤 **当前用户**：{user.first_name}
🔐 **权限检查**：正在验证...

⚙️ **管理功能**
• 消息管理（删除、置顶）
• 成员管理（踢出、禁言）
• 群组设置（标题、描述）
• 权限管理（管理员设置）

📊 **监控功能**
• 消息统计
• 成员活动监控
• 违规行为检测
• 自动化管理

⚠️ **注意**：需要相应管理员权限才能使用
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📝 消息管理", "callback_data": "group_message_management"},
            {"text": "👥 成员管理", "callback_data": "group_member_management"}
        ],
        [
            {"text": "📊 群组监控", "callback_data": "group_monitoring"},
            {"text": "⚙️ 权限设置", "callback_data": "group_permissions"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=admin_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_rules(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组规则"""
    query = update.callback_query
    chat = update.effective_chat

    rules_text = f"""
📋 **{chat.title} 群组规则**

✅ **基本规则**
1. 保持友善和尊重的交流
2. 禁止发送垃圾信息和广告
3. 不得发布违法违规内容
4. 尊重他人隐私和版权

🚫 **禁止行为**
• 恶意刷屏
• 人身攻击
• 政治敏感话题
• 商业广告推广

⚠️ **违规处理**
• 首次违规：警告
• 再次违规：临时禁言
• 严重违规：永久移除

📞 **申诉渠道**
如有异议可联系管理员申诉
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📞 联系管理员", "callback_data": "group_contact_admin"},
            {"text": "📖 详细规则", "url": "https://example.com/rules"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=rules_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_help(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组帮助"""
    query = update.callback_query

    help_text = """
❓ **群组帮助**

🤖 **Bot使用方法**
• 发送 /start 显示群组菜单
• @机器人名 + 消息 进行互动
• 回复Bot消息进行对话

🔧 **常用功能**
• 群组统计和分析
• 成员管理工具
• 活动组织功能
• 通知提醒服务

💡 **使用技巧**
• 管理员可使用更多功能
• 定期查看群组统计
• 参与群组活动获得积分

📞 **获取帮助**
• 群组内@管理员
• 私聊Bot获取详细帮助
• 查看在线文档
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📖 使用教程", "url": "https://example.com/group-tutorial"},
            {"text": "💬 私聊Bot", "url": f"https://t.me/{context.bot.username}"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=help_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_group_settings(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理群组设置"""
    query = update.callback_query

    settings_text = """
⚙️ **群组设置**

🔔 **通知设置**
• 新成员欢迎：✅ 已开启
• 离群通知：❌ 已关闭
• 管理员通知：✅ 已开启

🎯 **功能设置**
• 自动回复：✅ 已开启
• 关键词监控：✅ 已开启
• 垃圾信息过滤：✅ 已开启

🌐 **语言设置**
• 当前语言：中文
• 可选语言：中文、English

📊 **统计设置**
• 数据收集：✅ 已开启
• 隐私保护：✅ 已开启
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🔔 通知设置", "callback_data": "group_notification_config"},
            {"text": "🎯 功能设置", "callback_data": "group_feature_config"}
        ],
        [
            {"text": "🌐 语言设置", "callback_data": "group_language_config"},
            {"text": "📊 统计设置", "callback_data": "group_stats_config"}
        ],
        [{"text": "🏠 返回群组菜单", "callback_data": "back_to_group"}]
    ])

    await query.edit_message_text(
        text=settings_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_contact_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理联系我们菜单"""
    query = update.callback_query

    contact_text = """
💬 **联系我们**

📧 **邮箱**: <EMAIL>
📞 **电话**: +86 400-123-4567
🌐 **官网**: https://example.com

⏰ **服务时间**
周一至周五: 9:00 - 18:00
周末及节假日: 10:00 - 16:00

💡 **快速联系**
点击下方按钮直接联系我们的客服团队
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📧 发送邮件", "url": "mailto:<EMAIL>"},
            {"text": "🌐 访问官网", "url": "https://example.com"}
        ],
        [{"text": "🏠 返回主菜单", "callback_data": "back_to_main"}]
    ])

    await query.edit_message_text(
        text=contact_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_help_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理帮助中心菜单"""
    query = update.callback_query

    # 加载帮助中心模板
    help_data = menu_builder.load_menu_template("help_center")

    if help_data:
        help_text = help_data.get("description", "帮助中心")
        keyboard = menu_builder.create_inline_keyboard(help_data.get("buttons", []))
    else:
        help_text = """
❓ **帮助中心**

📖 **常见问题**
• 如何使用bot功能？
• 如何获取技术支持？
• 如何联系客服？

🔧 **使用指南**
• 发送 /start 显示主菜单
• 发送 /help 查看帮助信息
• 点击菜单按钮进行操作

💡 **小贴士**
如果遇到问题，可以随时联系我们的客服团队
        """
        keyboard = menu_builder.create_inline_keyboard([
            [{"text": "💬 联系客服", "callback_data": "menu_contact"}],
            [{"text": "🏠 返回主菜单", "callback_data": "back_to_main"}]
        ])

    await query.edit_message_text(
        text=help_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_settings_menu(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理设置菜单"""
    query = update.callback_query

    settings_text = """
⚙️ **设置**

🔔 **通知设置**
• 消息推送: 已开启
• 邮件通知: 已关闭
• 系统提醒: 已开启

🌐 **语言设置**
• 当前语言: 中文
• 可选语言: 中文、English

👤 **账户信息**
• 用户ID: {user_id}
• 注册时间: 今天
• 会员等级: 普通用户
    """.format(user_id=update.effective_user.id)

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🔔 通知设置", "callback_data": "settings_notifications"},
            {"text": "🌐 语言设置", "callback_data": "settings_language"}
        ],
        [{"text": "🏠 返回主菜单", "callback_data": "back_to_main"}]
    ])

    await query.edit_message_text(
        text=settings_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def help_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /help 命令"""
    help_message = """
📋 可用命令：

/start - 开始使用机器人
/help - 显示帮助信息
/status - 查看机器人状态
/echo <消息> - 回显消息

💬 你也可以直接发送消息与我对话！
    """
    
    await update.message.reply_text(help_message)


async def status_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /status 命令"""
    status_message = """
🟢 机器人状态：正常运行

📊 系统信息：
• 版本：1.0.0
• 运行模式：交互模式
• 连接状态：已连接

⏰ 最后更新：刚刚
    """
    
    await update.message.reply_text(status_message)


async def echo_command(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理 /echo 命令"""
    if context.args:
        echo_text = ' '.join(context.args)
        await update.message.reply_text(f"🔄 回显：{echo_text}")
    else:
        await update.message.reply_text("请在 /echo 后面添加要回显的内容")


async def handle_text_message(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理普通文本消息"""
    user = update.effective_user
    message_text = update.message.text
    
    logger.info(f"Received message from {user.id} ({user.username}): {message_text}")
    
    # 简单的消息处理逻辑
    if "你好" in message_text or "hello" in message_text.lower():
        response = f"你好，{user.first_name}！很高兴见到你！"
    elif "时间" in message_text or "time" in message_text.lower():
        from datetime import datetime
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        response = f"⏰ 当前时间：{current_time}"
    elif "谢谢" in message_text or "thank" in message_text.lower():
        response = "不客气！有什么其他需要帮助的吗？"
    else:
        response = f"收到你的消息：{message_text}\n\n💡 我正在学习如何更好地回应，请发送 /help 查看可用命令。"
    
    await update.message.reply_text(response)


async def handle_photo(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理图片消息"""
    user = update.effective_user
    logger.info(f"Received photo from {user.id} ({user.username})")
    
    response = "📸 收到你的图片！\n\n目前我还在学习如何处理图片，但我已经收到了。"
    if update.message.caption:
        response += f"\n\n图片说明：{update.message.caption}"
    
    await update.message.reply_text(response)


async def handle_document(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理文档消息"""
    user = update.effective_user
    document = update.message.document
    logger.info(f"Received document from {user.id} ({user.username}): {document.file_name}")
    
    response = f"📄 收到文档：{document.file_name}\n\n文件大小：{document.file_size} bytes"
    if update.message.caption:
        response += f"\n说明：{update.message.caption}"
    
    await update.message.reply_text(response)


async def handle_error(update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理错误"""
    logger.error(f"Exception while handling an update: {context.error}")


# 私聊特有功能处理函数
async def handle_private_services(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理个人服务"""
    query = update.callback_query
    user = update.effective_user

    services_text = f"""
📋 **个人服务中心**

👤 **用户信息**
• 用户名：{user.first_name}
• 用户ID：{user.id}
• 会员等级：普通用户

🎯 **专属服务**
• 个性化推荐
• 一对一咨询
• 定制化解决方案
• 优先技术支持

📊 **数据服务**
• 个人数据分析
• 使用习惯统计
• 服务使用报告
• 个性化建议

🔐 **隐私保护**
• 数据加密存储
• 隐私设置控制
• 安全访问验证
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🎯 个性推荐", "callback_data": "private_recommendations"},
            {"text": "💬 一对一咨询", "callback_data": "private_consultation"}
        ],
        [
            {"text": "📊 数据分析", "callback_data": "private_data"},
            {"text": "🔐 隐私设置", "callback_data": "private_privacy"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=services_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_private_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理智能问答"""
    query = update.callback_query

    chat_text = """
💬 **智能问答助手**

🤖 **AI能力**
• 自然语言理解
• 智能对话交流
• 知识问答解答
• 多轮对话记忆

🎯 **专业领域**
• 技术问题解答
• 生活常识咨询
• 学习辅导支持
• 工作效率提升

💡 **使用方法**
• 直接发送问题给我
• 支持文字、语音输入
• 可以连续对话交流
• 随时切换话题

🔧 **高级功能**
• 上下文理解
• 个性化回答
• 学习用户偏好
• 持续优化改进
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🎯 开始对话", "callback_data": "start_ai_chat"},
            {"text": "📚 知识库", "callback_data": "knowledge_base"}
        ],
        [
            {"text": "⚙️ 对话设置", "callback_data": "chat_settings"},
            {"text": "📊 对话历史", "callback_data": "chat_history"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=chat_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_private_data(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理数据查询"""
    query = update.callback_query
    user = update.effective_user

    data_text = f"""
📊 **个人数据中心**

👤 **基本信息**
• 用户ID：{user.id}
• 注册时间：今天
• 最后活跃：刚刚
• 使用天数：1天

📈 **使用统计**
• 总消息数：正在统计...
• 功能使用次数：正在统计...
• 平均响应时间：< 1秒
• 满意度评分：暂无评分

🎯 **偏好分析**
• 常用功能：智能问答
• 活跃时间：全天
• 交互方式：文字为主
• 兴趣标签：正在分析...

📋 **数据导出**
• 支持导出个人数据
• 多种格式可选
• 隐私安全保护
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "📈 详细统计", "callback_data": "detailed_stats"},
            {"text": "🎯 偏好设置", "callback_data": "preference_settings"}
        ],
        [
            {"text": "📋 数据导出", "callback_data": "data_export"},
            {"text": "🗑️ 数据清理", "callback_data": "data_cleanup"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=data_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_private_tasks(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理任务管理"""
    query = update.callback_query

    tasks_text = """
📝 **个人任务管理**

✅ **任务功能**
• 创建待办事项
• 设置提醒时间
• 任务优先级管理
• 完成状态跟踪

📅 **日程安排**
• 日历集成
• 重要事件提醒
• 定期任务设置
• 时间管理建议

🎯 **目标管理**
• 长期目标设定
• 进度跟踪分析
• 里程碑记录
• 成就系统

📊 **效率分析**
• 任务完成率统计
• 时间分配分析
• 效率提升建议
• 个人报告生成
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "➕ 创建任务", "callback_data": "create_task"},
            {"text": "📋 任务列表", "callback_data": "task_list"}
        ],
        [
            {"text": "📅 日程安排", "callback_data": "schedule_management"},
            {"text": "🎯 目标设定", "callback_data": "goal_setting"}
        ],
        [
            {"text": "📊 效率报告", "callback_data": "efficiency_report"},
            {"text": "⚙️ 任务设置", "callback_data": "task_settings"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=tasks_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_private_privacy(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理隐私设置"""
    query = update.callback_query

    privacy_text = """
🔐 **隐私安全设置**

🛡️ **数据保护**
• 个人信息加密：✅ 已开启
• 聊天记录保护：✅ 已开启
• 数据本地存储：✅ 已开启
• 第三方分享：❌ 已禁用

🔒 **访问控制**
• 双重验证：❌ 未开启
• 设备授权：✅ 已开启
• 异常登录提醒：✅ 已开启
• 会话管理：✅ 已开启

📊 **数据使用**
• 功能改进：✅ 允许
• 个性化服务：✅ 允许
• 数据分析：✅ 允许
• 营销推广：❌ 已禁用

🗑️ **数据管理**
• 自动清理：30天
• 手动删除：随时可用
• 数据导出：支持
• 账户注销：支持
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🔒 安全设置", "callback_data": "security_settings"},
            {"text": "📊 数据权限", "callback_data": "data_permissions"}
        ],
        [
            {"text": "🗑️ 数据清理", "callback_data": "data_cleanup"},
            {"text": "📋 隐私政策", "url": "https://example.com/privacy"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=privacy_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


async def handle_private_recommendations(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """处理个性推荐"""
    query = update.callback_query
    user = update.effective_user

    recommendations_text = f"""
🎯 **个性化推荐**

👤 **用户画像**
• 姓名：{user.first_name}
• 活跃度：新用户
• 兴趣标签：正在学习中...
• 使用偏好：智能助手

💡 **推荐内容**
• 热门功能：智能问答
• 实用工具：任务管理
• 学习资源：使用教程
• 社区活动：新手指南

🔥 **今日推荐**
• 📚 探索知识库功能
• 📝 尝试任务管理工具
• 💬 体验智能对话
• ⚙️ 完善个人设置

📈 **推荐算法**
• 基于使用行为
• 结合用户反馈
• 持续学习优化
• 个性化程度提升
    """

    keyboard = menu_builder.create_inline_keyboard([
        [
            {"text": "🔥 查看推荐", "callback_data": "view_recommendations"},
            {"text": "⚙️ 推荐设置", "callback_data": "recommendation_settings"}
        ],
        [
            {"text": "👍 反馈喜好", "callback_data": "feedback_preferences"},
            {"text": "📊 推荐历史", "callback_data": "recommendation_history"}
        ],
        [{"text": "🏠 返回私聊菜单", "callback_data": "back_to_private"}]
    ])

    await query.edit_message_text(
        text=recommendations_text,
        reply_markup=keyboard,
        parse_mode='Markdown'
    )


# 定制测试群组功能处理函数
async def handle_get_user_info(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """获取用户信息并显示"""
    query = update.callback_query
    user = update.effective_user
    chat = update.effective_chat

    try:
        # 获取用户头像信息
        user_photos = None
        try:
            photos = await context.bot.get_user_profile_photos(user.id, limit=1)
            if photos.total_count > 0:
                user_photos = photos.photos[0][-1].file_id  # 获取最大尺寸的头像
        except Exception as e:
            logger.warning(f"无法获取用户头像: {e}")

        # 收集完整的用户授权信息
        user_info = {
            "user_id": user.id,
            "first_name": user.first_name,
            "last_name": user.last_name,
            "username": user.username,
            "language_code": user.language_code,
            "is_bot": user.is_bot,
            "is_premium": getattr(user, 'is_premium', None),
            "is_verified": getattr(user, 'is_verified', None),
            "is_scam": getattr(user, 'is_scam', None),
            "is_fake": getattr(user, 'is_fake', None),
            "added_to_attachment_menu": getattr(user, 'added_to_attachment_menu', None),
            "can_join_groups": getattr(user, 'can_join_groups', None),
            "can_read_all_group_messages": getattr(user, 'can_read_all_group_messages', None),
            "supports_inline_queries": getattr(user, 'supports_inline_queries', None),
            "profile_photo_file_id": user_photos,
        }

        # 收集聊天信息
        chat_info = {
            "chat_id": chat.id,
            "chat_type": chat.type,
            "chat_title": chat.title,
            "chat_username": getattr(chat, 'username', None),
            "chat_description": getattr(chat, 'description', None),
        }

        # 收集会话和技术信息
        session_info = {
            "update_id": update.update_id,
            "message_id": query.message.message_id if query.message else None,
            "callback_query_id": query.id,
            "timestamp": query.message.date if query.message else None,
            "bot_username": context.bot.username,
            "bot_id": context.bot.id,
        }

        # 构建完整的授权登录信息
        auth_login_info = {
            "user": user_info,
            "chat": chat_info,
            "session": session_info,
            "auth_timestamp": query.message.date.isoformat() if query.message and query.message.date else None,
            "auth_source": "telegram_bot_interaction",
            "interaction_type": "callback_query",
        }

        # 在日志中打印完整的授权登录信息
        logger.info("=" * 80)
        logger.info("🔐 TELEGRAM 授权登录信息")
        logger.info("=" * 80)
        logger.info(f"👤 用户ID: {user_info['user_id']}")
        logger.info(f"📝 姓名: {user_info['first_name']} {user_info['last_name'] or ''}")
        logger.info(f"🏷️ 用户名: @{user_info['username'] or '未设置'}")
        logger.info(f"🌍 语言: {user_info['language_code'] or '未知'}")
        logger.info(f"⭐ 高级用户: {user_info['is_premium']}")
        logger.info(f"✅ 已验证: {user_info['is_verified']}")
        logger.info(f"🖼️ 头像ID: {user_info['profile_photo_file_id'] or '无头像'}")
        logger.info(f"💬 聊天ID: {chat_info['chat_id']}")
        logger.info(f"📱 聊天类型: {chat_info['chat_type']}")
        logger.info(f"🕐 授权时间: {auth_login_info['auth_timestamp']}")
        logger.info(f"🔗 会话ID: {session_info['callback_query_id']}")
        logger.info("=" * 80)

        # 打印JSON格式的完整信息（用于系统集成）
        import json
        logger.info("📋 完整授权信息 (JSON):")
        logger.info(json.dumps(auth_login_info, ensure_ascii=False, indent=2, default=str))

        # 格式化信息文本
        info_text = f"""🔐 Telegram 授权登录信息

👤 用户身份
• 用户ID: {user_info['user_id']}
• 姓名: {user_info['first_name']} {user_info['last_name'] or ''}
• 用户名: @{user_info['username'] or '未设置'}
• 语言: {user_info['language_code'] or '未知'}

⭐ 账户状态
• 高级用户: {'是' if user_info['is_premium'] else '否' if user_info['is_premium'] is not None else '未知'}
• 已验证: {'是' if user_info['is_verified'] else '否' if user_info['is_verified'] is not None else '未知'}
• 头像: {'有' if user_info['profile_photo_file_id'] else '无'}

💬 会话信息
• 聊天ID: {chat_info['chat_id']}
• 聊天类型: {chat_info['chat_type']}
• 群组名称: {chat_info['chat_title'] or '私聊'}

🔗 授权会话
• 会话ID: {session_info['callback_query_id'][:8]}...
• 授权时间: {auth_login_info['auth_timestamp'][:19] if auth_login_info['auth_timestamp'] else '刚刚'}
• 交互类型: {session_info['interaction_type']}

✅ 此信息可用于系统登录验证"""

        # 创建返回按钮
        keyboard = menu_builder.create_inline_keyboard([
            [{"text": "🔄 刷新信息", "callback_data": "get_user_info"}],
            [{"text": "🏠 返回菜单", "callback_data": "back_to_group"}]
        ])

        # 发送用户信息
        await query.edit_message_text(
            text=info_text,
            reply_markup=keyboard
        )

        # 记录日志
        logger.info(f"用户信息获取: User {user.id} (@{user.username}) in chat {chat.id}")

    except Exception as e:
        logger.error(f"获取用户信息失败: {e}")

        error_text = f"❌ 获取用户信息失败\n\n错误信息: {str(e)}\n\n请稍后重试或联系管理员。"

        keyboard = menu_builder.create_inline_keyboard([
            [{"text": "🔄 重试", "callback_data": "get_user_info"}],
            [{"text": "🏠 返回菜单", "callback_data": "back_to_group"}]
        ])

        await query.edit_message_text(
            text=error_text,
            reply_markup=keyboard
        )


async def handle_start_private_chat(update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
    """跳转到私聊页面"""
    query = update.callback_query
    user = update.effective_user
    bot_username = context.bot.username

    try:
        # 创建私聊链接
        private_chat_url = f"https://t.me/{bot_username}"

        # 显示跳转提示
        jump_text = f"💬 即将跳转到私聊页面\n\n👋 你好 {user.first_name}！\n\n点击下方按钮跳转到私聊对话"

        keyboard = menu_builder.create_inline_keyboard([
            [{"text": "💬 开始私聊", "url": private_chat_url}],
            [{"text": "🏠 返回菜单", "callback_data": "back_to_group"}]
        ])

        await query.edit_message_text(
            text=jump_text,
            reply_markup=keyboard
        )

        # 记录日志
        logger.info(f"私聊跳转: User {user.id} (@{user.username}) - private chat link provided")

    except Exception as e:
        logger.error(f"私聊跳转失败: {e}")

        # 显示失败提示
        error_text = f"❌ 私聊跳转失败\n\n请手动搜索 @{context.bot.username} 开始私聊"

        keyboard = menu_builder.create_inline_keyboard([
            [{"text": "🔄 重试", "callback_data": "start_private_chat"}],
            [{"text": "🏠 返回菜单", "callback_data": "back_to_group"}]
        ])

        await query.edit_message_text(
            text=error_text,
            reply_markup=keyboard
        )

        await query.edit_message_text(
            text=error_text,
            reply_markup=keyboard,
            parse_mode='Markdown'
        )


# 导出处理器列表
def get_handlers():
    """获取所有消息处理器"""
    return [
        CommandHandler("start", start_command),
        CommandHandler("help", help_command),
        CommandHandler("status", status_command),
        CommandHandler("echo", echo_command),
        CallbackQueryHandler(handle_menu_callback),  # 添加回调查询处理器
        MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text_message),
        MessageHandler(filters.PHOTO, handle_photo),
        MessageHandler(filters.Document.ALL, handle_document),
    ]
