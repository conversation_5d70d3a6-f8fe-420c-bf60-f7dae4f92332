#!/usr/bin/env python3
"""
Telegram Bot 交互模块主程序
用于接收和处理用户消息
"""
import asyncio
import logging
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from telegram.ext import Application
from config.settings import settings
from common.utils import setup_logging
from common.bot_client import bot_client
from interactive.handlers.message_handlers import get_handlers, handle_error
from interactive.utils.conversation import conversation_manager

logger = logging.getLogger(__name__)


class InteractiveBot:
    """交互式Bot主类"""
    
    def __init__(self):
        self.application: Application = None
        self.is_running = False
    
    async def initialize(self):
        """初始化Bot"""
        logger.info("Initializing Interactive Bot...")
        
        # 获取Application实例
        self.application = bot_client.get_application()
        
        # 添加消息处理器
        handlers = get_handlers()
        for handler in handlers:
            self.application.add_handler(handler)
        
        # 添加错误处理器
        self.application.add_error_handler(handle_error)
        
        logger.info("Bot initialized successfully")
    
    async def start(self):
        """启动Bot"""
        if not self.application:
            await self.initialize()
        
        logger.info("Starting Interactive Bot...")
        
        try:
            # 初始化应用
            await self.application.initialize()
            
            if settings.webhook_url:
                # 使用Webhook模式（生产环境）
                logger.info(f"Starting webhook on {settings.webhook_url}")
                await self.application.start()
                await self.application.bot.set_webhook(
                    url=settings.webhook_url,
                    drop_pending_updates=True
                )
                
                # 启动webhook服务器
                await self.application.updater.start_webhook(
                    listen="0.0.0.0",
                    port=settings.webhook_port,
                    url_path="webhook",
                    webhook_url=settings.webhook_url
                )
                
                self.is_running = True
                logger.info("Webhook started successfully")
                
                # 保持运行
                while self.is_running:
                    await asyncio.sleep(1)
                    
            else:
                # 使用轮询模式（开发环境）
                logger.info("Starting polling mode...")
                await self.application.start()
                
                # 删除可能存在的webhook
                await self.application.bot.delete_webhook(drop_pending_updates=True)
                
                # 开始轮询
                self.is_running = True
                await self.application.updater.start_polling(
                    drop_pending_updates=True,
                    allowed_updates=None
                )
                
                logger.info("Polling started successfully")
                
                # 保持运行
                while self.is_running:
                    await asyncio.sleep(1)
                    
        except Exception as e:
            logger.error(f"Error starting bot: {e}")
            raise
    
    async def stop(self):
        """停止Bot"""
        logger.info("Stopping Interactive Bot...")
        self.is_running = False
        
        if self.application:
            try:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                logger.info("Bot stopped successfully")
            except Exception as e:
                logger.error(f"Error stopping bot: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger.info("Starting Telegram Bot Interactive Module")
    
    # 检查配置
    try:
        token = settings.bot_token
        logger.info("Bot token loaded successfully")
        if settings.use_proxy:
            logger.info(f"Proxy enabled: {settings.proxy_url}")
        else:
            logger.info("Proxy disabled")
    except Exception as e:
        logger.error(f"Configuration error: {e}")
        return 1
    
    # 创建并启动Bot
    bot = InteractiveBot()
    bot.setup_signal_handlers()
    
    try:
        await bot.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return 1
    finally:
        await bot.stop()
        # 清理旧对话数据
        cleaned = conversation_manager.cleanup_old_conversations()
        if cleaned > 0:
            logger.info(f"Cleaned up {cleaned} old conversations")
    
    logger.info("Interactive Bot shutdown complete")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        logger.info("Program interrupted by user")
        sys.exit(0)
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)
