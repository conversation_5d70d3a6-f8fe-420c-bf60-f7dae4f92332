"""
对话管理工具
用于管理用户对话状态和上下文
"""
import json
import logging
from typing import Dict, Any, Optional
from pathlib import Path
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


class ConversationManager:
    """对话管理器"""
    
    def __init__(self, storage_path: str = "data/conversations.json"):
        self.storage_path = Path(storage_path)
        self.storage_path.parent.mkdir(parents=True, exist_ok=True)
        self.conversations: Dict[int, Dict[str, Any]] = {}
        self.load_conversations()
    
    def load_conversations(self):
        """加载对话数据"""
        try:
            if self.storage_path.exists():
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    # 转换字符串键为整数键
                    self.conversations = {int(k): v for k, v in data.items()}
        except Exception as e:
            logger.error(f"Failed to load conversations: {e}")
            self.conversations = {}
    
    def save_conversations(self):
        """保存对话数据"""
        try:
            # 转换整数键为字符串键以便JSON序列化
            data = {str(k): v for k, v in self.conversations.items()}
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save conversations: {e}")
    
    def get_user_context(self, user_id: int) -> Dict[str, Any]:
        """获取用户对话上下文"""
        if user_id not in self.conversations:
            self.conversations[user_id] = {
                'first_seen': datetime.now().isoformat(),
                'last_activity': datetime.now().isoformat(),
                'message_count': 0,
                'state': 'idle',
                'data': {}
            }
        
        return self.conversations[user_id]
    
    def update_user_activity(self, user_id: int, message_text: str = ""):
        """更新用户活动"""
        context = self.get_user_context(user_id)
        context['last_activity'] = datetime.now().isoformat()
        context['message_count'] += 1
        
        # 保存最近的消息（可选）
        if 'recent_messages' not in context['data']:
            context['data']['recent_messages'] = []
        
        context['data']['recent_messages'].append({
            'timestamp': datetime.now().isoformat(),
            'text': message_text[:100]  # 只保存前100个字符
        })
        
        # 只保留最近10条消息
        context['data']['recent_messages'] = context['data']['recent_messages'][-10:]
        
        self.save_conversations()
    
    def set_user_state(self, user_id: int, state: str, data: Optional[Dict[str, Any]] = None):
        """设置用户状态"""
        context = self.get_user_context(user_id)
        context['state'] = state
        if data:
            context['data'].update(data)
        self.save_conversations()
    
    def get_user_state(self, user_id: int) -> str:
        """获取用户状态"""
        return self.get_user_context(user_id).get('state', 'idle')
    
    def get_user_data(self, user_id: int, key: str, default: Any = None) -> Any:
        """获取用户数据"""
        context = self.get_user_context(user_id)
        return context['data'].get(key, default)
    
    def set_user_data(self, user_id: int, key: str, value: Any):
        """设置用户数据"""
        context = self.get_user_context(user_id)
        context['data'][key] = value
        self.save_conversations()
    
    def cleanup_old_conversations(self, days: int = 30):
        """清理旧的对话数据"""
        cutoff_date = datetime.now() - timedelta(days=days)
        to_remove = []
        
        for user_id, context in self.conversations.items():
            try:
                last_activity = datetime.fromisoformat(context['last_activity'])
                if last_activity < cutoff_date:
                    to_remove.append(user_id)
            except (ValueError, KeyError):
                # 如果日期格式有问题，也删除
                to_remove.append(user_id)
        
        for user_id in to_remove:
            del self.conversations[user_id]
            logger.info(f"Cleaned up conversation for user {user_id}")
        
        if to_remove:
            self.save_conversations()
        
        return len(to_remove)


# 全局对话管理器实例
conversation_manager = ConversationManager()
