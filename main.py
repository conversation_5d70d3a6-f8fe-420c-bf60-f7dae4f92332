#!/usr/bin/env python3
"""
Telegram Bot 服务主入口
同时启动 FastAPI 服务和定时任务调度器
"""
import asyncio
import uvicorn
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from api.main import create_app
from scheduler.main import scheduler_manager
from database.connection import init_database, close_database

import logging
logger = logging.getLogger(__name__)


class BotService:
    """Bot服务主类"""
    
    def __init__(self):
        self.app = None
        self.scheduler = None
        
    async def start_api_server(self):
        """启动FastAPI服务器"""
        try:
            logger.info("启动FastAPI服务器...")
            self.app = create_app()
            
            config = uvicorn.Config(
                app=self.app,
                host=settings.api_host,
                port=settings.api_port,
                log_level=settings.log_level.lower()
            )
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"FastAPI服务器启动失败: {e}")
            raise
    
    async def start_scheduler(self):
        """启动定时任务调度器"""
        try:
            logger.info("启动定时任务调度器...")
            self.scheduler = scheduler_manager
            await self.scheduler.start()
            
        except Exception as e:
            logger.error(f"定时任务调度器启动失败: {e}")
            raise
    
    async def start(self):
        """启动所有服务"""
        logger.info("启动Telegram Bot服务...")

        # 先初始化数据库（暂时注释，用于测试）
        # await init_database()

        # 暂时只启动API服务器进行测试
        await self.start_api_server()

        # 完整版本：并发启动API服务器和定时任务调度器
        # await asyncio.gather(
        #     self.start_api_server(),
        #     self.start_scheduler(),
        #     return_exceptions=True
        # )
    
    async def stop(self):
        """停止所有服务"""
        logger.info("停止Telegram Bot服务...")

        if self.scheduler:
            await self.scheduler.stop()

        # 关闭数据库连接（暂时注释，用于测试）
        # await close_database()

        # FastAPI服务器会在主进程结束时自动停止


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    logger.info("=" * 50)
    logger.info("Telegram Bot 服务启动")
    logger.info("=" * 50)
    logger.info(f"API服务地址: http://{settings.api_host}:{settings.api_port}")
    logger.info(f"定时任务: 启用")
    logger.info(f"数据库: {settings.database_url}")
    logger.info("=" * 50)
    
    service = BotService()
    
    try:
        await service.start()
    except KeyboardInterrupt:
        logger.info("收到停止信号...")
        await service.stop()
    except Exception as e:
        logger.error(f"服务运行错误: {e}")
        await service.stop()
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"服务启动失败: {e}")
        sys.exit(1)
