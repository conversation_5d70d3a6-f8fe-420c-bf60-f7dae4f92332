#!/usr/bin/env python3
"""
API日志中间件
"""
import time
import json
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from config.logging_config import api_logger

class APILoggingMiddleware(BaseHTTPMiddleware):
    """API日志中间件"""

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求和响应"""
        # 记录请求开始时间
        start_time = time.time()

        # 记录请求信息
        await self.log_request(request)

        # 处理请求
        try:
            response = await call_next(request)

            # 计算处理时间
            process_time = time.time() - start_time

            # 记录响应信息
            await self.log_response(request, response, process_time)

            return response

        except Exception as e:
            # 记录异常
            process_time = time.time() - start_time
            api_logger.error(f"API异常: {str(e)}")

            # 记录异常响应
            await self.log_error_response(request, e, process_time)

            raise
    
    async def log_request(self, request: Request):
        """记录请求信息"""
        try:
            # 获取请求体
            body = await request.body()
            
            # 构建请求日志
            request_info = {
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "headers": dict(request.headers),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown")
            }
            
            # 如果有请求体，尝试解析JSON
            if body:
                try:
                    request_info["body"] = json.loads(body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    request_info["body"] = body.decode('utf-8', errors='ignore')
            
            api_logger.info(f"📥 API请求: {json.dumps(request_info, ensure_ascii=False)}")
            
        except Exception as e:
            api_logger.error(f"记录请求日志失败: {str(e)}")
    
    async def log_response(self, request: Request, response: Response, process_time: float):
        """记录响应信息"""
        try:
            # 构建响应日志
            response_info = {
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2)
            }

            # 根据状态码选择日志级别
            if response.status_code >= 500:
                api_logger.error(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")
            elif response.status_code >= 400:
                api_logger.warning(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")
            else:
                api_logger.info(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")

        except Exception as e:
            api_logger.error(f"记录响应日志失败: {str(e)}")

    async def log_error_response(self, request: Request, error: Exception, process_time: float):
        """记录异常响应信息"""
        try:
            error_info = {
                "method": request.method,
                "path": request.url.path,
                "status_code": 500,
                "process_time_ms": round(process_time * 1000, 2),
                "error": str(error)
            }

            api_logger.error(f"📤 API异常响应: {json.dumps(error_info, ensure_ascii=False)}")

        except Exception as e:
            api_logger.error(f"记录异常响应日志失败: {str(e)}")

def add_api_logging_middleware(app):
    """添加API日志中间件到FastAPI应用"""
    app.add_middleware(APILoggingMiddleware)
