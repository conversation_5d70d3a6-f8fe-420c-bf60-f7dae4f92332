#!/usr/bin/env python3
"""
API日志中间件
"""
import time
import json
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import J<PERSON>NResponse
from config.logging_config import api_logger

class APILoggingMiddleware:
    """API日志中间件"""
    
    def __init__(self, app):
        self.app = app
    
    async def __call__(self, scope, receive, send):
        if scope["type"] != "http":
            await self.app(scope, receive, send)
            return
        
        # 创建请求对象
        request = Request(scope, receive)
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 记录请求信息
        await self.log_request(request)
        
        # 捕获响应
        response_body = b""
        response_status = 200
        response_headers = {}
        
        async def send_wrapper(message):
            nonlocal response_body, response_status, response_headers
            
            if message["type"] == "http.response.start":
                response_status = message["status"]
                response_headers = dict(message.get("headers", []))
            elif message["type"] == "http.response.body":
                response_body += message.get("body", b"")
            
            await send(message)
        
        # 处理请求
        try:
            await self.app(scope, receive, send_wrapper)
        except Exception as e:
            # 记录异常
            api_logger.error(f"API异常: {str(e)}")
            raise
        finally:
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            await self.log_response(request, response_status, response_body, process_time)
    
    async def log_request(self, request: Request):
        """记录请求信息"""
        try:
            # 获取请求体
            body = await request.body()
            
            # 构建请求日志
            request_info = {
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "headers": dict(request.headers),
                "client_ip": request.client.host if request.client else "unknown",
                "user_agent": request.headers.get("user-agent", "unknown")
            }
            
            # 如果有请求体，尝试解析JSON
            if body:
                try:
                    request_info["body"] = json.loads(body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    request_info["body"] = body.decode('utf-8', errors='ignore')
            
            api_logger.info(f"📥 API请求: {json.dumps(request_info, ensure_ascii=False)}")
            
        except Exception as e:
            api_logger.error(f"记录请求日志失败: {str(e)}")
    
    async def log_response(self, request: Request, status_code: int, body: bytes, process_time: float):
        """记录响应信息"""
        try:
            # 构建响应日志
            response_info = {
                "method": request.method,
                "path": request.url.path,
                "status_code": status_code,
                "process_time_ms": round(process_time * 1000, 2)
            }
            
            # 如果有响应体，尝试解析JSON
            if body:
                try:
                    response_info["body"] = json.loads(body.decode('utf-8'))
                except (json.JSONDecodeError, UnicodeDecodeError):
                    response_info["body"] = body.decode('utf-8', errors='ignore')
            
            # 根据状态码选择日志级别
            if status_code >= 500:
                api_logger.error(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")
            elif status_code >= 400:
                api_logger.warning(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")
            else:
                api_logger.info(f"📤 API响应: {json.dumps(response_info, ensure_ascii=False)}")
                
        except Exception as e:
            api_logger.error(f"记录响应日志失败: {str(e)}")

def add_api_logging_middleware(app):
    """添加API日志中间件到FastAPI应用"""
    app.middleware("http")(APILoggingMiddleware(app))
