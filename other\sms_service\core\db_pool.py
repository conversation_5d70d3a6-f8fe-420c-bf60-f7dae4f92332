import aiomysql

_db_pool = None

# ************
async def init_mysql_pool():
    global _db_pool
    _db_pool = await aiomysql.create_pool(
        host='************',
        port=3306,
        user='wgsaas',
        password='GGWStaCYX6ZfXica',
        db='wgsaas',
        autocommit=True,
        minsize=1,
        maxsize=10,
        charset='utf8mb4'
    )

def get_db_pool():
    if _db_pool is None:
        raise Exception("db_pool not initialized")
    return _db_pool
