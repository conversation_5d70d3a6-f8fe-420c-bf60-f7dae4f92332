import logging
from logging.handlers import TimedRotatingFileHandler
import os

LOG_DIR = "logs"
DEFAULT_LOG_FILE = "sms_service.log"

if not os.path.exists(LOG_DIR):
    os.makedirs(LOG_DIR)

# 日志格式
formatter = logging.Formatter('%(asctime)s [%(levelname)s] %(message)s')


def _create_handler(filename: str):
    log_path = os.path.join(LOG_DIR, filename)
    file_handler = TimedRotatingFileHandler(
        log_path, when="midnight", interval=1, backupCount=7, encoding="utf-8"
    )
    file_handler.setFormatter(formatter)

    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    return file_handler, console_handler


def get_logger(name: str, filename: str = DEFAULT_LOG_FILE) -> logging.Logger:
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)

    if not logger.hasHandlers():
        file_handler, console_handler = _create_handler(filename)
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

    return logger


# 默认 logger（供全局使用）
logger = get_logger("sms_service", DEFAULT_LOG_FILE)
