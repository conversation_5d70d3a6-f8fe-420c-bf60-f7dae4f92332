from motor.motor_asyncio import AsyncIOMotorClient
from typing import Optional, Dict, Mapping, Any
from sms_service.settings import MONGO_URI, MONGO_DB


class MongoSmsReader:
    def __init__(self):
        self.client = AsyncIOMotorClient(MONGO_URI)
        self.db = self.client[MONGO_DB]
        self.collection = self.db["c_autoSendSms"]

    async def get_all_configs(self) -> Dict[int, Dict]:
        cursor = self.collection.find({"status": 1})
        results = {}
        async for doc in cursor:
            filtered = self._extract_fields(doc)
            if filtered and "id" in filtered:
                results[filtered["id"]] = filtered
        print(f'✅ 成功获取配置数量: {len(results)}')
        return results

    async def get_config_by_id(self, config_id: int) -> Optional[Dict]:
        doc = await self.collection.find_one({"id": config_id, "status": 1})
        return self._extract_fields(doc) if doc else None

    def _extract_fields(self, doc: Mapping[str, Any]) -> Dict:
        return {
            "id": doc.get("id"),
            "business_no": doc.get("business_no"),
            "type": doc.get("type"),
            "status": doc.get("status"),
            "smsText": doc.get("smsText"),
            "minute": doc.get("minute"),
            "day": doc.get("day"),
            "amount": doc.get("amount"),
            "currencyId": doc.get("currencyId"),
            "sendType": doc.get("sendType"),
            "sendTime": doc.get("sendTime")
        }
