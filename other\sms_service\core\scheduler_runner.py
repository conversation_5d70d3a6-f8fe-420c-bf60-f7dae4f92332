import asyncio
from core.sms_dispatcher import send_sms_once, send_default_sms
from core.task_cache import get_all_enabled_tasks
from core.service import (
    get_non_recharged_users, get_non_beted_users, get_high_bet_users_yesterday,
    get_high_loss_users_yesterday, get_inactive_high_recharge_users, get_inactive_low_recharge_users,
    get_first_deposit_no_return_users_yesterday, get_verified_phone_numbers
)
from sms_service.settings import CHECK_WINDOW, TARGET_TIMEZONE_HOURS
from datetime import datetime, timedelta, timezone
from decimal import Decimal
from core.logger import logger
import math

def parse_send_time(send_time_str: str) -> tuple:
    try:
        h, m, s = map(int, send_time_str.strip().split(":"))
        return h, m, s
    except Exception:
        return None

def send_sms_tasks(send_type, phones, content, sms_type):
    if send_type == '0' or send_type == '1':
        send_sms_once(set(phones), content, sms_type)
    elif send_type == '2':
        send_default_sms(set(phones), content)

# ======== async handler =========

async def handle_task1(task, start_time=None, window_length_seconds=None):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    minute = int(task.get("minute"))
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    target_tz = timezone(timedelta(hours=TARGET_TIMEZONE_HOURS))
    if start_time is None:
        now = datetime.now(tz=target_tz)
        start_time = now - timedelta(minutes=(minute + CHECK_WINDOW))
        register_window_minutes = CHECK_WINDOW
        precise_minutes = CHECK_WINDOW
        precise_window_end = start_time + timedelta(minutes=CHECK_WINDOW)
    else:
        if window_length_seconds is not None:
            precise_minutes = window_length_seconds / 60
            register_window_minutes = math.ceil(window_length_seconds / 60)
            precise_window_end = start_time + timedelta(seconds=window_length_seconds)
        else:
            register_window_minutes = CHECK_WINDOW
            precise_minutes = CHECK_WINDOW
            precise_window_end = start_time + timedelta(minutes=CHECK_WINDOW)
    logger.info(
        f"handle_task1 start_time: {start_time}, minute: {minute}, CHECK_WINDOW: {CHECK_WINDOW}, 动态窗口长度: {precise_minutes:.4f} 分钟")
    logger.info(f"精确窗口范围: {start_time} 到 {precise_window_end}")
    query_window_end = start_time + timedelta(minutes=register_window_minutes)
    logger.info(f"实际查询范围(向上取整): {start_time} 到 {query_window_end}, 窗口长度: {register_window_minutes} 分钟")
    logger.info(f"但在 {start_time} 到 {datetime.now(tz=target_tz)} 之间未充值的用户")
    player_ids = await get_non_recharged_users(
        business_no=business_no,
        start_time=start_time,
        register_window_minutes=register_window_minutes,
        check_window_minutes=(minute + register_window_minutes)
    )
    player_ids_int = [int(pid) for pid in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values()
    logger.info(f"找到 {len(player_ids)} 个未充值用户, 有效电话 {len(phones)} 个")
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task2(task, start_time=None, window_length_seconds=None):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    minute = int(task.get("minute"))
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    target_tz = timezone(timedelta(hours=TARGET_TIMEZONE_HOURS))
    if start_time is None:
        now = datetime.now(tz=target_tz)
        start_time = now - timedelta(minutes=(minute + CHECK_WINDOW))
        register_window_minutes = CHECK_WINDOW
        precise_minutes = CHECK_WINDOW
        precise_window_end = start_time + timedelta(minutes=CHECK_WINDOW)
    else:
        if window_length_seconds is not None:
            precise_minutes = window_length_seconds / 60
            register_window_minutes = math.ceil(window_length_seconds / 60)
            precise_window_end = start_time + timedelta(seconds=window_length_seconds)
        else:
            register_window_minutes = CHECK_WINDOW
            precise_minutes = CHECK_WINDOW
            precise_window_end = start_time + timedelta(minutes=CHECK_WINDOW)
    logger.info(
        f"handle_task2 start_time: {start_time}, minute: {minute}, CHECK_WINDOW: {CHECK_WINDOW}, 动态窗口长度: {precise_minutes:.4f} 分钟")
    logger.info(f"精确窗口范围: {start_time} 到 {precise_window_end}")
    query_window_end = start_time + timedelta(minutes=register_window_minutes)
    logger.info(f"实际查询范围(向上取整): {start_time} 到 {query_window_end}, 窗口长度: {register_window_minutes} 分钟")
    logger.info(f"但在 {start_time} 到 {datetime.now(tz=target_tz)} 之间未投注的用户")
    player_ids = await get_non_beted_users(
        business_no=business_no,
        start_time=start_time,
        register_window_minutes=register_window_minutes,
        check_window_minutes=(minute + register_window_minutes)
    )
    player_ids_int = [int(pid) for pid in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values()
    logger.info(f"找到 {len(player_ids)} 个未投注用户, 有效电话 {len(phones)} 个")
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task3(task):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    currency_id = task.get("currencyId")
    amount = task.get("amount")
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    player_ids = await get_high_bet_users_yesterday(business_no, int(currency_id), Decimal(amount))
    player_ids_int = [int(player_id) for player_id in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values() if verified_phones else []
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task4(task):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    currency_id = task.get("currencyId")
    amount = task.get("amount")
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    player_ids = await get_high_loss_users_yesterday(business_no, int(currency_id), Decimal(amount))
    player_ids_int = [int(player_id) for player_id in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values() if verified_phones else []
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task5(task):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    currency_id = task.get("currencyId")
    amount = task.get("amount")
    day = task.get("day")
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    player_ids = await get_inactive_high_recharge_users(business_no, int(currency_id), Decimal(amount), int(day))
    player_ids_int = [int(player_id) for player_id in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values() if verified_phones else []
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task6(task):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    currency_id = task.get("currencyId")
    amount = task.get("amount")
    day = task.get("day")
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    player_ids = await get_inactive_low_recharge_users(business_no, int(currency_id), Decimal(amount), int(day))
    player_ids_int = [int(player_id) for player_id in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values() if verified_phones else []
    send_sms_tasks(send_type, phones, content, sms_type)

async def handle_task7(task):
    sms_type = str(task.get("type"))
    content = task.get("smsText")
    send_type = str(task.get("sendType"))
    business_no = str(task.get("business_no"))
    player_ids = await get_first_deposit_no_return_users_yesterday(business_no)
    player_ids_int = [int(player_id) for player_id in player_ids]
    verified_phones = await get_verified_phone_numbers(player_ids_int)
    phones = verified_phones.values() if verified_phones else []
    send_sms_tasks(send_type, phones, content, sms_type)

TASK_HANDLER_MAP = {
    1: handle_task1,
    2: handle_task2,
    3: handle_task3,
    4: handle_task4,
    5: handle_task5,
    6: handle_task6,
    7: handle_task7,
}

async def periodic_tasks_loop():
    logger.info("启动周期性任务主循环")
    periodic_tasks_last_window_end = {}
    periodic_tasks_last_execution_time = {}
    while True:
        try:
            target_tz = timezone(timedelta(hours=TARGET_TIMEZONE_HOURS))
            now = datetime.now(tz=target_tz)
            logger.info(f"周期性任务检查: 当前时间(UTC{TARGET_TIMEZONE_HOURS}): {now}")

            all_tasks = get_all_enabled_tasks()
            for business_no, task in all_tasks.items():
                task_id = task.get("id")
                task_type = str(task.get("type"))
                if task_type not in ("1", "2") or task.get("status") != 1:
                    continue
                minute = int(task.get("minute", 5))
                last_window_end = periodic_tasks_last_window_end.get(task_id)
                last_execution_time = periodic_tasks_last_execution_time.get(task_id)
                if last_window_end is None:
                    window_start = now - timedelta(minutes=(minute + CHECK_WINDOW))
                    window_end = window_start + timedelta(minutes=CHECK_WINDOW)
                else:
                    time_elapsed_seconds = (now - last_execution_time).total_seconds()
                    if time_elapsed_seconds >= CHECK_WINDOW * 60:
                        window_size_seconds = time_elapsed_seconds
                        window_start = last_window_end
                        window_end = window_start + timedelta(seconds=window_size_seconds)
                        max_window_end = now - timedelta(minutes=minute)
                        if window_end > max_window_end:
                            window_end = max_window_end
                            window_size_seconds = (window_end - window_start).total_seconds()
                    else:
                        continue
                handler = TASK_HANDLER_MAP.get(task_id)
                if handler:
                    window_length_seconds = (window_end - window_start).total_seconds()
                    await handler(task, window_start, window_length_seconds)
                    periodic_tasks_last_window_end[task_id] = window_end
                    periodic_tasks_last_execution_time[task_id] = now
            await asyncio.sleep(30)
        except Exception as e:
            logger.error(f"周期性任务主循环执行出错: {e}", exc_info=True)
            await asyncio.sleep(10)

async def scheduler_loop():
    print(f"📅 启动任务调度轮询线程...目标时区: UTC{TARGET_TIMEZONE_HOURS:+d}")
    last_trigger = {}
    target_offset = timedelta(hours=TARGET_TIMEZONE_HOURS)
    while True:
        try:
            now_utc = datetime.now(timezone.utc)
            now_target = now_utc + target_offset
            now_str = now_target.strftime("%H:%M")
            today = now_target.strftime("%Y-%m-%d")
            for business_no, task in get_all_enabled_tasks().items():
                task_id = task.get("id")
                task_type = str(task.get("type"))
                if task.get("status") != 1:
                    continue
                if task_type in ("1", "2"):
                    continue
                handler = TASK_HANDLER_MAP.get(task_id)
                if not handler:
                    continue
                send_time = task.get("sendTime")
                if not send_time or send_time == "0":
                    continue
                send_hms = parse_send_time(send_time)
                if not send_hms:
                    continue
                if (now_target.hour, now_target.minute) == send_hms[:2]:
                    if last_trigger.get(business_no) != today:
                        await handler(task)
                        last_trigger[business_no] = today
            await asyncio.sleep(60)
        except Exception as e:
            logger.error(f"调度器主循环异常: {e}", exc_info=True)
            await asyncio.sleep(10)

async def start_scheduler():
    # 让 main.py 在 on_startup 阶段 await 这个函数即可
    asyncio.create_task(periodic_tasks_loop())
    asyncio.create_task(scheduler_loop())
    logger.info("调度系统异步任务已启动")
