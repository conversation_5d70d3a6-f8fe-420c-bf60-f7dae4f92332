from datetime import datetime, timezone, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Set
from core.logger import logger
from core.db_pool import get_db_pool
import aiomysql

timezone_str = '-03:00'

def parse_timezone(tz_str) -> timezone:
    sign = tz_str[0]
    hours = int(tz_str[1:3])
    minutes = int(tz_str[4:6])
    delta = timedelta(hours=hours, minutes=minutes)
    if sign == '-':
        delta = -delta
    return timezone(delta)

def convert_to_timestamp_with_timezone(dt: datetime, tz_str) -> int:
    tz = parse_timezone(tz_str)
    if dt.tzinfo is None:
        dt = dt.replace(tzinfo=tz)
    else:
        dt = dt.astimezone(tz)
    return int(dt.timestamp() * 1000)

def get_utc_month_tables(start_time: datetime, end_time: datetime, prefix: str) -> List[str]:
    start_time_utc = start_time.astimezone(timezone.utc)
    end_time_utc = end_time.astimezone(timezone.utc)
    tables = []
    current_date = start_time_utc.replace(day=1)
    while current_date <= end_time_utc:
        month_str = current_date.strftime('%Y-%m')
        table_name = f'{prefix}{month_str}'
        tables.append(table_name)
        if current_date.month == 12:
            current_date = current_date.replace(year=current_date.year + 1, month=1)
        else:
            current_date = current_date.replace(month=current_date.month + 1)
    return tables

async def get_registered_users(business_no: str, start_time: datetime, end_time: datetime) -> List[str]:
    try:
        logger.info(f"查询业务线 {business_no} 在 {start_time} 至 {end_time} 之间注册的用户")

        start_timestamp = convert_to_timestamp_with_timezone(start_time, timezone_str)
        end_timestamp = convert_to_timestamp_with_timezone(end_time, timezone_str)

        tables_to_query = get_utc_month_tables(start_time, end_time, prefix="ea_platform_player_promotion_log_")
        logger.info(f"需要查询的月份表: {tables_to_query}")

        registered_user_ids = []

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                existing_tables = []
                for table_name in tables_to_query:
                    await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if await cursor.fetchone():
                        existing_tables.append(table_name)
                    else:
                        logger.warning(f"表 {table_name} 不存在，跳过查询")

                if not existing_tables:
                    logger.warning("没有找到任何需要查询的表")
                    return []

                if len(existing_tables) == 1:
                    query = f"""
                        SELECT DISTINCT playerId
                        FROM `{existing_tables[0]}`
                        WHERE business_no = %s
                        AND registerTime >= %s
                        AND registerTime < %s
                    """
                    await cursor.execute(query, [business_no, str(start_timestamp), str(end_timestamp)])
                else:
                    queries = []
                    params = []
                    for table_name in existing_tables:
                        queries.append(f"""
                            SELECT playerId
                            FROM `{table_name}`
                            WHERE business_no = %s
                            AND registerTime >= %s
                            AND registerTime < %s
                        """)
                        params.extend([business_no, str(start_timestamp), str(end_timestamp)])
                    union_query = " UNION ".join(queries)
                    await cursor.execute(union_query, params)

                rows = await cursor.fetchall()
                registered_user_ids = [row['playerId'] for row in rows]

        logger.info(f"共查询到 {len(registered_user_ids)} 个符合条件的注册用户")
        return registered_user_ids

    except Exception as e:
        logger.error(f"查询注册用户失败: {str(e)}", exc_info=True)
        return []

async def get_recharged_users(business_no: str, start_time: datetime, end_time: datetime) -> List[str]:
    try:
        logger.info(f"查询业务线 {business_no} 在 {start_time} 至 {end_time} 之间充值过的用户")

        start_timestamp = convert_to_timestamp_with_timezone(start_time, timezone_str)
        end_timestamp = convert_to_timestamp_with_timezone(end_time, timezone_str)

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                query = """
                    SELECT DISTINCT playerId
                    FROM ea_finance_recharge_order
                    WHERE business_no = %s
                    AND createTime >= %s
                    AND createTime < %s
                    AND status = 1
                    AND reStatus = 1
                """
                await cursor.execute(query, [business_no, str(start_timestamp), str(end_timestamp)])
                rows = await cursor.fetchall()
                user_ids = [row['playerId'] for row in rows]

        logger.info(f"共查询到 {len(user_ids)} 个在指定时间范围内充值过的用户")
        return user_ids

    except Exception as e:
        logger.error(f"查询充值用户列表失败: {str(e)}", exc_info=True)
        return []

async def get_non_recharged_users(business_no: str, start_time: datetime, register_window_minutes: int,
                                  check_window_minutes: int) -> List[str]:
    try:
        if check_window_minutes <= register_window_minutes:
            logger.warning(
                f"充值检查窗口({check_window_minutes}分钟)必须大于注册窗口({register_window_minutes}分钟)，返回空列表")
            return []

        register_end_time = start_time + timedelta(minutes=register_window_minutes)
        check_end_time = start_time + timedelta(minutes=check_window_minutes)

        logger.info(f"检查业务线 {business_no} 在 {start_time} 到 {register_end_time} 之间注册")
        logger.info(f"但在 {start_time} 到 {check_end_time} 之间未充值的用户")

        registered_user_ids = await get_registered_users(business_no, start_time, register_end_time)
        logger.info(f"注册窗口内注册的用户数: {len(registered_user_ids)}")
        logger.info(f"注册用户ID列表: {', '.join(map(str, registered_user_ids))}")

        if not registered_user_ids:
            logger.info("注册窗口内没有用户注册，返回空列表")
            return []

        recharged_user_ids = await get_recharged_users(business_no, start_time, check_end_time)
        logger.info(f"充值检查窗口内充值的用户数: {len(recharged_user_ids)}")
        logger.info(f"充值用户ID列表: {', '.join(map(str, recharged_user_ids))}")

        non_recharged_ids = list(set(registered_user_ids) - set(recharged_user_ids))

        logger.info(f"找到 {len(non_recharged_ids)} 个符合条件的未充值用户")
        logger.info(f"未充值用户ID列表: {', '.join(map(str, non_recharged_ids))}")

        if len(non_recharged_ids) > 0:
            if len(non_recharged_ids) <= 10:
                logger.debug(f"未充值用户ID: {', '.join(map(str, non_recharged_ids))}")
            else:
                logger.debug(f"未充值用户ID示例(前10个): {', '.join(map(str, non_recharged_ids[:10]))}")

        return non_recharged_ids

    except Exception as e:
        logger.error(f"查询未充值用户失败: {str(e)}", exc_info=True)
        return []

async def get_beted_users(business_no: str, start_time: datetime, end_time: datetime) -> List[str]:
    try:
        logger.info(f"查询业务线 {business_no} 在 {start_time} 至 {end_time} 之间有投注记录的用户")

        start_timestamp = convert_to_timestamp_with_timezone(start_time, timezone_str)
        end_timestamp = convert_to_timestamp_with_timezone(end_time, timezone_str)

        logger.info(f"转换后的时间戳范围: {start_timestamp} - {end_timestamp}")

        tables_to_query = get_utc_month_tables(start_time, end_time, prefix="ea_platform_agent_game_log_")
        logger.info(f"需要查询的月份表: {tables_to_query}")

        beted_user_ids = []

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                existing_tables = []
                for table_name in tables_to_query:
                    await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if await cursor.fetchone():
                        existing_tables.append(table_name)
                    else:
                        logger.warning(f"表 {table_name} 不存在，跳过查询")

                if not existing_tables:
                    logger.warning("没有找到任何需要查询的表")
                    return []

                if len(existing_tables) == 1:
                    query = f"""
                        SELECT DISTINCT playerId
                        FROM `{existing_tables[0]}`
                        WHERE business_no = %s
                        AND CAST(logTime AS UNSIGNED) >= %s
                        AND CAST(logTime AS UNSIGNED) < %s
                        AND amount > 0
                    """
                    await cursor.execute(query, [business_no, str(start_timestamp), str(end_timestamp)])
                else:
                    queries = []
                    params = []
                    for table_name in existing_tables:
                        queries.append(f"""
                            SELECT playerId
                            FROM `{table_name}`
                            WHERE business_no = %s
                            AND CAST(logTime AS UNSIGNED) >= %s
                            AND CAST(logTime AS UNSIGNED) < %s
                            AND amount > 0
                        """)
                        params.extend([business_no, str(start_timestamp), str(end_timestamp)])
                    union_query = " UNION ".join(queries)
                    await cursor.execute(union_query, params)

                rows = await cursor.fetchall()
                beted_user_ids = [row['playerId'] for row in rows]

        logger.info(f"共查询到 {len(beted_user_ids)} 个在指定时间范围内有投注记录的用户")
        return beted_user_ids

    except Exception as e:
        logger.error(f"查询投注用户列表失败: {str(e)}", exc_info=True)
        return []

async def get_non_beted_users(business_no: str, start_time: datetime, register_window_minutes: int,
                              check_window_minutes: int) -> List[str]:
    try:
        if check_window_minutes <= register_window_minutes:
            logger.warning(
                f"投注检查窗口({check_window_minutes}分钟)必须大于注册窗口({register_window_minutes}分钟)，返回空列表")
            return []

        register_end_time = start_time + timedelta(minutes=register_window_minutes)
        check_end_time = start_time + timedelta(minutes=check_window_minutes)

        logger.info(f"检查业务线 {business_no} 在 {start_time} 到 {register_end_time} 之间注册")
        logger.info(f"但在 {start_time} 到 {check_end_time} 之间未投注的用户")

        registered_user_ids = await get_registered_users(business_no, start_time, register_end_time)
        logger.info(f"注册窗口内注册的用户数: {len(registered_user_ids)}")
        logger.info(f"注册用户ID列表: {', '.join(map(str, registered_user_ids))}")

        registered_set = set(map(str, registered_user_ids))

        if not registered_user_ids:
            logger.info("注册窗口内没有用户注册，返回空列表")
            return []

        beted_user_ids = await get_beted_users(business_no, start_time, check_end_time)
        logger.info(f"投注检查窗口内投注的用户数: {len(beted_user_ids)}")
        logger.info(f"投注用户ID列表: {', '.join(map(str, beted_user_ids))}")

        beted_set = set(map(str, beted_user_ids))

        non_beted_ids = list(registered_set - beted_set)

        logger.info(
            f"验证计算是否正确: 注册用户数({len(registered_set)}) - 有投注用户数中的注册用户({len(registered_set & beted_set)}) = 未投注用户数({len(non_beted_ids)})")

        logger.info(f"找到 {len(non_beted_ids)} 个符合条件的未投注用户")
        logger.info(f"未投注用户ID列表: {', '.join(map(str, non_beted_ids))}")

        if len(non_beted_ids) > 0:
            if len(non_beted_ids) <= 10:
                logger.debug(f"未投注用户ID: {', '.join(map(str, non_beted_ids))}")
            else:
                logger.debug(f"未投注用户ID示例(前10个): {', '.join(map(str, non_beted_ids[:10]))}")

        return non_beted_ids

    except Exception as e:
        logger.error(f"查询未投注用户失败: {str(e)}", exc_info=True)
        return []



#############################################################################
# 轮询任务的分割线

async def get_high_bet_users_yesterday(business_no: str, currency_id: int, min_valid_bet_amount: Decimal) -> List[str]:
    try:
        tz = timezone(timedelta(hours=-3))
        today = datetime.now(tz)
        yesterday = today - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')

        logger.info(f"获取业务线 {business_no} 在 {yesterday_str} 有效投注大于 {min_valid_bet_amount} BRL 的用户")

        yesterday_start = datetime(yesterday.year, yesterday.month, yesterday.day, 0, 0, 0, tzinfo=tz)
        yesterday_end = datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59, 999999, tzinfo=tz)

        start_timestamp = int(yesterday_start.timestamp() * 1000)
        end_timestamp = int(yesterday_end.timestamp() * 1000)

        month_str = yesterday.strftime('%Y-%m')
        table_name = f"ea_platform_agent_game_log_{month_str}"

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if not await cursor.fetchone():
                    logger.warning(f"表 {table_name} 不存在，无法查询投注信息")
                    return []

                query = f"""
                    SELECT playerId
                    FROM `{table_name}`
                    WHERE business_no = %s
                      AND CAST(logTime AS UNSIGNED) >= %s
                      AND CAST(logTime AS UNSIGNED) <= %s
                      AND currencyId = %s
                    GROUP BY playerId
                    HAVING SUM(amount) > %s
                """
                await cursor.execute(query, [
                    business_no,
                    str(start_timestamp),
                    str(end_timestamp),
                    currency_id,
                    float(min_valid_bet_amount),
                ])
                rows = await cursor.fetchall()
                high_bet_user_ids = [row['playerId'] for row in rows]

        logger.info(f"共找到 {len(high_bet_user_ids)} 个昨日有效投注大于 {min_valid_bet_amount} BRL 的用户")
        return high_bet_user_ids

    except Exception as e:
        logger.error(f"获取高投注用户失败: {str(e)}", exc_info=True)
        return []

async def get_high_loss_users_yesterday(business_no: str, currency_id: int, min_loss_amount: Decimal) -> List[str]:
    try:
        tz = timezone(timedelta(hours=-3))
        today = datetime.now(tz)
        yesterday = today - timedelta(days=1)
        yesterday_str = yesterday.strftime('%Y-%m-%d')

        logger.info(f"获取业务线 {business_no} 在 {yesterday_str} 游戏亏损金额超过 {min_loss_amount} BRL 的用户")

        yesterday_start = datetime(yesterday.year, yesterday.month, yesterday.day, 0, 0, 0, tzinfo=tz)
        yesterday_end = datetime(yesterday.year, yesterday.month, yesterday.day, 23, 59, 59, 999999, tzinfo=tz)
        start_timestamp = int(yesterday_start.timestamp() * 1000)
        end_timestamp = int(yesterday_end.timestamp() * 1000)
        month_str = yesterday.strftime('%Y-%m')
        table_name = f"ea_platform_agent_game_log_{month_str}"

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                if not await cursor.fetchone():
                    logger.warning(f"表 {table_name} 不存在，无法查询投注信息")
                    return []

                query = f"""
                    SELECT playerId
                    FROM `{table_name}`
                    WHERE business_no = %s
                      AND CAST(logTime AS UNSIGNED) >= %s
                      AND CAST(logTime AS UNSIGNED) <= %s
                      AND currencyId = %s
                    GROUP BY playerId
                    HAVING (SUM(betAmount) - SUM(winAmount)) > %s
                """
                await cursor.execute(query, [
                    business_no,
                    str(start_timestamp),
                    str(end_timestamp),
                    currency_id,
                    float(min_loss_amount),
                ])
                rows = await cursor.fetchall()
                high_loss_user_ids = [row['playerId'] for row in rows]

        logger.info(f"共找到 {len(high_loss_user_ids)} 个昨日游戏亏损金额超过 {min_loss_amount} BRL 的用户")
        return high_loss_user_ids

    except Exception as e:
        logger.error(f"获取高亏损用户失败: {str(e)}", exc_info=True)
        return []

async def get_total_recharge_users(business_no: str, currency_id: int, amount: Decimal, operator: str) -> List[str]:
    try:
        valid_operators = [">=", ">", "<=", "<", "="]
        if operator not in valid_operators:
            logger.error(f"无效的比较操作符: {operator}，必须是 {', '.join(valid_operators)} 之一")
            return []

        logger.info(f"查询业务线 {business_no} 历史充值总金额 {operator} {amount} BRL 的用户")

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                query = f"""
                    SELECT playerId
                    FROM ea_finance_recharge_order
                    WHERE business_no = %s
                      AND status = 1
                      AND reStatus = 1
                      AND currencyId = %s
                    GROUP BY playerId
                    HAVING SUM(amounts) {operator} %s
                """
                await cursor.execute(query, [
                    business_no,
                    currency_id,
                    float(amount),
                ])
                rows = await cursor.fetchall()
                user_ids = [row['playerId'] for row in rows]

        logger.info(f"共查询到 {len(user_ids)} 个历史充值总金额 {operator} {amount} BRL 的用户")
        return user_ids

    except Exception as e:
        logger.error(f"查询充值金额用户失败: {str(e)}", exc_info=True)
        return []

async def get_inactive_bet_users(business_no: str, player_ids: List[str], inactive_days: int = 3) -> List[str]:
    try:
        if not player_ids:
            logger.info("输入的用户ID列表为空，直接返回空列表")
            return []

        tz = timezone(timedelta(hours=-3))
        now = datetime.now(tz)
        inactive_date = now - timedelta(days=inactive_days)

        logger.info(f"从 {len(player_ids)} 个用户中筛选出自 {inactive_date} 以来未投注的用户")

        table_prefix = "ea_platform_agent_game_log_"
        tables_to_check = get_utc_month_tables(inactive_date, now, table_prefix)
        logger.info(f"需要检查的投注日志月份表: {tables_to_check}")

        inactive_timestamp = int(inactive_date.timestamp() * 1000)
        active_bet_user_ids = set()

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                for table_name in tables_to_check:
                    await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if not await cursor.fetchone():
                        logger.warning(f"表 {table_name} 不存在，跳过查询")
                        continue

                    batch_size = 1000
                    for i in range(0, len(player_ids), batch_size):
                        batch_player_ids = player_ids[i:i + batch_size]
                        placeholders = ', '.join(['%s'] * len(batch_player_ids))
                        query = f"""
                            SELECT DISTINCT playerId
                            FROM `{table_name}`
                            WHERE business_no = %s
                              AND playerId IN ({placeholders})
                              AND CAST(logTime AS UNSIGNED) >= %s
                        """
                        params = [business_no] + batch_player_ids + [str(inactive_timestamp)]
                        await cursor.execute(query, params)
                        rows = await cursor.fetchall()
                        for row in rows:
                            active_bet_user_ids.add(row['playerId'])

        logger.info(f"找到 {len(active_bet_user_ids)} 个最近 {inactive_days} 天内有投注记录的用户")
        inactive_bet_user_ids = [user_id for user_id in player_ids if user_id not in active_bet_user_ids]
        logger.info(f"共筛选出 {len(inactive_bet_user_ids)} 个最近 {inactive_days} 天未投注的用户")
        return inactive_bet_user_ids

    except Exception as e:
        logger.error(f"筛选未投注用户失败: {str(e)}", exc_info=True)
        return []

async def get_inactive_login_users(business_no: str, player_ids: List[str], inactive_days: int = 3) -> List[str]:
    try:
        if not player_ids:
            logger.info("输入的用户ID列表为空，直接返回空列表")
            return []

        tz = timezone(timedelta(hours=-3))
        now = datetime.now(tz)
        inactive_date = now - timedelta(days=inactive_days)

        logger.info(f"从 {len(player_ids)} 个用户中筛选出自 {inactive_date} 以来未登录的用户")

        table_prefix = "ea_platform_player_login_log_"
        tables_to_check = get_utc_month_tables(inactive_date, now, table_prefix)
        logger.info(f"需要检查的登录日志月份表: {tables_to_check}")

        inactive_timestamp = int(inactive_date.timestamp() * 1000)
        active_login_user_ids = set()

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(f"SET time_zone = '{timezone_str}'")
                for table_name in tables_to_check:
                    await cursor.execute(f"SHOW TABLES LIKE '{table_name}'")
                    if not await cursor.fetchone():
                        logger.warning(f"表 {table_name} 不存在，跳过查询")
                        continue

                    batch_size = 1000
                    for i in range(0, len(player_ids), batch_size):
                        batch_player_ids = player_ids[i:i + batch_size]
                        placeholders = ', '.join(['%s'] * len(batch_player_ids))
                        query = f"""
                            SELECT DISTINCT playerId
                            FROM `{table_name}`
                            WHERE business_no = %s
                              AND playerId IN ({placeholders})
                              AND CAST(logTime AS UNSIGNED) >= %s
                        """
                        params = [business_no] + batch_player_ids + [str(inactive_timestamp)]
                        await cursor.execute(query, params)
                        rows = await cursor.fetchall()
                        for row in rows:
                            active_login_user_ids.add(row['playerId'])

        logger.info(f"找到 {len(active_login_user_ids)} 个最近 {inactive_days} 天内有登录记录的用户")
        inactive_login_user_ids = [user_id for user_id in player_ids if user_id not in active_login_user_ids]
        logger.info(f"共筛选出 {len(inactive_login_user_ids)} 个最近 {inactive_days} 天未登录的用户")
        return inactive_login_user_ids

    except Exception as e:
        logger.error(f"筛选未登录用户失败: {str(e)}", exc_info=True)
        return []

async def get_inactive_high_recharge_users(business_no: str, currency_id: int, min_total_amount: Decimal,
                                     inactive_days) -> List[str]:
    try:
        logger.info(
            f"查询业务线 {business_no} 中历史充值大于等于 {min_total_amount} BRL 且 {inactive_days} 天未活跃的用户")
        high_recharge_user_ids = await get_total_recharge_users(business_no, currency_id, min_total_amount, '>=')
        logger.info(f"历史充值大于等于 {min_total_amount} BRL 的用户数: {len(high_recharge_user_ids)}")
        if not high_recharge_user_ids:
            logger.info("没有找到满足充值条件的用户，返回空列表")
            return []
        inactive_login_user_ids = await get_inactive_login_users(business_no, high_recharge_user_ids, inactive_days)
        logger.info(f"高充值用户中最近 {inactive_days} 天未登录的用户数: {len(inactive_login_user_ids)}")
        if not inactive_login_user_ids:
            logger.info("没有找到同时满足充值和未登录条件的用户，返回空列表")
            return []
        inactive_user_ids = await get_inactive_bet_users(business_no, inactive_login_user_ids, inactive_days)
        logger.info(f"高充值未登录用户中最近 {inactive_days} 天未投注的用户数: {len(inactive_user_ids)}")
        logger.info(f"找到 {len(inactive_user_ids)} 个同时满足所有条件的用户")
        return inactive_user_ids
    except Exception as e:
        logger.error(f"查询满足条件的用户失败: {str(e)}", exc_info=True)
        return []

async def get_inactive_low_recharge_users(business_no: str, currency_id: int, max_total_amount: Decimal,
                                    inactive_days) -> List[str]:
    try:
        logger.info(f"查询业务线 {business_no} 中历史充值小于 {max_total_amount} BRL 且 {inactive_days} 天未活跃的用户")
        low_recharge_user_ids = await get_total_recharge_users(business_no, currency_id, max_total_amount, "<")
        logger.info(f"历史充值小于 {max_total_amount} BRL 的用户数: {len(low_recharge_user_ids)}")
        if not low_recharge_user_ids:
            logger.info("没有找到满足充值条件的用户，返回空列表")
            return []
        inactive_login_user_ids = await get_inactive_login_users(business_no, low_recharge_user_ids, inactive_days)
        logger.info(f"低充值用户中最近 {inactive_days} 天未登录的用户数: {len(inactive_login_user_ids)}")
        if not inactive_login_user_ids:
            logger.info("没有找到同时满足充值和未登录条件的用户，返回空列表")
            return []
        inactive_user_ids = await get_inactive_bet_users(business_no, inactive_login_user_ids, inactive_days)
        logger.info(f"低充值未登录用户中最近 {inactive_days} 天未投注的用户数: {len(inactive_user_ids)}")
        logger.info(f"找到 {len(inactive_user_ids)} 个同时满足所有条件的用户")
        return inactive_user_ids
    except Exception as e:
        logger.error(f"查询满足条件的用户失败: {str(e)}", exc_info=True)
        return []

# 其余类似按此模式继续写即可，如 get_first_deposit_users 等，也全部 async + await + aiomysql


#################################################################################################
async def get_date_range_timestamps(date_str: str, timezone_str: str = '-03:00', end_of_day: bool = False) -> tuple:
    tz = parse_timezone(timezone_str)
    dt = datetime.strptime(date_str, '%Y-%m-%d')
    start_dt = dt.replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=tz)
    start_timestamp = int(start_dt.timestamp() * 1000)
    if end_of_day:
        end_dt = dt.replace(hour=23, minute=59, second=59, microsecond=999000, tzinfo=tz)
        end_timestamp = int(end_dt.timestamp() * 1000)
    else:
        end_dt = (dt + timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0, tzinfo=tz)
        end_timestamp = int(end_dt.timestamp() * 1000)
    return start_timestamp, end_timestamp

async def get_first_deposit_users(business_no: str, date_str: str) -> List[Dict[str, Any]]:
    try:
        logger.info(f"查询业务线 {business_no} 在 {date_str} 首次充值的用户")

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute("SET time_zone = '-03:00'")
                sql = f"""
                    SELECT 
                        a.playerId, 
                        a.playerName,
                        a.business_no, 
                        a.createTime AS recharge_timestamp,
                        a.amounts,
                        a.currencyId
                    FROM ea_finance_recharge_order a
                    WHERE a.status = 1
                      AND a.reStatus = 1
                      AND a.business_no = %s
                      AND DATE(FROM_UNIXTIME(a.createTime/1000)) = %s
                      AND NOT EXISTS (
                          SELECT 1
                          FROM ea_finance_recharge_order
                          WHERE playerId = a.playerId
                            AND status = 1
                            AND reStatus = 1
                            AND createTime < a.createTime
                      )
                """
                await cursor.execute(sql, [business_no, date_str])
                rows = await cursor.fetchall()
                first_deposit_users = [dict(row) for row in rows]
        logger.info(f"找到 {len(first_deposit_users)} 个首次充值用户")
        return first_deposit_users
    except Exception as e:
        logger.error(f"查询首次充值用户失败: {str(e)}", exc_info=True)
        return []

async def check_next_day_deposit(business_no: str, player_ids: List[str], next_day_str: str) -> Dict[str, bool]:
    try:
        if not player_ids:
            logger.info("没有玩家ID需要检查次日充值")
            return {}

        logger.info(f"检查 {len(player_ids)} 个玩家在 {next_day_str} 是否有充值记录")
        next_day_start, next_day_end = await get_date_range_timestamps(next_day_str, end_of_day=True)
        has_deposit = {pid: False for pid in player_ids}

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute("SET time_zone = '-03:00'")
                batch_size = 1000
                for i in range(0, len(player_ids), batch_size):
                    batch_player_ids = player_ids[i:i + batch_size]
                    placeholders = ', '.join(['%s'] * len(batch_player_ids))
                    query = f"""
                        SELECT DISTINCT playerId
                        FROM ea_finance_recharge_order
                        WHERE business_no = %s
                          AND playerId IN ({placeholders})
                          AND status = 1
                          AND reStatus = 1
                          AND createTime BETWEEN %s AND %s
                    """
                    params = [business_no] + batch_player_ids + [str(next_day_start), str(next_day_end)]
                    await cursor.execute(query, params)
                    rows = await cursor.fetchall()
                    for row in rows:
                        player_id = row['playerId']
                        has_deposit[player_id] = True
        deposit_count = sum(1 for has_dep in has_deposit.values() if has_dep)
        non_deposit_count = len(player_ids) - deposit_count
        logger.info(f"{next_day_str} 有 {deposit_count} 个玩家有充值记录，{non_deposit_count} 个玩家无充值记录")
        return has_deposit
    except Exception as e:
        logger.error(f"检查次日充值失败: {str(e)}", exc_info=True)
        return {pid: False for pid in player_ids}

async def get_first_deposit_no_return_users(business_no: str, date_str: str) -> Set[str]:
    try:
        logger.info(f"查询业务线 {business_no} 在 {date_str} 首充后次日未复充的用户")
        first_deposit_users = await get_first_deposit_users(business_no, date_str)
        if not first_deposit_users:
            logger.info(f"{date_str} 没有找到首次充值用户")
            return set()
        player_ids = [str(user['playerId']) for user in first_deposit_users]
        date_dt = datetime.strptime(date_str, '%Y-%m-%d')
        next_day_str = (date_dt + timedelta(days=1)).strftime('%Y-%m-%d')
        has_next_day_deposit = await check_next_day_deposit(business_no, player_ids, next_day_str)
        no_return_player_ids = set()
        for user in first_deposit_users:
            player_id = str(user['playerId'])
            if player_id in has_next_day_deposit and not has_next_day_deposit[player_id]:
                no_return_player_ids.add(player_id)
        for user in first_deposit_users:
            player_id = str(user['playerId'])
            if player_id in no_return_player_ids:
                recharge_timestamp = user.get('recharge_timestamp')
                if recharge_timestamp:
                    try:
                        if isinstance(recharge_timestamp, str) and recharge_timestamp.isdigit():
                            recharge_timestamp = int(recharge_timestamp)
                        recharge_dt = datetime.fromtimestamp(recharge_timestamp / 1000, parse_timezone('-03:00'))
                    except (ValueError, TypeError) as e:
                        logger.warning(f"无法格式化充值时间 {recharge_timestamp}: {e}")
        logger.info(f"找到 {len(no_return_player_ids)} 个首充后次日未复充的用户")
        return no_return_player_ids
    except Exception as e:
        logger.error(f"查询首充后次日未复充用户失败: {str(e)}", exc_info=True)
        return set()

async def get_first_deposit_no_return_users_yesterday(business_no: str):
    try:
        tz = timedelta(hours=-3)
        today = datetime.utcnow() + tz
        yesterday_str = (today - timedelta(days=1)).strftime('%Y-%m-%d')
        logger.info(f"开始查询业务线 {business_no} 昨日（{yesterday_str}）首充后次日未复充的用户")
        users = await get_first_deposit_no_return_users(business_no, yesterday_str)
        logger.info(f"共找到 {len(users)} 个昨日首充后次日未复充的用户")
        return users
    except Exception as e:
        logger.error(f"查询昨日首充后未复充用户失败: {e}", exc_info=True)
        return []

async def get_verified_phone_numbers(player_ids: List[int]) -> Dict[int, str]:
    try:
        if not player_ids:
            logger.info("输入的玩家ID列表为空，返回空字典")
            return {}
        logger.info(f"输入的玩家ID数量: {len(player_ids)}")
        logger.debug(f"玩家ID列表: {', '.join(map(str, player_ids))}")
        player_ids_str = ', '.join(str(pid) for pid in player_ids)
        sql = f"""
            SELECT playerId, areaCode, phone 
            FROM ea_info_player 
            WHERE playerId IN ({player_ids_str}) 
            AND phoneValid = 1
        """
        verified_phones = {}

        db_pool = get_db_pool()
        async with db_pool.acquire() as conn:
            async with conn.cursor(aiomysql.DictCursor) as cursor:
                await cursor.execute(sql)
                results = await cursor.fetchall()
                for row in results:
                    player_id = row['playerId']
                    area_code = row['areaCode']
                    phone = row['phone']
                    if area_code and phone:
                        full_phone = f"{area_code}{phone}"
                        verified_phones[player_id] = full_phone
                    elif phone:
                        verified_phones[player_id] = phone
                    else:
                        verified_phones[player_id] = ""
        logger.info(f"查询到 {len(verified_phones)} 个已验证手机号的玩家")
        if verified_phones:
            logger.debug(f"已验证手机号的玩家ID: {', '.join(map(str, verified_phones.keys()))}")
        return verified_phones
    except Exception as e:
        logger.error(f"查询已验证手机号失败: {str(e)}", exc_info=True)
        return {}

