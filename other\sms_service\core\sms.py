import hashlib
import time
import random
import string
import requests
import json
from typing import List, Set
from sms_service.settings import API_URL, USERNAME, API_KEY
from core.logger import get_logger

# === 日志 ===
sms_logger = get_logger(name="sms_logger", filename="sms.log")

# === 配置区 ===
SIGN_TYPE = "MD5"
TA_VERSION = "v2"
MAX_BATCH_SIZE = 100  # 每批最多100个号码
SEND_INTERVAL = 1  # 每秒最多1次请求


# === 工具函数 ===
def generate_nonce(length=16):
    return ''.join(random.choices(string.ascii_letters + string.digits, k=length))


def phones_to_sign_str(phones: List[dict]) -> str:
    return "[" + ", ".join(f"{{phone={p['phone']}}}" for p in phones) + "]"


def generate_signature_fixed_order(params: dict, api_key: str) -> str:
    fields = ["content", "nonceStr", "phones", "signType", "timestamp", "username"]
    sign_parts = []
    for field in fields:
        value = params.get(field)
        if field == "phones" and isinstance(value, list):
            value = phones_to_sign_str(value)
        sign_parts.append(f"{field}={value}")
    plain_text = '&'.join(sign_parts) + f"&key={api_key}"
    sms_logger.info(f"🔐 固定顺序签名字符串：{plain_text}")
    return hashlib.md5(plain_text.encode('utf-8')).hexdigest()


def send_sms_batch(phone_list: List[str], content: str):
    nonce_str = generate_nonce()
    timestamp = str(int(time.time() * 1000))
    phones = [{"phone": phone} for phone in phone_list]

    sign_params = {
        "content": content,
        "nonceStr": nonce_str,
        "phones": phones,
        "signType": SIGN_TYPE,
        "timestamp": timestamp,
        "username": USERNAME,
    }

    sign = generate_signature_fixed_order(sign_params, API_KEY)
    sms_logger.info(f"✅ 最终签名值：{sign}")

    body = dict(sign_params)
    body["sign"] = sign

    headers = {
        "Content-Type": "application/json",
        "ta-version": TA_VERSION
    }

    sms_logger.info("📤 请求参数（body）：")
    sms_logger.info(json.dumps(body, indent=2, ensure_ascii=False))

    sms_logger.info(f"📤 批次发送号码数: {len(phone_list)}")
    sms_logger.info(f"签名字符串: {phones_to_sign_str(phones)}")

    try:
        response = requests.post(API_URL, headers=headers, json=body)
        result = response.json()
    except requests.exceptions.JSONDecodeError:
        result = response.text
        sms_logger.warning(f"⚠️ 接口未返回 JSON 格式数据，原始响应: {result}")
        return response.status_code, result
    except Exception as e:
        sms_logger.error(f"❌ 请求异常: {e}", exc_info=True)
        return 500, str(e)

    sms_logger.info(f"响应状态: {response.status_code}, 响应结果: {result}")
    return response.status_code, result


def send_sms(phone_set: Set[str], content: str):
    phone_list = list(phone_set)
    results = []

    for i in range(0, len(phone_list), MAX_BATCH_SIZE):
        batch = phone_list[i:i + MAX_BATCH_SIZE]
        status, result = send_sms_batch(batch, content)
        results.append((status, result))
        if i + MAX_BATCH_SIZE < len(phone_list):
            sms_logger.info("⏱️ 等待1秒以满足频率限制...")
            time.sleep(SEND_INTERVAL)

    return results
