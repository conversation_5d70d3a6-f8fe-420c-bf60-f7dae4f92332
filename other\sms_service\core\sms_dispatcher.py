import sqlite3
from datetime import datetime
from typing import Set
from core.sms import send_sms
from core.logger import logger
from sms_service.settings import DB_PATH


def init_db():
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS sent_records (
            phone TEXT NOT NULL,
            type TEXT NOT NULL,
            sent_at TEXT NOT NULL,
            PRIMARY KEY (phone, type)
        )
    ''')
    conn.commit()
    conn.close()


def has_sent(phone: str, sms_type: str) -> bool:
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute('SELECT 1 FROM sent_records WHERE phone = ? AND type = ?', (phone, sms_type))
    result = cursor.fetchone()
    conn.close()
    return result is not None


def mark_sent(phone: str, sms_type: str):
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    cursor.execute(
        'INSERT OR IGNORE INTO sent_records (phone, type, sent_at) VALUES (?, ?, ?)',
        (phone, sms_type, datetime.now().isoformat())
    )
    conn.commit()
    conn.close()


def send_sms_once(phone_set: Set[str], content: str, sms_type: str):
    init_db()
    to_send = {phone for phone in phone_set if not has_sent(phone, sms_type)}

    logger.info(f"[类型 {sms_type}] 总计 {len(phone_set)} 个号码，未发送数: {len(to_send)}")

    if not to_send:
        logger.info("✅ 所有号码都已发送过，无需重复发送。")
        return []

    results = send_sms(to_send, content)

    for phone in to_send:
        mark_sent(phone, sms_type)
        logger.info(f"📤 已标记发送: {phone} -> {content}")

    logger.info(f"✅ 本轮共发送 {len(to_send)} 个手机号，任务完成。")
    return results


def send_default_sms(phone_set: Set[str], content: str):
    logger.info(f"📤 [默认短信] 正在向 {len(phone_set)} 个号码发送短信: {content}")
    return send_sms(phone_set, content)
