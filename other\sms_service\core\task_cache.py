# task_cache.py

from typing import Dict, Any, <PERSON><PERSON>, Optional
import threading

# 缓存结构：{(business_no, type): task_config_dict}
task_cache: Dict[Tuple[str, int], Dict[str, Any]] = {}
cache_lock = threading.Lock()


def update_task_config(business_no: str, task_config: Dict[str, Any]):
    """
    添加或更新指定 business_no 和 type 对应的任务配置
    """
    task_type = int(task_config.get("type", -1))
    if task_type == -1:
        return

    key = (business_no, task_type)
    with cache_lock:
        task_cache[key] = task_config


def get_all_enabled_tasks() -> Dict[Tuple[str, int], Dict[str, Any]]:
    """
    获取所有启用的任务（status=1）
    """
    with cache_lock:
        return {
            k: v for k, v in task_cache.items()
            if v.get("status") == 1
        }
    

def remove_task_config_by_id(config_id: int) -> bool:
    """
    根据 config_id 删除缓存项（如果存在）。
    返回 True 表示成功删除，False 表示未命中。
    """
    with cache_lock:
        for key, value in list(task_cache.items()):
            if value.get("id") == config_id:
                task_cache.pop(key)
                return True
    return False
