import asyncio
import json
from aiohttp import web
from core.mongo_reader import MongoSmsReader
from core.task_cache import update_task_config, remove_task_config_by_id
from core.logger import logger


routes = web.RouteTableDef()


@routes.post("/config/refresh")
async def refresh_config(request):
    try:
        data = await request.json()
        config_id = data.get("config_id")
        if config_id is None:
            return web.json_response({"success": False, "message": "缺少参数 config_id"}, status=200)

        logger.info(f"📥 收到刷新请求: config_id={config_id}")
        reader = MongoSmsReader()
        cfg = await reader.get_config_by_id(config_id)

        if cfg:
            update_task_config(cfg["business_no"], cfg)
            logger.info(f"✅ 成功刷新配置 ID={config_id}, business_no={cfg['business_no']}, type={cfg['type']}")
            return web.json_response({
                "success": True,
                "message": f"成功刷新 ID={config_id} 的配置",
                "config": cfg
            })
        else:
            removed = remove_task_config_by_id(config_id)
            logger.info(f"ℹ️ 配置 ID={config_id} 未启用或已删除，缓存清理结果: {'已清除' if removed else '未命中缓存'}")

            return web.json_response({
                "success": True,
                "message": f"配置 ID={config_id} 当前无效，缓存清理结果: {'已清除' if removed else '未命中缓存'}"
            })


    except Exception as e:
        logger.exception("❌ 刷新配置失败")
        return web.json_response({"success": False, "message": f"刷新配置失败: {str(e)}"}, status=500)


def start_http_server():
    app = web.Application()
    app.add_routes(routes)
    web.run_app(app, host="0.0.0.0", port=8080)
