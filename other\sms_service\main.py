import asyncio
from aiohttp import web
from core.watch import routes
from core.scheduler_runner import start_scheduler
from motor.motor_asyncio import AsyncIOMotorClient
from core.mongo_reader import MongoSmsReader
from core.task_cache import update_task_config
from core.db_pool import init_mysql_pool

import os
import django

os.environ.setdefault("DJANGO_SETTINGS_MODULE", "sms_service.settings")
django.setup()

async def preload_tasks_from_mongo():
    reader = MongoSmsReader()
    configs = await reader.get_all_configs()
    print(f"✅ 成功获取配置数量: {len(configs)}")
    for cfg in configs.values():
        print(cfg)
        update_task_config(cfg["business_no"], cfg)

async def start_background_tasks(app):
    await init_mysql_pool()           # 等数据库连接池完全初始化
    await preload_tasks_from_mongo()  # 加载任务配置
    await start_scheduler()           # 启动调度任务（现在是 async 版本）

async def cleanup_background_tasks(app):
    pass

def main():
    app = web.Application()
    app.add_routes(routes)
    app.on_startup.append(start_background_tasks)
    app.on_cleanup.append(cleanup_background_tasks)
    print("🚀 启动 aiohttp 服务监听 127.0.0.1:8080 中...")
    web.run_app(app, host="0.0.0.0", port=39880)

if __name__ == '__main__':
    try:
        main()
    except Exception as e:
        print("❌ 启动失败，异常：", e)