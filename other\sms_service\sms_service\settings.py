"""
Django settings for sms_service project.

Generated by 'django-admin startproject' using Django 5.2.

For more information on this file, see
https://docs.djangoproject.com/en/5.2/topics/settings/

For the full list of settings and their values, see
https://docs.djangoproject.com/en/5.2/ref/settings/
"""

from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent


# Quick-start development settings - unsuitable for production
# See https://docs.djangoproject.com/en/5.2/howto/deployment/checklist/

# SECURITY WARNING: keep the secret key used in production secret!
SECRET_KEY = 'django-insecure-4(0q^lr75sw12belece@)vs^y280p7lo86*no%2h2e)n0z90da'

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = True

ALLOWED_HOSTS = []


# Application definition

INSTALLED_APPS = [
    'django.contrib.admin',
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'sms_service',
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
]

ROOT_URLCONF = 'sms_service.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [BASE_DIR / 'templates'],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'sms_service.wsgi.application'


# Database
# https://docs.djangoproject.com/en/5.2/ref/settings/#databases

DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.mysql',
        'NAME': 'wingame',
        'USER': 'wingame',
        'PASSWORD': 'ws82H4HRFbzjmNtD',
        'HOST': '*************',
        'PORT': '3306',
    },

    'mongodb': {
        'ENGINE': 'djongo',
        'HOST': '*********************************************************************************************************',
        'NAME': 'wingame_config',
        'OPTIONS': {
            'maxPoolSize': 100,
            'minPoolSize': 10,
            'connectTimeoutMS': 30000,
            'socketTimeoutMS': 360000,
            'retryWrites': True,
            'serverSelectionTimeoutMS': 5000,
            # 👇 这里删除 authSource
        }
    },
}


# Password validation
# https://docs.djangoproject.com/en/5.2/ref/settings/#auth-password-validators

AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
]


# Internationalization
# https://docs.djangoproject.com/en/5.2/topics/i18n/

LANGUAGE_CODE = 'en-us'

TIME_ZONE = 'UTC'

USE_I18N = True

USE_TZ = True


# Static files (CSS, JavaScript, Images)
# https://docs.djangoproject.com/en/5.2/howto/static-files/

STATIC_URL = 'static/'

# Default primary key field type
# https://docs.djangoproject.com/en/5.2/ref/settings/#default-auto-field

DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'


# SMS服务的自定义配置

# MongoDB配置（供非Django应用使用，例如异步服务）
MONGO_URI = DATABASES['mongodb']['HOST']
MONGO_DB = DATABASES['mongodb']['NAME']
MONGO_OPTIONS = DATABASES['mongodb']['OPTIONS']

# MySQL连接字符串（供SQLAlchemy使用）
MYSQL_URL = f"mysql+asyncmy://{DATABASES['default']['USER']}:{DATABASES['default']['PASSWORD']}@{DATABASES['default']['HOST']}:{DATABASES['default']['PORT']}/{DATABASES['default']['NAME']}"

# 定时任务配置
TASK_INTERVAL = 60  # 每 60 秒轮询一次

CHECK_WINDOW = 2 # 滑动窗口

DB_PATH = 'sms_records.db'

API_URL = "http://**************/ta-sms/openapi/submittal"
USERNAME = "nLo2mi"
API_KEY = "UtRyYKdWtFzxnpjfvESPmwUQ9wXgHZqg"

TARGET_TIMEZONE_HOURS = -3  # 任务执行目标时区 UTC-3
