# test_non_recharged_users.py

import sys
import logging
import os
from datetime import datetime, timedelta
from django.conf import settings
from decimal import Decimal
from sms_service.settings import CHECK_WINDOW
from datetime import datetime, timedelta, timezone


# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_non_recharged_users.log')
    ]
)
logger = logging.getLogger(__name__)


# 初始化Django环境（如果在Django项目外运行）
def init_django():
    try:
        import django
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'sms_service.settings')
        django.setup()
        logger.info("Django环境初始化成功")
    except Exception as e:
        logger.error(f"Django环境初始化失败: {e}")
        sys.exit(1)


def test_non_recharged_users():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_non_recharged_users

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        # 测试场景1: 最近一小时内注册但2小时内未充值的用户
        now = datetime.now(tz=timezone.utc)
        start_time = now - timedelta(minutes=7)
        users1 = get_non_recharged_users(
            business_no=business_no,
            start_time=start_time,
            register_window_minutes=2,  # 1小时注册窗口
            check_window_minutes=7  # 2小时充值检查窗口
        )
        logger.info(f"找到 {len(users1)} 个用户")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")


    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_non_beted_users():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_non_beted_users

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        # 测试场景1: 最近一小时内注册但2小时内未充值的用户
        now = datetime.now(tz=timezone.utc)
        start_time = now - timedelta(hours=24*5)
        users1 = get_non_beted_users(
            business_no=business_no,
            start_time=start_time,
            register_window_minutes=60 * 24 * 2,  # 1小时注册窗口
            check_window_minutes=60 * 24 * 5  # 2小时充值检查窗口
        )
        logger.info(f"找到 {len(users1)} 个用户")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")


    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_get_high_bet_users_yesterday():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_high_bet_users_yesterday

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        users1 = get_high_bet_users_yesterday(
            business_no, 1000, Decimal('0')
        )
        logger.info(f"找到 {len(users1)} 个用户")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")

    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_get_high_loss_users_yesterday():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_high_loss_users_yesterday

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        users1 = get_high_loss_users_yesterday(
            business_no, 1000, Decimal('0')
        )
        logger.info(f"找到 {len(users1)} 个用户")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")

    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_get_inactive_high_recharge_users():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_inactive_high_recharge_users

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        users1 = get_inactive_high_recharge_users(
            business_no, 1000, Decimal('0'), 3
        )
        logger.info(f"找到 {len(users1)} 个用户")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")

    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_get_inactive_low_recharge_users():
    """测试未充值用户查询功能及获取验证手机号"""
    try:
        # 导入要测试的函数
        from core.service import get_inactive_low_recharge_users, get_verified_phone_numbers

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        # 1. 获取低充值且不活跃的用户
        inactive_users = get_inactive_low_recharge_users(
            business_no, 1000, Decimal('0'), 3
        )
        logger.info(f"找到 {len(inactive_users)} 个低充值不活跃用户")

        # 2. 将字符串ID转换为整数ID
        inactive_users_int = [int(user_id) for user_id in inactive_users]

        # 3. 获取这些用户中已验证手机号的信息
        verified_phones = get_verified_phone_numbers(inactive_users_int)
        logger.info(f"在 {len(inactive_users)} 个用户中，有 {len(verified_phones)} 个用户有已验证的手机号")

        # 打印验证率
        if inactive_users:
            verification_rate = (len(verified_phones) / len(inactive_users)) * 100
            logger.info(f"手机号验证率: {verification_rate:.2f}%")

        # 打印已验证用户ID和手机号的对应关系（前10个）
        if verified_phones:
            logger.info("已验证用户ID和手机号对应关系(前10个):")
            for i, (user_id, phone) in enumerate(verified_phones.items()):
                if i < 10:
                    logger.info(f"用户ID: {user_id} -> 手机号: {phone}")
                else:
                    break

        # 找出未通过验证的用户ID
        unverified_user_ids = [uid for uid in inactive_users_int if uid not in verified_phones]
        logger.info(f"未通过手机号验证的用户数: {len(unverified_user_ids)}")

        # 打印所有未验证用户ID
        if unverified_user_ids:
            logger.info("未验证用户ID列表:")
            # 分批打印，每行最多10个ID，提高可读性
            batch_size = 10
            for i in range(0, len(unverified_user_ids), batch_size):
                batch = unverified_user_ids[i:i + batch_size]
                logger.info(f"  {', '.join(map(str, batch))}")

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"低充值不活跃用户: {len(inactive_users)} 个")
        logger.info(f"已验证手机号用户: {len(verified_phones)} 个")
        logger.info(f"未验证手机号用户: {len(unverified_user_ids)} 个")

    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


def test_test():
    """测试未充值用户查询功能"""
    try:
        # 导入要测试的函数
        from core.service import get_first_deposit_no_return_users_yesterday

        # 设置测试参数
        business_no = '39bac42a'  # 替换为实际的业务编号

        users1 = get_first_deposit_no_return_users_yesterday(
            business_no
        )
        logger.info(f"找到 {len(users1)} 个用户")
        logger.info(users1)

        # 输出测试结果摘要
        logger.info("\n=== 测试结果摘要 ===")
        logger.info(f"测试场景1: {len(users1)} 个用户")

    except Exception as e:
        logger.error(f"测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    # 初始化Django环境（如果在Django项目外运行）
    init_django()

    # 运行测试
    logger.info("开始测试")

    # 选择要运行的测试
    test_non_recharged_users()
    # test_non_beted_users()
    # test_get_high_bet_users_yesterday()
    # test_get_high_loss_users_yesterday()
    # test_get_inactive_high_recharge_users()
    # test_get_inactive_low_recharge_users()
    # test_test()

    logger.info("测试结束")
