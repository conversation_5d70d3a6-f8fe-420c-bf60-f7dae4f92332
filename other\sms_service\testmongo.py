from pymongo import MongoClient
from pymongo.errors import ServerSelectionTimeoutError

# MongoDB 连接配置
MONGO_URI = "*********************************************************************************************************"
DATABASE_NAME = "wingame_config"
COLLECTION_NAME = "c_autoSendSms"


def test_mongo_connection():
    try:
        # 创建 MongoDB 客户端
        client = MongoClient(MONGO_URI, serverSelectionTimeoutMS=5000)

        # 检测连接是否成功
        print("Connected to MongoDB!")

        # 选择数据库
        db = client[DATABASE_NAME]

        # 检查集合是否存在
        if COLLECTION_NAME in db.list_collection_names():
            print(f"Collection '{COLLECTION_NAME}' exists.")

            # 查询并打印集合中的数据
            collection = db[COLLECTION_NAME]
            documents = collection.find().limit(10)  # 限制只打印前 10 条数据

            print(f"Data in '{COLLECTION_NAME}':")
            for doc in documents:
                print(doc)
        else:
            print(f"Collection '{COLLECTION_NAME}' does NOT exist.")

        # 关闭连接
        client.close()

    except ServerSelectionTimeoutError as e:
        print(f"Failed to connect to MongoDB: {e}")
    except Exception as e:
        print(f"An error occurred: {e}")


# 执行测试
if __name__ == "__main__":
    test_mongo_connection()