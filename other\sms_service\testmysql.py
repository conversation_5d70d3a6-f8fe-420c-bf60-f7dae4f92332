import asyncio
import aiomysql

async def test_mysql_connection():
    try:
        _db_pool = await aiomysql.create_pool(
            host='************',
            port=3306,
            user='read',
            password='NWeGvmfMyrhxUP!Wkgp36',
            db='wgsaas',
            autocommit=True,
            minsize=1,
            maxsize=10,
            charset='utf8mb4',
            auth_plugin='mysql_native_password',  # 强制使用 mysql_native_password
        )
        async with _db_pool.acquire() as conn:
            async with conn.cursor() as cur:
                await cur.execute("SELECT DATABASE();")
                result = await cur.fetchone()
                print(f"Connected to database: {result[0]}")
        _db_pool.close()
        await _db_pool.wait_closed()
    except Exception as e:
        print(f"Error: {e}")

asyncio.run(test_mysql_connection())