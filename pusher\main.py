#!/usr/bin/env python3
"""
Telegram Bot 推送模块主程序
用于运维人员推送消息和菜单
"""
import asyncio
import argparse
import json
import sys
from pathlib import Path
from typing import List, Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import settings
from common.utils import setup_logging, validate_chat_id
from pusher.utils.message_sender import message_sender
from pusher.menu.menu_builder import menu_builder

import logging
logger = logging.getLogger(__name__)


class PusherCLI:
    """推送模块命令行界面"""
    
    def __init__(self):
        self.parser = self.create_parser()
    
    def create_parser(self) -> argparse.ArgumentParser:
        """创建命令行参数解析器"""
        parser = argparse.ArgumentParser(
            description="Telegram Bot 推送工具",
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog="""
使用示例:
  # 发送文本消息到单个聊天
  python pusher/main.py send-text --chat-id 123456789 --text "Hello World"
  
  # 发送菜单到多个聊天
  python pusher/main.py send-menu --chat-list subscribers --menu main_menu
  
  # 发送富媒体消息（图片+菜单）
  python pusher/main.py send-rich --chat-list all_users --config message_config.json
  
  # 创建菜单模板
  python pusher/main.py create-menu --name welcome_menu
  
  # 管理聊天列表
  python pusher/main.py manage-list --name subscribers --add 123456789,987654321
            """
        )
        
        subparsers = parser.add_subparsers(dest='command', help='可用命令')
        
        # 发送文本消息
        send_text_parser = subparsers.add_parser('send-text', help='发送文本消息')
        send_text_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_text_parser.add_argument('--chat-list', help='聊天列表名称')
        send_text_parser.add_argument('--text', required=True, help='消息内容')
        
        # 发送菜单
        send_menu_parser = subparsers.add_parser('send-menu', help='发送菜单')
        send_menu_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_menu_parser.add_argument('--chat-list', help='聊天列表名称')
        send_menu_parser.add_argument('--menu', required=True, help='菜单模板名称')

        # 发送图片
        send_photo_parser = subparsers.add_parser('send-photo', help='发送图片')
        send_photo_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_photo_parser.add_argument('--chat-list', help='聊天列表名称')
        send_photo_parser.add_argument('--photo', required=True, help='图片文件路径')
        send_photo_parser.add_argument('--caption', help='图片说明文字')

        # 发送动图
        send_animation_parser = subparsers.add_parser('send-animation', help='发送动图')
        send_animation_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_animation_parser.add_argument('--chat-list', help='聊天列表名称')
        send_animation_parser.add_argument('--animation', required=True, help='动图文件路径')
        send_animation_parser.add_argument('--caption', help='动图说明文字')

        # 发送带菜单的图片
        send_photo_menu_parser = subparsers.add_parser('send-photo-menu', help='发送带菜单的图片')
        send_photo_menu_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_photo_menu_parser.add_argument('--chat-list', help='聊天列表名称')
        send_photo_menu_parser.add_argument('--photo', required=True, help='图片文件路径')
        send_photo_menu_parser.add_argument('--menu', required=True, help='菜单模板名称')
        send_photo_menu_parser.add_argument('--caption', help='图片说明文字')

        # 发送带菜单的动图
        send_animation_menu_parser = subparsers.add_parser('send-animation-menu', help='发送带菜单的动图')
        send_animation_menu_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_animation_menu_parser.add_argument('--chat-list', help='聊天列表名称')
        send_animation_menu_parser.add_argument('--animation', required=True, help='动图文件路径')
        send_animation_menu_parser.add_argument('--menu', required=True, help='菜单模板名称')
        send_animation_menu_parser.add_argument('--caption', help='动图说明文字')
        
        # 发送富媒体消息
        send_rich_parser = subparsers.add_parser('send-rich', help='发送富媒体消息')
        send_rich_parser.add_argument('--chat-id', type=int, help='目标聊天ID')
        send_rich_parser.add_argument('--chat-list', help='聊天列表名称')
        send_rich_parser.add_argument('--config', required=True, help='消息配置文件路径')
        
        # 创建菜单
        create_menu_parser = subparsers.add_parser('create-menu', help='创建菜单模板')
        create_menu_parser.add_argument('--name', required=True, help='菜单名称')
        create_menu_parser.add_argument('--type', choices=['main', 'services', 'contact'], 
                                      default='main', help='菜单类型')
        
        # 管理聊天列表
        manage_list_parser = subparsers.add_parser('manage-list', help='管理聊天列表')
        manage_list_parser.add_argument('--name', required=True, help='列表名称')
        manage_list_parser.add_argument('--add', help='添加聊天ID（逗号分隔）')
        manage_list_parser.add_argument('--remove', help='移除聊天ID（逗号分隔）')
        manage_list_parser.add_argument('--list', action='store_true', help='显示列表内容')
        manage_list_parser.add_argument('--description', help='列表描述')
        
        return parser
    
    def get_chat_ids(self, args) -> List[int]:
        """获取目标聊天ID列表"""
        chat_ids = []
        
        if args.chat_id:
            chat_ids.append(args.chat_id)
        elif args.chat_list:
            chat_ids = message_sender.load_chat_list(args.chat_list)
            if not chat_ids:
                logger.warning(f"Chat list '{args.chat_list}' is empty or not found")
        
        return chat_ids
    
    async def send_text_command(self, args):
        """执行发送文本消息命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False
        
        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_text_message(chat_ids[0], args.text)
            if success:
                print(f"✅ 消息已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            message_config = {'text': args.text}
            results = await message_sender.broadcast_message(chat_ids, message_config)
            print(f"📊 广播结果：成功 {results['success']}/{results['total']}")
            if results['failed'] > 0:
                print(f"❌ 失败的聊天：{results['failed_chats']}")
            return results['failed'] == 0
    
    async def send_menu_command(self, args):
        """执行发送菜单命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False
        
        # 检查菜单是否存在
        menu_data = menu_builder.load_menu_template(args.menu)
        if not menu_data:
            print(f"错误：菜单模板 '{args.menu}' 不存在")
            return False
        
        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_menu_message(chat_ids[0], args.menu)
            if success:
                print(f"✅ 菜单已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            message_config = {'menu': args.menu}
            results = await message_sender.broadcast_message(chat_ids, message_config)
            print(f"📊 广播结果：成功 {results['success']}/{results['total']}")
            if results['failed'] > 0:
                print(f"❌ 失败的聊天：{results['failed_chats']}")
            return results['failed'] == 0
    
    async def send_rich_command(self, args):
        """执行发送富媒体消息命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False
        
        # 加载消息配置
        config_path = Path(args.config)
        if not config_path.exists():
            print(f"错误：配置文件 '{args.config}' 不存在")
            return False
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                message_config = json.load(f)
        except Exception as e:
            print(f"错误：无法解析配置文件：{e}")
            return False
        
        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_rich_message(chat_ids[0], message_config)
            if success:
                print(f"✅ 富媒体消息已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            results = await message_sender.broadcast_message(chat_ids, message_config)
            print(f"📊 广播结果：成功 {results['success']}/{results['total']}")
            if results['failed'] > 0:
                print(f"❌ 失败的聊天：{results['failed_chats']}")
            return results['failed'] == 0
    
    def create_menu_command(self, args):
        """执行创建菜单命令"""
        if args.type == 'main':
            menu_data = menu_builder.create_main_menu()
        elif args.type == 'services':
            menu_data = menu_builder.create_services_menu()
        elif args.type == 'contact':
            menu_data = menu_builder.create_contact_menu()
        else:
            print(f"错误：不支持的菜单类型 '{args.type}'")
            return False
        
        # 如果指定了自定义名称，保存为新模板
        if args.name != args.type + '_menu':
            success = menu_builder.save_menu_template(args.name, menu_data)
            if success:
                print(f"✅ 菜单模板 '{args.name}' 创建成功")
            else:
                print(f"❌ 创建菜单模板 '{args.name}' 失败")
            return success
        else:
            print(f"✅ 默认菜单模板 '{args.name}' 已创建")
            return True
    
    def manage_list_command(self, args):
        """执行管理聊天列表命令"""
        if args.list:
            # 显示列表内容
            chat_ids = message_sender.load_chat_list(args.name)
            if chat_ids:
                print(f"📋 聊天列表 '{args.name}' ({len(chat_ids)} 个聊天):")
                for chat_id in chat_ids:
                    print(f"  - {chat_id}")
            else:
                print(f"📋 聊天列表 '{args.name}' 为空或不存在")
            return True
        
        # 加载现有列表
        existing_chat_ids = message_sender.load_chat_list(args.name)
        chat_ids = set(existing_chat_ids)
        
        # 添加聊天ID
        if args.add:
            new_ids = [validate_chat_id(id_str.strip()) for id_str in args.add.split(',')]
            new_ids = [id for id in new_ids if id is not None]
            chat_ids.update(new_ids)
            print(f"➕ 添加了 {len(new_ids)} 个聊天ID")
        
        # 移除聊天ID
        if args.remove:
            remove_ids = [validate_chat_id(id_str.strip()) for id_str in args.remove.split(',')]
            remove_ids = [id for id in remove_ids if id is not None]
            for id in remove_ids:
                chat_ids.discard(id)
            print(f"➖ 移除了 {len(remove_ids)} 个聊天ID")
        
        # 保存列表
        success = message_sender.save_chat_list(
            args.name, 
            list(chat_ids), 
            args.description or f"聊天列表 {args.name}"
        )
        
        if success:
            print(f"✅ 聊天列表 '{args.name}' 已保存 ({len(chat_ids)} 个聊天)")
        else:
            print(f"❌ 保存聊天列表 '{args.name}' 失败")
        
        return success
    
    async def send_photo_command(self, args):
        """执行发送图片命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False

        # 检查图片文件是否存在
        if not Path(args.photo).exists():
            print(f"错误：图片文件不存在：{args.photo}")
            return False

        caption = args.caption or ""

        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_photo_message(chat_ids[0], args.photo, caption)
            if success:
                print(f"✅ 图片已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            message_config = {'image': args.photo, 'image_caption': caption}
            results = await message_sender.broadcast_message(chat_ids, message_config)
            print(f"📊 广播结果：成功 {results['success']}/{results['total']}")
            if results['failed'] > 0:
                print(f"❌ 失败的聊天：{results['failed_chats']}")
            return results['failed'] == 0

    async def send_animation_command(self, args):
        """执行发送动图命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False

        # 检查动图文件是否存在
        if not Path(args.animation).exists():
            print(f"错误：动图文件不存在：{args.animation}")
            return False

        caption = args.caption or ""

        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_animation_message(chat_ids[0], args.animation, caption)
            if success:
                print(f"✅ 动图已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送（需要扩展message_config支持动图）
            print("⚠️ 批量发送动图功能待实现")
            return False

    async def send_photo_menu_command(self, args):
        """执行发送带菜单的图片命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False

        # 检查图片文件是否存在
        if not Path(args.photo).exists():
            print(f"错误：图片文件不存在：{args.photo}")
            return False

        caption = args.caption or ""

        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_photo_with_menu(chat_ids[0], args.photo, args.menu, caption)
            if success:
                print(f"✅ 带菜单的图片已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            print("📤 批量发送带菜单的图片...")
            success_count = 0
            for chat_id in chat_ids:
                if await message_sender.send_photo_with_menu(chat_id, args.photo, args.menu, caption):
                    success_count += 1
                await asyncio.sleep(0.1)  # 避免速率限制

            print(f"📊 批量发送结果：成功 {success_count}/{len(chat_ids)}")
            return success_count == len(chat_ids)

    async def send_animation_menu_command(self, args):
        """执行发送带菜单的动图命令"""
        chat_ids = self.get_chat_ids(args)
        if not chat_ids:
            print("错误：未指定有效的聊天ID或聊天列表")
            return False

        # 检查动图文件是否存在
        if not Path(args.animation).exists():
            print(f"错误：动图文件不存在：{args.animation}")
            return False

        caption = args.caption or ""

        if len(chat_ids) == 1:
            # 单个聊天
            success = await message_sender.send_animation_with_menu(chat_ids[0], args.animation, args.menu, caption)
            if success:
                print(f"✅ 带菜单的动图已发送到聊天 {chat_ids[0]}")
            else:
                print(f"❌ 发送到聊天 {chat_ids[0]} 失败")
            return success
        else:
            # 批量发送
            print("📤 批量发送带菜单的动图...")
            success_count = 0
            for chat_id in chat_ids:
                if await message_sender.send_animation_with_menu(chat_id, args.animation, args.menu, caption):
                    success_count += 1
                await asyncio.sleep(0.1)  # 避免速率限制

            print(f"📊 批量发送结果：成功 {success_count}/{len(chat_ids)}")
            return success_count == len(chat_ids)

    async def run(self, args):
        """运行命令"""
        if args.command == 'send-text':
            return await self.send_text_command(args)
        elif args.command == 'send-menu':
            return await self.send_menu_command(args)
        elif args.command == 'send-photo':
            return await self.send_photo_command(args)
        elif args.command == 'send-animation':
            return await self.send_animation_command(args)
        elif args.command == 'send-photo-menu':
            return await self.send_photo_menu_command(args)
        elif args.command == 'send-animation-menu':
            return await self.send_animation_menu_command(args)
        elif args.command == 'send-rich':
            return await self.send_rich_command(args)
        elif args.command == 'create-menu':
            return self.create_menu_command(args)
        elif args.command == 'manage-list':
            return self.manage_list_command(args)
        else:
            self.parser.print_help()
            return False


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger.info("Starting Telegram Bot Pusher Module")
    
    # 检查配置
    try:
        token = settings.bot_token
        logger.info("Bot token loaded successfully")
        if settings.use_proxy:
            logger.info(f"Proxy enabled: {settings.proxy_url}")
    except Exception as e:
        print(f"配置错误：{e}")
        return 1
    
    # 创建CLI并解析参数
    cli = PusherCLI()
    args = cli.parser.parse_args()
    
    if not args.command:
        cli.parser.print_help()
        return 0
    
    try:
        success = await cli.run(args)
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        return 0
    except Exception as e:
        logger.error(f"执行命令时发生错误：{e}")
        print(f"错误：{e}")
        return 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(0)
    except Exception as e:
        logger.error(f"致命错误：{e}")
        sys.exit(1)
