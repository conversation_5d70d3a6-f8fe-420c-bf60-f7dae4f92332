"""
菜单构建器
用于创建和管理Telegram Bot菜单
支持Bot级别和群组级别的菜单定制
"""
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
from telegram import InlineKeyboardButton, InlineKeyboardMarkup

# 尝试导入WebApp，使用多种方式确保兼容性
WebApp = None
try:
    from telegram._webappinfo import WebAppInfo as WebApp
except ImportError:
    try:
        from telegram import WebApp
    except ImportError:
        try:
            from telegram.types import WebApp
        except ImportError:
            # 创建一个简单的WebApp替代类
            class WebApp:
                def __init__(self, url):
                    self.url = url

                def __str__(self):
                    return f"WebApp(url='{self.url}')"
from .menu_config_manager import menu_config_manager

logger = logging.getLogger(__name__)


class MenuBuilder:
    """菜单构建器类"""
    
    def __init__(self, menu_data_path: str = "data/menus"):
        self.menu_data_path = Path(menu_data_path)
        self.menu_data_path.mkdir(parents=True, exist_ok=True)
    
    def create_inline_keyboard(self, buttons: List[List[Dict[str, str]]]) -> InlineKeyboardMarkup:
        """创建内联键盘"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for button in row:
                if 'url' in button:
                    # URL按钮
                    keyboard_row.append(
                        InlineKeyboardButton(
                            text=button['text'],
                            url=button['url']
                        )
                    )
                elif 'callback_data' in button:
                    # 回调按钮
                    keyboard_row.append(
                        InlineKeyboardButton(
                            text=button['text'],
                            callback_data=button['callback_data']
                        )
                    )
                elif 'web_app' in button:
                    # WebApp按钮
                    web_app_url = button['web_app'].get('url') if isinstance(button['web_app'], dict) else button['web_app']
                    keyboard_row.append(
                        InlineKeyboardButton(
                            text=button['text'],
                            web_app=WebApp(url=web_app_url)
                        )
                    )
            if keyboard_row:
                keyboard.append(keyboard_row)
        
        return InlineKeyboardMarkup(keyboard)
    
    def get_menu_for_context(self, bot_token: str, chat_type: str,
                            group_id: Optional[int] = None) -> Optional[Dict[str, Any]]:
        """
        根据上下文获取菜单

        Args:
            bot_token: Bot Token
            chat_type: 聊天类型 ('private' 或 'group')
            group_id: 群组ID（群聊时提供）

        Returns:
            菜单数据
        """
        try:
            # 获取菜单模板名称
            template_name = menu_config_manager.get_menu_template(bot_token, chat_type, group_id)

            # 加载菜单模板
            menu_data = self.load_menu_template(template_name)

            if menu_data:
                # 添加上下文信息
                menu_data["_context"] = {
                    "bot_token": bot_token[:10] + "...",
                    "chat_type": chat_type,
                    "group_id": group_id,
                    "template_used": template_name
                }

                # 获取Bot和群组信息
                bot_info = menu_config_manager.get_bot_info(bot_token)
                menu_data["_bot_info"] = bot_info

                if group_id:
                    group_info = menu_config_manager.get_group_info(bot_token, group_id)
                    menu_data["_group_info"] = group_info

            return menu_data

        except Exception as e:
            logger.error(f"获取上下文菜单失败: {e}")
            # 降级到默认模板
            fallback_template = "private_menu" if chat_type == "private" else "group_menu"
            return self.load_menu_template(fallback_template)

    def load_menu_template(self, template_name: str) -> Optional[Dict[str, Any]]:
        """加载菜单模板"""
        template_path = self.menu_data_path / f"{template_name}.json"
        try:
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"Failed to load menu template {template_name}: {e}")
        return None
    
    def save_menu_template(self, template_name: str, menu_data: Dict[str, Any]) -> bool:
        """保存菜单模板"""
        template_path = self.menu_data_path / f"{template_name}.json"
        try:
            with open(template_path, 'w', encoding='utf-8') as f:
                json.dump(menu_data, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"Failed to save menu template {template_name}: {e}")
            return False
    
    def create_main_menu(self) -> Dict[str, Any]:
        """创建主菜单"""
        menu_data = {
            "title": "🏠 主菜单",
            "description": "欢迎使用我们的服务！请选择您需要的功能：",
            "image": None,
            "buttons": [
                [
                    {"text": "📋 服务介绍", "callback_data": "menu_services"},
                    {"text": "💬 联系我们", "callback_data": "menu_contact"}
                ],
                [
                    {"text": "❓ 帮助中心", "callback_data": "menu_help"},
                    {"text": "⚙️ 设置", "callback_data": "menu_settings"}
                ],
                [
                    {"text": "🌐 官方网站", "url": "https://example.com"}
                ]
            ]
        }
        
        # 保存模板
        self.save_menu_template("main_menu", menu_data)
        return menu_data
    
    def create_services_menu(self) -> Dict[str, Any]:
        """创建服务菜单"""
        menu_data = {
            "title": "📋 我们的服务",
            "description": "我们提供以下专业服务：",
            "image": None,
            "buttons": [
                [
                    {"text": "🔧 技术支持", "callback_data": "service_tech"},
                    {"text": "📊 数据分析", "callback_data": "service_data"}
                ],
                [
                    {"text": "🎨 设计服务", "callback_data": "service_design"},
                    {"text": "📱 应用开发", "callback_data": "service_app"}
                ],
                [
                    {"text": "🔙 返回主菜单", "callback_data": "menu_main"}
                ]
            ]
        }
        
        self.save_menu_template("services_menu", menu_data)
        return menu_data
    
    def create_contact_menu(self) -> Dict[str, Any]:
        """创建联系菜单"""
        menu_data = {
            "title": "💬 联系我们",
            "description": "有任何问题？我们随时为您服务！",
            "image": None,
            "buttons": [
                [
                    {"text": "📧 发送邮件", "url": "mailto:<EMAIL>"},
                    {"text": "📞 电话咨询", "callback_data": "contact_phone"}
                ],
                [
                    {"text": "💬 在线客服", "callback_data": "contact_chat"},
                    {"text": "📍 地址信息", "callback_data": "contact_address"}
                ],
                [
                    {"text": "🔙 返回主菜单", "callback_data": "menu_main"}
                ]
            ]
        }
        
        self.save_menu_template("contact_menu", menu_data)
        return menu_data
    
    def build_menu_message(self, menu_data: Dict[str, Any]) -> tuple:
        """构建菜单消息"""
        # 构建消息文本
        message_text = f"{menu_data['title']}\n\n{menu_data['description']}"
        
        # 构建键盘
        keyboard = self.create_inline_keyboard(menu_data['buttons'])
        
        return message_text, keyboard
    
    def get_menu_by_callback(self, callback_data: str) -> Optional[Dict[str, Any]]:
        """根据回调数据获取菜单"""
        menu_mapping = {
            "menu_main": "main_menu",
            "menu_services": "services_menu",
            "menu_contact": "contact_menu",
            "menu_help": "help_menu",
            "menu_settings": "settings_menu"
        }
        
        template_name = menu_mapping.get(callback_data)
        if template_name:
            menu_data = self.load_menu_template(template_name)
            if menu_data:
                return menu_data
            
            # 如果模板不存在，创建默认菜单
            if template_name == "main_menu":
                return self.create_main_menu()
            elif template_name == "services_menu":
                return self.create_services_menu()
            elif template_name == "contact_menu":
                return self.create_contact_menu()
        
        return None


# 全局菜单构建器实例
menu_builder = MenuBuilder()
