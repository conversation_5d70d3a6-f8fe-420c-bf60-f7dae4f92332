"""
菜单配置管理器
支持Bot级别和群组级别的菜单定制
"""
import json
import logging
import sys
from typing import Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from config.settings import settings

logger = logging.getLogger(__name__)


class MenuConfigManager:
    """菜单配置管理器"""
    
    def __init__(self, config_path: Optional[str] = None):
        if config_path is None:
            config_path = settings.menu_config_path
        self.config_path = Path(config_path)
        self.config_data = {}
        self.load_config()
    
    def load_config(self):
        """加载菜单配置"""
        try:
            if self.config_path.exists():
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    self.config_data = json.load(f)
                logger.info(f"菜单配置加载成功: {self.config_path}")
            else:
                logger.warning(f"菜单配置文件不存在: {self.config_path}")
                self.config_data = self._get_default_config()
        except Exception as e:
            logger.error(f"加载菜单配置失败: {e}")
            self.config_data = self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "menu_configuration": {
                "system_default": {
                    "private_menu": "private_menu",
                    "group_menu": "group_menu"
                },
                "bot_configs": {},
                "group_custom_configs": {}
            }
        }
    
    def get_menu_template(self, bot_token: str, chat_type: str, 
                         group_id: Optional[int] = None) -> str:
        """
        获取菜单模板名称
        
        Args:
            bot_token: Bot Token
            chat_type: 聊天类型 ('private' 或 'group')
            group_id: 群组ID（群聊时提供）
            
        Returns:
            菜单模板名称
        """
        try:
            config = self.config_data.get("menu_configuration", {})
            
            # 1. 优先检查群组定制配置
            if group_id and chat_type == "group":
                group_config = self._get_group_custom_config(bot_token, group_id)
                if group_config:
                    custom_menu = group_config.get("custom_menus", {}).get("group_menu")
                    if custom_menu:
                        logger.info(f"使用群组定制菜单: {custom_menu} (Bot: {bot_token[:10]}..., Group: {group_id})")
                        return custom_menu
            
            # 2. 检查Bot默认配置
            bot_config = self._get_bot_config(bot_token)
            if bot_config:
                menu_key = "private_menu" if chat_type == "private" else "group_menu"
                bot_menu = bot_config.get("default_menus", {}).get(menu_key)
                if bot_menu:
                    logger.info(f"使用Bot默认菜单: {bot_menu} (Bot: {bot_token[:10]}..., Type: {chat_type})")
                    return bot_menu
            
            # 3. 使用系统默认配置
            system_default = config.get("system_default", {})
            menu_key = "private_menu" if chat_type == "private" else "group_menu"
            default_menu = system_default.get(menu_key, menu_key)
            
            logger.info(f"使用系统默认菜单: {default_menu} (Bot: {bot_token[:10]}..., Type: {chat_type})")
            return default_menu
            
        except Exception as e:
            logger.error(f"获取菜单模板失败: {e}")
            # 降级到硬编码默认值
            return "private_menu" if chat_type == "private" else "group_menu"
    
    def _get_bot_config(self, bot_token: str) -> Optional[Dict[str, Any]]:
        """获取Bot配置"""
        try:
            bot_configs = self.config_data.get("menu_configuration", {}).get("bot_configs", {})
            return bot_configs.get(bot_token)
        except Exception as e:
            logger.error(f"获取Bot配置失败: {e}")
            return None
    
    def _get_group_custom_config(self, bot_token: str, group_id: int) -> Optional[Dict[str, Any]]:
        """获取群组定制配置"""
        try:
            group_configs = self.config_data.get("menu_configuration", {}).get("group_custom_configs", {})
            bot_group_configs = group_configs.get(bot_token, {})
            return bot_group_configs.get(str(group_id))
        except Exception as e:
            logger.error(f"获取群组定制配置失败: {e}")
            return None
    
    def get_bot_info(self, bot_token: str) -> Dict[str, Any]:
        """获取Bot信息"""
        bot_config = self._get_bot_config(bot_token)
        if bot_config:
            return {
                "bot_name": bot_config.get("bot_name", "未知Bot"),
                "description": bot_config.get("description", ""),
                "features": bot_config.get("features", [])
            }
        return {
            "bot_name": "默认Bot",
            "description": "使用系统默认配置",
            "features": []
        }
    
    def get_group_info(self, bot_token: str, group_id: int) -> Dict[str, Any]:
        """获取群组信息"""
        group_config = self._get_group_custom_config(bot_token, group_id)
        if group_config:
            return {
                "group_name": group_config.get("group_name", "未知群组"),
                "description": group_config.get("description", ""),
                "enabled_features": group_config.get("enabled_features", [])
            }
        return {
            "group_name": "默认群组",
            "description": "使用默认配置",
            "enabled_features": []
        }
    
    def is_feature_enabled(self, bot_token: str, feature: str, 
                          group_id: Optional[int] = None) -> bool:
        """检查功能是否启用"""
        try:
            # 检查群组级别的功能配置
            if group_id:
                group_config = self._get_group_custom_config(bot_token, group_id)
                if group_config:
                    enabled_features = group_config.get("enabled_features", [])
                    if enabled_features:  # 如果有明确配置，以群组配置为准
                        return feature in enabled_features
            
            # 检查Bot级别的功能配置
            bot_config = self._get_bot_config(bot_token)
            if bot_config:
                features = bot_config.get("features", [])
                return feature in features
            
            # 默认启用所有功能
            return True
            
        except Exception as e:
            logger.error(f"检查功能启用状态失败: {e}")
            return True
    
    def get_available_templates(self) -> Dict[str, str]:
        """获取可用的菜单模板"""
        try:
            templates = self.config_data.get("menu_configuration", {}).get("menu_templates", {})
            return templates.get("template_descriptions", {})
        except Exception as e:
            logger.error(f"获取可用模板失败: {e}")
            return {}
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("重新加载菜单配置...")
        self.load_config()


# 创建全局实例
menu_config_manager = MenuConfigManager()
