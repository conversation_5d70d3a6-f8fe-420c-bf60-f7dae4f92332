#!/usr/bin/env python3
"""
模板消息处理器
处理基于notify表的模板消息推送
"""
import logging
from typing import Dict, List, Any, Optional, Union

from scheduler.config_manager import config_manager
from common.translation_manager import translation_manager
from pusher.utils.message_sender import message_sender

logger = logging.getLogger(__name__)


class TemplateMessageHandler:
    """模板消息处理器"""
    
    def __init__(self):
        pass
        
    async def send_template_message(self, business_no: str, message_type: int, params: List[Any], activity_id: Optional[int] = None) -> Dict:
        """发送模板消息"""
        try:
            logger.info("🎯 开始处理模板消息")
            logger.info(f"   📋 输入参数: business_no={business_no}, type={message_type}")
            logger.info(f"   📋 参数列表: {params}")
            logger.info(f"   📋 活动ID: {activity_id}")
            logger.info(f"   📋 目标聊天: 从notify配置获取")

            # 1. 根据type查找对应的notify配置
            logger.info(f"🔍 步骤1: 查找notify配置...")
            notify_config = self._find_notify_config_by_type(message_type)
            if not notify_config:
                logger.error(f"❌ 未找到类型为 {message_type} 的通知配置")
                return {
                    "success": False,
                    "message": f"未找到类型为 {message_type} 的通知配置"
                }

            logger.info(f"✅ 找到notify配置: ID={notify_config.get('id')}, 名称='{notify_config.get('tgName')}'")
            logger.info(f"   配置详情: language={notify_config.get('language')}, target={notify_config.get('notifyTarget')}")
            
            # 2. 获取Bot配置
            logger.info(f"🤖 步骤2: 获取Bot配置...")
            bot_info = config_manager.get_bot_info(business_no)
            if not bot_info:
                logger.error(f"❌ 未找到商户 {business_no} 的Bot配置")
                return {
                    "success": False,
                    "message": f"未找到商户 {business_no} 的Bot配置"
                }

            logger.info(f"✅ 找到Bot配置: bot_name={bot_info.get('bot_name')}")
            logger.info(f"   Token: {bot_info.get('bot_token', '')[:20]}...")

            # 3. 确定目标聊天
            logger.info(f"🎯 步骤3: 从notify配置获取目标聊天...")
            chat_ids = notify_config.get("notifyTarget", [])
            if not chat_ids:
                logger.error(f"❌ notify配置中未指定目标聊天")
                return {
                    "success": False,
                    "message": "notify配置中未指定目标聊天"
                }

            logger.info(f"✅ 目标聊天: {chat_ids}")

            # 4. 获取语言设置
            logger.info(f"🌐 步骤4: 获取语言设置...")
            language_id = notify_config.get("language", 1)  # 默认英文
            language_id_str = translation_manager.get_language_id(language_id)

            # 确保翻译管理器已初始化
            if not translation_manager.language_mapping:
                logger.warning("⚠️ 翻译管理器未初始化，正在加载...")
                translation_manager.load_translations()

            language_name = translation_manager.language_mapping.get(language_id_str, f"Unknown({language_id_str})")

            logger.info(f"✅ 语言设置: ID={language_id_str}, 名称={language_name}")
            logger.info(f"📋 可用语言映射: {list(translation_manager.language_mapping.keys())[:5]}...")
            
            # 5. 渲染消息模板
            logger.info(f"📝 步骤5: 渲染消息模板...")
            template_text = notify_config.get("text", "")
            if not template_text or template_text.strip() == "":
                logger.error(f"❌ 消息模板为空")
                return {
                    "success": False,
                    "message": "消息模板为空"
                }

            logger.info(f"📄 原始模板: {template_text}")
            logger.info(f"🔄 开始翻译和渲染...")

            rendered_message = translation_manager.render_template(
                template_text, params, language_id_str, activity_id=activity_id
            )

            logger.info(f"✅ 渲染完成")
            logger.info(f"📄 渲染结果: {rendered_message}")
            
            # 6. 发送消息到各个目标聊天
            results = []

            # 获取消息相关配置
            image_url = self._get_image_url(notify_config)
            jump_link = notify_config.get("jumpLink", "")
            tg_link = notify_config.get("tgLink", "")
            link_text = notify_config.get("urlLabel", "")
            link_type = self._get_link_type(notify_config)

            logger.info(f"📋 消息配置:")
            logger.info(f"   图片URL: {image_url}")
            logger.info(f"   跳转链接: {jump_link}")
            logger.info(f"   TG链接: {tg_link}")
            logger.info(f"   链接文本: {link_text}")
            logger.info(f"   链接类型: {link_type}")

            for chat_id in chat_ids:
                try:
                    logger.info(f"📤 发送消息到聊天: {chat_id}")

                    # 创建Bot客户端实例
                    from common.bot_client import TelegramBotClient
                    bot_client = TelegramBotClient(token=bot_info["bot_token"])

                    # 根据是否有图片选择发送方式
                    if image_url:
                        # 发送图片消息
                        try:
                            await bot_client.send_photo(
                                chat_id=chat_id,
                                photo=image_url,
                                caption=rendered_message,
                                parse_mode='Markdown'
                            )
                            success = True
                        except Exception as e:
                            logger.error(f"发送图片消息失败: {e}")
                            success = False
                    else:
                        # 发送文本消息
                        try:
                            await bot_client.send_message(
                                chat_id=chat_id,
                                text=rendered_message,
                                parse_mode='Markdown'
                            )
                            success = True
                        except Exception as e:
                            logger.error(f"发送文本消息失败: {e}")
                            success = False

                    results.append({
                        "chat_id": chat_id,
                        "success": success,
                        "message": "发送成功" if success else "发送失败"
                    })

                    if success:
                        logger.info(f"✅ 消息发送成功: {chat_id}")
                    else:
                        logger.error(f"❌ 消息发送失败: {chat_id}")

                except Exception as e:
                    logger.error(f"❌ 发送消息到 {chat_id} 失败: {e}")
                    results.append({
                        "chat_id": chat_id,
                        "success": False,
                        "message": f"发送异常: {str(e)}"
                    })
            
            # 8. 统计结果
            success_count = sum(1 for r in results if r["success"])
            total_count = len(results)
            
            return {
                "success": success_count > 0,
                "message": f"发送完成: {success_count}/{total_count} 成功",
                "details": {
                    "template_id": notify_config.get("id"),
                    "template_name": notify_config.get("tgName"),
                    "language": language_id_str,
                    "rendered_message": rendered_message,
                    "target_chats": chat_ids,
                    "success_count": success_count,
                    "total_count": total_count,
                    "results": results
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 发送模板消息失败: {e}")
            return {
                "success": False,
                "message": f"发送失败: {str(e)}"
            }
    
    def _find_notify_config_by_type(self, message_type: int) -> Optional[Dict]:
        """根据消息类型查找notify配置"""
        try:
            # 获取所有notify配置（不限制notifyType）
            all_configs = config_manager.get_all_notify_configs()

            logger.info(f"🔍 查找消息类型: {message_type} (类型: {type(message_type).__name__})")
            logger.info(f"📋 可用的notify配置: {len(all_configs)} 个")

            # 直接使用int类型进行精确匹配
            for config in all_configs:
                config_types = config.get("types", [])
                config_id = config.get("id")
                config_name = config.get("tgName", "")
                notify_type = config.get("notifyType", "")

                logger.info(f"   检查配置 {config_id}: {config_name}, types={config_types}, notifyType={notify_type}")

                if message_type in config_types:
                    logger.info(f"✅ 精确匹配: type={message_type} -> 配置ID={config_id}")
                    return config

            # 3. 默认匹配：使用第一个可用配置
            if all_configs:
                default_config = all_configs[0]
                logger.warning(f"⚠️ 未找到类型 {message_type} 的匹配，使用默认配置: {default_config.get('tgName')}")
                return default_config

            logger.error(f"❌ 没有可用的notify配置")
            return None

        except Exception as e:
            logger.error(f"❌ 查找notify配置失败: {e}")
            return None
    
    def _get_image_url(self, notify_config: Dict) -> str:
        """获取图片URL"""
        banner = notify_config.get("msgBanner", "")
        if banner and banner != "0" and banner != "":
            logger.debug(f"🖼️ 检查图片配置: msgBanner={banner}")

            # 支持多种图片格式
            if banner.startswith("http"):
                # 如果已经是完整URL，直接返回
                logger.debug(f"✅ 使用完整URL: {banner}")
                return banner
            else:
                # 构建图片URL - 暂时禁用，因为没有真实的图片服务器
                logger.warning(f"⚠️ msgBanner不是完整URL: {banner}，暂时跳过图片发送")
                return ""  # 返回空字符串，不发送图片

        logger.debug(f"📄 无图片配置，发送纯文本消息")
        return ""
    
    def _get_link_type(self, notify_config: Dict) -> str:
        """获取链接类型"""
        # 根据jumpLink和tgLink判断链接类型
        if notify_config.get("tgLink"):
            return "webapp"
        elif notify_config.get("jumpLink"):
            return "browser"
        else:
            return ""
    
    async def preview_template_message(self, business_no: str, message_type: str, params: List[Any]) -> Dict:
        """预览模板消息（不实际发送）"""
        try:
            # 查找配置
            notify_config = self._find_notify_config_by_type(message_type)
            if not notify_config:
                return {
                    "success": False,
                    "message": f"未找到类型为 {message_type} 的通知配置"
                }
            
            # 获取语言设置
            language_id = notify_config.get("language", 1)
            language_id_str = translation_manager.get_language_id(language_id)

            # 渲染模板
            template_text = notify_config.get("text", "")
            rendered_message = translation_manager.render_template(
                template_text, params, language_id_str, activity_id=activity_id
            )
            
            return {
                "success": True,
                "preview": {
                    "template_id": notify_config.get("id"),
                    "template_name": notify_config.get("tgName"),
                    "original_template": template_text,
                    "language": language_id_str,
                    "translated_params": translation_manager.translate_params(params, language_id_str),
                    "rendered_message": rendered_message,
                    "target_chats": notify_config.get("notifyTarget", []),
                    "image_url": self._get_image_url(notify_config),
                    "link": notify_config.get("jumpLink", ""),
                    "link_type": self._get_link_type(notify_config)
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 预览模板消息失败: {e}")
            return {
                "success": False,
                "message": f"预览失败: {str(e)}"
            }


# 全局模板消息处理器实例
template_handler = TemplateMessageHandler()
