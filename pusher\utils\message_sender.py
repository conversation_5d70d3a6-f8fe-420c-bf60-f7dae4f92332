"""
消息发送工具
用于批量发送消息、图片和菜单
"""
import asyncio
import logging
import re
import aiohttp
import uuid
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import json
from datetime import datetime

from common.bot_client import bot_client
from common.utils import validate_chat_id, get_timestamp
from pusher.menu.menu_builder import menu_builder

logger = logging.getLogger(__name__)


class MessageSender:
    """消息发送器类"""
    
    def __init__(self):
        self.sent_log_path = Path("data/sent_messages.json")
        self.sent_log_path.parent.mkdir(parents=True, exist_ok=True)
        self.rate_limit_delay = 0.1  # 发送间隔（秒）
    
    async def send_text_message(self, chat_id: int, text: str, link: str = None,
                               tg_short_link: str = None, link_text: str = None,
                               link_type: str = "browser", **kwargs) -> bool:
        """
        发送文本消息，支持在文本中嵌入链接

        Args:
            chat_id: 聊天ID
            text: 消息文本
            link: 普通链接URL
            tg_short_link: TG短链接URL
            link_type: 链接类型 ("browser" 或 "webapp")，暂时不影响文本链接
            **kwargs: 其他参数
        """
        try:
            # 如果有单独的link参数，使用格式化方法
            if link or tg_short_link:
                formatted_text = self._format_text_with_links(text, link, tg_short_link, link_text, link_type)
            else:
                # 否则检测文本中的URL并自动格式化
                formatted_text = self._auto_format_urls_in_text(text)

            await bot_client.send_message(
                chat_id=chat_id,
                text=formatted_text,
                parse_mode='HTML',  # 使用HTML格式支持链接和标签
                disable_web_page_preview=False,  # 允许显示链接预览
                **kwargs
            )
            logger.info(f"Text message with embedded links sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send text message to {chat_id}: {e}")
            return False
    
    async def send_photo_message(self, chat_id: int, photo_path: str, caption: str = "",
                                link: str = None, tg_short_link: str = None,
                                link_text: str = None, link_type: str = "browser", **kwargs) -> bool:
        """发送本地图片消息，支持在说明文字中嵌入链接"""
        try:
            photo_file = Path(photo_path)
            if not photo_file.exists():
                logger.error(f"Photo file not found: {photo_path}")
                return False

            # 构建带链接的说明文字
            formatted_caption = self._format_text_with_links(caption, link, tg_short_link, link_text, link_type)

            with open(photo_file, 'rb') as photo:
                await bot_client.send_photo(
                    chat_id=chat_id,
                    photo=photo,
                    caption=formatted_caption,
                    parse_mode='HTML',
                    **kwargs
                )

            logger.info(f"Photo message sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send photo message to {chat_id}: {e}")
            return False

    async def send_photo_url_message(self, chat_id: int, photo_url: str, caption: str = "",
                                    link: str = None, tg_short_link: str = None,
                                    link_text: str = None, link_type: str = "browser",
                                    download_image: bool = True, **kwargs) -> bool:
        """
        发送网络图片消息，支持在说明文字中嵌入链接

        Args:
            download_image: 是否下载图片后发送（True=发送真实图片，False=直接发送URL）
        """
        try:
            # 构建带链接的说明文字
            formatted_caption = self._format_text_with_links(caption, link, tg_short_link, link_text, link_type)

            if download_image:
                # 下载图片并发送
                return await self._download_and_send_photo(chat_id, photo_url, formatted_caption, **kwargs)
            else:
                # 直接发送URL
                await bot_client.send_photo(
                    chat_id=chat_id,
                    photo=photo_url,
                    caption=formatted_caption,
                    parse_mode='Markdown',
                    **kwargs
                )

                logger.info(f"Photo URL message sent to {chat_id}")
                return True

        except Exception as e:
            logger.error(f"Failed to send photo URL message to {chat_id}: {e}")
            return False

    async def _download_and_send_photo(self, chat_id: int, photo_url: str, caption: str = "", **kwargs) -> bool:
        """下载图片并发送，保存到项目的images/tmp目录"""
        saved_file_path = None
        try:
            logger.info(f"开始下载图片: {photo_url}")

            # 确保images/tmp目录存在
            images_tmp_dir = Path("images/tmp")
            images_tmp_dir.mkdir(parents=True, exist_ok=True)

            # 设置超时和请求头
            timeout = aiohttp.ClientTimeout(total=30)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            async with aiohttp.ClientSession(timeout=timeout, headers=headers) as session:
                async with session.get(photo_url) as response:
                    logger.info(f"HTTP响应状态: {response.status}")

                    if response.status == 200:
                        # 获取图片数据
                        image_data = await response.read()
                        logger.info(f"图片下载完成，大小: {len(image_data)} bytes")

                        # 生成唯一文件名
                        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                        unique_id = str(uuid.uuid4())[:8]

                        # 尝试从URL获取文件扩展名
                        file_extension = self._get_image_extension(photo_url, response.headers.get('content-type', ''))

                        filename = f"img_{timestamp}_{unique_id}{file_extension}"
                        saved_file_path = images_tmp_dir / filename

                        # 保存图片文件
                        with open(saved_file_path, 'wb') as img_file:
                            img_file.write(image_data)

                        logger.info(f"图片已保存: {saved_file_path}")

                        # 发送图片文件
                        with open(saved_file_path, 'rb') as photo_file:
                            await bot_client.send_photo(
                                chat_id=chat_id,
                                photo=photo_file,
                                caption=caption,
                                parse_mode='Markdown',
                                **kwargs
                            )

                        logger.info(f"Downloaded photo sent to {chat_id}, saved as {filename}")
                        return True
                    else:
                        logger.error(f"Failed to download image: HTTP {response.status}")
                        # 降级到直接发送URL
                        logger.info("降级到直接发送URL")
                        await bot_client.send_photo(
                            chat_id=chat_id,
                            photo=photo_url,
                            caption=caption,
                            parse_mode='Markdown',
                            **kwargs
                        )
                        return True

        except aiohttp.ClientError as e:
            logger.error(f"网络请求失败: {e}")
            # 降级到直接发送URL
            try:
                logger.info("降级到直接发送URL")
                await bot_client.send_photo(
                    chat_id=chat_id,
                    photo=photo_url,
                    caption=caption,
                    parse_mode='Markdown',
                    **kwargs
                )
                return True
            except Exception as fallback_e:
                logger.error(f"降级发送也失败: {fallback_e}")
                return False

        except Exception as e:
            logger.error(f"下载并发送图片失败: {e}")
            return False

        finally:
            # 不删除文件，保留在images/tmp目录中
            if saved_file_path:
                logger.info(f"图片已保存到: {saved_file_path}")
            pass

    async def send_animation_message(self, chat_id: int, animation_path: str, caption: str = "", **kwargs) -> bool:
        """发送动图消息（GIF等）"""
        try:
            animation_file = Path(animation_path)
            if not animation_file.exists():
                logger.error(f"Animation file not found: {animation_path}")
                return False

            with open(animation_file, 'rb') as animation:
                await bot_client.send_animation(
                    chat_id=chat_id,
                    animation=animation,
                    caption=caption,
                    **kwargs
                )

            logger.info(f"Animation message sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send animation message to {chat_id}: {e}")
            return False

    async def send_photo_with_menu(self, chat_id: int, photo_path: str, menu_name: str, caption: str = "", **kwargs) -> bool:
        """发送带菜单的图片消息"""
        try:
            from pusher.menu.menu_builder import menu_builder

            # 检查图片文件
            photo_file = Path(photo_path)
            if not photo_file.exists():
                logger.error(f"Photo file not found: {photo_path}")
                return False

            # 获取菜单
            menu_data = menu_builder.load_menu_template(menu_name)
            if not menu_data:
                logger.error(f"Menu template not found: {menu_name}")
                return False

            # 构建菜单键盘
            _, keyboard = menu_builder.build_menu_message(menu_data)

            # 发送带菜单的图片
            with open(photo_file, 'rb') as photo:
                await bot_client.send_photo(
                    chat_id=chat_id,
                    photo=photo,
                    caption=caption,
                    reply_markup=keyboard,
                    **kwargs
                )

            logger.info(f"Photo with menu '{menu_name}' sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send photo with menu to {chat_id}: {e}")
            return False

    async def send_animation_with_menu(self, chat_id: int, animation_path: str, menu_name: str, caption: str = "", **kwargs) -> bool:
        """发送带菜单的动图消息"""
        try:
            from pusher.menu.menu_builder import menu_builder

            # 检查动图文件
            animation_file = Path(animation_path)
            if not animation_file.exists():
                logger.error(f"Animation file not found: {animation_path}")
                return False

            # 获取菜单
            menu_data = menu_builder.load_menu_template(menu_name)
            if not menu_data:
                logger.error(f"Menu template not found: {menu_name}")
                return False

            # 构建菜单键盘
            _, keyboard = menu_builder.build_menu_message(menu_data)

            # 发送带菜单的动图
            with open(animation_file, 'rb') as animation:
                await bot_client.send_animation(
                    chat_id=chat_id,
                    animation=animation,
                    caption=caption,
                    reply_markup=keyboard,
                    **kwargs
                )

            logger.info(f"Animation with menu '{menu_name}' sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send animation with menu to {chat_id}: {e}")
            return False
    
    async def send_menu_message(self, chat_id: int, menu_name: str, **kwargs) -> bool:
        """发送菜单消息"""
        try:
            menu_data = menu_builder.load_menu_template(menu_name)
            if not menu_data:
                logger.error(f"Menu template not found: {menu_name}")
                return False
            
            message_text, keyboard = menu_builder.build_menu_message(menu_data)
            
            await bot_client.send_message(
                chat_id=chat_id,
                text=message_text,
                reply_markup=keyboard,
                **kwargs
            )
            
            logger.info(f"Menu message '{menu_name}' sent to {chat_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to send menu message to {chat_id}: {e}")
            return False
    
    async def send_rich_message(self, chat_id: int, message_config: Dict[str, Any]) -> bool:
        """发送富媒体消息（包含图片和菜单）"""
        try:
            success = True
            
            # 发送图片（如果有）
            if 'image' in message_config and message_config['image']:
                image_success = await self.send_photo_message(
                    chat_id=chat_id,
                    photo_path=message_config['image'],
                    caption=message_config.get('image_caption', '')
                )
                success = success and image_success
                
                # 添加延迟
                await asyncio.sleep(self.rate_limit_delay)
            
            # 发送菜单消息
            if 'menu' in message_config:
                menu_success = await self.send_menu_message(
                    chat_id=chat_id,
                    menu_name=message_config['menu']
                )
                success = success and menu_success
            elif 'text' in message_config:
                # 发送普通文本消息
                text_success = await self.send_text_message(
                    chat_id=chat_id,
                    text=message_config['text']
                )
                success = success and text_success
            
            return success
        except Exception as e:
            logger.error(f"Failed to send rich message to {chat_id}: {e}")
            return False
    
    async def broadcast_message(self, chat_ids: List[int], message_config: Dict[str, Any]) -> Dict[str, Any]:
        """广播消息到多个聊天"""
        results = {
            'total': len(chat_ids),
            'success': 0,
            'failed': 0,
            'failed_chats': []
        }
        
        logger.info(f"Starting broadcast to {len(chat_ids)} chats")
        
        for chat_id in chat_ids:
            try:
                success = await self.send_rich_message(chat_id, message_config)
                if success:
                    results['success'] += 1
                else:
                    results['failed'] += 1
                    results['failed_chats'].append(chat_id)
                
                # 添加延迟以避免触发速率限制
                await asyncio.sleep(self.rate_limit_delay)
                
            except Exception as e:
                logger.error(f"Error broadcasting to {chat_id}: {e}")
                results['failed'] += 1
                results['failed_chats'].append(chat_id)
        
        # 记录发送日志
        self.log_broadcast(message_config, results)
        
        logger.info(f"Broadcast completed: {results['success']} success, {results['failed']} failed")
        return results
    
    def log_broadcast(self, message_config: Dict[str, Any], results: Dict[str, Any]):
        """记录广播日志"""
        try:
            log_entry = {
                'timestamp': get_timestamp(),
                'message_config': message_config,
                'results': results
            }
            
            # 读取现有日志
            logs = []
            if self.sent_log_path.exists():
                with open(self.sent_log_path, 'r', encoding='utf-8') as f:
                    logs = json.load(f)
            
            # 添加新日志
            logs.append(log_entry)
            
            # 只保留最近100条记录
            logs = logs[-100:]
            
            # 保存日志
            with open(self.sent_log_path, 'w', encoding='utf-8') as f:
                json.dump(logs, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error(f"Failed to log broadcast: {e}")
    
    def load_chat_list(self, list_name: str) -> List[int]:
        """加载聊天列表"""
        list_path = Path(f"data/chat_lists/{list_name}.json")
        try:
            if list_path.exists():
                with open(list_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return [validate_chat_id(str(chat_id)) for chat_id in data.get('chat_ids', []) if validate_chat_id(str(chat_id))]
        except Exception as e:
            logger.error(f"Failed to load chat list {list_name}: {e}")
        return []
    
    def save_chat_list(self, list_name: str, chat_ids: List[int], description: str = "") -> bool:
        """保存聊天列表"""
        list_path = Path(f"data/chat_lists/{list_name}.json")
        list_path.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            data = {
                'name': list_name,
                'description': description,
                'created_at': get_timestamp(),
                'chat_ids': chat_ids
            }
            
            with open(list_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
        except Exception as e:
            logger.error(f"Failed to save chat list {list_name}: {e}")
            return False

    def _format_text_with_links(self, text: str, link: str = None, tg_short_link: str = None,
                               link_text: str = None, link_type: str = "browser") -> str:
        """
        格式化文本，根据type参数选择添加相应的链接

        Args:
            text: 原始文本
            link: 普通链接URL
            tg_short_link: TG短链接URL
            link_text: 自定义链接显示文字
            link_type: 链接类型 ("browser" 使用link, "webapp" 使用tg_short_link)

        Returns:
            格式化后的文本，包含Markdown格式的链接
        """
        formatted_text = text

        # 根据type参数选择使用哪个链接
        if link_type == "webapp" and tg_short_link:
            # webapp类型使用tg_short_link
            display_text = link_text or "📱 在Telegram中打开"
            formatted_text += f"\n\n[{display_text}]({tg_short_link})"
            logger.info(f"使用webapp链接: {tg_short_link}, 显示文字: {display_text}")

        elif link_type == "browser" and link:
            # browser类型使用link
            display_text = link_text or "🌐 在浏览器中打开"
            formatted_text += f"\n\n[{display_text}]({link})"
            logger.info(f"使用浏览器链接: {link}, 显示文字: {display_text}")

        else:
            # 降级处理：如果指定类型的链接不存在，尝试使用另一个
            if link:
                display_text = link_text or "🔗 点击访问链接"
                formatted_text += f"\n\n[{display_text}]({link})"
                logger.warning(f"降级使用普通链接: {link}, 显示文字: {display_text}")
            elif tg_short_link:
                display_text = link_text or "📱 Telegram链接"
                formatted_text += f"\n\n[{display_text}]({tg_short_link})"
                logger.warning(f"降级使用TG链接: {tg_short_link}, 显示文字: {display_text}")

        return formatted_text

    def _auto_format_urls_in_text(self, text: str) -> str:
        """
        自动检测文本中的URL并格式化为Markdown链接

        Args:
            text: 包含URL的原始文本

        Returns:
            格式化后的文本，URL被转换为Markdown链接格式
        """
        # URL正则表达式模式
        url_pattern = r'https?://[^\s<>"{}|\\^`\[\]]+'

        def replace_url(match):
            url = match.group(0)
            # 生成友好的链接文字
            if 'google.com' in url.lower():
                link_text = '🔍 Google'
            elif 't.me' in url.lower():
                link_text = '📱 Telegram'
            elif 'github.com' in url.lower():
                link_text = '💻 GitHub'
            else:
                # 提取域名作为链接文字
                domain = re.search(r'https?://([^/]+)', url)
                if domain:
                    link_text = f'🔗 {domain.group(1)}'
                else:
                    link_text = '🔗 链接'

            return f'[{link_text}]({url})'

        # 替换所有URL
        formatted_text = re.sub(url_pattern, replace_url, text)
        return formatted_text

    def _get_image_extension(self, url: str, content_type: str = "") -> str:
        """
        根据URL和Content-Type获取图片扩展名

        Args:
            url: 图片URL
            content_type: HTTP响应的Content-Type

        Returns:
            文件扩展名（包含点号）
        """
        # 首先尝试从Content-Type获取
        if content_type:
            if 'jpeg' in content_type or 'jpg' in content_type:
                return '.jpg'
            elif 'png' in content_type:
                return '.png'
            elif 'gif' in content_type:
                return '.gif'
            elif 'webp' in content_type:
                return '.webp'

        # 然后尝试从URL获取
        url_lower = url.lower()
        if url_lower.endswith('.jpg') or url_lower.endswith('.jpeg'):
            return '.jpg'
        elif url_lower.endswith('.png'):
            return '.png'
        elif url_lower.endswith('.gif'):
            return '.gif'
        elif url_lower.endswith('.webp'):
            return '.webp'

        # 默认使用jpg
        return '.jpg'


# 全局消息发送器实例
message_sender = MessageSender()
