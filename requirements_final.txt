# Telegram Bot Service - Production Dependencies
# 生产环境部署所需的所有依赖包

# ===== 核心框架 =====
# Telegram Bot API
python-telegram-bot==20.7

# Web框架和API服务
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# ===== 数据库 =====
# MongoDB数据库驱动
pymongo>=4.6.0
motor>=3.3.0  # Async MongoDB driver

# MySQL数据库驱动 (如果需要)
aiomysql>=0.2.0

# ===== HTTP客户端 =====
# HTTP客户端库
httpx>=0.25.0,<0.28.0  # Telegram Bot使用
aiohttp>=3.8.0         # 异步HTTP客户端
requests>=2.31.0       # 同步HTTP客户端，用于API测试

# ===== 任务调度 =====
# 定时任务调度器
apscheduler>=3.10.0

# ===== 配置和工具 =====
# 配置文件解析
PyYAML>=6.0.0

# 环境变量管理
python-dotenv>=1.0.0

# ===== 图像处理 (可选) =====
# 图像处理库
Pillow>=10.0.0
imageio>=2.33.0

# ===== 消息队列 (可选) =====
# MQTT客户端 (如果需要消息队列)
asyncio-mqtt>=0.13.0

# ===== 开发和测试依赖 (可选) =====
# 取消注释以启用开发工具
# pytest>=7.4.0
# pytest-asyncio>=0.21.0
# black>=23.0.0
# flake8>=6.0.0
# mypy>=1.0.0

# ===== 部署相关 =====
# 进程管理 (生产环境推荐)
# gunicorn>=21.2.0

# 监控和日志 (可选)
# sentry-sdk>=1.38.0  # 错误监控
# structlog>=23.2.0   # 结构化日志
