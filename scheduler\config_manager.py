#!/usr/bin/env python3
"""
配置管理器 - 处理Token缓存和热更新
"""
import asyncio
import logging
from typing import Dict, Optional, List
from datetime import datetime, timedelta
import threading

from database.mongodb_connection import MongoDBConnection

logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器 - 负责三表缓存和热更新"""

    def __init__(self):
        self.mongo = MongoDBConnection()

        # 三表缓存
        self.robot_config_cache = {}    # c_tgRobotConfig: {business_no: config}
        self.notify_config_cache = {}   # c_tgNotify: {notify_id: config}
        self.scheduled_tasks_cache = {} # c_tgScheduledPushTasks: {task_id: config}

        # 缓存锁和时间戳
        self.cache_lock = threading.RLock()
        self.last_robot_config_update = None
        self.last_notify_config_update = None
        self.last_scheduled_tasks_update = None
        self.cache_ttl = 300  # 缓存5分钟过期
        
    async def initialize(self):
        """初始化配置管理器"""
        logger.info("🚀 初始化配置管理器 - 加载三表缓存")

        if not self.mongo.connect():
            logger.error("❌ MongoDB连接失败")
            return False

        # 加载三张核心表的数据到内存
        success = True
        success &= await self.load_robot_config()      # c_tgRobotConfig
        success &= await self.load_notify_config()     # c_tgNotify
        success &= await self.load_scheduled_tasks()   # c_tgScheduledPushTasks

        if success:
            logger.info("✅ 三表缓存初始化完成")
        else:
            logger.error("❌ 部分表缓存初始化失败")

        return success

    async def load_robot_config(self):
        """加载Bot配置表 (c_tgRobotConfig) 到缓存"""
        try:
            collection = self.mongo.get_collection("c_tgRobotConfig")
            if collection is None:
                logger.error("❌ 无法获取c_tgRobotConfig集合")
                return False

            # 查询所有配置
            configs = list(collection.find({}))

            with self.cache_lock:
                self.robot_config_cache.clear()

                for config in configs:
                    business_no = config.get("business_no")
                    if business_no:
                        self.robot_config_cache[business_no] = {
                            "business_no": business_no,
                            "bot_token": config.get("botToken", ""),      # 数据库字段: botToken
                            "bot_name": config.get("botName", ""),        # 数据库字段: botName
                            "bot_id": config.get("botId", ""),            # 数据库字段: botId
                            "api_url": config.get("apiUrl", ""),          # 数据库字段: apiUrl
                            "status": "active",  # 默认为active，数据库中没有status字段
                            "created_at": config.get("createTime"),       # 数据库字段: createTime
                            "updated_at": config.get("updateTime"),       # 数据库字段: updateTime
                            "cache_updated_at": datetime.now()
                        }

                self.last_robot_config_update = datetime.now()

            logger.info(f"✅ 加载了 {len(self.robot_config_cache)} 个Bot配置到缓存")
            return True

        except Exception as e:
            logger.error(f"❌ 加载Bot配置失败: {e}")
            return False

    async def load_notify_config(self):
        """加载通知配置表 (c_tgNotify) 到缓存"""
        try:
            collection = self.mongo.get_collection("c_tgNotify")
            if collection is None:
                logger.error("❌ 无法获取c_tgNotify集合")
                return False

            # 查询所有配置
            configs = list(collection.find({}))

            with self.cache_lock:
                self.notify_config_cache.clear()

                for config in configs:
                    notify_id = config.get("id")
                    if notify_id:
                        self.notify_config_cache[notify_id] = {
                            **config,  # 保存完整配置
                            "cache_updated_at": datetime.now()
                        }

                self.last_notify_config_update = datetime.now()

            logger.info(f"✅ 加载了 {len(self.notify_config_cache)} 个通知配置到缓存")
            return True

        except Exception as e:
            logger.error(f"❌ 加载通知配置失败: {e}")
            return False

    async def load_scheduled_tasks(self):
        """加载定时任务表 (c_tgScheduledPushTasks) 到缓存"""
        try:
            collection = self.mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                logger.error("❌ 无法获取c_tgScheduledPushTasks集合")
                return False

            # 查询所有启用的任务，按_id排序确保优先级
            tasks = list(collection.find({"enabled": True}).sort("_id", 1))

            with self.cache_lock:
                self.scheduled_tasks_cache.clear()

                for task in tasks:
                    # 使用_id作为唯一标识，支持同一notifyId的多条记录
                    task_id = str(task.get("_id"))
                    if task_id:
                        self.scheduled_tasks_cache[task_id] = {
                            **task,  # 保存完整任务配置
                            "cache_updated_at": datetime.now()
                        }

                self.last_scheduled_tasks_update = datetime.now()

            logger.info(f"✅ 加载了 {len(self.scheduled_tasks_cache)} 个定时任务到缓存")
            logger.info(f"📋 任务分布: {self._get_task_distribution()}")
            return True

        except Exception as e:
            logger.error(f"❌ 加载定时任务失败: {e}")
            return False

    def _get_task_distribution(self):
        """获取任务分布统计"""
        distribution = {}
        for task in self.scheduled_tasks_cache.values():
            frequency = task.get("sendFrequency", "unknown")
            if frequency not in distribution:
                distribution[frequency] = 0
            distribution[frequency] += 1
        return distribution

    async def load_token_config(self):
        """从数据库加载Token配置到缓存"""
        try:
            collection = self.mongo.get_collection("c_tgRobotConfig")
            if collection is None:
                logger.error("❌ 无法获取c_tgRobotConfig集合")
                return False
            
            # 查询所有启用的Token配置
            configs = list(collection.find({"status": {"$ne": "disabled"}}))
            
            with self.cache_lock:
                self.token_cache.clear()
                
                for config in configs:
                    business_no = config.get("business_no")
                    if business_no:
                        self.token_cache[business_no] = {
                            "token": config.get("bot_token", ""),
                            "bot_name": config.get("bot_name", ""),
                            "bot_username": config.get("bot_username", ""),
                            "status": config.get("status", "active"),
                            "updated_at": datetime.now()
                        }
                
                self.last_token_update = datetime.now()

            logger.info(f"✅ 加载了 {len(self.token_cache)} 个Token配置到缓存")
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载Token配置失败: {e}")
            return False

    async def load_tasks_config(self):
        """从数据库加载定时任务配置到缓存"""
        try:
            collection = self.mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                logger.error("❌ 无法获取c_tgScheduledPushTasks集合")
                return False

            # 查询所有启用的任务
            tasks = list(collection.find({"enabled": True}))

            with self.cache_lock:
                self.tasks_cache.clear()

                for task in tasks:
                    task_id = task.get("notifyId")
                    if task_id:
                        self.tasks_cache[task_id] = {
                            "config": task,
                            "updated_at": datetime.now()
                        }

                self.last_tasks_update = datetime.now()

            logger.info(f"✅ 加载了 {len(self.tasks_cache)} 个定时任务到缓存")
            return True

        except Exception as e:
            logger.error(f"❌ 加载定时任务配置失败: {e}")
            return False
    
    def get_bot_token(self, business_no: str) -> Optional[str]:
        """获取指定商户的Bot Token"""
        with self.cache_lock:
            config = self.robot_config_cache.get(business_no)
            if config and config.get("status") == "active":
                return config.get("bot_token")
            return None

    def get_bot_info(self, business_no: str) -> Optional[Dict]:
        """获取指定商户的Bot完整信息"""
        with self.cache_lock:
            config = self.robot_config_cache.get(business_no)
            if config and config.get("status") == "active":
                return {
                    "bot_token": config.get("bot_token"),
                    "bot_name": config.get("bot_name"),
                    "bot_username": config.get("bot_username"),
                    "status": config.get("status")
                }
            return None

    def get_notify_config(self, notify_id: int) -> Optional[Dict]:
        """获取指定通知配置"""
        with self.cache_lock:
            return self.notify_config_cache.get(notify_id)

    def get_all_notify_configs(self, notify_type: int = None) -> List[Dict]:
        """获取所有通知配置，可按类型过滤"""
        with self.cache_lock:
            if notify_type is not None:
                return [config for config in self.notify_config_cache.values()
                       if config.get("notifyType") == notify_type]
            return list(self.notify_config_cache.values())

    def get_scheduled_task(self, task_id: int) -> Optional[Dict]:
        """获取指定定时任务"""
        with self.cache_lock:
            return self.scheduled_tasks_cache.get(task_id)

    def get_enabled_scheduled_tasks(self) -> List[Dict]:
        """获取所有启用的定时任务"""
        with self.cache_lock:
            return [task for task in self.scheduled_tasks_cache.values()
                   if task.get("enabled", False)]

    def get_all_robot_configs(self) -> List[Dict]:
        """获取所有Bot配置"""
        with self.cache_lock:
            return list(self.robot_config_cache.values())

    def get_scheduled_tasks_for_date(self, date):
        """获取指定日期的定时任务"""
        day_of_month = date.day
        day_of_week = date.weekday() + 1  # 1=周一, 7=周日

        with self.cache_lock:
            # 按优先级分类任务
            monthly_tasks = []
            weekly_tasks = []
            daily_tasks = []

            for task in self.scheduled_tasks_cache.values():
                frequency = task.get("sendFrequency", "")
                schedule_days = task.get("scheduleDays", [])

                if frequency == "monthly" and day_of_month in schedule_days:
                    monthly_tasks.append(task)
                elif frequency == "weekly" and day_of_week in schedule_days:
                    weekly_tasks.append(task)
                elif frequency == "daily" and not schedule_days:  # 空数组表示每天
                    daily_tasks.append(task)

            # 优先级: monthly > weekly > daily
            if monthly_tasks:
                return monthly_tasks
            elif weekly_tasks:
                return weekly_tasks
            else:
                return daily_tasks

    def get_all_scheduled_tasks(self):
        """获取所有定时任务"""
        with self.cache_lock:
            return list(self.scheduled_tasks_cache.values())

    def get_scheduled_tasks_by_business(self, business_no):
        """获取指定商户的定时任务"""
        with self.cache_lock:
            return [task for task in self.scheduled_tasks_cache.values()
                   if task.get("business_no") == business_no]

    def get_enabled_tasks(self) -> List[Dict]:
        """获取所有启用的定时任务"""
        with self.cache_lock:
            return [task_info["config"] for task_info in self.tasks_cache.values()]

    def get_task_config(self, task_id: int) -> Optional[Dict]:
        """获取指定任务的配置"""
        with self.cache_lock:
            task_info = self.tasks_cache.get(task_id)
            if task_info:
                return task_info["config"]
            return None

    def is_robot_config_expired(self) -> bool:
        """检查Bot配置缓存是否过期"""
        if not self.last_robot_config_update:
            return True
        return (datetime.now() - self.last_robot_config_update).seconds > self.cache_ttl

    def is_notify_config_expired(self) -> bool:
        """检查通知配置缓存是否过期"""
        if not self.last_notify_config_update:
            return True
        return (datetime.now() - self.last_notify_config_update).seconds > self.cache_ttl

    def is_scheduled_tasks_expired(self) -> bool:
        """检查定时任务缓存是否过期"""
        if not self.last_scheduled_tasks_update:
            return True
        return (datetime.now() - self.last_scheduled_tasks_update).seconds > self.cache_ttl
    
    async def refresh_all_cache(self):
        """刷新所有三表缓存"""
        logger.info("🔄 刷新所有三表缓存")

        success = True
        success &= await self.load_robot_config()      # c_tbRobotConfig
        success &= await self.load_notify_config()     # c_tgNotify
        success &= await self.load_scheduled_tasks()   # c_tgScheduledPushTasks

        if success:
            logger.info("✅ 所有三表缓存刷新完成")
        else:
            logger.error("❌ 部分表缓存刷新失败")

        return success

    async def refresh_robot_config(self, business_nos: List[str] = None):
        """刷新Bot配置缓存"""
        if business_nos:
            logger.info(f"🔄 刷新指定商户Bot配置: {business_nos}")
            for business_no in business_nos:
                await self._refresh_single_robot_config(business_no)
        else:
            logger.info("🔄 刷新所有Bot配置")
            await self.load_robot_config()

    async def refresh_notify_config(self, notify_ids: List[int] = None):
        """刷新通知配置缓存"""
        if notify_ids:
            logger.info(f"🔄 刷新指定通知配置: {notify_ids}")
            for notify_id in notify_ids:
                await self._refresh_single_notify_config(notify_id)
        else:
            logger.info("🔄 刷新所有通知配置")
            await self.load_notify_config()

    async def refresh_scheduled_tasks(self, task_ids: List[int] = None):
        """刷新定时任务缓存"""
        if task_ids:
            logger.info(f"🔄 刷新指定定时任务: {task_ids}")
            for task_id in task_ids:
                await self._refresh_single_scheduled_task(task_id)
        else:
            logger.info("🔄 刷新所有定时任务")
            await self.load_scheduled_tasks()
    
    async def _refresh_single_robot_config(self, business_no: str):
        """刷新单个商户的Bot配置"""
        try:
            collection = self.mongo.get_collection("c_tgRobotConfig")
            if collection is None:
                return False

            config = collection.find_one({"business_no": business_no})

            with self.cache_lock:
                if config:
                    self.robot_config_cache[business_no] = {
                        "business_no": business_no,
                        "bot_token": config.get("botToken", ""),      # 数据库字段: botToken
                        "bot_name": config.get("botName", ""),        # 数据库字段: botName
                        "bot_id": config.get("botId", ""),            # 数据库字段: botId
                        "api_url": config.get("apiUrl", ""),          # 数据库字段: apiUrl
                        "status": "active",  # 默认为active，数据库中没有status字段
                        "created_at": config.get("createTime"),       # 数据库字段: createTime
                        "updated_at": config.get("updateTime"),       # 数据库字段: updateTime
                        "cache_updated_at": datetime.now()
                    }
                    logger.info(f"✅ 刷新商户 {business_no} 的Bot配置")
                else:
                    # 配置被删除，从缓存中移除
                    if business_no in self.robot_config_cache:
                        del self.robot_config_cache[business_no]
                        logger.info(f"🗑️ 从缓存中移除商户 {business_no} 的配置")

            return True

        except Exception as e:
            logger.error(f"❌ 刷新单个Bot配置失败: {e}")
            return False

    async def _refresh_single_notify_config(self, notify_id: int):
        """刷新单个通知配置"""
        try:
            collection = self.mongo.get_collection("c_tgNotify")
            if collection is None:
                return False

            config = collection.find_one({"id": notify_id})

            with self.cache_lock:
                if config:
                    self.notify_config_cache[notify_id] = {
                        **config,
                        "cache_updated_at": datetime.now()
                    }
                    logger.info(f"✅ 刷新通知配置 {notify_id}")
                else:
                    # 配置被删除，从缓存中移除
                    if notify_id in self.notify_config_cache:
                        del self.notify_config_cache[notify_id]
                        logger.info(f"🗑️ 从缓存中移除通知配置 {notify_id}")

            return True

        except Exception as e:
            logger.error(f"❌ 刷新单个通知配置失败: {e}")
            return False

    async def _refresh_single_scheduled_task(self, task_id: int):
        """刷新单个定时任务"""
        try:
            collection = self.mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                return False

            task = collection.find_one({"notifyId": task_id})

            with self.cache_lock:
                if task:
                    self.scheduled_tasks_cache[task_id] = {
                        **task,
                        "cache_updated_at": datetime.now()
                    }
                    logger.info(f"✅ 刷新定时任务 {task_id}")
                else:
                    # 任务被删除，从缓存中移除
                    if task_id in self.scheduled_tasks_cache:
                        del self.scheduled_tasks_cache[task_id]
                        logger.info(f"🗑️ 从缓存中移除定时任务 {task_id}")

            return True

        except Exception as e:
            logger.error(f"❌ 刷新单个定时任务失败: {e}")
            return False

    async def _refresh_single_task(self, task_id: int):
        """刷新单个任务的配置"""
        try:
            collection = self.mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                return False

            task = collection.find_one({"notifyId": task_id})

            with self.cache_lock:
                if task and task.get("enabled", False):
                    self.tasks_cache[task_id] = {
                        "config": task,
                        "updated_at": datetime.now()
                    }
                    logger.info(f"✅ 刷新任务 {task_id} 的配置")
                else:
                    # 任务被删除或禁用，从缓存中移除
                    if task_id in self.tasks_cache:
                        del self.tasks_cache[task_id]
                        logger.info(f"🗑️ 从缓存中移除任务 {task_id}")

            return True

        except Exception as e:
            logger.error(f"❌ 刷新单个任务失败: {e}")
            return False
    
    async def auto_refresh_check(self):
        """自动检查并刷新过期的三表缓存"""
        refresh_tasks = []

        if self.is_robot_config_expired():
            logger.info("⏰ Bot配置缓存已过期，自动刷新")
            refresh_tasks.append(self.load_robot_config())

        if self.is_notify_config_expired():
            logger.info("⏰ 通知配置缓存已过期，自动刷新")
            refresh_tasks.append(self.load_notify_config())

        if self.is_scheduled_tasks_expired():
            logger.info("⏰ 定时任务缓存已过期，自动刷新")
            refresh_tasks.append(self.load_scheduled_tasks())

        # 并发刷新过期的缓存
        if refresh_tasks:
            await asyncio.gather(*refresh_tasks, return_exceptions=True)
    
    def get_cache_stats(self) -> Dict:
        """获取三表缓存统计信息"""
        with self.cache_lock:
            # Bot配置统计
            active_robot_configs = sum(1 for config in self.robot_config_cache.values()
                                     if config.get("status") == "active")

            # 通知配置统计
            realtime_notify_configs = sum(1 for config in self.notify_config_cache.values()
                                        if config.get("notifyType") == 1)
            scheduled_notify_configs = sum(1 for config in self.notify_config_cache.values()
                                         if config.get("notifyType") == 2)

            # 定时任务统计
            enabled_tasks = sum(1 for task in self.scheduled_tasks_cache.values()
                              if task.get("enabled", False))

            return {
                "robot_configs": {
                    "total": len(self.robot_config_cache),
                    "active": active_robot_configs,
                    "last_update": self.last_robot_config_update.isoformat() if self.last_robot_config_update else None
                },
                "notify_configs": {
                    "total": len(self.notify_config_cache),
                    "realtime": realtime_notify_configs,
                    "scheduled": scheduled_notify_configs,
                    "last_update": self.last_notify_config_update.isoformat() if self.last_notify_config_update else None
                },
                "scheduled_tasks": {
                    "total": len(self.scheduled_tasks_cache),
                    "enabled": enabled_tasks,
                    "last_update": self.last_scheduled_tasks_update.isoformat() if self.last_scheduled_tasks_update else None
                },
                "cache_health": {
                    "robot_config_expired": self.is_robot_config_expired(),
                    "notify_config_expired": self.is_notify_config_expired(),
                    "scheduled_tasks_expired": self.is_scheduled_tasks_expired()
                }
            }
    
    def list_all_configs(self) -> Dict:
        """列出所有缓存的配置 (用于调试)"""
        with self.cache_lock:
            return {
                business_no: {
                    "bot_name": config.get("bot_name"),
                    "bot_username": config.get("bot_username"), 
                    "status": config.get("status"),
                    "has_token": bool(config.get("token")),
                    "updated_at": config.get("updated_at").isoformat()
                }
                for business_no, config in self.token_cache.items()
            }


# 全局配置管理器实例
config_manager = ConfigManager()
