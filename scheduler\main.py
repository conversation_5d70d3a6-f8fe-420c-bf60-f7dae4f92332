"""
定时任务调度器主程序
"""
import asyncio
import logging
from scheduler.manager import SchedulerManager

logger = logging.getLogger(__name__)

# 全局调度器管理器实例
scheduler_manager = SchedulerManager()


async def main():
    """定时任务调度器主函数"""
    logger.info("启动定时任务调度器...")
    
    try:
        await scheduler_manager.start()
        
        # 保持运行
        while True:
            await asyncio.sleep(60)  # 每分钟检查一次
            
    except KeyboardInterrupt:
        logger.info("收到停止信号...")
        await scheduler_manager.stop()
    except Exception as e:
        logger.error(f"调度器运行错误: {e}")
        await scheduler_manager.stop()
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n调度器已停止")
    except Exception as e:
        print(f"调度器启动失败: {e}")
