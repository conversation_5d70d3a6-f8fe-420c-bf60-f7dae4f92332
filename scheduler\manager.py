"""
定时任务管理器
负责任务的调度、执行和管理
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.jobstores.memory import MemoryJobStore
from apscheduler.executors.asyncio import AsyncIOExecutor

from config.settings import settings
from scheduler.executor import TaskExecutor
from database.connection import get_enabled_tasks, get_task_config

logger = logging.getLogger(__name__)


class SchedulerManager:
    """定时任务调度管理器"""
    
    def __init__(self):
        self.scheduler: Optional[AsyncIOScheduler] = None
        self.executor = TaskExecutor()
        self.running_tasks: Dict[str, Any] = {}
        
    async def start(self):
        """启动调度器"""
        try:
            logger.info("初始化定时任务调度器...")
            
            # 配置调度器
            jobstores = {
                'default': MemoryJobStore()
            }
            executors = {
                'default': AsyncIOExecutor()
            }
            job_defaults = {
                'coalesce': False,
                'max_instances': 3
            }
            
            self.scheduler = AsyncIOScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone=settings.timezone
            )
            
            # 启动调度器
            self.scheduler.start()
            logger.info("定时任务调度器启动成功")
            
            # 从数据库加载任务
            await self.load_tasks_from_database()
            
        except Exception as e:
            logger.error(f"启动调度器失败: {e}")
            raise
    
    async def stop(self):
        """停止调度器"""
        try:
            if self.scheduler:
                logger.info("停止定时任务调度器...")
                self.scheduler.shutdown(wait=True)
                logger.info("定时任务调度器已停止")
                
        except Exception as e:
            logger.error(f"停止调度器失败: {e}")
    
    async def load_tasks_from_database(self):
        """从数据库加载任务"""
        try:
            logger.info("从数据库加载定时任务...")

            # 从数据库获取启用的任务
            tasks = await get_enabled_tasks()

            for task in tasks:
                await self.add_job(task)

            logger.info(f"定时任务加载完成，共加载 {len(tasks)} 个任务")

        except Exception as e:
            logger.error(f"加载任务失败: {e}")
    
    async def add_job(self, task_config: Dict[str, Any]) -> bool:
        """添加任务到调度器"""
        try:
            task_id = task_config.get('id')
            schedule = task_config.get('schedule')
            
            logger.info(f"添加定时任务: {task_id}, schedule: {schedule}")
            
            # 解析Cron表达式
            trigger = CronTrigger.from_crontab(schedule)
            
            # 添加任务到调度器
            self.scheduler.add_job(
                func=self._execute_task,
                trigger=trigger,
                args=[task_config],
                id=task_id,
                name=task_config.get('name', task_id),
                replace_existing=True
            )
            
            logger.info(f"任务添加成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"添加任务失败: {e}")
            return False
    
    async def remove_job(self, task_id: str) -> bool:
        """从调度器移除任务"""
        try:
            logger.info(f"移除定时任务: {task_id}")
            
            if self.scheduler.get_job(task_id):
                self.scheduler.remove_job(task_id)
                logger.info(f"任务移除成功: {task_id}")
                return True
            else:
                logger.warning(f"任务不存在: {task_id}")
                return False
                
        except Exception as e:
            logger.error(f"移除任务失败: {e}")
            return False
    
    async def update_job(self, task_config: Dict[str, Any]) -> bool:
        """更新任务"""
        try:
            task_id = task_config.get('id')
            logger.info(f"更新定时任务: {task_id}")
            
            # 先移除旧任务
            await self.remove_job(task_id)
            
            # 添加新任务
            return await self.add_job(task_config)
            
        except Exception as e:
            logger.error(f"更新任务失败: {e}")
            return False
    
    async def execute_job(self, task_config: Dict[str, Any]):
        """手动执行任务"""
        try:
            task_id = task_config.get('id')
            logger.info(f"手动执行任务: {task_id}")
            
            await self._execute_task(task_config)
            
        except Exception as e:
            logger.error(f"手动执行任务失败: {e}")
    
    async def _execute_task(self, task_config: Dict[str, Any]):
        """执行任务的内部方法"""
        task_id = task_config.get('id')
        
        try:
            # 检查任务是否已在运行
            if task_id in self.running_tasks:
                logger.warning(f"任务已在运行中，跳过: {task_id}")
                return
            
            # 标记任务为运行中
            self.running_tasks[task_id] = {
                'started_at': datetime.now(),
                'status': 'running'
            }
            
            logger.info(f"开始执行任务: {task_id}")
            
            # 执行任务
            result = await self.executor.execute(task_config)
            
            # 记录执行结果
            self.running_tasks[task_id].update({
                'finished_at': datetime.now(),
                'status': 'completed',
                'result': result
            })
            
            logger.info(f"任务执行完成: {task_id}")
            
            # TODO: 记录执行结果到数据库
            
        except Exception as e:
            logger.error(f"任务执行失败: {task_id}, error: {e}")
            
            # 记录错误
            self.running_tasks[task_id].update({
                'finished_at': datetime.now(),
                'status': 'failed',
                'error': str(e)
            })
            
            # TODO: 记录错误到数据库
            
        finally:
            # 清理运行状态（延迟清理，保留一段时间用于查询）
            asyncio.create_task(self._cleanup_task_status(task_id))
    
    async def _cleanup_task_status(self, task_id: str, delay: int = 300):
        """清理任务运行状态（延迟执行）"""
        await asyncio.sleep(delay)  # 5分钟后清理
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
    
    def get_job_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        if task_id in self.running_tasks:
            return self.running_tasks[task_id]
        
        # 检查调度器中的任务
        job = self.scheduler.get_job(task_id)
        if job:
            return {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'status': 'scheduled'
            }
        
        return None
    
    def list_jobs(self) -> List[Dict[str, Any]]:
        """列出所有任务"""
        jobs = []
        
        for job in self.scheduler.get_jobs():
            job_info = {
                'id': job.id,
                'name': job.name,
                'next_run_time': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            }
            
            # 添加运行状态信息
            if job.id in self.running_tasks:
                job_info.update(self.running_tasks[job.id])
            
            jobs.append(job_info)
        
        return jobs
