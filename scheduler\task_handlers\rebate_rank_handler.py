#!/usr/bin/env python3
"""
投注返利排行榜任务处理器 (taskType=500)
查询ea_platform_wagered_rebate_rank_log表并组合消息
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from database.mysql_connection import get_mysql_connection
from scheduler.config_manager import config_manager

logger = logging.getLogger(__name__)

class RebateRankHandler:
    """投注返利排行榜处理器"""
    
    def __init__(self):
        self.mysql_conn = get_mysql_connection()
    
    async def handle_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理taskType=500的任务"""
        try:
            task_id = task.get("_id")
            business_no = task.get("business_no", "")
            notify_id = task.get("notifyId")
            
            logger.info(f"🎯 处理投注返利排行榜任务: {task_id}")
            logger.info(f"   商户: {business_no}")
            logger.info(f"   通知ID: {notify_id}")
            
            # 1. 查询排行榜数据
            rank_data = await self._query_rebate_rank_data(business_no)
            
            if not rank_data:
                logger.warning(f"⚠️ 商户 {business_no} 没有找到排行榜数据")
                return {
                    "success": False,
                    "message": "没有找到排行榜数据",
                    "data": None
                }
            
            # 2. 获取通知配置
            notify_config = config_manager.get_notify_config(notify_id)
            if not notify_config:
                logger.error(f"❌ 未找到通知配置: {notify_id}")
                return {
                    "success": False,
                    "message": f"未找到通知配置: {notify_id}",
                    "data": None
                }
            
            # 3. 组合消息
            message_data = await self._compose_message(rank_data, notify_config, task)
            
            logger.info(f"✅ 投注返利排行榜任务处理完成")
            return {
                "success": True,
                "message": "任务处理成功",
                "data": message_data
            }
            
        except Exception as e:
            logger.error(f"❌ 处理投注返利排行榜任务失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"任务处理失败: {str(e)}",
                "data": None
            }
    
    async def _query_rebate_rank_data(self, business_no: str) -> List[Dict[str, Any]]:
        """查询投注返利排行榜数据"""
        try:
            # 查询当天的数据
            today = datetime.now().strftime("%Y-%m-%d")

            # 简化的查询SQL - 只查询需要的4个字段
            query = """
                SELECT playerId, ranking, currencyId, wagered
                FROM ea_platform_wagered_rebate_rank_log
                WHERE business_no = %s AND date = %s
                ORDER BY ranking
            """

            logger.info(f"🔍 查询投注返利排行榜数据")
            logger.info(f"   商户: {business_no}")
            logger.info(f"   日期: {today}")

            results = await self.mysql_conn.execute_query(
                query,
                (business_no, today)
            )

            logger.info(f"📊 查询到 {len(results)} 条排行榜记录")

            # 记录查询结果的详细信息
            logger.info(f"🔍 MySQL查询结果详情:")
            for i, record in enumerate(results[:3], 1):  # 只记录前3条
                ranking = record.get('ranking', 'N/A')
                player_id = record.get('playerId', 'N/A')
                currency_id = record.get('currencyId', 'N/A')
                wagered = record.get('wagered', 0)
                logger.info(f"   记录{i}: ranking={ranking}, playerId={player_id}, currencyId={currency_id}, wagered={wagered}")
                logger.info(f"   原始数据: {record}")

            return results

        except Exception as e:
            logger.error(f"❌ 查询投注返利排行榜数据失败: {e}")
            raise
    
    async def _compose_message(self, rank_data: List[Dict[str, Any]], 
                             notify_config: Dict[str, Any], 
                             task: Dict[str, Any]) -> Dict[str, Any]:
        """组合消息内容"""
        try:
            # 获取消息模板
            template_text = notify_config.get("text", "")
            language = notify_config.get("language", 2)  # 默认葡萄牙语

            # 准备模板参数 - 传入模板文本进行动态解析
            params = await self._prepare_template_params(rank_data, language, template_text)
            
            # 获取其他配置
            target_chats = notify_config.get("notifyTarget", [])
            image_url = notify_config.get("image", "")
            
            logger.info(f"📝 组合消息内容")
            logger.info(f"   模板: {template_text[:50]}...")
            logger.info(f"   语言: {language}")
            logger.info(f"   参数数量: {len(params)}")
            logger.info(f"   目标聊天: {len(target_chats)} 个")
            
            return {
                "template_text": template_text,
                "params": params,
                "target_chats": target_chats,
                "image_url": image_url,
                "language": language,
                "rank_count": len(rank_data)
            }
            
        except Exception as e:
            logger.error(f"❌ 组合消息内容失败: {e}")
            raise
    
    async def _prepare_template_params(self, rank_data: List[Dict[str, Any]],
                                     language: int, template_text: str) -> List[Any]:
        """准备模板参数 - 动态适配模板，无硬编码"""
        try:
            import re

            # 动态解析模板中的repeat部分，提取占位符
            start_repeat = template_text.find("[repeat]")
            end_repeat = template_text.find("[/repeat]")

            if start_repeat == -1 or end_repeat == -1:
                logger.warning(f"⚠️ 模板中没有找到[repeat]块，使用默认格式")
                # 如果没有repeat块，使用简单的参数列表
                return [str(len(rank_data)), "排行榜数据"]

            # 提取repeat内容
            repeat_content = template_text[start_repeat + len("[repeat]"):end_repeat]

            # 动态提取占位符 - 支持各种格式
            placeholders = re.findall(r'\{(\w+)\}', repeat_content)

            logger.info(f"🔍 动态解析模板:")
            logger.info(f"   repeat内容: {repeat_content}")
            logger.info(f"   占位符: {placeholders}")
            logger.info(f"   占位符数量: {len(placeholders)}")

            # 动态组装数据 - 根据占位符数量和MySQL查询结果
            rank_list = []
            logger.info(f"🔍 开始组装参数数据:")

            for i, record in enumerate(rank_data, 1):
                # 从MySQL查询结果中提取数据
                ranking = record.get("ranking", 0)
                player_id = record.get("playerId", 0)
                currency_id = record.get("currencyId", 0)
                wagered = record.get("wagered", 0)

                if i <= 3:  # 只详细记录前3条
                    logger.info(f"   处理记录{i}: ranking={ranking}, playerId={player_id}, currencyId={currency_id}, wagered={wagered}")

                # 格式化数据
                rank_str = str(ranking)
                player_str = str(player_id)
                currency_str = str(currency_id)
                wagered_str = f"{float(wagered):,.2f}" if wagered else "0.00"

                if i <= 3:
                    logger.info(f"   格式化后{i}: rank_str={rank_str}, player_str={player_str}, currency_str={currency_str}, wagered_str={wagered_str}")

                # 动态组装数据数组 - 按照模板期望的顺序
                # 模板 {1}::{2} – {3}~{4} 期望: 排名, 玩家ID, 货币, 投注额
                # 注意：这里要按照模板的逻辑顺序，不是SQL查询顺序
                data_values = [rank_str, player_str, currency_str, wagered_str]

                if i <= 3:
                    logger.info(f"   组装数据{i}: {data_values}")

                # 确保数据数量与占位符数量匹配
                if len(data_values) >= len(placeholders):
                    # 取前N个数据，N为占位符数量
                    row_data = data_values[:len(placeholders)]
                else:
                    # 数据不够时，用空字符串填充
                    row_data = data_values + [""] * (len(placeholders) - len(data_values))

                if i <= 3:
                    logger.info(f"   最终行数据{i}: {row_data}")

                rank_list.append(row_data)

            # 返回嵌套数组作为参数
            params = [rank_list]

            logger.info(f"📋 动态模板参数准备完成")
            logger.info(f"   排行榜记录数: {len(rank_list)}")
            logger.info(f"   每行数据长度: {len(rank_list[0]) if rank_list else 0}")
            logger.info(f"   占位符映射: {dict(zip(placeholders, ['排名', '玩家ID', '货币', '投注额'][:len(placeholders)]))}")

            # 显示前3条记录
            for i, rank_item in enumerate(rank_list[:3], 1):
                logger.info(f"   记录{i}: {rank_item}")

            return params

        except Exception as e:
            logger.error(f"❌ 动态准备模板参数失败: {e}")
            raise

# 创建全局处理器实例
rebate_rank_handler = RebateRankHandler()
