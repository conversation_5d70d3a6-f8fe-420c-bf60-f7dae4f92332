#!/usr/bin/env python3
"""
投注返利排行榜任务处理器 (taskType=500)
查询ea_platform_wagered_rebate_rank_log表并组合消息
"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from database.mysql_connection import get_mysql_connection
from scheduler.config_manager import config_manager

logger = logging.getLogger(__name__)

class RebateRankHandler:
    """投注返利排行榜处理器"""
    
    def __init__(self):
        self.mysql_conn = get_mysql_connection()
    
    async def handle_task(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """处理taskType=500的任务"""
        try:
            task_id = task.get("_id")
            business_no = task.get("business_no", "")
            notify_id = task.get("notifyId")
            
            logger.info(f"🎯 处理投注返利排行榜任务: {task_id}")
            logger.info(f"   商户: {business_no}")
            logger.info(f"   通知ID: {notify_id}")
            
            # 1. 查询排行榜数据
            rank_data = await self._query_rebate_rank_data(business_no)
            
            if not rank_data:
                logger.warning(f"⚠️ 商户 {business_no} 没有找到排行榜数据")
                return {
                    "success": False,
                    "message": "没有找到排行榜数据",
                    "data": None
                }
            
            # 2. 获取通知配置
            notify_config = config_manager.get_notify_config(notify_id)
            if not notify_config:
                logger.error(f"❌ 未找到通知配置: {notify_id}")
                return {
                    "success": False,
                    "message": f"未找到通知配置: {notify_id}",
                    "data": None
                }
            
            # 3. 组合消息
            message_data = await self._compose_message(rank_data, notify_config, task)
            
            logger.info(f"✅ 投注返利排行榜任务处理完成")
            return {
                "success": True,
                "message": "任务处理成功",
                "data": message_data
            }
            
        except Exception as e:
            logger.error(f"❌ 处理投注返利排行榜任务失败: {e}")
            import traceback
            traceback.print_exc()
            return {
                "success": False,
                "message": f"任务处理失败: {str(e)}",
                "data": None
            }
    
    async def _query_rebate_rank_data(self, business_no: str) -> List[Dict[str, Any]]:
        """查询投注返利排行榜数据"""
        try:
            # 先查询该商户最新的数据日期
            date_query = """
                SELECT date
                FROM ea_platform_wagered_rebate_rank_log
                WHERE business_no = %s
                ORDER BY date DESC
                LIMIT 1
            """

            date_result = await self.mysql_conn.execute_single_query(date_query, (business_no,))

            if not date_result:
                logger.warning(f"⚠️ 商户 {business_no} 没有找到任何数据")
                return []

            latest_date = date_result.get("date")

            # 构造查询SQL (根据实际表结构)
            query = """
                SELECT
                    id,
                    playerId,
                    business_no,
                    ranking,
                    wagered,
                    reward,
                    prize,
                    currencyId,
                    date,
                    logTime
                FROM ea_platform_wagered_rebate_rank_log
                WHERE business_no = %s
                AND date = %s
                ORDER BY ranking ASC
                LIMIT 10
            """

            logger.info(f"🔍 查询投注返利排行榜数据")
            logger.info(f"   商户: {business_no}")
            logger.info(f"   最新日期: {latest_date}")

            results = await self.mysql_conn.execute_query(
                query,
                (business_no, latest_date)
            )

            logger.info(f"📊 查询到 {len(results)} 条排行榜记录")

            # 记录查询结果的详细信息
            for i, record in enumerate(results[:3], 1):  # 只记录前3条
                logger.info(f"   排名 {i}: 玩家ID {record.get('playerId', 'N/A')}, "
                          f"投注 {record.get('wagered', 0)}, "
                          f"奖励 {record.get('reward', 0)}")

            return results

        except Exception as e:
            logger.error(f"❌ 查询投注返利排行榜数据失败: {e}")
            raise
    
    async def _compose_message(self, rank_data: List[Dict[str, Any]], 
                             notify_config: Dict[str, Any], 
                             task: Dict[str, Any]) -> Dict[str, Any]:
        """组合消息内容"""
        try:
            # 获取消息模板
            template_text = notify_config.get("text", "")
            language = notify_config.get("language", 2)  # 默认葡萄牙语
            
            # 准备模板参数
            params = await self._prepare_template_params(rank_data, language)
            
            # 获取其他配置
            target_chats = notify_config.get("notifyTarget", [])
            image_url = notify_config.get("image", "")
            
            logger.info(f"📝 组合消息内容")
            logger.info(f"   模板: {template_text[:50]}...")
            logger.info(f"   语言: {language}")
            logger.info(f"   参数数量: {len(params)}")
            logger.info(f"   目标聊天: {len(target_chats)} 个")
            
            return {
                "template_text": template_text,
                "params": params,
                "target_chats": target_chats,
                "image_url": image_url,
                "language": language,
                "rank_count": len(rank_data)
            }
            
        except Exception as e:
            logger.error(f"❌ 组合消息内容失败: {e}")
            raise
    
    async def _prepare_template_params(self, rank_data: List[Dict[str, Any]],
                                     language: int) -> List[Any]:
        """准备模板参数"""
        try:
            params = []

            # 参数1: 排行榜标题/日期
            today = datetime.now().strftime("%Y-%m-%d")
            params.append(f"投注返利排行榜 - {today}")

            # 参数2: 排行榜数据 (嵌套数组格式)
            rank_list = []
            for record in rank_data:
                ranking = record.get("ranking", 0)
                player_id = record.get("playerId", 0)
                wagered = record.get("wagered", 0)
                reward = record.get("reward", 0)

                # 格式化金额
                wagered_str = f"{float(wagered):,.2f}" if wagered else "0.00"
                reward_str = f"{float(reward):,.2f}" if reward else "0.00"

                # 玩家显示名 (使用ID的后4位)
                player_display = f"玩家{str(player_id)[-4:]}" if player_id else "匿名"

                # 每行数据: [排名, 玩家名, 投注额, 奖励额]
                rank_list.append([
                    str(ranking),
                    player_display,
                    wagered_str,
                    reward_str
                ])

            params.append(rank_list)

            # 参数3: 总结信息
            total_players = len(rank_data)
            total_wagered = sum(float(record.get("wagered", 0)) for record in rank_data)
            total_reward = sum(float(record.get("reward", 0)) for record in rank_data)
            summary = f"共 {total_players} 位玩家，总投注 {total_wagered:,.2f}，总奖励 {total_reward:,.2f}"
            params.append(summary)

            logger.info(f"📋 模板参数准备完成")
            logger.info(f"   参数1: {params[0]}")
            logger.info(f"   参数2: {len(rank_list)} 条排行榜记录")
            logger.info(f"   参数3: {params[2]}")

            return params

        except Exception as e:
            logger.error(f"❌ 准备模板参数失败: {e}")
            raise

# 创建全局处理器实例
rebate_rank_handler = RebateRankHandler()
