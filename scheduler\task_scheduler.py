#!/usr/bin/env python3
"""
定时任务调度器
基于MongoDB中的c_tgScheduledPushTasks表实现定时推送功能
"""
import asyncio
import logging
from datetime import datetime, time, timedelta, timezone
from typing import List, Dict, Any, Optional
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from database.mongodb_connection import MongoDBConnection
from database.mysql_connection import init_mysql, close_mysql
from common.utils import setup_logging
from telegram import Bot
from telegram.error import TelegramError
from scheduler.config_manager import config_manager
from scheduler.timezone_manager import TimeZoneManager
from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler

logger = logging.getLogger(__name__)


class TaskScheduler:
    """定时任务调度器"""
    
    def __init__(self):
        self.mongo = MongoDBConnection()
        self.running = False
        self.tasks = []
        self.timezone_manager = TimeZoneManager()
        
    async def start(self):
        """启动调度器"""
        logger.info("🚀 启动定时任务调度器")

        if not self.mongo.connect():
            logger.error("❌ MongoDB连接失败")
            return False

        # 初始化配置管理器
        if not await config_manager.initialize():
            logger.error("❌ 配置管理器初始化失败")
            return False

        # 初始化时区管理器
        if not await self.timezone_manager.load_merchant_timezones(self.mongo):
            logger.error("❌ 时区管理器初始化失败")
            return False

        # 初始化MySQL连接
        if not await init_mysql():
            logger.error("❌ MySQL连接初始化失败")
            return False

        self.running = True

        # 启动主循环
        await self._run_scheduler()
        
    async def stop(self):
        """停止调度器"""
        logger.info("🛑 停止定时任务调度器")
        self.running = False
        self.mongo.disconnect()
        await close_mysql()
        
    async def _run_scheduler(self):
        """运行调度器主循环"""
        logger.info("⏰ 定时任务调度器开始运行")
        
        while self.running:
            try:
                # 自动检查配置缓存是否过期
                await config_manager.auto_refresh_check()

                # 加载任务
                await self._load_tasks()

                # 检查并执行到期任务
                await self._check_and_execute_tasks()

                # 等待下一次检查（每分钟检查一次）
                await asyncio.sleep(60)
                
            except Exception as e:
                logger.error(f"❌ 调度器运行异常: {e}")
                await asyncio.sleep(60)  # 出错后等待1分钟再继续
                
    async def _load_tasks(self):
        """从缓存加载启用的任务"""
        try:
            # 从配置管理器的缓存获取所有定时任务
            self.tasks = config_manager.get_all_scheduled_tasks()
            logger.debug(f"📋 从缓存加载了 {len(self.tasks)} 个启用的任务")

        except Exception as e:
            logger.error(f"❌ 加载任务失败: {e}")
            # 降级到直接从数据库加载
            await self._load_tasks_from_db()

    async def _load_tasks_from_db(self):
        """从数据库直接加载任务（降级方案）"""
        try:
            collection = self.mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                logger.error("❌ 无法获取任务集合")
                return

            # 查询启用的任务，按_id排序确保优先级
            query = {"enabled": True}
            tasks = list(collection.find(query).sort("_id", 1))

            self.tasks = tasks
            logger.debug(f"📋 从数据库加载了 {len(tasks)} 个启用的任务")

        except Exception as e:
            logger.error(f"❌ 从数据库加载任务失败: {e}")
            
    async def _check_and_execute_tasks(self):
        """检查并执行到期的任务（支持多时区）"""
        current_utc_time = datetime.now(timezone.utc)

        # 使用时区管理器获取应该执行的任务
        tasks_to_execute = self.timezone_manager.get_tasks_for_execution(self.tasks, current_utc_time)

        logger.debug(f"🔍 当前UTC时间: {current_utc_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.debug(f"🎯 应该执行的任务: {len(tasks_to_execute)}")

        for task in tasks_to_execute:
            try:
                await self._execute_task(task)

            except Exception as e:
                task_id = task.get('_id', 'unknown')
                logger.error(f"❌ 执行任务 {task_id} 失败: {e}")

            
    async def _execute_task(self, task: Dict[str, Any]):
        """执行定时任务"""
        task_id = task.get("_id")
        notify_id = task.get("notifyId")
        task_type = task.get("taskType", 0)
        send_frequency = task.get("sendFrequency", "")

        logger.info(f"🎯 执行定时任务: ID={task_id}, notifyId={notify_id}, type={task_type}, frequency={send_frequency}")

        try:
            # 解析任务参数
            business_no = task.get("business_no", "")

            # 记录时区信息
            self.timezone_manager.log_timezone_info(business_no)

            # 检查是否需要专门的处理器
            if task_type == 500:
                # 使用投注返利排行榜处理器
                logger.info(f"🎯 使用专门处理器处理taskType=500任务")
                result = await rebate_rank_handler.handle_task(task)

                if not result.get("success", False):
                    logger.error(f"❌ 专门处理器处理失败: {result.get('message', '未知错误')}")
                    return

                # 获取处理后的消息数据
                message_data = result.get("data", {})
                await self._send_composed_message(task, message_data)
                return
            channel_ids = task.get("channelId", [])
            message_text = task.get("messageText", "")
            banner_url = task.get("bannerUrl", "")
            external_url = task.get("externalUrl", "")
            internal_url = task.get("internalUrl", "")
            url_label = task.get("urlLabel", "")
            url_type = task.get("urlType", "")
            language = task.get("language", 1)

            # 从配置管理器获取Bot Token
            bot_token = config_manager.get_bot_token(business_no)
            if not bot_token:
                logger.error(f"❌ 任务 {notify_id} 无法获取Bot Token，商户号: {business_no}")
                return
                
            if not channel_ids:
                logger.error(f"❌ 任务 {notify_id} 缺少channelId")
                return
                
            if not message_text:
                logger.error(f"❌ 任务 {notify_id} 缺少messageText")
                return
            
            # 处理channelId（可能包含字符串和数字）
            target_chat_ids = []
            for channel_id in channel_ids:
                if isinstance(channel_id, (int, float)):
                    target_chat_ids.append(int(channel_id))
                elif isinstance(channel_id, str) and channel_id.lstrip('-').isdigit():
                    target_chat_ids.append(int(channel_id))
                else:
                    logger.warning(f"⚠️ 跳过无效的channelId: {channel_id}")
            
            if not target_chat_ids:
                logger.error(f"❌ 任务 {notify_id} 没有有效的目标聊天ID")
                return
            
            # 创建Bot实例
            bot = Bot(token=bot_token)

            # 发送消息到所有目标聊天
            success_count = 0
            for chat_id in target_chat_ids:
                try:
                    success = await self._send_scheduled_message(
                        bot, chat_id, message_text, banner_url,
                        external_url, internal_url, url_label, url_type, language
                    )
                    
                    if success:
                        success_count += 1
                        logger.info(f"✅ 任务 {notify_id} 发送到 {chat_id} 成功")
                    else:
                        logger.error(f"❌ 任务 {notify_id} 发送到 {chat_id} 失败")
                        
                except Exception as e:
                    logger.error(f"❌ 任务 {notify_id} 发送到 {chat_id} 异常: {e}")
            
            logger.info(f"📊 任务 {notify_id} 执行完成: {success_count}/{len(target_chat_ids)} 成功")
            
        except Exception as e:
            logger.error(f"❌ 执行任务 {notify_id} 失败: {e}")

    async def _send_scheduled_message(self, bot: Bot, chat_id: int, message_text: str,
                                    banner_url: str, external_url: str, internal_url: str,
                                    url_label: str, url_type: str, language: int = 1) -> bool:
        """发送定时任务消息"""
        try:
            # 构建按钮（如果有有效链接）
            reply_markup = None

            # 验证URL有效性
            valid_url = None
            if external_url and self._is_valid_url(external_url):
                valid_url = external_url
            elif internal_url and self._is_valid_url(internal_url):
                valid_url = internal_url

            if valid_url:
                from telegram import InlineKeyboardButton, InlineKeyboardMarkup

                button_text = url_label if url_label else "点击查看"
                keyboard = [[InlineKeyboardButton(button_text, url=valid_url)]]
                reply_markup = InlineKeyboardMarkup(keyboard)

                logger.debug(f"🔗 添加按钮: {button_text} -> {valid_url}")
            else:
                if external_url or internal_url:
                    logger.warning(f"⚠️ 无效的URL被忽略: external='{external_url}', internal='{internal_url}'")

            # 发送消息
            if banner_url:
                # 发送图片消息
                await bot.send_photo(
                    chat_id=chat_id,
                    photo=banner_url,
                    caption=message_text,
                    reply_markup=reply_markup
                )
            else:
                # 发送文本消息
                await bot.send_message(
                    chat_id=chat_id,
                    text=message_text,
                    reply_markup=reply_markup
                )

            return True

        except TelegramError as e:
            logger.error(f"❌ Telegram API错误: {e}")
            return False
        except Exception as e:
            logger.error(f"❌ 发送消息异常: {e}")
            return False

    def _is_valid_url(self, url: str) -> bool:
        """验证URL是否有效"""
        if not url or not isinstance(url, str):
            return False

        # 基本的URL格式检查
        url = url.strip()
        if len(url) < 4:  # 最短的URL至少4个字符，如 "a.co"
            return False

        # 检查是否以http://或https://开头
        if url.startswith(('http://', 'https://')):
            return True

        # 检查是否是Telegram链接格式
        if url.startswith('t.me/'):
            return True

        # 其他情况认为无效
        return False

    async def _send_composed_message(self, task: Dict[str, Any], message_data: Dict[str, Any]):
        """发送组合消息（用于专门处理器）"""
        try:
            business_no = task.get("business_no", "")
            notify_id = task.get("notifyId")

            # 获取Bot Token
            bot_token = config_manager.get_bot_token(business_no)
            if not bot_token:
                logger.error(f"❌ 任务 {notify_id} 无法获取Bot Token，商户号: {business_no}")
                return

            # 创建Bot实例
            bot = Bot(token=bot_token)

            # 获取消息内容
            template_text = message_data.get("template_text", "")
            params = message_data.get("params", [])
            target_chats = message_data.get("target_chats", [])
            image_url = message_data.get("image_url", "")

            # 使用翻译管理器渲染消息
            from common.translation_manager import translation_manager

            # 获取语言ID
            language_id = str(message_data.get("language", 2))

            # 渲染消息文本
            rendered_text = translation_manager.render_template(template_text, params, language_id)

            logger.info(f"📝 发送组合消息")
            logger.info(f"   目标聊天: {len(target_chats)} 个")
            logger.info(f"   消息长度: {len(rendered_text)} 字符")

            # 发送到每个目标聊天
            success_count = 0
            for chat_id in target_chats:
                try:
                    if image_url:
                        # 发送图片消息
                        await bot.send_photo(
                            chat_id=chat_id,
                            photo=image_url,
                            caption=rendered_text,
                            parse_mode='HTML'
                        )
                    else:
                        # 发送文本消息
                        await bot.send_message(
                            chat_id=chat_id,
                            text=rendered_text,
                            parse_mode='HTML'
                        )

                    success_count += 1
                    logger.info(f"✅ 消息发送成功到 {chat_id}")

                except TelegramError as e:
                    logger.error(f"❌ Telegram API错误: {e}")
                    logger.error(f"❌ 任务 {notify_id} 发送到 {chat_id} 失败")
                except Exception as e:
                    logger.error(f"❌ 发送消息到 {chat_id} 失败: {e}")

            logger.info(f"📊 任务 {notify_id} 执行完成: {success_count}/{len(target_chats)} 成功")

        except Exception as e:
            logger.error(f"❌ 发送组合消息失败: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    setup_logging()
    
    scheduler = TaskScheduler()
    
    try:
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在停止调度器...")
        await scheduler.stop()
    except Exception as e:
        logger.error(f"❌ 调度器异常: {e}")
        await scheduler.stop()


if __name__ == "__main__":
    asyncio.run(main())
