#!/usr/bin/env python3
"""
时区管理器 - 处理多时区定时任务调度
"""
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
import pytz

logger = logging.getLogger(__name__)

class TimeZoneManager:
    """时区管理器"""
    
    def __init__(self):
        self.merchant_timezones: Dict[str, str] = {}
        self.timezone_cache: Dict[str, pytz.BaseTzInfo] = {}
        
    async def load_merchant_timezones(self, mongo_connection) -> bool:
        """加载商户时区配置"""
        try:
            collection = mongo_connection.get_collection("c_baseMerchant")
            merchants = list(collection.find({}, {"business_no": 1, "timeZone": 1}))
            
            self.merchant_timezones.clear()
            for merchant in merchants:
                business_no = merchant.get("business_no", "")
                timezone_str = merchant.get("timeZone", "UTC")
                
                if business_no:
                    self.merchant_timezones[business_no] = timezone_str
                    
                    # 预加载时区对象
                    if timezone_str not in self.timezone_cache:
                        try:
                            # 处理UTC+8, UTC-3等格式
                            if timezone_str.startswith("UTC"):
                                if timezone_str == "UTC":
                                    tz_obj = pytz.UTC
                                else:
                                    # 解析UTC+8, UTC-3等
                                    offset_str = timezone_str[3:]  # +8, -3
                                    if offset_str:
                                        offset_hours = int(offset_str)
                                        # 创建固定偏移时区
                                        from datetime import timedelta
                                        tz_obj = timezone(timedelta(hours=offset_hours))
                                    else:
                                        tz_obj = pytz.UTC
                            else:
                                # 标准时区名称
                                tz_obj = pytz.timezone(timezone_str)
                            
                            self.timezone_cache[timezone_str] = tz_obj
                            
                        except Exception as e:
                            logger.warning(f"⚠️ 无效时区 {timezone_str}，使用UTC: {e}")
                            self.timezone_cache[timezone_str] = pytz.UTC
            
            logger.info(f"✅ 加载了 {len(self.merchant_timezones)} 个商户的时区配置")
            logger.info(f"📊 时区分布: {self._get_timezone_stats()}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载商户时区失败: {e}")
            return False
    
    def _get_timezone_stats(self) -> Dict[str, int]:
        """获取时区统计"""
        stats = {}
        for tz in self.merchant_timezones.values():
            stats[tz] = stats.get(tz, 0) + 1
        return stats
    
    def get_merchant_timezone(self, business_no: str) -> str:
        """获取商户时区"""
        return self.merchant_timezones.get(business_no, "UTC")
    
    def get_merchant_local_time(self, business_no: str, utc_time: Optional[datetime] = None) -> datetime:
        """获取商户本地时间"""
        if utc_time is None:
            utc_time = datetime.now(timezone.utc)
        
        timezone_str = self.get_merchant_timezone(business_no)
        tz_obj = self.timezone_cache.get(timezone_str, pytz.UTC)
        
        # 转换为商户本地时间
        local_time = utc_time.astimezone(tz_obj)
        
        return local_time
    
    def should_execute_task(self, task: Dict, current_utc_time: Optional[datetime] = None) -> bool:
        """判断任务是否应该在当前时间执行"""
        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)

        business_no = task.get("business_no", "")
        task_id = task.get("_id", "unknown")
        task_type = task.get("taskType", "unknown")

        # 获取商户本地时间
        local_time = self.get_merchant_local_time(business_no, current_utc_time)
        local_date = local_time.date()

        # 检查日期匹配
        send_frequency = task.get("sendFrequency", "")
        schedule_days = task.get("scheduleDays", [])

        is_date_match = False
        if send_frequency == "daily":
            # 每日任务，总是匹配
            is_date_match = True
            logger.debug(f"📅 任务 {task_id} (taskType={task_type}) 每日任务: 匹配={is_date_match}")
        elif send_frequency == "weekly":
            day_of_week = local_date.weekday() + 1  # 1=周一, 7=周日
            is_date_match = day_of_week in schedule_days
            logger.debug(f"📅 任务 {task_id} (taskType={task_type}) 周任务检查: 今天={day_of_week}, 调度日={schedule_days}, 匹配={is_date_match}")
        elif send_frequency == "monthly":
            day_of_month = local_date.day
            is_date_match = day_of_month in schedule_days
            logger.debug(f"📅 任务 {task_id} (taskType={task_type}) 月任务检查: 今天={day_of_month}, 调度日={schedule_days}, 匹配={is_date_match}")
        else:
            logger.warning(f"⚠️ 任务 {task_id} (taskType={task_type}) 未知频率: {send_frequency}")

        if not is_date_match:
            logger.debug(f"❌ 任务 {task_id} (taskType={task_type}) 日期不匹配")
            return False

        # 检查时间匹配
        schedule_times = task.get("scheduleTimes", [])
        current_time_str = local_time.strftime("%H:%M")

        logger.debug(f"⏰ 任务 {task_id} (taskType={task_type}) 时间检查: 当前={current_time_str}, 调度时间={schedule_times}")

        for schedule_time in schedule_times:
            # 处理时间格式 (HH:MM:SS -> HH:MM)
            if len(schedule_time.split(':')) == 3:
                schedule_time_str = ':'.join(schedule_time.split(':')[:2])
            else:
                schedule_time_str = schedule_time

            if current_time_str == schedule_time_str:
                logger.info(f"🎯 任务匹配: {task_id} (taskType={task_type}) 在商户时区 {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                return True

        logger.debug(f"❌ 任务 {task_id} (taskType={task_type}) 时间不匹配")
        return False
    
    def filter_tasks_by_priority(self, tasks: List[Dict], current_utc_time: Optional[datetime] = None) -> List[Dict]:
        """按优先级过滤任务 (月任务优先于周任务，每日任务独立执行)"""
        if not tasks:
            return []

        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)

        # 分离每日任务和其他任务
        daily_tasks = []
        other_tasks = []

        for task in tasks:
            frequency = task.get("sendFrequency", "")
            if frequency == "daily":
                daily_tasks.append(task)
            else:
                other_tasks.append(task)

        # 每日任务直接执行，不参与优先级过滤
        filtered_tasks = daily_tasks.copy()

        if daily_tasks:
            logger.info(f"📅 每日任务: {len(daily_tasks)} 个任务直接执行")

        # 对其他任务按商户、语言、群组分组进行优先级过滤
        groups = {}
        for task in other_tasks:
            business_no = task.get("business_no", "")
            notify_id = task.get("notifyId", "")
            channel_id = task.get("channelId", "")

            # 获取通知配置的语言 (需要从notify配置中获取)
            # 这里简化处理，实际需要查询c_tgNotify表
            group_key = f"{business_no}_{notify_id}_{channel_id}"

            if group_key not in groups:
                groups[group_key] = {"weekly": [], "monthly": []}

            frequency = task.get("sendFrequency", "")
            if frequency in groups[group_key]:
                groups[group_key][frequency].append(task)

        # 应用优先级规则 (月任务优先于周任务)
        for group_key, group_tasks in groups.items():
            monthly_tasks = group_tasks["monthly"]
            weekly_tasks = group_tasks["weekly"]

            if monthly_tasks:
                # 有月任务时，只执行月任务
                filtered_tasks.extend(monthly_tasks)
                if weekly_tasks:
                    logger.info(f"📅 优先级过滤: {group_key} 有月任务，忽略 {len(weekly_tasks)} 个周任务")
            else:
                # 没有月任务时，执行周任务
                filtered_tasks.extend(weekly_tasks)

        return filtered_tasks
    
    def get_tasks_for_execution(self, all_tasks: List[Dict], current_utc_time: Optional[datetime] = None) -> List[Dict]:
        """获取当前时间应该执行的任务"""
        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)

        logger.info(f"🔍 开始任务匹配 - UTC时间: {current_utc_time.strftime('%H:%M:%S')}")

        # 1. 过滤出时间匹配的任务
        matching_tasks = []
        for task in all_tasks:
            task_id = task.get('_id', 'unknown')
            task_type = task.get('taskType', 'unknown')
            business_no = task.get('business_no', 'unknown')

            # 详细检查每个任务
            should_execute = self.should_execute_task(task, current_utc_time)

            if should_execute:
                matching_tasks.append(task)
                logger.info(f"✅ 任务匹配: ID={task_id}, taskType={task_type}, 商户={business_no}")
            else:
                # 记录不匹配的原因
                local_time = self.get_merchant_local_time(business_no, current_utc_time)
                schedule_times = task.get("scheduleTimes", [])
                current_time_str = local_time.strftime("%H:%M")

                logger.debug(f"❌ 任务不匹配: ID={task_id}, taskType={task_type}, 商户={business_no}")
                logger.debug(f"   本地时间: {current_time_str}, 调度时间: {schedule_times}")

        logger.info(f"🔍 时间匹配的任务: {len(matching_tasks)}")

        # 2. 应用优先级过滤
        filtered_tasks = self.filter_tasks_by_priority(matching_tasks, current_utc_time)

        logger.info(f"🎯 优先级过滤后的任务: {len(filtered_tasks)}")

        return filtered_tasks
    
    def log_timezone_info(self, business_no: str):
        """记录时区信息用于调试"""
        utc_time = datetime.now(timezone.utc)
        local_time = self.get_merchant_local_time(business_no, utc_time)
        timezone_str = self.get_merchant_timezone(business_no)
        
        logger.info(f"🌍 商户 {business_no} 时区信息:")
        logger.info(f"   时区: {timezone_str}")
        logger.info(f"   UTC时间: {utc_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"   本地时间: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"   时差: {(local_time.utcoffset().total_seconds() / 3600):.1f} 小时")
