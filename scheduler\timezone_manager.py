#!/usr/bin/env python3
"""
时区管理器 - 处理多时区定时任务调度
"""
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional, Tuple
import pytz

logger = logging.getLogger(__name__)

class TimeZoneManager:
    """时区管理器"""
    
    def __init__(self):
        self.merchant_timezones: Dict[str, str] = {}
        self.timezone_cache: Dict[str, pytz.BaseTzInfo] = {}
        
    async def load_merchant_timezones(self, mongo_connection) -> bool:
        """加载商户时区配置"""
        try:
            collection = mongo_connection.get_collection("c_baseMerchant")
            merchants = list(collection.find({}, {"business_no": 1, "timeZone": 1}))
            
            self.merchant_timezones.clear()
            for merchant in merchants:
                business_no = merchant.get("business_no", "")
                timezone_str = merchant.get("timeZone", "UTC")
                
                if business_no:
                    self.merchant_timezones[business_no] = timezone_str
                    
                    # 预加载时区对象
                    if timezone_str not in self.timezone_cache:
                        try:
                            # 处理UTC+8, UTC-3等格式
                            if timezone_str.startswith("UTC"):
                                if timezone_str == "UTC":
                                    tz_obj = pytz.UTC
                                else:
                                    # 解析UTC+8, UTC-3等
                                    offset_str = timezone_str[3:]  # +8, -3
                                    if offset_str:
                                        offset_hours = int(offset_str)
                                        # 创建固定偏移时区
                                        from datetime import timedelta
                                        tz_obj = timezone(timedelta(hours=offset_hours))
                                    else:
                                        tz_obj = pytz.UTC
                            else:
                                # 标准时区名称
                                tz_obj = pytz.timezone(timezone_str)
                            
                            self.timezone_cache[timezone_str] = tz_obj
                            
                        except Exception as e:
                            logger.warning(f"⚠️ 无效时区 {timezone_str}，使用UTC: {e}")
                            self.timezone_cache[timezone_str] = pytz.UTC
            
            logger.info(f"✅ 加载了 {len(self.merchant_timezones)} 个商户的时区配置")
            logger.info(f"📊 时区分布: {self._get_timezone_stats()}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 加载商户时区失败: {e}")
            return False
    
    def _get_timezone_stats(self) -> Dict[str, int]:
        """获取时区统计"""
        stats = {}
        for tz in self.merchant_timezones.values():
            stats[tz] = stats.get(tz, 0) + 1
        return stats
    
    def get_merchant_timezone(self, business_no: str) -> str:
        """获取商户时区"""
        return self.merchant_timezones.get(business_no, "UTC")
    
    def get_merchant_local_time(self, business_no: str, utc_time: Optional[datetime] = None) -> datetime:
        """获取商户本地时间"""
        if utc_time is None:
            utc_time = datetime.now(timezone.utc)
        
        timezone_str = self.get_merchant_timezone(business_no)
        tz_obj = self.timezone_cache.get(timezone_str, pytz.UTC)
        
        # 转换为商户本地时间
        local_time = utc_time.astimezone(tz_obj)
        
        return local_time
    
    def should_execute_task(self, task: Dict, current_utc_time: Optional[datetime] = None) -> bool:
        """判断任务是否应该在当前时间执行"""
        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)
        
        business_no = task.get("business_no", "")
        
        # 获取商户本地时间
        local_time = self.get_merchant_local_time(business_no, current_utc_time)
        local_date = local_time.date()
        
        # 检查日期匹配
        send_frequency = task.get("sendFrequency", "")
        schedule_days = task.get("scheduleDays", [])
        
        is_date_match = False
        if send_frequency == "weekly":
            day_of_week = local_date.weekday() + 1  # 1=周一, 7=周日
            is_date_match = day_of_week in schedule_days
        elif send_frequency == "monthly":
            day_of_month = local_date.day
            is_date_match = day_of_month in schedule_days
        
        if not is_date_match:
            return False
        
        # 检查时间匹配
        schedule_times = task.get("scheduleTimes", [])
        current_time_str = local_time.strftime("%H:%M")
        
        for schedule_time in schedule_times:
            # 处理时间格式 (HH:MM:SS -> HH:MM)
            if len(schedule_time.split(':')) == 3:
                schedule_time_str = ':'.join(schedule_time.split(':')[:2])
            else:
                schedule_time_str = schedule_time
            
            if current_time_str == schedule_time_str:
                logger.debug(f"🎯 任务匹配: {task.get('_id')} 在商户时区 {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                return True
        
        return False
    
    def filter_tasks_by_priority(self, tasks: List[Dict], current_utc_time: Optional[datetime] = None) -> List[Dict]:
        """按优先级过滤任务 (月任务优先于周任务)"""
        if not tasks:
            return []
        
        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)
        
        # 按商户、语言、群组分组
        groups = {}
        for task in tasks:
            business_no = task.get("business_no", "")
            notify_id = task.get("notifyId", "")
            channel_id = task.get("channelId", "")
            
            # 获取通知配置的语言 (需要从notify配置中获取)
            # 这里简化处理，实际需要查询c_tgNotify表
            group_key = f"{business_no}_{notify_id}_{channel_id}"
            
            if group_key not in groups:
                groups[group_key] = {"weekly": [], "monthly": []}
            
            frequency = task.get("sendFrequency", "")
            if frequency in groups[group_key]:
                groups[group_key][frequency].append(task)
        
        # 应用优先级规则
        filtered_tasks = []
        for group_key, group_tasks in groups.items():
            monthly_tasks = group_tasks["monthly"]
            weekly_tasks = group_tasks["weekly"]
            
            if monthly_tasks:
                # 有月任务时，只执行月任务
                filtered_tasks.extend(monthly_tasks)
                if weekly_tasks:
                    logger.info(f"📅 优先级过滤: {group_key} 有月任务，忽略 {len(weekly_tasks)} 个周任务")
            else:
                # 没有月任务时，执行周任务
                filtered_tasks.extend(weekly_tasks)
        
        return filtered_tasks
    
    def get_tasks_for_execution(self, all_tasks: List[Dict], current_utc_time: Optional[datetime] = None) -> List[Dict]:
        """获取当前时间应该执行的任务"""
        if current_utc_time is None:
            current_utc_time = datetime.now(timezone.utc)
        
        # 1. 过滤出时间匹配的任务
        matching_tasks = []
        for task in all_tasks:
            if self.should_execute_task(task, current_utc_time):
                matching_tasks.append(task)
        
        logger.debug(f"🔍 时间匹配的任务: {len(matching_tasks)}")
        
        # 2. 应用优先级过滤
        filtered_tasks = self.filter_tasks_by_priority(matching_tasks, current_utc_time)
        
        logger.debug(f"🎯 优先级过滤后的任务: {len(filtered_tasks)}")
        
        return filtered_tasks
    
    def log_timezone_info(self, business_no: str):
        """记录时区信息用于调试"""
        utc_time = datetime.now(timezone.utc)
        local_time = self.get_merchant_local_time(business_no, utc_time)
        timezone_str = self.get_merchant_timezone(business_no)
        
        logger.info(f"🌍 商户 {business_no} 时区信息:")
        logger.info(f"   时区: {timezone_str}")
        logger.info(f"   UTC时间: {utc_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"   本地时间: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        logger.info(f"   时差: {(local_time.utcoffset().total_seconds() / 3600):.1f} 小时")
