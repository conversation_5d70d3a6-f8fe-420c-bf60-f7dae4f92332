#!/usr/bin/env python3
"""
周期性推送处理器基类
定义所有业务处理器必须实现的接口
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, List
from config.logging_config import app_logger


class BasePeriodicHandler(ABC):
    """周期性推送处理器基类"""
    
    def __init__(self, message_type: int):
        self.message_type = message_type
        self.type_key = str(message_type)
    
    @abstractmethod
    async def check_win_condition(self, log: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """
        检查是否满足获奖条件
        
        Args:
            log: 游戏日志记录
            config: 通知配置
            
        Returns:
            bool: 是否满足条件
        """
        pass
    
    @abstractmethod
    async def process_winning_log(self, log: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """
        处理满足条件的获奖日志
        
        Args:
            log: 游戏日志记录
            config: 通知配置
            
        Returns:
            bool: 处理是否成功
        """
        pass
    
    @abstractmethod
    def get_default_config(self) -> Dict[str, Any]:
        """
        获取默认配置（仅用于文档说明，实际不使用）
        
        Returns:
            Dict[str, Any]: 默认配置
        """
        pass
    
    def validate_config(self, type_config: Dict[str, Any]) -> bool:
        """
        验证type配置是否有效
        
        Args:
            type_config: type配置
            
        Returns:
            bool: 配置是否有效
        """
        try:
            required_fields = ['check_interval', 'game_end_wait', 'query_time_range', 'max_records_per_query']
            
            for field in required_fields:
                if field not in type_config:
                    app_logger.error(f"❌ Type {self.message_type} 配置缺少必需字段: {field}")
                    return False
            
            # 验证query_time_range结构
            query_time_range = type_config.get('query_time_range', {})
            if 'min_ago' not in query_time_range or 'max_ago' not in query_time_range:
                app_logger.error(f"❌ Type {self.message_type} query_time_range配置不完整")
                return False
            
            return True
            
        except Exception as e:
            app_logger.error(f"❌ Type {self.message_type} 配置验证失败: {e}")
            return False
    
    def log_processing_info(self, action: str, **kwargs):
        """记录处理信息"""
        app_logger.info(f"🔄 Type {self.message_type} {action}: {kwargs}")
    
    def log_processing_debug(self, action: str, **kwargs):
        """记录调试信息"""
        app_logger.debug(f"🔍 Type {self.message_type} {action}: {kwargs}")
    
    def log_processing_error(self, action: str, error: Exception, **kwargs):
        """记录错误信息"""
        app_logger.error(f"❌ Type {self.message_type} {action} 失败: {error}, {kwargs}")
        import traceback
        traceback.print_exc()
