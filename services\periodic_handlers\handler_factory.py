#!/usr/bin/env python3
"""
周期性推送处理器工厂
负责创建和管理各种类型的处理器
"""
from typing import Dict, Optional
from .base_handler import BasePeriodicHandler
from config.logging_config import app_logger


class HandlerFactory:
    """处理器工厂"""
    
    def __init__(self):
        self._handlers: Dict[int, BasePeriodicHandler] = {}
        self._register_handlers()
    
    def _register_handlers(self):
        """注册所有可用的处理器"""
        try:
            # 注册 Type 300 处理器
            from .type_300_handler import type_300_handler
            self._handlers[300] = type_300_handler
            app_logger.info("📋 注册 Type 300 处理器")
            
            # 未来可以在这里注册更多处理器
            # from .type_400_handler import type_400_handler
            # self._handlers[400] = type_400_handler
            
        except Exception as e:
            app_logger.error(f"❌ 注册处理器失败: {e}")
    
    def get_handler(self, message_type: int) -> Optional[BasePeriodicHandler]:
        """
        获取指定类型的处理器
        
        Args:
            message_type: 消息类型
            
        Returns:
            BasePeriodicHandler: 处理器实例，如果不存在返回None
        """
        handler = self._handlers.get(message_type)
        if handler is None:
            app_logger.warning(f"⚠️ 未找到 Type {message_type} 的处理器")
        return handler
    
    def get_supported_types(self) -> list[int]:
        """
        获取所有支持的消息类型
        
        Returns:
            List[int]: 支持的消息类型列表
        """
        return list(self._handlers.keys())
    
    def validate_type_config(self, message_type: int, type_config: Dict) -> bool:
        """
        验证指定类型的配置
        
        Args:
            message_type: 消息类型
            type_config: 类型配置
            
        Returns:
            bool: 配置是否有效
        """
        handler = self.get_handler(message_type)
        if handler is None:
            return False
        
        return handler.validate_config(type_config)


# 创建全局工厂实例
handler_factory = HandlerFactory()
