#!/usr/bin/env python3
"""
周期性推送服务
定期查询游戏日志表，检测满足条件的数据并执行推送
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from config.settings import settings
from database.mysql_connection import MySQLConnection
from database.mongodb_connection import MongoDBConnection
from pusher.template_message_handler import TemplateMessageHandler


class PeriodicPushService:
    """周期性推送服务类"""
    
    def __init__(self):
        self.mysql_conn = MySQLConnection()
        self.mongo_conn = MongoDBConnection()
        self.pusher = TemplateMessageHandler()
        
        # 配置缓存
        self.notify_configs = {}  # c_tgNotify配置缓存
        self.type_configs = {}    # 按type分组的配置和状态
        
        # 运行状态
        self.is_running = False
        self.task = None
        
    async def initialize(self):
        """初始化服务"""
        try:
            app_logger.info("🔧 初始化周期性推送服务")
            
            # 连接数据库
            await self.mysql_conn.connect()
            if not self.mongo_conn.connect():
                raise Exception("MongoDB连接失败")
            
            # 加载配置
            await self.load_notify_configs()
            await self.load_type_configs()
            
            app_logger.info("✅ 周期性推送服务初始化完成")
            return True
            
        except Exception as e:
            app_logger.error(f"❌ 周期性推送服务初始化失败: {e}")
            return False
    
    async def load_notify_configs(self):
        """加载通知配置（types=300）"""
        try:
            app_logger.info("📋 加载通知配置")

            # 尝试从配置管理器获取配置（如果在统一服务器中运行）
            try:
                from scheduler.config_manager import config_manager

                # 检查配置管理器是否已初始化
                if hasattr(config_manager, 'notify_cache') and config_manager.notify_cache:
                    app_logger.info("🔄 从配置管理器缓存加载配置")

                    # 从缓存中筛选types包含300的配置
                    self.notify_configs = {}
                    for config in config_manager.notify_cache.values():
                        types = config.get('types', [])
                        if isinstance(types, list) and 300 in types and config.get('open', False):
                            business_no = config.get('business_no')
                            if business_no:
                                self.notify_configs[business_no] = config

                    app_logger.info(f"📊 从缓存加载了 {len(self.notify_configs)} 个商户的周期性推送配置")

                else:
                    # 配置管理器未初始化，直接查询数据库
                    app_logger.info("📋 配置管理器未初始化，直接查询数据库")
                    await self._load_configs_from_db()

            except ImportError:
                # 不在统一服务器环境中，直接查询数据库
                app_logger.info("📋 独立运行模式，直接查询数据库")
                await self._load_configs_from_db()

            # 显示配置详情
            for business_no, config in self.notify_configs.items():
                platform_ids = config.get('platformId', [])
                game_ids = config.get('gameId', [])
                app_logger.info(f"   商户 {business_no}: gameType={config.get('gameType')}, "
                              f"platformId={platform_ids}, gameId={game_ids}, "
                              f"gameWinMul={config.get('gameWinMul')}")

        except Exception as e:
            app_logger.error(f"❌ 加载通知配置失败: {e}")
            self.notify_configs = {}

    async def _load_configs_from_db(self):
        """从数据库直接加载配置"""
        # 获取集合并查询types包含300的配置
        collection = self.mongo_conn.get_collection("c_tgNotify")
        if collection is None:
            app_logger.error("❌ 无法获取c_tgNotify集合")
            return

        # types是list类型，需要查询包含300的记录
        query = {"types": {"$in": [300]}, "open": True}
        configs = list(collection.find(query))

        self.notify_configs = {}
        for config in configs:
            business_no = config.get('business_no')
            if business_no:
                self.notify_configs[business_no] = config

        app_logger.info(f"📊 从数据库加载了 {len(self.notify_configs)} 个商户的周期性推送配置")
    
    async def load_type_configs(self):
        """加载按type分组的配置和状态"""
        try:
            app_logger.info("📋 加载type配置和状态")

            # 从状态文件加载
            state_file = Path(__file__).parent.parent / "config" / "periodic_push_state.json"

            if state_file.exists():
                try:
                    import json
                    with open(state_file, 'r', encoding='utf-8') as f:
                        state_data = json.load(f)

                    self.type_configs = state_data
                    app_logger.info(f"📂 从状态文件加载: {len(self.type_configs)} 个type的配置")

                except Exception as e:
                    app_logger.warning(f"⚠️ 读取状态文件失败: {e}，使用默认值")
                    self.type_configs = {}
            else:
                app_logger.info("📂 状态文件不存在，初始化默认配置")
                self.type_configs = {}

            # 验证配置的有效性，移除无效配置
            from .periodic_handlers import handler_factory

            valid_types = []
            for type_key, type_data in list(self.type_configs.items()):
                try:
                    message_type = int(type_key)
                    type_config = type_data.get('config', {})

                    # 检查是否有对应的处理器
                    handler = handler_factory.get_handler(message_type)
                    if handler is None:
                        app_logger.warning(f"⚠️ Type {message_type} 没有对应的处理器，忽略配置")
                        del self.type_configs[type_key]
                        continue

                    # 验证配置有效性
                    if not handler.validate_config(type_config):
                        app_logger.error(f"❌ Type {message_type} 配置无效，忽略配置")
                        del self.type_configs[type_key]
                        continue

                    valid_types.append(message_type)
                    app_logger.info(f"✅ Type {message_type} 配置有效")

                except (ValueError, TypeError) as e:
                    app_logger.error(f"❌ 无效的type key: {type_key}, 错误: {e}")
                    del self.type_configs[type_key]

            if not valid_types:
                app_logger.warning("⚠️ 没有找到有效的type配置")
            else:
                app_logger.info(f"📊 加载了 {len(valid_types)} 个有效的type配置: {valid_types}")

            # 确保当前月份的表有初始值
            current_month = datetime.now().strftime("%Y-%m")
            table_name = f"ea_platform_agent_game_log_{current_month}"

            for type_key, type_data in self.type_configs.items():
                last_numbers = type_data.get('last_numbers', {})
                if table_name not in last_numbers:
                    last_numbers[table_name] = "0"
                    app_logger.info(f"📊 初始化type {type_key} 表 {table_name} 的last_number: 0")
                else:
                    app_logger.info(f"📊 type {type_key} 表 {table_name} 的最后处理number: {last_numbers[table_name]}")

        except Exception as e:
            app_logger.error(f"❌ 加载type配置失败: {e}")
            self.type_configs = {}
    
    async def save_last_number(self, message_type: int, table_name: str, last_number: str):
        """保存最后处理的number"""
        try:
            type_key = str(message_type)

            # 确保type配置存在
            if type_key not in self.type_configs:
                self.type_configs[type_key] = {
                    "config": {},
                    "last_numbers": {},
                    "last_updated": None
                }

            # 更新内存中的值
            self.type_configs[type_key]["last_numbers"][table_name] = last_number
            self.type_configs[type_key]["last_updated"] = datetime.now().isoformat()

            # 持久化到状态文件
            await self._persist_state()

            app_logger.debug(f"💾 保存type {message_type} 最后处理number: {table_name} -> {last_number}")

        except Exception as e:
            app_logger.error(f"❌ 保存最后处理number失败: {e}")

    async def get_last_number(self, message_type: int, table_name: str) -> str:
        """获取最后处理的number"""
        try:
            type_key = str(message_type)
            if type_key in self.type_configs:
                last_numbers = self.type_configs[type_key].get("last_numbers", {})
                return last_numbers.get(table_name, "0")
            return "0"
        except Exception as e:
            app_logger.error(f"❌ 获取最后处理number失败: {e}")
            return "0"

    async def get_type_config(self, message_type: int) -> Dict[str, Any]:
        """获取指定type的配置"""
        try:
            type_key = str(message_type)
            if type_key in self.type_configs:
                return self.type_configs[type_key].get("config", {})

            # 不提供默认配置，只处理明确配置的类型
            app_logger.warning(f"⚠️ Type {message_type} 没有配置，跳过处理")
            return {}
        except Exception as e:
            app_logger.error(f"❌ 获取type配置失败: {e}")
            return {}

    async def _persist_state(self):
        """持久化状态到文件"""
        try:
            import json

            state_file = Path(__file__).parent.parent / "config" / "periodic_push_state.json"

            # 确保目录存在
            state_file.parent.mkdir(parents=True, exist_ok=True)

            # 写入文件
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(self.type_configs, f, indent=2, ensure_ascii=False)

            app_logger.debug(f"💾 状态已持久化到: {state_file}")

        except Exception as e:
            app_logger.error(f"❌ 持久化状态失败: {e}")

    async def refresh_configs(self):
        """刷新配置（用于热更新）"""
        try:
            app_logger.info("🔄 刷新周期性推送配置")
            old_count = len(self.notify_configs)

            # 重新加载配置
            await self.load_notify_configs()

            new_count = len(self.notify_configs)
            app_logger.info(f"📊 配置刷新完成: {old_count} -> {new_count}")

            return True

        except Exception as e:
            app_logger.error(f"❌ 刷新配置失败: {e}")
            return False
    
    async def get_current_table_name(self) -> str:
        """获取当前月份的表名"""
        current_month = datetime.now().strftime("%Y-%m")
        return f"ea_platform_agent_game_log_{current_month}"
    
    async def query_new_logs(self, table_name: str, last_number: str, type_config: Dict[str, Any]) -> List[Dict[str, Any]]:
        """查询新的游戏日志"""
        try:
            # 从type配置获取参数
            query_time_range = type_config.get('query_time_range', {})
            min_ago = query_time_range.get('min_ago', 240)  # 默认4分钟前
            max_ago = query_time_range.get('max_ago', 3600)  # 默认1小时前
            max_records = type_config.get('max_records_per_query', 1000)

            # 计算时间戳
            current_time = int(time.time())
            min_time = current_time - min_ago  # 4分钟前
            max_time = current_time - max_ago  # 1小时前

            # 构建查询SQL
            query = f"""
            SELECT number, create_time, gameType, platformId, gameId,
                   playerId, playerName, betAmount, winAmount, profit,
                   agentId, currencyId, status
            FROM `{table_name}`
            WHERE number > %s
              AND create_time < %s
              AND create_time > %s
            ORDER BY number ASC
            LIMIT %s
            """

            params = (last_number, min_time, max_time, max_records)

            app_logger.debug(f"🔍 查询新游戏日志: {table_name}")
            app_logger.debug(f"   last_number > {last_number}")
            app_logger.debug(f"   create_time < {min_time} ({datetime.fromtimestamp(min_time)})")
            app_logger.debug(f"   create_time > {max_time} ({datetime.fromtimestamp(max_time)})")
            app_logger.debug(f"   limit: {max_records}")

            results = await self.mysql_conn.execute_query(query, params)

            app_logger.debug(f"📊 查询到 {len(results) if results else 0} 条新记录")

            return results or []

        except Exception as e:
            app_logger.error(f"❌ 查询新游戏日志失败: {e}")
            return []
    
    # 业务逻辑已移至各自的处理器中
    # check_win_condition 和 process_winning_log 方法已移至 periodic_handlers

    
    async def process_periodic_check(self):
        """执行一次周期性检查"""
        try:
            app_logger.debug("🔄 开始周期性检查")

            # 处理每个商户的配置
            for business_no, config in self.notify_configs.items():
                types = config.get('types', [])

                # 处理每个type
                for message_type in types:
                    if message_type == 300:  # 只处理周期性推送类型
                        await self.process_type_check(message_type, config)

        except Exception as e:
            app_logger.error(f"❌ 周期性检查失败: {e}")

    async def process_type_check(self, message_type: int, config: Dict[str, Any]):
        """处理指定type的检查"""
        try:
            app_logger.debug(f"🔄 处理type {message_type} 检查")

            # 获取type配置
            type_config = await self.get_type_config(message_type)
            if not type_config:
                app_logger.debug(f"📊 type {message_type} 没有配置，跳过处理")
                return

            # 获取对应的处理器
            from .periodic_handlers import handler_factory
            handler = handler_factory.get_handler(message_type)
            if handler is None:
                app_logger.warning(f"⚠️ type {message_type} 没有对应的处理器，跳过处理")
                return

            # 获取当前月份的表名
            current_month = datetime.now().strftime("%Y-%m")
            table_name = f"ea_platform_agent_game_log_{current_month}"

            # 获取最后处理的number
            last_number = await self.get_last_number(message_type, table_name)

            # 查询新的日志记录
            new_logs = await self.query_new_logs(table_name, last_number, type_config)

            if not new_logs:
                app_logger.debug(f"📊 type {message_type} 表 {table_name} 没有新的记录")
                return

            app_logger.info(f"📊 type {message_type} 表 {table_name} 找到 {len(new_logs)} 条新记录")

            # 处理每条日志
            processed_count = 0
            winning_count = 0
            last_processed_number = None

            # 处理每条日志
            for log in new_logs:
                last_processed_number = log.get('number')
                processed_count += 1

                # 使用处理器检查获奖条件
                if await handler.check_win_condition(log, config):
                    # 使用处理器处理获奖日志
                    success = await handler.process_winning_log(log, config)
                    if success:
                        winning_count += 1

            # 保存最后处理的number
            if last_processed_number:
                await self.save_last_number(message_type, table_name, last_processed_number)

            app_logger.info(f"✅ type {message_type} 检查完成: 处理 {processed_count} 条记录, 获奖 {winning_count} 条")

        except Exception as e:
            app_logger.error(f"❌ type {message_type} 检查失败: {e}")
            import traceback
            traceback.print_exc()
    
    async def start(self):
        """启动周期性推送服务"""
        if self.is_running:
            app_logger.warning("⚠️ 周期性推送服务已在运行")
            return
        
        app_logger.info("🚀 启动周期性推送服务")
        self.is_running = True
        
        # 创建后台任务
        self.task = asyncio.create_task(self._run_periodic_loop())
        
        app_logger.info("✅ 周期性推送服务已启动")
    
    async def stop(self):
        """停止周期性推送服务"""
        if not self.is_running:
            app_logger.warning("⚠️ 周期性推送服务未在运行")
            return
        
        app_logger.info("🛑 停止周期性推送服务")
        self.is_running = False
        
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        app_logger.info("✅ 周期性推送服务已停止")
    
    async def _run_periodic_loop(self):
        """运行周期性循环"""
        app_logger.info("🔄 开始周期性推送循环 (每60秒检查一次)")

        config_refresh_counter = 0

        while self.is_running:
            try:
                # 每10次循环（10分钟）刷新一次配置
                config_refresh_counter += 1
                if config_refresh_counter >= 10:
                    app_logger.debug("🔄 定期刷新配置")
                    await self.refresh_configs()
                    config_refresh_counter = 0

                # 执行周期性检查
                await self.process_periodic_check()

                # 等待60秒
                await asyncio.sleep(60)

            except asyncio.CancelledError:
                app_logger.info("🛑 周期性推送循环被取消")
                break
            except Exception as e:
                app_logger.error(f"❌ 周期性推送循环异常: {e}")
                # 出错后等待30秒再继续
                await asyncio.sleep(30)
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop()
            await self.mysql_conn.disconnect()
            self.mongo_conn.disconnect()
            app_logger.info("✅ 周期性推送服务资源清理完成")
        except Exception as e:
            app_logger.error(f"❌ 周期性推送服务资源清理失败: {e}")


# 全局服务实例
periodic_push_service = PeriodicPushService()
