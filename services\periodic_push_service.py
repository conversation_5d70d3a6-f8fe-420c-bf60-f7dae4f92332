#!/usr/bin/env python3
"""
周期性推送服务
定期查询游戏日志表，检测满足条件的数据并执行推送
"""
import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import sys

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from config.settings import settings
from database.mysql_connection import MySQLConnection
from database.mongodb_connection import MongoDBConnection
from pusher.template_message_handler import TemplateMessageHandler


class PeriodicPushService:
    """周期性推送服务类"""
    
    def __init__(self):
        self.mysql_conn = MySQLConnection()
        self.mongo_conn = MongoDBConnection()
        self.pusher = TemplateMessageHandler()
        
        # 配置缓存
        self.notify_configs = {}  # c_tgNotify配置缓存
        self.last_numbers = {}    # 每个表的最后处理number
        
        # 运行状态
        self.is_running = False
        self.task = None
        
    async def initialize(self):
        """初始化服务"""
        try:
            app_logger.info("🔧 初始化周期性推送服务")
            
            # 连接数据库
            await self.mysql_conn.connect()
            if not self.mongo_conn.connect():
                raise Exception("MongoDB连接失败")
            
            # 加载配置
            await self.load_notify_configs()
            await self.load_last_numbers()
            
            app_logger.info("✅ 周期性推送服务初始化完成")
            return True
            
        except Exception as e:
            app_logger.error(f"❌ 周期性推送服务初始化失败: {e}")
            return False
    
    async def load_notify_configs(self):
        """加载通知配置（types=300）"""
        try:
            app_logger.info("📋 加载通知配置")
            
            # 获取集合并查询types=300的配置
            collection = self.mongo_conn.get_collection("c_tgNotify")
            if collection is None:
                app_logger.error("❌ 无法获取c_tgNotify集合")
                return

            query = {"types": 300}
            configs = list(collection.find(query))
            
            self.notify_configs = {}
            for config in configs:
                merchant_id = config.get('merchantId')
                if merchant_id:
                    self.notify_configs[merchant_id] = config
            
            app_logger.info(f"📊 加载了 {len(self.notify_configs)} 个商户的周期性推送配置")
            
            # 显示配置详情
            for merchant_id, config in self.notify_configs.items():
                app_logger.info(f"   商户 {merchant_id}: gameType={config.get('gameType')}, "
                              f"platformId={config.get('platformId')}, gameId={config.get('gameId')}, "
                              f"gameWinMul={config.get('gameWinMul')}")
            
        except Exception as e:
            app_logger.error(f"❌ 加载通知配置失败: {e}")
            self.notify_configs = {}
    
    async def load_last_numbers(self):
        """加载最后处理的number"""
        try:
            app_logger.info("📋 加载最后处理的number")
            
            # 从配置中加载或初始化为0
            self.last_numbers = {}
            
            # 获取当前年月
            current_month = datetime.now().strftime("%Y-%m")
            table_name = f"ea_platform_agent_game_log_{current_month}"
            
            # 从配置文件或数据库加载last_number，如果没有则设为0
            config_key = f"periodic_push.last_numbers.{table_name}"
            last_number = settings.get(config_key, "0")
            
            self.last_numbers[table_name] = last_number
            
            app_logger.info(f"📊 表 {table_name} 的最后处理number: {last_number}")
            
        except Exception as e:
            app_logger.error(f"❌ 加载最后处理number失败: {e}")
            self.last_numbers = {}
    
    async def save_last_number(self, table_name: str, last_number: str):
        """保存最后处理的number"""
        try:
            self.last_numbers[table_name] = last_number
            
            # 这里可以保存到配置文件或数据库
            # 暂时只保存在内存中
            app_logger.debug(f"💾 保存最后处理number: {table_name} -> {last_number}")
            
        except Exception as e:
            app_logger.error(f"❌ 保存最后处理number失败: {e}")
    
    async def get_current_table_name(self) -> str:
        """获取当前月份的表名"""
        current_month = datetime.now().strftime("%Y-%m")
        return f"ea_platform_agent_game_log_{current_month}"
    
    async def query_new_game_logs(self, table_name: str) -> List[Dict[str, Any]]:
        """查询新的游戏日志"""
        try:
            # 获取最后处理的number
            last_number = self.last_numbers.get(table_name, "0")
            
            # 计算4分钟前的时间戳
            four_minutes_ago = int(time.time()) - 240
            
            # 构建查询SQL
            query = f"""
            SELECT number, create_time, gameType, platformId, gameId, 
                   playerId, playerName, betAmount, winAmount, profit,
                   agentId, currencyId, status
            FROM `{table_name}`
            WHERE number > %s 
              AND create_time < %s
              AND create_time > %s
            ORDER BY number ASC
            LIMIT 1000
            """
            
            # 查询1小时前到4分钟前的数据
            one_hour_ago = int(time.time()) - 3600
            
            params = (last_number, four_minutes_ago, one_hour_ago)
            
            app_logger.debug(f"🔍 查询新游戏日志: {table_name}")
            app_logger.debug(f"   last_number > {last_number}")
            app_logger.debug(f"   create_time < {four_minutes_ago} ({datetime.fromtimestamp(four_minutes_ago)})")
            app_logger.debug(f"   create_time > {one_hour_ago} ({datetime.fromtimestamp(one_hour_ago)})")
            
            results = await self.mysql_conn.execute_query(query, params)
            
            app_logger.debug(f"📊 查询到 {len(results) if results else 0} 条新记录")
            
            return results or []
            
        except Exception as e:
            app_logger.error(f"❌ 查询新游戏日志失败: {e}")
            return []
    
    async def check_win_condition(self, log: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """检查是否满足获奖条件"""
        try:
            # 检查游戏类型
            if config.get('gameType') and log.get('gameType') != config.get('gameType'):
                return False
            
            # 检查平台ID
            if config.get('platformId') and log.get('platformId') != config.get('platformId'):
                return False
            
            # 检查游戏ID
            if config.get('gameId') and log.get('gameId') != config.get('gameId'):
                return False
            
            # 检查获奖倍数
            game_win_mul = config.get('gameWinMul', 0)
            if game_win_mul > 0:
                bet_amount = float(log.get('betAmount', 0))
                win_amount = float(log.get('winAmount', 0))
                
                if bet_amount > 0:
                    win_multiplier = win_amount / bet_amount
                    if win_multiplier < game_win_mul:
                        return False
                else:
                    return False
            
            app_logger.debug(f"✅ 记录满足获奖条件: number={log.get('number')}, "
                           f"gameType={log.get('gameType')}, platformId={log.get('platformId')}, "
                           f"gameId={log.get('gameId')}, winMul={win_amount/bet_amount if bet_amount > 0 else 0:.2f}")
            
            return True
            
        except Exception as e:
            app_logger.error(f"❌ 检查获奖条件失败: {e}")
            return False
    
    async def process_winning_log(self, log: Dict[str, Any], config: Dict[str, Any]):
        """处理满足条件的获奖日志"""
        try:
            app_logger.info(f"🎉 处理获奖日志: number={log.get('number')}")

            # 构建推送参数
            player_name = log.get('playerName', f"WG{log.get('playerId')}")
            bet_amount = float(log.get('betAmount', 0))
            win_amount = float(log.get('winAmount', 0))
            win_multiplier = win_amount / bet_amount if bet_amount > 0 else 0

            # 构建参数列表 (对应模板中的 {arg1}, {arg2}, {arg3}, {arg4}, {arg5})
            params = [
                player_name,                    # {arg1} - 玩家名
                f"{win_multiplier:.1f}",       # {arg2} - 获奖倍数
                f"{bet_amount:.2f}",           # {arg3} - 投注金额
                "Game",                        # {arg4} - 游戏名 (暂时固定)
                f"{win_amount:.2f}"            # {arg5} - 总奖金
            ]

            # 使用模板消息处理器发送消息
            # 这里使用 merchantId 作为 business_no，types 作为 message_type
            result = await self.pusher.send_template_message(
                business_no=str(config.get('merchantId')),
                message_type=config.get('types', 300),
                params=params
            )

            if result.get('success'):
                app_logger.info(f"✅ 获奖推送成功: number={log.get('number')}")
            else:
                app_logger.error(f"❌ 获奖推送失败: number={log.get('number')}, 原因: {result.get('message')}")

        except Exception as e:
            app_logger.error(f"❌ 处理获奖日志失败: {e}")
    
    async def process_periodic_check(self):
        """执行一次周期性检查"""
        try:
            app_logger.debug("🔄 开始周期性检查")
            
            # 获取当前表名
            table_name = await self.get_current_table_name()
            
            # 查询新的游戏日志
            new_logs = await self.query_new_game_logs(table_name)
            
            if not new_logs:
                app_logger.debug("📊 没有新的游戏日志")
                return
            
            app_logger.info(f"📊 处理 {len(new_logs)} 条新游戏日志")
            
            processed_count = 0
            winning_count = 0
            last_number = None
            
            # 处理每条日志
            for log in new_logs:
                last_number = log.get('number')
                processed_count += 1
                
                # 检查每个商户的配置
                for merchant_id, config in self.notify_configs.items():
                    if await self.check_win_condition(log, config):
                        await self.process_winning_log(log, config)
                        winning_count += 1
            
            # 更新最后处理的number
            if last_number:
                await self.save_last_number(table_name, last_number)
            
            app_logger.info(f"📊 周期性检查完成: 处理 {processed_count} 条记录, 发现 {winning_count} 条获奖记录")
            
        except Exception as e:
            app_logger.error(f"❌ 周期性检查失败: {e}")
    
    async def start(self):
        """启动周期性推送服务"""
        if self.is_running:
            app_logger.warning("⚠️ 周期性推送服务已在运行")
            return
        
        app_logger.info("🚀 启动周期性推送服务")
        self.is_running = True
        
        # 创建后台任务
        self.task = asyncio.create_task(self._run_periodic_loop())
        
        app_logger.info("✅ 周期性推送服务已启动")
    
    async def stop(self):
        """停止周期性推送服务"""
        if not self.is_running:
            app_logger.warning("⚠️ 周期性推送服务未在运行")
            return
        
        app_logger.info("🛑 停止周期性推送服务")
        self.is_running = False
        
        if self.task:
            self.task.cancel()
            try:
                await self.task
            except asyncio.CancelledError:
                pass
        
        app_logger.info("✅ 周期性推送服务已停止")
    
    async def _run_periodic_loop(self):
        """运行周期性循环"""
        app_logger.info("🔄 开始周期性推送循环 (每60秒检查一次)")
        
        while self.is_running:
            try:
                await self.process_periodic_check()
                
                # 等待60秒
                await asyncio.sleep(60)
                
            except asyncio.CancelledError:
                app_logger.info("🛑 周期性推送循环被取消")
                break
            except Exception as e:
                app_logger.error(f"❌ 周期性推送循环异常: {e}")
                # 出错后等待30秒再继续
                await asyncio.sleep(30)
    
    async def cleanup(self):
        """清理资源"""
        try:
            await self.stop()
            await self.mysql_conn.disconnect()
            self.mongo_conn.disconnect()
            app_logger.info("✅ 周期性推送服务资源清理完成")
        except Exception as e:
            app_logger.error(f"❌ 周期性推送服务资源清理失败: {e}")


# 全局服务实例
periodic_push_service = PeriodicPushService()
