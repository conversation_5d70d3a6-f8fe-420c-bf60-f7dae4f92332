#!/usr/bin/env python3
"""
Type 300 周期性获奖推送处理器
处理获奖推送的具体业务逻辑
"""
from typing import Dict, Any, List
from config.logging_config import app_logger


class Type300Handler:
    """Type 300 获奖推送处理器"""
    
    def __init__(self):
        self.message_type = 300
    
    async def check_win_condition(self, log: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """检查是否满足获奖条件"""
        try:
            # 检查游戏类型
            config_game_type = config.get('gameType')
            if config_game_type and log.get('gameType') != config_game_type:
                return False
            
            # 检查平台ID (platformId是list类型)
            config_platform_ids = config.get('platformId', [])
            if config_platform_ids and log.get('platformId') not in config_platform_ids:
                return False
            
            # 检查游戏ID (gameId是list类型)
            config_game_ids = config.get('gameId', [])
            if config_game_ids and log.get('gameId') not in config_game_ids:
                return False
            
            # 检查获奖倍数
            game_win_mul = config.get('gameWinMul', 0)
            if game_win_mul > 0:
                bet_amount = float(log.get('betAmount', 0))
                win_amount = float(log.get('winAmount', 0))
                
                if bet_amount > 0:
                    win_multiplier = win_amount / bet_amount
                    if win_multiplier < game_win_mul:
                        return False
                else:
                    return False
            
            app_logger.debug(f"✅ Type 300 记录满足获奖条件: number={log.get('number')}, "
                           f"gameType={log.get('gameType')}, platformId={log.get('platformId')}, "
                           f"gameId={log.get('gameId')}, winMul={win_amount/bet_amount if bet_amount > 0 else 0:.2f}")
            
            return True
            
        except Exception as e:
            app_logger.error(f"❌ Type 300 检查获奖条件失败: {e}")
            return False
    
    async def process_winning_log(self, log: Dict[str, Any], config: Dict[str, Any]) -> bool:
        """处理满足条件的获奖日志"""
        try:
            app_logger.info(f"🎉 Type 300 处理获奖日志: number={log.get('number')}")
            
            # 获取基础数据
            player_id = log.get('playerId')
            game_id = log.get('gameId')
            currency_id = log.get('currencyId')
            bet_amount = float(log.get('betAmount', 0))
            win_amount = float(log.get('winAmount', 0))

            # 使用数据转换器获取真实的玩家名、游戏名和币种名
            from common.data_converters import data_converter

            # 转换数据
            player_name = await data_converter.get_player_name(player_id)
            game_name = await data_converter.get_game_name(game_id, language=config.get('language', 1))
            currency_name = await data_converter.get_currency_name(currency_id) if currency_id else "USDT"

            app_logger.debug(f"🔄 Type 300 数据转换结果:")
            app_logger.debug(f"   playerId={player_id} -> playerName='{player_name}'")
            app_logger.debug(f"   gameId={game_id} -> gameName='{game_name}'")
            app_logger.debug(f"   currencyId={currency_id} -> currencyName='{currency_name}'")

            # 构建参数列表，根据模板格式
            # 模板: "Congratulations to {playname1} for winning {currency3}{amount4} at {gamename2}."
            params = [
                player_name,                    # 参数1 - {playname1} 玩家名
                game_name,                     # 参数2 - {gamename2} 游戏名
                currency_name,                 # 参数3 - {currency3} 币种名
                win_amount                    # 参数4 - {amount4} 获奖金额
            ]

            app_logger.debug(f"📋 Type 300 模板参数: {params}")

            # 获取语言设置
            language_id = config.get('language', 1)

            # 使用现有的模板渲染系统
            from common.translation_manager import translation_manager

            template_text = config.get('text', '')
            language_id_str = translation_manager.get_language_id(language_id)

            # 渲染消息模板
            rendered_message = translation_manager.render_template(
                template_text, 
                params, 
                language_id_str, 
                activity_id=None, 
                message_type=self.message_type
            )

            app_logger.info(f"📄 Type 300 模板渲染结果: {rendered_message}")

            # 获取通知目标和其他配置
            notify_targets = config.get('notifyTarget', [])
            image_url = config.get('msgBanner')

            # 发送到每个通知目标
            success_count = 0
            for target in notify_targets:
                if isinstance(target, int):  # 群组ID
                    try:
                        # 使用全局的bot_client
                        from common.bot_client import bot_client
                        
                        if image_url:
                            # 发送图片消息
                            await bot_client.send_photo(
                                chat_id=target,
                                photo=image_url,
                                caption=rendered_message,
                                parse_mode='HTML'
                            )
                        else:
                            # 发送文本消息
                            await bot_client.send_message(
                                chat_id=target,
                                text=rendered_message,
                                parse_mode='HTML'
                            )
                        
                        success_count += 1
                        app_logger.debug(f"✅ Type 300 推送成功到群组: {target}")
                        
                    except Exception as e:
                        app_logger.error(f"❌ Type 300 推送失败到群组 {target}: {e}")

            if success_count > 0:
                app_logger.info(f"✅ Type 300 获奖推送完成: number={log.get('number')}, "
                              f"成功 {success_count}/{len(notify_targets)}")
                return True
            else:
                app_logger.error(f"❌ Type 300 获奖推送全部失败: number={log.get('number')}")
                return False
            
        except Exception as e:
            app_logger.error(f"❌ Type 300 处理获奖日志失败: {e}")
            import traceback
            traceback.print_exc()
            return False


# 创建全局实例
type_300_handler = Type300Handler()
