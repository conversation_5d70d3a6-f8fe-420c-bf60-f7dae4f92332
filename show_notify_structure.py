#!/usr/bin/env python3
"""
展示c_tgNotify表结构
分析即时推送的消息模板设计
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def show_notify_table_structure():
    """展示notify表结构"""
    print("📋 c_tgNotify表结构分析")
    print("=" * 70)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgNotify")
        if collection is None:
            print("❌ 无法获取c_tgNotify集合")
            return False
        
        # 获取所有记录
        all_records = list(collection.find({}))
        
        if not all_records:
            print("❌ 表中没有数据")
            return False
        
        print(f"📊 总记录数: {len(all_records)}")
        
        # 分析字段结构
        print(f"\n📋 字段结构分析:")
        print("-" * 50)
        
        # 获取所有字段
        all_fields = set()
        for record in all_records:
            all_fields.update(record.keys())
        
        # 分析每个字段的类型和示例值
        field_analysis = {}
        for field in all_fields:
            if field == '_id':
                continue
                
            values = []
            types = set()
            
            for record in all_records:
                if field in record:
                    value = record[field]
                    values.append(value)
                    types.add(type(value).__name__)
            
            field_analysis[field] = {
                'types': list(types),
                'sample_values': list(set(str(v) for v in values[:3])),  # 前3个不重复的值
                'count': len(values)
            }
        
        # 按功能分组显示字段
        field_groups = {
            "基础信息": ["id", "business_no", "tgName", "notifyType"],
            "消息内容": ["text", "msgBanner"],
            "链接配置": ["jumpLink", "tgLink", "urlLabel", "urlType"],
            "目标配置": ["notifyTarget"],
            "定时配置": ["pushTimeString", "pushTime", "cycle"],
            "触发配置": ["types"],
            "业务字段": ["currencyId", "rewardType", "turnoverMul", "amount"],
            "状态时间": ["open", "createTime", "updateTime"]
        }
        
        for group_name, fields in field_groups.items():
            print(f"\n🔸 {group_name}:")
            for field in fields:
                if field in field_analysis:
                    info = field_analysis[field]
                    types_str = "/".join(info['types'])
                    samples_str = ", ".join(info['sample_values'][:2])
                    print(f"  {field:<20} {types_str:<15} 示例: {samples_str}")
        
        # 显示完整记录示例
        print(f"\n📄 完整记录示例:")
        print("=" * 70)
        
        # 按notifyType分组显示
        realtime_records = [r for r in all_records if r.get("notifyType") == 1]
        scheduled_records = [r for r in all_records if r.get("notifyType") == 2]
        
        if realtime_records:
            print(f"\n🔴 实时通知 (notifyType=1) 示例:")
            print("-" * 50)
            example = realtime_records[0]
            for key, value in example.items():
                if key != '_id':
                    print(f"  {key}: {value}")
        
        if scheduled_records:
            print(f"\n🔵 定时通知 (notifyType=2) 示例:")
            print("-" * 50)
            example = scheduled_records[0]
            for key, value in example.items():
                if key != '_id':
                    print(f"  {key}: {value}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_realtime_push_design():
    """分析即时推送设计"""
    print(f"\n🚀 即时推送接口设计分析")
    print("=" * 70)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgNotify")
        if collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 获取实时通知记录
        realtime_records = list(collection.find({"notifyType": 1}))
        
        print(f"📊 实时通知模板数量: {len(realtime_records)}")
        
        if realtime_records:
            print(f"\n📋 实时通知模板列表:")
            print("-" * 50)
            
            for i, record in enumerate(realtime_records, 1):
                notify_id = record.get("id")
                name = record.get("tgName", "")
                types = record.get("types", [])
                text = record.get("text", "")
                
                print(f"模板 {i}:")
                print(f"  ID: {notify_id}")
                print(f"  名称: {name}")
                print(f"  触发类型: {types}")
                print(f"  消息模板: {text}")
                print(f"  业务字段:")
                
                business_fields = ["currencyId", "rewardType", "turnoverMul", "amount"]
                for field in business_fields:
                    if field in record and record[field] not in [0, "", None]:
                        print(f"    {field}: {record[field]}")
                
                print("-" * 30)
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def suggest_realtime_api_design():
    """建议即时推送API设计"""
    print(f"\n💡 即时推送API设计建议")
    print("=" * 70)
    
    print("基于notify表结构，建议设计两个即时推送接口:")
    print()
    
    print("🔸 接口1: 完整消息推送")
    print("-" * 40)
    print("POST /api/realtime-push/direct")
    print()
    print("请求参数:")
    api1_example = {
        "bot_token": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
        "chat_ids": [-1002316158105, "pd001"],
        "message": {
            "text": "您有新的奖励！",
            "image_url": "https://example.com/image.png",
            "buttons": [
                {
                    "text": "查看详情",
                    "url": "https://example.com/details",
                    "type": "browser"
                }
            ]
        }
    }
    print(json.dumps(api1_example, indent=2, ensure_ascii=False))
    
    print("\n🔸 接口2: 模板消息推送")
    print("-" * 40)
    print("POST /api/realtime-push/template")
    print()
    print("请求参数:")
    api2_example = {
        "business_no": "39bac42a",
        "template_type": 1,  # 对应notify表的types字段
        "target_chats": [-1002316158105],  # 可选，覆盖模板默认目标
        "variables": {  # 动态替换变量
            "amount": 100.0,
            "currency": "USDT",
            "user_name": "张三"
        }
    }
    print(json.dumps(api2_example, indent=2, ensure_ascii=False))
    
    print("\n🔄 模板消息推送流程:")
    print("1. 根据business_no + template_type查找notify表")
    print("2. 获取消息模板 (text, msgBanner, jumpLink等)")
    print("3. 使用variables替换模板中的占位符")
    print("4. 从robot_config获取bot_token")
    print("5. 发送最终消息")
    
    print("\n📝 模板变量替换示例:")
    print("模板文本: '恭喜 {user_name} 获得 {amount} {currency} 奖励！'")
    print("替换后: '恭喜 张三 获得 100.0 USDT 奖励！'")


def show_template_variable_analysis():
    """分析模板变量"""
    print(f"\n🔍 模板变量分析")
    print("=" * 70)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            return False
        
        collection = mongo.get_collection("c_tgNotify")
        realtime_records = list(collection.find({"notifyType": 1}))
        
        print("📋 实时通知模板的文本内容:")
        print("-" * 50)
        
        for record in realtime_records:
            notify_id = record.get("id")
            text = record.get("text", "")
            name = record.get("tgName", "")
            
            print(f"模板 {notify_id} ({name}):")
            print(f"  原文: {text}")
            
            # 分析可能的变量占位符
            import re
            # 查找 {变量名} 格式的占位符
            variables = re.findall(r'\{([^}]+)\}', text)
            if variables:
                print(f"  变量: {variables}")
            else:
                print("  变量: 无占位符，静态文本")
            
            # 分析业务字段
            business_fields = {}
            for field in ["currencyId", "rewardType", "turnoverMul", "amount"]:
                if field in record and record[field] not in [0, "", None]:
                    business_fields[field] = record[field]
            
            if business_fields:
                print(f"  业务字段: {business_fields}")
            
            print("-" * 30)
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 c_tgNotify表结构分析")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 展示表结构
    success1 = show_notify_table_structure()
    
    # 2. 分析实时推送设计
    success2 = analyze_realtime_push_design()
    
    # 3. 建议API设计
    suggest_realtime_api_design()
    
    # 4. 分析模板变量
    success3 = show_template_variable_analysis()
    
    print(f"\n" + "=" * 70)
    print("📊 分析完成")
    print("=" * 70)
    
    if success1 and success2 and success3:
        print("✅ 表结构分析成功")
        print("💡 可以基于此结构设计即时推送接口")
    else:
        print("❌ 分析过程中出现问题")
    
    return 0 if (success1 and success2 and success3) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
