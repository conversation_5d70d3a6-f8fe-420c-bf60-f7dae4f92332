#!/usr/bin/env python3
"""
简单活动名称测试
"""
from config.logging_config import app_logger
import requests
import json

def test_activity_api():
    """测试活动名称API"""
    app_logger.info("🔧 测试活动名称API")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据 - 使用已知存在的类型
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": [
            "活动名测试消息",
            "其他参数"
        ],
        "activity_id": 12
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        app_logger.info(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("status") == "success":
                    app_logger.info(f"\n✅ 活动名称功能测试成功！")
                    return True
                else:
                    app_logger.info(f"\n❌ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.info(f"\n⚠️ 响应格式异常")
                return False
        else:
            app_logger.info(f"\n❌ HTTP错误")
            return False
            
    except Exception as e:
        app_logger.info(f"\n❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 活动名称功能测试")
    app_logger.info("=" * 80)
    
    success = test_activity_api()
    
    if success:
        app_logger.info(f"\n🎉 活动名称功能已实现！")
        app_logger.info(f"💡 新增功能:")
        app_logger.info(f"   - API增加了activity_id可选参数")
        app_logger.info(f"   - 自动从数据库查询活动名称")
        app_logger.info(f"   - 在参数中替换'活动名'为真实名称")
        
        app_logger.info(f"\n📋 使用方法:")
        app_logger.info(f"   POST /api/realtime-push/template")
        app_logger.info(f"   添加 \"activity_id\": 12 参数")
        app_logger.info(f"   在params中使用 \"活动名\" 占位符")
    else:
        app_logger.info(f"\n⚠️ 测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
