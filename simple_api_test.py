#!/usr/bin/env python3
import requests
import json

# 测试预览接口
print("测试预览接口...")
try:
    response = requests.post(
        "http://localhost:8000/api/realtime-push/template/preview",
        json={
            "business_no": "39bac42a",
            "type": "18000",
            "params": ["test_user"]
        },
        timeout=10
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 预览接口响应:")
        print(f"  status: {result.get('status')}")
        print(f"  message: {result.get('message')}")
        print(f"  data keys: {list(result.get('data', {}).keys())}")
        
        data = result.get('data', {})
        if 'rendered_message' in data:
            print(f"  rendered_message: {data['rendered_message'][:50]}...")
    else:
        print(f"❌ 预览接口失败: {response.status_code}")
        print(response.text)
        
except Exception as e:
    print(f"❌ 预览接口异常: {e}")

print("\n" + "="*50)

# 测试发送接口
print("测试发送接口...")
try:
    response = requests.post(
        "http://localhost:8000/api/realtime-push/template",
        json={
            "business_no": "39bac42a",
            "type": "18000",
            "params": ["test_user"]
        },
        timeout=20
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 发送接口响应:")
        print(f"  status: {result.get('status')}")
        print(f"  message: {result.get('message')}")
        print(f"  data keys: {list(result.get('data', {}).keys())}")
        
        data = result.get('data', {})
        if 'rendered_message' in data:
            print(f"  rendered_message: {data['rendered_message'][:50]}...")
        if 'success_count' in data:
            print(f"  success_count: {data['success_count']}")
        if 'total_count' in data:
            print(f"  total_count: {data['total_count']}")
    else:
        print(f"❌ 发送接口失败: {response.status_code}")
        print(response.text)
        
except Exception as e:
    print(f"❌ 发送接口异常: {e}")

print("\n格式验证完成！")
