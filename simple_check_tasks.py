#!/usr/bin/env python3
"""
简单查看定时任务表
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

try:
    from database.mongodb_connection import MongoDBConnection
    
    print("🔍 查看定时任务表 c_tgScheduledPushTasks")
    print("=" * 50)
    
    mongo = MongoDBConnection()
    if mongo.connect():
        print("✅ MongoDB连接成功")
        
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        if collection is not None:
            print("✅ 获取集合成功")
            
            # 统计记录数
            count = collection.count_documents({})
            print(f"📊 记录数: {count}")
            
            # 查看第一条记录
            if count > 0:
                first_doc = collection.find_one()
                print(f"\n📋 第一条记录的字段:")
                for key in first_doc.keys():
                    print(f"  • {key}")
                
                print(f"\n📄 第一条记录内容:")
                for key, value in first_doc.items():
                    print(f"  {key}: {value}")
            else:
                print("ℹ️ 表中暂无数据")
        else:
            print("❌ 无法获取集合")
        
        mongo.disconnect()
    else:
        print("❌ MongoDB连接失败")

except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
