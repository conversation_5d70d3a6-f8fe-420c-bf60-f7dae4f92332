#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    print("🔧 测试配置文件驱动的特殊参数替换")
    
    # 1. 测试配置加载
    from config.settings import settings
    
    keywords = settings.special_params_keywords
    mappings = settings.special_params_mappings
    
    print(f"✅ 关键字配置: {len(keywords)} 个")
    print(f"✅ 映射配置: {len(mappings)} 个类型")
    
    # 2. 测试模板渲染
    from common.translation_manager import TranslationManager
    
    translator = TranslationManager()
    
    template = "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
    params = ["张三", "18000", "1", "100"]
    language = "1"
    
    print(f"\n📋 测试模板: {template}")
    print(f"📋 测试参数: {params}")
    
    result = translator.render_template(template, params, language)
    print(f"📋 渲染结果: {result}")
    
    # 3. 验证结果
    expected = ["张三", "每日签到活动", "金币", "100"]
    success = all(item in result for item in expected)
    
    if success:
        print(f"🎉 配置文件驱动的特殊参数替换功能正常工作！")
        print(f"💡 配置位置: config/config.yaml -> special_params")
    else:
        print(f"❌ 功能异常，请检查配置")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
