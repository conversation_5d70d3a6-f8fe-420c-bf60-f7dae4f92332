#!/usr/bin/env python3
"""
简单HTML标签测试
"""
import requests
import json

def test_html_simple():
    """简单HTML测试"""
    print("🔧 简单HTML标签测试")
    print("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据 - 使用已知存在的notifyId=8
    test_data = {
        "business_no": "39bac42a",
        "type": 8,
        "params": [
            "<p><b>HTML测试消息</b></p>\n<p>这是一个包含 <i>HTML标签</i> 的测试消息</p>\n<p>代码示例：<code>HTML_SUCCESS</code></p>",
            "测试参数2"
        ]
    }
    
    print(f"📡 API地址: {api_url}")
    print(f"📋 测试数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        print(f"\n📊 响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("status") == "success":
                    print(f"\n✅ HTML标签测试成功！")
                    print(f"💬 消息已发送到Telegram")
                    print(f"📱 请检查频道中的消息是否正确显示HTML格式")
                    return True
                else:
                    print(f"\n❌ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print(f"\n⚠️ 响应格式异常")
                return False
        else:
            print(f"\n❌ HTTP错误")
            return False
            
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 HTML标签功能测试")
    print("=" * 80)
    
    success = test_html_simple()
    
    if success:
        print(f"\n🎉 HTML标签功能已启用！")
        print(f"💡 现在支持:")
        print(f"   - <p>段落标签</p>")
        print(f"   - <b>粗体标签</b>")
        print(f"   - <i>斜体标签</i>")
        print(f"   - <code>代码标签</code>")
        print(f"   - <a href='url'>链接标签</a>")
        
        print(f"\n📋 测试建议:")
        print(f"   1. 在模板中添加 <b>粗体文字</b>")
        print(f"   2. 使用 <i>斜体强调</i>")
        print(f"   3. 添加 <code>代码样式</code>")
        print(f"   4. 插入 <a href='https://example.com'>链接</a>")
    else:
        print(f"\n⚠️ 测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
