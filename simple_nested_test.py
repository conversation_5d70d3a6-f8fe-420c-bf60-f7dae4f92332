#!/usr/bin/env python3
"""
简单测试嵌套数组功能
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 测试嵌套数组功能")
    
    translator = TranslationManager()
    
    # 你的用例
    template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
    
    params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    language = "2"
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    print(f"语言: {language}")
    
    print(f"\n说明:")
    print(f"   参数2是嵌套数组: [['10', 'USDT'], ['50', 'BRL'], ['100', 'USDC']]")
    print(f"   Currency2 应该取子数组的索引1: USDT, BRL, USDC")
    print(f"   Amount3 应该取参数3，但参数3是字符串，不是数组")
    
    result = translator.render_template(template, params, language)
    
    print(f"\n渲染结果:")
    print(f"{result}")
    
    print(f"\n建议修改为:")
    print(f"   模板: Parabéns {{test1}} [repeat]pelo depósito – {{Amount2}} {{Currency2}} [/repeat] {{gg3}}")
    print(f"   参数: ['user_name_li', [['10', 'USDT'], ['50', 'BRL'], ['100', 'USDC']], 'thank_you_message']")
    print(f"   这样Amount2和Currency2都从同一个嵌套数组中取值")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
