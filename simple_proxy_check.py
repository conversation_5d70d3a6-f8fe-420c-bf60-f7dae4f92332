#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from config.settings import settings
    
    print("代理配置检查:")
    print(f"  use_proxy: {settings.use_proxy}")
    print(f"  proxy_url: {settings.proxy_url}")
    
    if settings.use_proxy:
        print("⚠️ 代理已启用")
    else:
        print("✅ 代理已关闭 - 适合测试环境")
        
except Exception as e:
    print(f"❌ 检查失败: {e}")
