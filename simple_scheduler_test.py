#!/usr/bin/env python3
"""
简单的调度器测试
"""
import asyncio
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager

async def test_basic():
    """基础测试"""
    print("🔧 基础测试")
    
    try:
        # 初始化
        success = await config_manager.initialize()
        print(f"初始化: {'✅' if success else '❌'}")
        
        # 获取任务
        tasks = config_manager.get_all_scheduled_tasks()
        print(f"任务数量: {len(tasks)}")
        
        if tasks:
            task = tasks[0]
            print(f"第一个任务:")
            print(f"  _id: {task.get('_id')}")
            print(f"  notifyId: {task.get('notifyId')}")
            print(f"  sendFrequency: {task.get('sendFrequency')}")
            print(f"  scheduleDays: {task.get('scheduleDays')}")
            print(f"  scheduleTimes: {task.get('scheduleTimes')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    asyncio.run(test_basic())
