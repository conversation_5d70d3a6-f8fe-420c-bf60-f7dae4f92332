#!/usr/bin/env python3
"""
简单智能活动名称测试
"""
from config.logging_config import app_logger
import requests
import json

def test_api():
    """测试API"""
    app_logger.info("🔧 测试智能活动名称API")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据 - 有activity_id，使用已知存在的类型
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": ["xxx", "ttttt", "100", "BRL"],
        "activity_id": 12
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    app_logger.info(f"📋 期望: 忽略params[0]='xxx'，使用activity_id=12的活动名称")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        app_logger.info(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("status") == "success":
                    app_logger.info(f"\n✅ 智能活动名称功能测试成功！")
                    app_logger.info(f"💬 消息已发送到Telegram")
                    app_logger.info(f"📱 请检查频道中的消息，应该不包含'xxx'")
                    return True
                else:
                    app_logger.info(f"\n❌ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.info(f"\n⚠️ 响应格式异常")
                return False
        else:
            app_logger.info(f"\n❌ HTTP错误")
            return False
            
    except Exception as e:
        app_logger.info(f"\n❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 智能活动名称功能测试")
    app_logger.info("=" * 80)
    
    success = test_api()
    
    if success:
        app_logger.info(f"\n🎉 智能活动名称功能已实现！")
        app_logger.info(f"💡 功能特点:")
        app_logger.info(f"   1. 优先使用activity_id查询活动名称")
        app_logger.info(f"   2. 无activity_id时使用type对应的活动名称")
        app_logger.info(f"   3. params中的活动名参数被无效化")
        app_logger.info(f"   4. 减少调用方出错的概率")
        
        app_logger.info(f"\n📋 测试结果:")
        app_logger.info(f"   模板: Parabéns a {{活动名1}} por conquistar {{3}}{{4}} em {{2}}.")
        app_logger.info(f"   参数: ['xxx', 'ttttt', '100', 'BRL']")
        app_logger.info(f"   期望: 'xxx'被无效化，使用智能查询的活动名称")
    else:
        app_logger.info(f"\n⚠️ 测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
