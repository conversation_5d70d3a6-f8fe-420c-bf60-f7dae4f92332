#!/usr/bin/env python3
"""
简单智能活动名称测试
"""
import requests
import json

def test_api():
    """测试API"""
    print("🔧 测试智能活动名称API")
    print("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据 - 有activity_id，使用已知存在的类型
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": ["xxx", "ttttt", "100", "BRL"],
        "activity_id": 12
    }
    
    print(f"📡 API地址: {api_url}")
    print(f"📋 测试数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print(f"📋 期望: 忽略params[0]='xxx'，使用activity_id=12的活动名称")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        print(f"\n📊 响应:")
        print(f"   状态码: {response.status_code}")
        print(f"   响应内容: {response.text}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("status") == "success":
                    print(f"\n✅ 智能活动名称功能测试成功！")
                    print(f"💬 消息已发送到Telegram")
                    print(f"📱 请检查频道中的消息，应该不包含'xxx'")
                    return True
                else:
                    print(f"\n❌ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print(f"\n⚠️ 响应格式异常")
                return False
        else:
            print(f"\n❌ HTTP错误")
            return False
            
    except Exception as e:
        print(f"\n❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 智能活动名称功能测试")
    print("=" * 80)
    
    success = test_api()
    
    if success:
        print(f"\n🎉 智能活动名称功能已实现！")
        print(f"💡 功能特点:")
        print(f"   1. 优先使用activity_id查询活动名称")
        print(f"   2. 无activity_id时使用type对应的活动名称")
        print(f"   3. params中的活动名参数被无效化")
        print(f"   4. 减少调用方出错的概率")
        
        print(f"\n📋 测试结果:")
        print(f"   模板: Parabéns a {{活动名1}} por conquistar {{3}}{{4}} em {{2}}.")
        print(f"   参数: ['xxx', 'ttttt', '100', 'BRL']")
        print(f"   期望: 'xxx'被无效化，使用智能查询的活动名称")
    else:
        print(f"\n⚠️ 测试失败，请检查服务状态")

if __name__ == "__main__":
    main()
