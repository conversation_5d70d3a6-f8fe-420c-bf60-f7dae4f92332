#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("测试特殊参数替换:")
    
    translator = TranslationManager()
    
    # 测试活动名替换
    template = "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
    params = ["张三", "18000", "1", "100"]
    language = "1"
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    print(f"语言: {language}")
    
    result = translator.render_template(template, params, language)
    print(f"结果: {result}")
    
    # 检查是否包含期望的替换
    if "每日签到活动" in result:
        print("✅ 活动名替换成功")
    else:
        print("❌ 活动名替换失败")
        
    if "金币" in result:
        print("✅ 币种替换成功")
    else:
        print("❌ 币种替换失败")
        
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
