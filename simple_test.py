#!/usr/bin/env python3
"""
简单测试脚本
"""
import requests
import json

def test_preview():
    """测试预览功能"""
    try:
        url = "http://localhost:8000/api/realtime-push/template/preview"
        data = {
            "business_no": "39bac42a",
            "type": "18000",
            "params": ["user_name_zhang"]
        }
        
        print(f"发送请求到: {url}")
        print(f"请求数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(url, json=data, timeout=10)
        
        print(f"响应状态: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 预览成功")
                preview = result.get("data", {})
                print(f"渲染结果: {preview.get('rendered_message')}")
            else:
                print(f"❌ 预览失败: {result.get('message')}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_preview()
