#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.task_scheduler import TaskScheduler

scheduler = TaskScheduler()

# 测试URL验证
test_urls = [
    "a",  # 无效
    "https://example.com",  # 有效
    "t.me/bot",  # 有效
    "",  # 无效
]

print("URL验证测试:")
for url in test_urls:
    result = scheduler._is_valid_url(url)
    print(f"'{url}' -> {result}")
