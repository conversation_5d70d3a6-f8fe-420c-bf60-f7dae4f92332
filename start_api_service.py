#!/usr/bin/env python3
"""
Telegram Bot API推送服务启动脚本
专门用于启动API服务器和定时任务调度器
"""
import asyncio
import uvicorn
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from api.main import create_app
from scheduler.main import scheduler_manager
from database.connection import init_database, close_database

import logging
logger = logging.getLogger(__name__)


class ApiPushService:
    """API推送服务类"""
    
    def __init__(self):
        self.app = None
        self.scheduler = None
        self.is_running = False
        
    async def start_api_server(self):
        """启动FastAPI服务器"""
        try:
            logger.info("📡 启动FastAPI服务器...")
            self.app = create_app()
            
            config = uvicorn.Config(
                app=self.app,
                host=settings.api_host,
                port=settings.api_port,
                log_level=settings.log_level.lower()
            )
            server = uvicorn.Server(config)
            await server.serve()
            
        except Exception as e:
            logger.error(f"❌ FastAPI服务器启动失败: {e}")
            raise
    
    async def start_scheduler(self):
        """启动定时任务调度器"""
        try:
            logger.info("⏰ 启动定时任务调度器...")
            self.scheduler = scheduler_manager
            await self.scheduler.start()
            
            # 保持调度器运行
            while self.is_running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"❌ 定时任务调度器启动失败: {e}")
            raise
    
    async def start(self):
        """启动API推送服务"""
        logger.info("=" * 60)
        logger.info("📡 启动 Telegram Bot API推送服务")
        logger.info("=" * 60)
        logger.info(f"🌐 API服务地址: http://{settings.api_host}:{settings.api_port}")
        logger.info(f"⏰ 定时任务: 已启用")
        logger.info(f"🗄️ 数据库: {settings.database_url}")
        logger.info("=" * 60)
        
        self.is_running = True
        
        # 先初始化数据库（如果需要）
        try:
            # await init_database()
            logger.info("🗄️ 数据库初始化跳过（开发模式）")
        except Exception as e:
            logger.warning(f"⚠️ 数据库初始化失败: {e}")
        
        try:
            # 并发启动API服务器和定时任务调度器
            await asyncio.gather(
                self.start_api_server(),
                self.start_scheduler(),
                return_exceptions=True
            )
        except Exception as e:
            logger.error(f"❌ 服务启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止API推送服务"""
        logger.info("🛑 正在停止API推送服务...")
        self.is_running = False
        
        # 停止调度器
        if self.scheduler:
            try:
                await self.scheduler.stop()
                logger.info("✅ 定时任务调度器已停止")
            except Exception as e:
                logger.error(f"❌ 停止调度器失败: {e}")
        
        # 关闭数据库连接（如果需要）
        try:
            # await close_database()
            logger.info("🗄️ 数据库连接关闭跳过（开发模式）")
        except Exception as e:
            logger.warning(f"⚠️ 关闭数据库连接失败: {e}")
        
        # FastAPI服务器会在主进程结束时自动停止
        logger.info("✅ API推送服务已停止")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"📡 收到停止信号 {signum}")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    logger.info("🚀 初始化API推送服务...")
    
    # 检查配置
    try:
        token = settings.bot_token
        if not token:
            raise ValueError("Bot token未配置")
        logger.info("✅ Bot Token配置检查通过")
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        logger.error("💡 请检查 config/config.yaml 中的配置")
        return 1
    
    # 创建并启动服务
    service = ApiPushService()
    service.setup_signal_handlers()
    
    try:
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("📡 收到键盘中断信号")
        
    except Exception as e:
        logger.error(f"❌ 服务运行错误: {e}")
        return 1
        
    finally:
        await service.stop()
    
    logger.info("🎉 API推送服务完全关闭")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 API推送服务已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ API推送服务启动失败: {e}")
        sys.exit(1)
