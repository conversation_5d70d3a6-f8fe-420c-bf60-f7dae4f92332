#!/usr/bin/env python3
"""
Telegram Bot 交互服务启动脚本
专门用于启动Bot交互监听功能，处理用户消息和菜单交互
"""
import asyncio
import logging
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from interactive.main import InteractiveBot

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


class BotInteractiveService:
    """Bot交互服务类"""
    
    def __init__(self):
        self.bot = None
        self.is_running = False
    
    async def start(self):
        """启动Bot交互服务"""
        logger.info("=" * 60)
        logger.info("🤖 启动 Telegram Bot 交互服务")
        logger.info("=" * 60)
        logger.info(f"🔧 Bot Token: {settings.bot_token[:10]}...")
        logger.info(f"🌐 运行模式: {'Webhook' if settings.webhook_url else 'Polling'}")
        if settings.use_proxy:
            logger.info(f"🔗 代理设置: {settings.proxy_url}")
        else:
            logger.info("🔗 代理设置: 未启用")
        logger.info("=" * 60)
        
        # 创建并启动Bot
        self.bot = InteractiveBot()
        self.is_running = True
        
        try:
            await self.bot.start()
            
            if not settings.webhook_url:
                # Polling模式，显示使用提示
                logger.info("✅ Bot交互服务启动成功！")
                logger.info("📱 功能说明：")
                logger.info("   • 处理用户 /start 命令显示菜单")
                logger.info("   • 支持私聊和群聊不同菜单")
                logger.info("   • 处理菜单按钮点击交互")
                logger.info("   • 智能消息回复功能")
                logger.info("")
                logger.info("🎯 测试方法：")
                logger.info("   • 私聊Bot发送 /start 查看私聊菜单")
                logger.info("   • 群组中发送 /start 查看群组菜单")
                logger.info("   • 点击菜单按钮测试各种功能")
                logger.info("")
                logger.info("🛑 按 Ctrl+C 停止服务")
                logger.info("=" * 60)
                
                # 保持运行
                while self.is_running and self.bot.is_running:
                    await asyncio.sleep(1)
            else:
                # Webhook模式
                logger.info("✅ Webhook模式启动完成")
                logger.info("🌐 Bot正在等待Webhook请求...")
                logger.info("🛑 按 Ctrl+C 停止服务")
                
                # 保持运行
                while self.is_running:
                    await asyncio.sleep(60)
                    
        except Exception as e:
            logger.error(f"❌ Bot交互服务启动失败: {e}")
            raise
    
    async def stop(self):
        """停止Bot交互服务"""
        logger.info("🛑 正在停止Bot交互服务...")
        self.is_running = False
        
        if self.bot:
            try:
                await self.bot.stop()
                logger.info("✅ Bot交互服务已停止")
            except Exception as e:
                logger.error(f"❌ 停止Bot服务时出错: {e}")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"📡 收到停止信号 {signum}")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    logger.info("🚀 初始化Bot交互服务...")
    
    # 检查配置
    try:
        token = settings.bot_token
        if not token:
            raise ValueError("Bot token未配置")
        logger.info("✅ Bot Token配置检查通过")
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        logger.error("💡 请检查 config/config.yaml 中的 bot.token 配置")
        return 1
    
    # 创建并启动服务
    service = BotInteractiveService()
    service.setup_signal_handlers()
    
    try:
        await service.start()
        
    except KeyboardInterrupt:
        logger.info("📡 收到键盘中断信号")
        
    except Exception as e:
        logger.error(f"❌ 服务运行错误: {e}")
        return 1
        
    finally:
        await service.stop()
    
    logger.info("🎉 Bot交互服务完全关闭")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 Bot交互服务已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ Bot交互服务启动失败: {e}")
        sys.exit(1)
