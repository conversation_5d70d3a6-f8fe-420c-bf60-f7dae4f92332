#!/usr/bin/env python3
"""
完整的Telegram Bot服务启动脚本
同时启动API服务器、定时任务调度器和Bot交互监听
"""
import asyncio
import uvicorn
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from api.main import create_app
from scheduler.main import scheduler_manager
from interactive.main import InteractiveBot

import logging
logger = logging.getLogger(__name__)


class FullBotService:
    """完整的Bot服务类"""
    
    def __init__(self):
        self.api_server = None
        self.scheduler = None
        self.interactive_bot = None
        self.running = False
        
    async def start_api_server(self):
        """启动FastAPI服务器"""
        try:
            logger.info("启动FastAPI服务器...")
            app = create_app()
            
            config = uvicorn.Config(
                app=app,
                host=settings.api_host,
                port=settings.api_port,
                log_level=settings.log_level.lower()
            )
            self.api_server = uvicorn.Server(config)
            await self.api_server.serve()
            
        except Exception as e:
            logger.error(f"FastAPI服务器启动失败: {e}")
            raise
    
    async def start_scheduler(self):
        """启动定时任务调度器"""
        try:
            logger.info("启动定时任务调度器...")
            self.scheduler = scheduler_manager
            await self.scheduler.start()
            
            # 保持调度器运行
            while self.running:
                await asyncio.sleep(1)
                
        except Exception as e:
            logger.error(f"定时任务调度器启动失败: {e}")
            raise
    
    async def start_interactive_bot(self):
        """启动Bot交互监听"""
        try:
            logger.info("启动Bot交互监听...")
            self.interactive_bot = InteractiveBot()
            await self.interactive_bot.start()
            
        except Exception as e:
            logger.error(f"Bot交互监听启动失败: {e}")
            raise
    
    async def start(self):
        """启动所有服务"""
        logger.info("=" * 60)
        logger.info("🚀 启动完整的Telegram Bot服务")
        logger.info("=" * 60)
        logger.info(f"📡 API服务地址: http://{settings.api_host}:{settings.api_port}")
        logger.info(f"⏰ 定时任务: 已启用")
        logger.info(f"🤖 Bot交互: 已启用")
        logger.info(f"🔧 运行模式: {'Webhook' if settings.webhook_url else 'Polling'}")
        logger.info("=" * 60)
        
        self.running = True
        
        try:
            # 并发启动所有服务
            await asyncio.gather(
                self.start_api_server(),
                self.start_scheduler(),
                self.start_interactive_bot(),
                return_exceptions=True
            )
        except Exception as e:
            logger.error(f"服务启动失败: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """停止所有服务"""
        logger.info("🛑 停止所有服务...")
        self.running = False
        
        # 停止Bot交互
        if self.interactive_bot:
            try:
                await self.interactive_bot.stop()
                logger.info("✅ Bot交互监听已停止")
            except Exception as e:
                logger.error(f"停止Bot交互失败: {e}")
        
        # 停止调度器
        if self.scheduler:
            try:
                await self.scheduler.stop()
                logger.info("✅ 定时任务调度器已停止")
            except Exception as e:
                logger.error(f"停止调度器失败: {e}")
        
        # API服务器会在主进程结束时自动停止
        logger.info("✅ 所有服务已停止")
    
    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            logger.info(f"收到信号 {signum}，正在关闭服务...")
            asyncio.create_task(self.stop())
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    # 检查配置
    try:
        token = settings.bot_token
        logger.info("✅ Bot Token配置正确")
        if settings.use_proxy:
            logger.info(f"🌐 代理已启用: {settings.proxy_url}")
        else:
            logger.info("🌐 代理未启用")
    except Exception as e:
        logger.error(f"❌ 配置错误: {e}")
        return 1
    
    # 创建并启动服务
    service = FullBotService()
    service.setup_signal_handlers()
    
    try:
        await service.start()
    except KeyboardInterrupt:
        logger.info("收到键盘中断信号")
    except Exception as e:
        logger.error(f"服务运行错误: {e}")
        return 1
    finally:
        await service.stop()
    
    logger.info("🎉 服务完全关闭")
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 服务已停止")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)
