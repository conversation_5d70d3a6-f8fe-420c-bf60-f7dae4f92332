#!/usr/bin/env python3
"""
周期性推送服务启动脚本
独立运行周期性推送服务，监控游戏日志并执行获奖推送
"""
import asyncio
import signal
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from services.periodic_push_service import periodic_push_service


class PeriodicPushServiceRunner:
    """周期性推送服务运行器"""
    
    def __init__(self):
        self.service = periodic_push_service
        self.is_running = False
    
    async def start(self):
        """启动服务"""
        try:
            app_logger.info("🚀 启动周期性推送服务运行器")
            app_logger.info("=" * 80)
            
            # 初始化服务
            app_logger.info("🔧 初始化服务")
            success = await self.service.initialize()
            
            if not success:
                app_logger.error("❌ 服务初始化失败")
                return False
            
            # 启动周期性推送
            app_logger.info("🚀 启动周期性推送")
            await self.service.start()
            
            self.is_running = True
            app_logger.info("✅ 周期性推送服务已启动")
            app_logger.info("💡 服务配置:")
            app_logger.info(f"   📊 监控商户数量: {len(self.service.notify_configs)}")
            app_logger.info(f"   ⏱️ 检查间隔: 60秒")
            app_logger.info(f"   📋 当前监控表: {await self.service.get_current_table_name()}")
            
            # 显示配置详情
            for merchant_id, config in self.service.notify_configs.items():
                app_logger.info(f"   商户 {merchant_id}: "
                              f"gameType={config.get('gameType')}, "
                              f"platformId={config.get('platformId')}, "
                              f"gameId={config.get('gameId')}, "
                              f"gameWinMul={config.get('gameWinMul')}")
            
            app_logger.info("=" * 80)
            app_logger.info("🔄 周期性推送服务正在运行...")
            app_logger.info("💡 按 Ctrl+C 停止服务")
            
            return True
            
        except Exception as e:
            app_logger.error(f"❌ 启动服务失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def stop(self):
        """停止服务"""
        if not self.is_running:
            return
        
        app_logger.info("🛑 停止周期性推送服务")
        
        try:
            await self.service.stop()
            await self.service.cleanup()
            self.is_running = False
            app_logger.info("✅ 周期性推送服务已停止")
        except Exception as e:
            app_logger.error(f"❌ 停止服务失败: {e}")
    
    async def run_forever(self):
        """持续运行服务"""
        try:
            # 启动服务
            success = await self.start()
            if not success:
                return 1
            
            # 等待中断信号
            while self.is_running:
                await asyncio.sleep(1)
            
            return 0
            
        except KeyboardInterrupt:
            app_logger.info("🛑 收到中断信号，正在停止服务...")
            await self.stop()
            return 0
        except Exception as e:
            app_logger.error(f"❌ 服务运行异常: {e}")
            await self.stop()
            return 1


async def main():
    """主函数"""
    app_logger.info("🎯 周期性推送服务")
    app_logger.info("=" * 100)
    app_logger.info("📋 功能说明:")
    app_logger.info("   • 监控游戏日志表，检测满足条件的获奖记录")
    app_logger.info("   • 根据配置的获奖倍数阈值自动发送推送通知")
    app_logger.info("   • 支持多商户、多条件的灵活配置")
    app_logger.info("   • 避免重复处理，确保数据不遗漏")
    app_logger.info("=" * 100)
    
    # 创建服务运行器
    runner = PeriodicPushServiceRunner()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        app_logger.info(f"🛑 收到信号 {signum}，准备停止服务...")
        runner.is_running = False
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行服务
    exit_code = await runner.run_forever()
    
    app_logger.info("=" * 100)
    app_logger.info(f"📊 服务退出，退出码: {exit_code}")
    
    return exit_code


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 服务被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)
