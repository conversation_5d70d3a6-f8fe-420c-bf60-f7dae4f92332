#!/usr/bin/env python3
"""
启动定时任务调度器
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.task_scheduler import TaskScheduler
from common.utils import setup_logging

import logging
logger = logging.getLogger(__name__)


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    logger.info("🚀 启动定时任务调度器服务")
    logger.info("=" * 60)
    logger.info("📋 服务信息:")
    logger.info("   • 服务名称: Telegram定时任务调度器")
    logger.info("   • 数据源: MongoDB c_tgScheduledPushTasks")
    logger.info("   • 检查频率: 每分钟")
    logger.info("   • 支持频率: daily, weekly, monthly")
    logger.info("=" * 60)
    
    scheduler = TaskScheduler()
    
    try:
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在停止服务...")
    except Exception as e:
        logger.error(f"❌ 服务异常: {e}")
    finally:
        await scheduler.stop()
        logger.info("✅ 定时任务调度器服务已停止")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
