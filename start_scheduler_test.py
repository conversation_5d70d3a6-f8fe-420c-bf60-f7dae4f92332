#!/usr/bin/env python3
"""
启动定时任务调度器进行测试
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def start_scheduler_test():
    """启动调度器测试"""
    print("🚀 启动定时任务调度器测试")
    print("=" * 50)
    
    try:
        from scheduler.task_scheduler import TaskScheduler
        
        # 创建调度器实例
        scheduler = TaskScheduler()
        
        print("1. 初始化调度器...")
        
        # 启动调度器
        print("2. 启动调度器...")
        await scheduler.start()
        
        print("✅ 调度器启动成功")
        print(f"📅 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"⏰ 下一个执行时间: 00:55 (约14分钟后)")
        print(f"🔍 调度器将每分钟检查一次任务...")
        
        # 运行一段时间进行测试
        print(f"\n🕐 运行调度器进行测试 (运行5分钟)...")
        
        try:
            # 运行5分钟
            await asyncio.sleep(300)
        except KeyboardInterrupt:
            print(f"\n⏹️ 用户中断测试")
        
        # 停止调度器
        print(f"\n🛑 停止调度器...")
        await scheduler.stop()
        
        print(f"✅ 调度器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_single_task():
    """测试单个任务执行"""
    print(f"\n🎯 测试单个任务执行")
    print("=" * 50)
    
    try:
        from scheduler.task_executor import TaskExecutor
        from database.mongodb_connection import MongoDBConnection
        
        # 获取一个测试任务
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        task = collection.find_one({"enabled": True})
        
        if not task:
            print("❌ 没有找到启用的任务")
            return False
        
        print(f"📋 测试任务: {task.get('_id')}")
        print(f"   notifyId: {task.get('notifyId')}")
        print(f"   business_no: {task.get('business_no')}")
        print(f"   taskType: {task.get('taskType')}")
        
        # 创建执行器
        executor = TaskExecutor()
        
        # 执行任务
        print(f"\n🚀 执行任务...")
        result = await executor.execute_task(task)
        
        print(f"📊 执行结果: {'✅ 成功' if result else '❌ 失败'}")
        
        mongo.disconnect()
        return result
        
    except Exception as e:
        print(f"❌ 单任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 定时任务测试选项:")
    print("1. 启动调度器进行完整测试 (运行5分钟)")
    print("2. 测试单个任务执行")
    print("3. 退出")
    
    try:
        choice = input("\n请选择测试选项 (1-3): ").strip()
        
        if choice == "1":
            success = await start_scheduler_test()
        elif choice == "2":
            success = await test_single_task()
        elif choice == "3":
            print("👋 退出测试")
            return
        else:
            print("❌ 无效选择")
            return
        
        if success:
            print(f"\n🎉 测试完成")
        else:
            print(f"\n⚠️ 测试失败")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
