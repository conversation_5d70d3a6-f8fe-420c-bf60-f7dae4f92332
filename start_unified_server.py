#!/usr/bin/env python3
"""
启动统一服务器
集成FastAPI和定时任务调度器的单一服务
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from unified_server import main

if __name__ == "__main__":
    # 导入配置以获取端口信息
    from config.settings import settings

    print("🚀 启动TG Bot统一服务器")
    print("=" * 60)
    print("📋 服务信息:")
    print("   • 服务类型: 统一服务器 (FastAPI + 定时任务)")
    print(f"   • 服务地址: http://localhost:{settings.api_port}")
    print(f"   • API文档: http://localhost:{settings.api_port}/docs")
    print(f"   • 健康检查: http://localhost:{settings.api_port}/health")
    print(f"   • 统一状态: http://localhost:{settings.api_port}/api/unified-status")
    print("=" * 60)
    print("💡 优势:")
    print("   • 单一进程，资源节约")
    print("   • 内存共享，配置实时同步")
    print("   • 部署简单，统一管理")
    print("   • 调试方便，日志集中")
    print("=" * 60)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)
