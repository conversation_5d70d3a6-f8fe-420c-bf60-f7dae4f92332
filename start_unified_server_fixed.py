#!/usr/bin/env python3
"""
统一服务器 - 集成FastAPI、定时任务调度器和周期性推送
单一进程同时提供API服务、定时任务功能和周期性推送功能
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

# 先设置日志
from config.logging_config import app_logger

app_logger.info("🚀 启动统一服务器")
app_logger.info("=" * 80)

try:
    # 导入必要的模块
    app_logger.info("📦 导入模块...")
    
    import uvicorn
    import logging
    from contextlib import asynccontextmanager
    from fastapi import FastAPI
    
    from api.main import create_app
    from scheduler.task_scheduler import TaskScheduler
    from scheduler.config_manager import config_manager
    from services.periodic_push_service import periodic_push_service
    from config.settings import settings
    
    app_logger.info("✅ 模块导入成功")
    
except Exception as e:
    app_logger.error(f"❌ 模块导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)

logger = logging.getLogger(__name__)

# 全局实例
scheduler_instance = None
periodic_push_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global scheduler_instance, periodic_push_instance
    
    # 启动阶段
    app_logger.info("🚀 启动统一服务器组件")
    
    try:
        # 1. 初始化配置管理器
        app_logger.info("📋 初始化配置管理器...")
        if not await config_manager.initialize():
            app_logger.error("❌ 配置管理器初始化失败")
            raise Exception("配置管理器初始化失败")
        
        # 2. 创建并启动调度器
        app_logger.info("⏰ 启动定时任务调度器...")
        scheduler_instance = TaskScheduler()
        
        # 在后台启动调度器
        asyncio.create_task(scheduler_instance.start())
        
        # 3. 初始化并启动周期性推送服务
        app_logger.info("🔄 启动周期性推送服务...")
        periodic_push_instance = periodic_push_service
        
        # 初始化周期性推送服务
        if await periodic_push_instance.initialize():
            # 在后台启动周期性推送
            await periodic_push_instance.start()
            app_logger.info("✅ 周期性推送服务启动成功")
        else:
            app_logger.error("❌ 周期性推送服务初始化失败")
        
        app_logger.info("✅ 统一服务器启动完成")
        app_logger.info("=" * 80)
        app_logger.info(f"🌐 FastAPI服务: http://localhost:{settings.api_port}")
        app_logger.info(f"📖 API文档: http://localhost:{settings.api_port}/docs")
        app_logger.info(f"💚 健康检查: http://localhost:{settings.api_port}/health")
        app_logger.info(f"📊 统一状态: http://localhost:{settings.api_port}/api/unified-status")
        app_logger.info("⏰ 定时任务调度器: 运行中")
        app_logger.info("🔄 周期性推送服务: 运行中")
        app_logger.info("=" * 80)
        
        yield
        
    except Exception as e:
        app_logger.error(f"❌ 服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        raise
    
    # 关闭阶段
    app_logger.info("🛑 关闭统一服务器")
    
    try:
        # 停止周期性推送服务
        if periodic_push_instance:
            await periodic_push_instance.stop()
            await periodic_push_instance.cleanup()
            app_logger.info("✅ 周期性推送服务已停止")
        
        # 停止调度器
        if scheduler_instance:
            await scheduler_instance.stop()
            app_logger.info("✅ 定时任务调度器已停止")
        
        # 关闭配置管理器
        config_manager.mongo.disconnect()
        app_logger.info("✅ 配置管理器已关闭")
        
    except Exception as e:
        app_logger.error(f"❌ 服务关闭异常: {e}")


def create_unified_app() -> FastAPI:
    """创建统一的FastAPI应用"""
    
    # 创建基础应用
    app = create_app()
    
    # 设置生命周期管理
    app.router.lifespan_context = lifespan
    
    # 添加统一状态接口
    @app.get("/api/unified-status")
    async def get_unified_status():
        """获取统一服务器状态"""
        try:
            # 获取配置管理器状态
            config_stats = config_manager.get_cache_stats()
            
            # 获取调度器状态
            scheduler_status = {
                "running": scheduler_instance.running if scheduler_instance else False,
                "task_count": len(scheduler_instance.tasks) if scheduler_instance else 0
            }
            
            # 获取周期性推送状态
            periodic_push_status = {
                "running": periodic_push_instance.is_running if periodic_push_instance else False,
                "config_count": len(periodic_push_instance.notify_configs) if periodic_push_instance else 0,
                "last_numbers": periodic_push_instance.last_numbers if periodic_push_instance else {}
            }
            
            return {
                "success": True,
                "message": "统一服务器状态获取成功",
                "data": {
                    "server_type": "unified",
                    "services": {
                        "fastapi": "running",
                        "scheduler": "running" if scheduler_status["running"] else "stopped",
                        "periodic_push": "running" if periodic_push_status["running"] else "stopped"
                    },
                    "config_manager": config_stats,
                    "scheduler": scheduler_status,
                    "periodic_push": periodic_push_status,
                    "memory_shared": True,
                    "process_count": 1
                }
            }
            
        except Exception as e:
            app_logger.error(f"❌ 获取统一服务器状态失败: {e}")
            return {
                "success": False,
                "message": f"获取状态失败: {str(e)}"
            }
    
    return app


async def main():
    """主函数"""
    app_logger.info("🚀 启动统一服务器 (FastAPI + 定时任务调度器 + 周期性推送)")
    
    try:
        # 创建应用
        app = create_unified_app()
        
        # 配置服务器
        config = uvicorn.Config(
            app,
            host=settings.api_host,
            port=settings.api_port,
            log_level="info",
            access_log=True
        )
        
        # 启动服务器
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        app_logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        app_logger.error(f"❌ 服务器运行异常: {e}")
        import traceback
        traceback.print_exc()
        raise


if __name__ == "__main__":
    print("🚀 启动TG Bot统一服务器")
    print("=" * 80)
    print("📋 服务信息:")
    print("   • 服务类型: 统一服务器 (FastAPI + 定时任务 + 周期性推送)")
    print(f"   • 服务地址: http://localhost:{settings.api_port}")
    print(f"   • API文档: http://localhost:{settings.api_port}/docs")
    print(f"   • 健康检查: http://localhost:{settings.api_port}/health")
    print(f"   • 统一状态: http://localhost:{settings.api_port}/api/unified-status")
    print("=" * 80)
    print("💡 集成功能:")
    print("   • FastAPI Web服务")
    print("   • 定时任务调度器")
    print("   • 周期性推送服务")
    print("   • 配置热更新机制")
    print("=" * 80)
    print("🎯 优势:")
    print("   • 单一进程，资源节约")
    print("   • 内存共享，配置实时同步")
    print("   • 部署简单，统一管理")
    print("   • 调试方便，日志集中")
    print("=" * 80)
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        sys.exit(1)
