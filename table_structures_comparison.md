# 📊 定时任务表与配置表结构对比

## 🎯 **当前定时任务表结构**

### **c_tgScheduledPushTasks** (现有定时任务表)：

```json
{
  "_id": "ObjectId",
  "notifyId": 1,                                    // int - 任务ID
  "business_no": "39bac42a",                        // str - 业务编号
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw", // str - Bot令牌
  "channelId": ["pd001", -1002316158105],           // list - 目标聊天ID数组
  "bannerUrl": "https://test.wgsaas.ai/...",        // str - 图片URL
  "messageText": "起床啦！！！",                     // str - 消息文本
  "externalUrl": "abc",                             // str - 外部链接
  "internalUrl": "def",                             // str - 内部链接
  "urlLabel": "",                                   // str - 链接按钮文本
  "urlType": "",                                    // str - 链接类型
  "sendTime": "05:00:00",                           // str - 发送时间
  "sendFrequency": "weekly",                        // str - 发送频率
  "weekDays": [1, 2, 3, 4, 5, 6, 7],              // list - 星期配置
  "monthDays": [],                                  // list - 月日配置
  "enabled": true,                                  // bool - 是否启用
  "createTime": 1751686484175,                      // int64 - 创建时间
  "updateTime": 1751686484175                       // int64 - 更新时间
}
```

#### **字段说明**：
- **核心字段**: 17个
- **功能**: 纯定时任务执行
- **特点**: 结构简洁，专注调度

---

## 🔧 **当前配置表结构**

### **c_tgNotify** (现有配置表)：

```json
{
  "_id": "ObjectId",
  "id": 1,                                          // int - 通知ID
  "business_no": "39bac42a",                        // str - 业务编号
  "notifyTarget": ["pd001", -1002316158105],        // list - 通知目标
  "tgName": "定时通知001",                           // str - 通知名称
  "notifyType": 2,                                  // int - 通知类型 (1=实时, 2=定时)
  "cycle": [1, 2, 3, 4, 5, 6, 7],                 // list - 周期配置
  "types": [],                                      // list - 类型数组
  "jumpLink": "abc",                                // str - 跳转链接
  "tgLink": "def",                                  // str - TG链接
  "text": "起床啦！！！",                            // str - 消息文本
  "msgBanner": "https://test.wgsaas.ai/...",        // str - 消息横幅
  "currencyId": 0,                                  // int - 货币ID
  "rewardType": 0,                                  // int - 奖励类型
  "turnoverMul": 0,                                 // int - 流水倍数
  "amount": 0.0,                                    // float - 金额
  "open": true,                                     // bool - 是否开启
  "pushTimeString": "05:00:00",                     // str - 推送时间字符串
  "pushTime": 18000000,                             // int - 推送时间毫秒
  "createTime": 1751686484047,                      // int64 - 创建时间
  "updateTime": 1751686484047                       // int64 - 更新时间
}
```

#### **字段说明**：
- **核心字段**: 20个
- **功能**: 配置管理 + 业务扩展
- **特点**: 功能丰富，支持多种业务场景

---

## 🎯 **预期增强后的配置表结构**

### **c_tgNotify** (增强版，支持完整同步)：

```json
{
  // ========== 基础字段 (现有) ==========
  "_id": "ObjectId",
  "id": 1,                                          // int - 通知ID
  "business_no": "39bac42a",                        // str - 业务编号
  "tgName": "定时通知001",                           // str - 通知名称
  
  // ========== 通知类型和内容 (现有) ==========
  "notifyType": 2,                                  // int - 通知类型 (1=实时, 2=定时)
  "text": "起床啦！！！",                            // str - 消息文本
  "msgBanner": "https://test.wgsaas.ai/...",        // str - 消息图片URL
  "notifyTarget": ["pd001", -1002316158105],        // list - 目标聊天ID数组
  
  // ========== 链接配置 (现有+新增) ==========
  "jumpLink": "abc",                                // str - 外部链接 (现有)
  "tgLink": "def",                                  // str - TG内部链接 (现有)
  "urlLabel": "点击查看",                           // str - 链接按钮文本 (🆕新增)
  "urlType": "browser",                             // str - 链接类型 browser/webapp (🆕新增)
  
  // ========== 定时配置 (现有+新增) ==========
  "pushTimeString": "05:00:00",                     // str - 推送时间 (现有)
  "pushTime": 18000000,                             // int - 推送时间毫秒 (现有)
  "cycle": [1, 2, 3, 4, 5, 6, 7],                 // list - 星期周期 (现有)
  "sendFrequency": "weekly",                        // str - 发送频率 daily/weekly/monthly (🆕新增)
  "monthDays": [],                                  // list - 月日配置 [1-31] (🆕新增)
  
  // ========== Bot配置 (新增) ==========
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw", // str - Bot令牌 (🆕新增)
  
  // ========== 实时推送配置 (现有) ==========
  "types": [],                                      // list - 触发类型数组
  
  // ========== 业务字段 (现有) ==========
  "currencyId": 0,                                  // int - 货币ID
  "rewardType": 0,                                  // int - 奖励类型
  "turnoverMul": 0,                                 // int - 流水倍数
  "amount": 0.0,                                    // float - 金额
  
  // ========== 状态和时间 (现有) ==========
  "open": true,                                     // bool - 是否启用
  "createTime": 1751686484047,                      // int64 - 创建时间
  "updateTime": 1751686484047                       // int64 - 更新时间
}
```

#### **新增字段**：
- **`urlLabel`** - 链接按钮文本
- **`urlType`** - 链接类型 (browser/webapp)
- **`sendFrequency`** - 发送频率 (daily/weekly/monthly)
- **`monthDays`** - 月日配置 [1-31]
- **`botToken`** - Bot令牌

---

## 🔄 **字段映射关系表**

| 定时任务表字段 | 配置表字段 | 映射状态 | 转换说明 |
|---------------|-----------|---------|----------|
| `notifyId` | `id` | ✅ 直接映射 | 任务ID |
| `business_no` | `business_no` | ✅ 直接映射 | 业务编号 |
| `botToken` | `botToken` | 🆕 新增字段 | Bot令牌 |
| `channelId` | `notifyTarget` | ✅ 直接映射 | 目标聊天 |
| `bannerUrl` | `msgBanner` | ✅ 直接映射 | 图片URL |
| `messageText` | `text` | ✅ 直接映射 | 消息文本 |
| `externalUrl` | `jumpLink` | ✅ 直接映射 | 外部链接 |
| `internalUrl` | `tgLink` | ✅ 直接映射 | 内部链接 |
| `urlLabel` | `urlLabel` | 🆕 新增字段 | 按钮文本 |
| `urlType` | `urlType` | 🆕 新增字段 | 链接类型 |
| `sendTime` | `pushTimeString` | ✅ 直接映射 | 发送时间 |
| `sendFrequency` | `sendFrequency` | 🆕 新增字段 | 发送频率 |
| `weekDays` | `cycle` | ✅ 直接映射 | 星期配置 |
| `monthDays` | `monthDays` | 🆕 新增字段 | 月日配置 |
| `enabled` | `open` | ✅ 直接映射 | 启用状态 |
| `createTime` | `createTime` | ✅ 直接映射 | 创建时间 |
| `updateTime` | `updateTime` | ✅ 直接映射 | 更新时间 |

---

## 📊 **字段统计对比**

| 表名 | 总字段数 | 核心功能字段 | 业务扩展字段 | 新增字段 |
|------|---------|-------------|-------------|---------|
| **c_tgScheduledPushTasks** | 17 | 17 | 0 | - |
| **c_tgNotify (现有)** | 20 | 12 | 8 | - |
| **c_tgNotify (增强版)** | 24 | 16 | 8 | 4 |

---

## 🚀 **实施建议**

### **阶段1: 字段补全**
```javascript
// MongoDB迁移脚本
db.c_tgNotify.updateMany(
    {},
    {
        $set: {
            "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
            "urlLabel": "",
            "urlType": "browser",
            "sendFrequency": "weekly",
            "monthDays": []
        }
    }
)
```

### **阶段2: 同步机制**
- 实现配置表到任务表的自动同步
- 支持增量更新和全量同步
- 添加同步状态监控

### **阶段3: 功能验证**
- 测试定时任务正常执行
- 验证配置变更能正确同步
- 确保业务功能不受影响

---

## 💡 **优势分析**

### **✅ 保持独立性**：
- 配置表专注配置管理
- 任务表专注任务执行
- 职责分离，维护简单

### **✅ 功能完整性**：
- 支持定时和实时两种模式
- 保留所有业务扩展字段
- 向后兼容现有功能

### **✅ 同步可靠性**：
- 明确的字段映射关系
- 自动化同步机制
- 数据一致性保证
