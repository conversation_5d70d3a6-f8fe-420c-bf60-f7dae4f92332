#!/usr/bin/env python3
"""
测试活动名称调试
"""
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_direct_debug():
    """直接测试调试"""
    print("🔧 直接测试活动名称调试")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "2"  # 葡萄牙语
        
        print(f"📋 测试模板: {template}")
        print(f"📋 语言: {language_id}")
        
        # 测试用例：activity_id=1
        print(f"\n🔍 测试activity_id=1的情况:")
        params = ["xxx", "ttttt", "100", "BRL"]
        activity_id = 1
        message_type = 19000
        
        print(f"   参数: {params}")
        print(f"   activity_id: {activity_id}")
        print(f"   message_type: {message_type}")
        print(f"   期望: 查询activity_id=1对应的活动名称")
        
        print(f"\n📋 开始渲染，请查看详细日志:")
        print("=" * 40)
        
        result = translation_manager.render_template(
            template, params, language_id, 
            activity_id=activity_id, message_type=message_type
        )
        
        print("=" * 40)
        print(f"📊 最终结果: {result}")
        
        # 分析结果
        if "xxx" not in result:
            print(f"✅ 参数无效化成功：'xxx'被替换")
        else:
            print(f"❌ 参数无效化失败：仍包含'xxx'")
        
        if "充值排行" in result:
            print(f"✅ 期望结果：显示了'充值排行'")
        elif "First Deposit Bonus" in result:
            print(f"⚠️ 意外结果：显示了'First Deposit Bonus'（可能是type映射）")
        else:
            print(f"❓ 其他结果：显示了其他内容")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_debug():
    """测试API调试"""
    print(f"\n🔧 测试API调试")
    print("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": ["xxx", "ttttt", "100", "BRL"],
        "activity_id": 1
    }
    
    print(f"📡 API地址: {api_url}")
    print(f"📋 测试数据:")
    print(json.dumps(test_data, indent=2, ensure_ascii=False))
    print(f"📋 期望: 查询activity_id=1，显示'充值排行'")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        print(f"\n📊 响应:")
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get("status") == "success":
                    print(f"   ✅ API调用成功")
                    print(f"   💬 请检查Telegram消息和服务器日志")
                    return True
                else:
                    print(f"   ❌ API失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                print(f"   ⚠️ 响应格式异常: {response.text}")
                return False
        else:
            print(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    print("🔧 活动名称调试测试")
    print("=" * 80)
    
    print("📋 调试目标:")
    print("   1. 查看activity_id=1查询到的activityName是什么")
    print("   2. 确认最终替换的活动名称")
    print("   3. 确认替换来源：activity_id、type还是现有映射")
    print("   4. 期望结果：显示'充值排行'而不是'First Deposit Bonus'")
    
    # 1. 测试直接渲染
    direct_ok = test_direct_debug()
    
    # 2. 测试API（如果有可用的通知配置）
    if direct_ok:
        print(f"\n💡 如果有可用的通知配置，可以测试API:")
        # api_ok = test_api_debug()
    
    print(f"\n" + "=" * 80)
    print(f"📋 调试说明:")
    print(f"   请查看上面的详细日志，重点关注:")
    print(f"   1. 🔍 开始查询activity_id: 1, language_id: 2")
    print(f"   2. 📄 原始activityData: [数据库内容]")
    print(f"   3. ✅/❌ 通过activity_id找到活动名称: [结果]")
    print(f"   4. 🎯 最终替换结果: [来源说明]")
    
    print(f"\n💡 如果显示'First Deposit Bonus'而不是'充值排行':")
    print(f"   可能原因:")
    print(f"   1. activity_id=1在数据库中不存在")
    print(f"   2. language_id=2在activityData中不存在")
    print(f"   3. 降级到了type=19000的映射")

if __name__ == "__main__":
    main()
