#!/usr/bin/env python3
"""
测试活动名修复
"""
from config.logging_config import app_logger
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_direct_template():
    """直接测试模板渲染"""
    app_logger.info("🔧 直接测试模板渲染")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"📋 测试模板: {template}")
        app_logger.info(f"📋 语言: {language_id}")
        
        # 测试用例1: 有activity_id
        app_logger.info(f"\n1. 测试有activity_id的情况:")
        params1 = ["xxx", "ttttt", "100", "BRL"]
        activity_id = 12
        message_type = 19000
        
        app_logger.info(f"   参数: {params1}")
        app_logger.info(f"   activity_id: {activity_id}")
        app_logger.info(f"   message_type: {message_type}")
        app_logger.info(f"   期望: 'xxx'被替换为activity_id=12查询的活动名称")
        
        result1 = translation_manager.render_template(
            template, params1, language_id, 
            activity_id=activity_id, message_type=message_type
        )
        
        app_logger.info(f"   结果: {result1}")
        
        if "xxx" not in result1:
            app_logger.info(f"   ✅ 成功：'xxx'被正确替换")
        else:
            app_logger.info(f"   ❌ 失败：仍包含'xxx'")
        
        # 测试用例2: 无activity_id，有type
        app_logger.info(f"\n2. 测试无activity_id，有type的情况:")
        params2 = ["yyy", "ttttt", "100", "BRL"]
        message_type2 = 19000
        
        app_logger.info(f"   参数: {params2}")
        app_logger.info(f"   activity_id: None")
        app_logger.info(f"   message_type: {message_type2}")
        app_logger.info(f"   期望: 'yyy'被替换为type=19000对应的活动名称")
        
        result2 = translation_manager.render_template(
            template, params2, language_id, 
            activity_id=None, message_type=message_type2
        )
        
        app_logger.info(f"   结果: {result2}")
        
        if "yyy" not in result2:
            app_logger.info(f"   ✅ 成功：'yyy'被正确替换")
        else:
            app_logger.info(f"   ❌ 失败：仍包含'yyy'")
        
        # 测试用例3: 都没有，使用现有映射
        app_logger.info(f"\n3. 测试使用现有映射的情况:")
        params3 = ["19000", "ttttt", "100", "BRL"]
        
        app_logger.info(f"   参数: {params3}")
        app_logger.info(f"   activity_id: None")
        app_logger.info(f"   message_type: None")
        app_logger.info(f"   期望: '19000'通过现有映射被替换")
        
        result3 = translation_manager.render_template(
            template, params3, language_id, 
            activity_id=None, message_type=None
        )
        
        app_logger.info(f"   结果: {result3}")
        
        if "19000" not in result3 and ("Bônus" in result3 or "Primeiro" in result3):
            app_logger.info(f"   ✅ 成功：使用了现有映射")
        else:
            app_logger.info(f"   ❌ 失败：现有映射不工作")
        
        return True
        
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api():
    """测试API"""
    app_logger.info(f"\n🔧 测试API")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": ["xxx", "ttttt", "100", "BRL"],
        "activity_id": 12
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    app_logger.info(f"📋 期望: 'xxx'被替换，不在最终消息中出现")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                if result.get("status") == "success":
                    app_logger.info(f"   ✅ API调用成功")
                    app_logger.info(f"   💬 请检查Telegram消息，应该不包含'xxx'")
                    return True
                else:
                    app_logger.info(f"   ❌ API失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.info(f"   ⚠️ 响应格式异常: {response.text}")
                return False
        else:
            app_logger.info(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        app_logger.info(f"   ❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 测试活动名修复")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 问题分析:")
    app_logger.info("   - 当前结果: 'Parabéns a xxx por conquistar  em ttttt.'")
    app_logger.info("   - 问题: 'xxx'没有被替换，说明活动名参数没有被正确无效化")
    app_logger.info("   - 修复: 精确识别活动名占位符位置，智能替换参数")
    
    # 1. 测试直接渲染
    direct_ok = test_direct_template()
    
    # 2. 测试API
    if direct_ok:
        api_ok = test_api()
    else:
        api_ok = False
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📊 修复测试结果:")
    app_logger.info(f"   直接渲染: {'✅ 成功' if direct_ok else '❌ 失败'}")
    app_logger.info(f"   API测试: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if direct_ok:
        app_logger.info(f"\n🎉 活动名修复成功！")
        app_logger.info(f"💡 修复内容:")
        app_logger.info(f"   1. 精确识别活动名占位符位置")
        app_logger.info(f"   2. 智能替换：activity_id > type > 现有映射")
        app_logger.info(f"   3. 参数无效化：忽略原始参数值")
        
        app_logger.info(f"\n📱 现在的行为:")
        app_logger.info(f"   'xxx' -> 被智能替换，不再出现在最终消息中")
        app_logger.info(f"   活动名由系统智能查找，减少调用方错误")
    else:
        app_logger.info(f"\n⚠️ 修复失败，请检查代码")

if __name__ == "__main__":
    main()
