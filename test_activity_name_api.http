### 测试活动名映射功能的API接口

### 1. 充值成功（系统通知）- 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 100,
  "params": [
    "张三",
    "100"
  ]
}

### 2. 提现成功（系统通知）- 英文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 200,
  "params": [
    "John",
    "200"
  ]
}

### 3. 游戏获奖 - 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 300,
  "params": [
    "李四",
    "300"
  ]
}

### 4. 红包雨（活动参与提醒）- 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 400,
  "params": [
    "400"
  ]
}

### 5. 红包雨（活动参与提醒）- 英文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 400,
  "params": [
    "400"
  ]
}

### 6. 投注返利（活动信息通知）
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 500,
  "params": [
    "500"
  ]
}

### 7. 投注返利（领取奖励通知）
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": [
    "王五",
    "18000",
    "1",
    "100"
  ]
}

### 8. 首充拉新（领取奖励通知）- 繁体中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 19000,
  "params": [
    "趙六",
    "19000",
    "2",
    "200"
  ]
}

### 9. 测试复杂模板 - 多个活动名参数
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": [
    "张三",
    "18000",
    "1",
    "100",
    "500"
  ]
}
