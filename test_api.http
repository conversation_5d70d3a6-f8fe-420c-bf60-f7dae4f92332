### Telegram Bot Service API 测试文件
### 使用方法：在VSCode中安装REST Client插件，然后点击"Send Request"

### 1. 健康检查
GET http://localhost:8000/health

###

### 2. 查看API根路径
GET http://localhost:8000/

###

### 3. 发送简单文本消息到测试群组
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🤖 API测试消息\n\n这是通过FastAPI发送的测试消息！\n\n时间: 2024-01-01 12:00:00"
}

###

### 4. 发送带图片的消息（图片+文字）- 简单测试
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "📸 这是一条带图片的测试消息\n\n🌟 图片来自 Picsum Photos\n📅 发送时间: 2024-01-01 12:00:00",
  "banner_image_url": "https://picsum.photos/400/300"
}

###

### 4.1 发送并保存图片到项目目录
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "💾 图片保存测试\n\n这张图片会被下载并保存到 images/tmp/ 目录中\n\n📁 文件名格式: img_时间戳_唯一ID.扩展名",
  "banner_image_url": "https://picsum.photos/600/400"
}

###

### 4.2 发送图片+文字+链接（type=browser，使用link，自定义链接文字）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🌐 浏览器链接测试\n\n这是一个图片+文字+浏览器链接的组合消息\n\n点击下方自定义链接文字",
  "banner_image_url": "https://picsum.photos/500/300",
  "type": "browser",
  "link": "https://www.google.com",
  "tg_short_link": "https://t.me/telegram",
  "link_text": "🔍 搜索Google"
}

###

### 4.3 发送图片+文字+链接（type=webapp，使用tg_short_link，自定义链接文字）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "📱 Telegram WebApp链接测试\n\n这是一个图片+文字+TG链接的组合消息\n\n点击下方自定义链接文字",
  "banner_image_url": "https://picsum.photos/500/300",
  "type": "webapp",
  "link": "https://www.google.com",
  "tg_short_link": "https://t.me/telegram",
  "link_text": "📢 访问Telegram官方"
}

###

### 4.4 发送图片+文字+双链接（完整测试，自定义链接文字）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🔗 双链接完整测试\n\n这个消息包含两种类型的链接，根据type参数选择显示哪个\n\n当前设置为browser模式，应该显示GitHub链接",
  "banner_image_url": "https://picsum.photos/600/350",
  "type": "browser",
  "link": "https://github.com",
  "tg_short_link": "https://t.me/durov",
  "link_text": "💻 访问GitHub代码仓库"
}

###

### 4.5 发送带真实图片的消息
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🏞️ 美丽的风景图片\n\n这是一张来自网络的高清风景图片，展示了大自然的美丽景色。",
  "banner_image_url": "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=400&fit=crop"
}

###

### 4.2 发送真实图片（下载后发送）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "📸 真实图片测试\n\n这是一张下载后发送的真实图片，不是链接预览。\n\n🔍 图片会作为真正的图片消息显示",
  "banner_image_url": "https://picsum.photos/600/400"
}

###

### 4.3 发送图片+文字+链接（type=browser，使用link）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🌐 浏览器链接测试\n\n这是一个图片+文字+浏览器链接的组合消息\n\n点击下方链接将在外部浏览器中打开Google",
  "banner_image_url": "https://picsum.photos/500/300",
  "type": "browser",
  "link": "https://www.google.com",
  "tg_short_link": "https://t.me/telegram"
}

###

### 4.4 发送图片+文字+链接（type=webapp，使用tg_short_link）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "📱 Telegram WebApp链接测试\n\n这是一个图片+文字+TG链接的组合消息\n\n点击下方链接将在Telegram内打开",
  "banner_image_url": "https://picsum.photos/500/300",
  "type": "webapp",
  "link": "https://www.google.com",
  "tg_short_link": "https://t.me/telegram"
}

###

### 4.5 发送图片+文字+双链接（完整测试）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🔗 双链接完整测试\n\n这个消息包含两种类型的链接，根据type参数选择显示哪个",
  "banner_image_url": "https://picsum.photos/600/350",
  "type": "browser",
  "link": "https://github.com",
  "tg_short_link": "https://t.me/durov"
}

###

### 4.6 发送带Logo的消息
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🤖 Telegram Bot 测试\n\n✅ API接口正常运行\n📊 消息发送功能测试\n🔧 图片+文字组合测试",
  "banner_image_url": "https://telegram.org/img/t_logo.png"
}

###

### 5. 发送带网址链接的消息
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🔗 点击访问Google搜索 https://www.google.com\n\n这是一个带链接的测试消息"
}

###

### 6. 发送带TG短链接的消息
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "📱 Telegram官方频道\n\n点击访问Telegram官方频道",
  "tg_short_link": "https://t.me/telegram",
  "type": "webapp"
}

###

### 7. 发送带图片和链接的组合消息
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "text": "🌟 Google搜索 - 全球最受欢迎的搜索引擎\n\n点击下方链接访问Google主页",
  "banner_image_url": "https://picsum.photos/800/400",
  "link": "https://www.google.com",
  "type": "browser"
}

###

### 8. 发送到多个目标（需要替换chat_ids为实际的用户ID）
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "group_ids": [-1002316158105],
  "chat_ids": [123456789],
  "text": "📢 多目标测试消息\n\n这条消息会发送到群组和个人聊天"
}

###

### 9. 测试任务重载（需要有实际的任务ID）
POST http://localhost:8000/api/task-reload
Content-Type: application/json

{
  "task_id": "test_task",
  "action": "reload"
}

###

### 10. 查看调度器状态
GET http://localhost:8000/api/scheduler-status

###

### 11. 错误测试 - 缺少必填参数
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "**********:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw"
}

###

### 12. 错误测试 - 无效的bot_token
POST http://localhost:8000/api/send-message
Content-Type: application/json

{
  "bot_token": "invalid_token",
  "group_ids": [-1002316158105],
  "text": "这个请求应该会失败"
}

###
