#!/usr/bin/env python3
"""
测试API日志记录
"""
import requests
import json
from config.logging_config import app_logger

def test_api_logging():
    """测试API日志记录"""
    app_logger.info("🔧 测试API日志记录功能")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,
        "params": ["测试日志", "ttttt", "100", "BRL"],
        "activity_id": 1
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                app_logger.info(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get("status") == "success":
                    app_logger.info(f"\n✅ API调用成功！")
                    app_logger.info(f"💬 消息已发送到Telegram")
                    app_logger.info(f"📁 请检查 logs/2025-07-12/ 目录中的日志文件")
                    return True
                else:
                    app_logger.warning(f"\n⚠️ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.error(f"   ❌ 响应格式异常: {response.text}")
                return False
        else:
            app_logger.error(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        app_logger.error(f"   ❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 API日志记录测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 测试目标:")
    app_logger.info("   1. 验证API请求/响应日志记录")
    app_logger.info("   2. 检查日志文件按日期分目录存储")
    app_logger.info("   3. 确认所有print已替换为logger")
    
    success = test_api_logging()
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        app_logger.info(f"\n🎉 日志系统完全正常！")
        app_logger.info(f"💡 功能特点:")
        app_logger.info(f"   1. API请求/响应完整记录")
        app_logger.info(f"   2. 日志按日期分目录存储")
        app_logger.info(f"   3. 同时输出到控制台和文件")
        app_logger.info(f"   4. 所有print已替换为logger")
        
        app_logger.info(f"\n📁 日志文件位置:")
        app_logger.info(f"   - API日志: logs/2025-07-12/api_2025-07-12.log")
        app_logger.info(f"   - 应用日志: logs/2025-07-12/app_2025-07-12.log")
        app_logger.info(f"   - 数据库日志: logs/2025-07-12/database_2025-07-12.log")
        app_logger.info(f"   - 推送日志: logs/2025-07-12/pusher_2025-07-12.log")
    else:
        app_logger.warning(f"\n⚠️ 日志系统有问题，请检查配置")

if __name__ == "__main__":
    main()
