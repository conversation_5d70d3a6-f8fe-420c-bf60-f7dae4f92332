#!/usr/bin/env python3
"""
测试Bot交互功能
启动Bot并等待用户交互
"""
import asyncio
import logging
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from interactive.main import InteractiveBot

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


async def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("启动 Telegram Bot 交互测试")
    logger.info("=" * 50)
    logger.info(f"Bot Token: {settings.bot_token[:10]}...")
    logger.info(f"使用模式: {'Webhook' if settings.webhook_url else 'Polling'}")
    logger.info("=" * 50)
    
    # 创建并启动Bot
    bot = InteractiveBot()
    
    try:
        await bot.start()
        
        if not settings.webhook_url:
            # Polling模式，保持运行
            logger.info("Bot正在运行中...")
            logger.info("发送 /start 命令到Bot来测试菜单功能")
            logger.info("按 Ctrl+C 停止Bot")
            
            # 保持运行
            while bot.is_running:
                await asyncio.sleep(1)
        else:
            # Webhook模式
            logger.info("Webhook模式启动完成")
            logger.info("Bot正在等待Webhook请求...")
            
            # 保持运行
            while True:
                await asyncio.sleep(60)
                
    except KeyboardInterrupt:
        logger.info("收到停止信号...")
        await bot.stop()
    except Exception as e:
        logger.error(f"Bot运行错误: {e}")
        await bot.stop()
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\nBot已停止")
    except Exception as e:
        print(f"Bot启动失败: {e}")
        sys.exit(1)
