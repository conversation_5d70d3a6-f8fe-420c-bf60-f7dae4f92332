#!/usr/bin/env python3
"""
测试缓存更新功能
验证config-update API是否能正确更新定时任务服务的缓存
"""
import asyncio
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager
from common.utils import setup_logging

import logging
logger = logging.getLogger(__name__)


async def test_cache_functionality():
    """测试缓存功能"""
    print("🧪 测试缓存更新功能")
    print("=" * 60)
    
    # 1. 初始化配置管理器
    print("1️⃣ 初始化配置管理器...")
    success = await config_manager.initialize()
    if not success:
        print("❌ 配置管理器初始化失败")
        return False
    
    # 2. 查看初始缓存状态
    print("\n2️⃣ 初始缓存状态:")
    stats = config_manager.get_cache_stats()
    print(f"   Token配置数量: {stats['total_configs']}")
    print(f"   启用的Token: {stats['active_configs']}")
    
    tasks = config_manager.get_enabled_tasks()
    print(f"   定时任务数量: {len(tasks)}")
    
    # 3. 测试API调用更新缓存
    print("\n3️⃣ 测试API更新缓存...")
    
    # 测试更新Bot配置
    print("   📡 调用API更新Bot配置...")
    response = requests.post(
        "http://localhost:8000/api/config-update",
        json={"update_type": "bot_config"},
        timeout=10
    )
    
    if response.status_code == 200:
        print("   ✅ Bot配置更新API调用成功")
        result = response.json()
        print(f"   📄 响应: {result['message']}")
    else:
        print(f"   ❌ Bot配置更新API调用失败: {response.status_code}")
        print(f"   📄 错误: {response.text}")
    
    # 等待后台任务完成
    await asyncio.sleep(2)
    
    # 测试更新定时任务
    print("   📡 调用API更新定时任务...")
    response = requests.post(
        "http://localhost:8000/api/config-update",
        json={"update_type": "scheduled_tasks"},
        timeout=10
    )
    
    if response.status_code == 200:
        print("   ✅ 定时任务更新API调用成功")
        result = response.json()
        print(f"   📄 响应: {result['message']}")
    else:
        print(f"   ❌ 定时任务更新API调用失败: {response.status_code}")
        print(f"   📄 错误: {response.text}")
    
    # 等待后台任务完成
    await asyncio.sleep(2)
    
    # 4. 查看更新后的缓存状态
    print("\n4️⃣ 更新后缓存状态:")
    stats = config_manager.get_cache_stats()
    print(f"   Token配置数量: {stats['total_configs']}")
    print(f"   启用的Token: {stats['active_configs']}")
    
    tasks = config_manager.get_enabled_tasks()
    print(f"   定时任务数量: {len(tasks)}")
    
    # 5. 测试指定ID更新
    print("\n5️⃣ 测试指定ID更新...")
    
    # 更新指定商户
    print("   📡 更新指定商户Bot配置...")
    response = requests.post(
        "http://localhost:8000/api/config-update",
        json={
            "update_type": "bot_config",
            "target_ids": ["39bac42a"]
        },
        timeout=10
    )
    
    if response.status_code == 200:
        print("   ✅ 指定商户更新成功")
    else:
        print(f"   ❌ 指定商户更新失败: {response.status_code}")
    
    # 更新指定任务
    print("   📡 更新指定定时任务...")
    response = requests.post(
        "http://localhost:8000/api/config-update",
        json={
            "update_type": "scheduled_tasks",
            "target_ids": ["1"]
        },
        timeout=10
    )
    
    if response.status_code == 200:
        print("   ✅ 指定任务更新成功")
    else:
        print(f"   ❌ 指定任务更新失败: {response.status_code}")
    
    await asyncio.sleep(2)
    
    print("\n✅ 缓存更新功能测试完成")
    return True


async def test_scheduler_integration():
    """测试调度器集成"""
    print("\n🔗 测试调度器集成")
    print("=" * 60)
    
    # 模拟调度器的任务加载过程
    from scheduler.task_scheduler import TaskScheduler
    
    scheduler = TaskScheduler()
    
    # 初始化
    if not scheduler.mongo.connect():
        print("❌ MongoDB连接失败")
        return False
    
    if not await config_manager.initialize():
        print("❌ 配置管理器初始化失败")
        return False
    
    # 测试任务加载
    print("📋 测试从缓存加载任务...")
    await scheduler._load_tasks()
    print(f"✅ 加载了 {len(scheduler.tasks)} 个任务")
    
    # 显示任务详情
    for i, task in enumerate(scheduler.tasks[:3], 1):  # 只显示前3个
        print(f"   任务{i}: ID={task.get('notifyId')}, 消息='{task.get('messageText')}'")
    
    scheduler.mongo.disconnect()
    return True


def test_api_status():
    """测试API状态"""
    print("\n📊 测试API状态")
    print("=" * 60)
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务运行正常")
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
        
        # 测试配置状态
        response = requests.get("http://localhost:8000/api/config-status", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ 配置状态API正常")
            print(f"   📄 状态: {json.dumps(result['data'], indent=2, ensure_ascii=False)}")
        else:
            print(f"❌ 配置状态API异常: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        print("💡 请确保API服务已启动: python -m uvicorn api.main:create_app --factory --host 0.0.0.0 --port 8000")
        return False


async def main():
    """主函数"""
    setup_logging()
    
    print("🚀 缓存更新功能测试")
    print("=" * 80)
    
    # 1. 测试API状态
    api_ok = test_api_status()
    if not api_ok:
        print("\n❌ API服务不可用，无法进行完整测试")
        return 1
    
    # 2. 测试缓存功能
    try:
        cache_ok = await test_cache_functionality()
        if not cache_ok:
            print("\n❌ 缓存功能测试失败")
            return 1
    except Exception as e:
        print(f"\n❌ 缓存功能测试异常: {e}")
        return 1
    
    # 3. 测试调度器集成
    try:
        scheduler_ok = await test_scheduler_integration()
        if not scheduler_ok:
            print("\n❌ 调度器集成测试失败")
            return 1
    except Exception as e:
        print(f"\n❌ 调度器集成测试异常: {e}")
        return 1
    
    print("\n" + "=" * 80)
    print("🎉 所有测试通过！")
    print("✅ config-update API可以正确更新定时任务服务的缓存")
    print("=" * 80)
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
