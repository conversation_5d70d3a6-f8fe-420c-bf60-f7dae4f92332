# TG Bot统一配置更新API测试

### 1. 健康检查
GET http://localhost:8000/health

### 2. 获取配置状态
GET http://localhost:8000/api/config-status

### 2.1 获取统一服务器状态 (新增)
GET http://localhost:8000/api/unified-status

### 3. 更新所有三表配置 (c_tbRobotConfig + c_tgNotify + c_tgScheduledPushTasks)
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "all"
}

### 4. 更新所有Bot配置 (c_tbRobotConfig)
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "robot_config"
}

### 5. 更新指定商户的Bot配置
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "robot_config",
  "target_ids": ["39bac42a", "40bac42b"]
}

### 6. 更新单个商户的Bot配置
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "robot_config",
  "target_ids": ["39bac42a"]
}

### 6.1 更新所有通知配置 (c_tgNotify)
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "notify_config"
}

### 6.2 更新指定通知配置
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "notify_config",
  "target_ids": ["1", "2"]
}

### 7. 更新所有定时任务
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "scheduled_tasks"
}

### 8. 更新指定的定时任务
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "scheduled_tasks",
  "target_ids": ["1", "2", "3"]
}

### 9. 模板消息预览
POST http://localhost:8000/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": [
    "user_name_zhang",
    [1, 2, 3],
    ["game_a", "game_b", "game_c"],
    ["100", "50", "200"],
    "thank_you_message"
  ]
}

### 10. 模板消息发送
POST http://localhost:8000/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": [
    "user_name_zhang",
    ["game_a", "game_b"],
    ["100", "50"],
    "thank_you_message"
  ],
  "target_chats": [-1002316158105]
}

### 11. 简单模板消息测试
POST http://localhost:8000/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": ["张三"]
}

### 9. 更新单个定时任务
POST http://localhost:8000/api/config-update
Content-Type: application/json

{
  "update_type": "scheduled_tasks",
  "target_ids": ["1"]
}
