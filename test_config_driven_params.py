#!/usr/bin/env python3
"""
测试配置文件驱动的特殊参数替换功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_config_loading():
    """测试配置加载"""
    print("🔧 测试配置加载")
    print("=" * 50)
    
    try:
        from config.settings import settings
        
        # 测试关键字配置
        keywords = settings.special_params_keywords
        print(f"📋 关键字配置: {keywords}")
        
        # 测试映射配置
        mappings = settings.special_params_mappings
        print(f"📋 映射配置类型: {list(mappings.keys())}")
        
        # 检查活动名映射
        if "activity_name" in mappings:
            activity_mappings = mappings["activity_name"]
            print(f"📋 活动名映射: {list(activity_mappings.keys())}")
            
            # 检查18000的映射
            if "18000" in activity_mappings:
                mapping_18000 = activity_mappings["18000"]
                print(f"📋 18000映射: {mapping_18000}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_special_params_replacement():
    """测试特殊参数替换"""
    print(f"\n🎯 测试特殊参数替换")
    print("=" * 50)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试用例
        test_cases = [
            {
                "name": "活动名+币种替换",
                "template": "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}",
                "params": ["张三", "18000", "1", "100"],
                "language": "1",
                "expected_contains": ["张三", "每日签到活动", "金币", "100"]
            },
            {
                "name": "游戏名替换",
                "template": "玩家 {玩家名1} 在 {游戏名2} 中获得大奖！",
                "params": ["王五", "1001"],
                "language": "1",
                "expected_contains": ["王五", "老虎机"]
            },
            {
                "name": "等级替换",
                "template": "恭喜 {玩家名1} 升级到 {等级2}！",
                "params": ["李四", "2"],
                "language": "1",
                "expected_contains": ["李四", "白银"]
            },
            {
                "name": "英文活动名替换",
                "template": "Congratulations {玩家名1} won {币种2} in {活动名3}",
                "params": ["John", "2", "19000"],
                "language": "2",
                "expected_contains": ["John", "Diamond", "Recharge Bonus"]
            },
            {
                "name": "未知ID保持原值",
                "template": "恭喜 {玩家ID1} 在{活动名2} 获得奖励",
                "params": ["测试玩家", "99999"],
                "language": "1",
                "expected_contains": ["测试玩家", "99999"]
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {case['name']}")
            print(f"   模板: {case['template']}")
            print(f"   参数: {case['params']}")
            print(f"   语言: {case['language']}")
            
            try:
                result = translator.render_template(
                    case['template'], 
                    case['params'], 
                    case['language']
                )
                
                print(f"   结果: {result}")
                
                # 检查期望内容
                all_found = True
                for expected in case['expected_contains']:
                    if expected in result:
                        print(f"   ✅ 包含: {expected}")
                    else:
                        print(f"   ❌ 缺失: {expected}")
                        all_found = False
                
                if all_found:
                    print(f"   🎉 测试通过")
                    success_count += 1
                else:
                    print(f"   ⚠️ 测试部分失败")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 通过")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_modification():
    """测试配置修改的效果"""
    print(f"\n🔧 测试配置修改效果")
    print("=" * 50)
    
    try:
        from config.settings import settings
        from common.translation_manager import TranslationManager
        
        # 显示当前配置
        mappings = settings.special_params_mappings
        if "activity_name" in mappings and "18000" in mappings["activity_name"]:
            current_mapping = mappings["activity_name"]["18000"]["1"]
            print(f"📋 当前18000的中文映射: {current_mapping}")
        
        # 测试渲染
        translator = TranslationManager()
        template = "恭喜 {玩家ID1} 在{活动名2} 获得奖励"
        params = ["张三", "18000"]
        language = "1"
        
        result = translator.render_template(template, params, language)
        print(f"📋 渲染结果: {result}")
        
        print(f"\n💡 要修改映射，请编辑 config/config.yaml 文件中的 special_params.mappings 部分")
        print(f"💡 修改后重启服务即可生效，无需修改代码")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 配置文件驱动的特殊参数替换测试")
    print("=" * 60)
    
    # 1. 测试配置加载
    config_ok = test_config_loading()
    
    # 2. 测试特殊参数替换
    params_ok = test_special_params_replacement() if config_ok else False
    
    # 3. 测试配置修改效果
    modify_ok = test_config_modification() if config_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   🔧 配置加载: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"   🎯 参数替换: {'✅ 正常' if params_ok else '❌ 异常'}")
    print(f"   🔧 配置修改: {'✅ 正常' if modify_ok else '❌ 异常'}")
    
    if config_ok and params_ok and modify_ok:
        print(f"\n🎉 配置文件驱动的特殊参数替换功能正常！")
        print(f"💡 功能特点:")
        print(f"   • 所有映射配置都在 config/config.yaml 中")
        print(f"   • 支持动态添加新的关键字和映射")
        print(f"   • 修改配置后重启服务即可生效")
        print(f"   • 无需修改代码，完全配置驱动")
        print(f"   • 支持多语言映射")
        
        print(f"\n📝 配置文件位置:")
        print(f"   • 主配置: config/config.yaml")
        print(f"   • 关键字配置: special_params.keywords")
        print(f"   • 映射配置: special_params.mappings")
    else:
        print(f"\n⚠️ 部分功能异常，请检查配置文件")
    
    print("=" * 60)
    
    return 0 if (config_ok and params_ok and modify_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
