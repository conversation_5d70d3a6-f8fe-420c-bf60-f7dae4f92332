#!/usr/bin/env python3
"""
测试正确的语言映射配置
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_language_mapping_alignment():
    """测试语言映射对齐"""
    print("🌐 测试语言映射对齐")
    print("=" * 70)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 根据实际的language_mapping测试
        # 1=English, 2=Português, 12=繁體中文
        test_cases = [
            {
                "name": "红包雨活动 - English",
                "template": "Join {活动名1} event to win rewards!",
                "params": ["400"],
                "language": "1",  # English
                "expected": "Red Envelope Rain"
            },
            {
                "name": "红包雨活动 - Português", 
                "template": "Participe do evento {活动名1} para ganhar recompensas!",
                "params": ["400"],
                "language": "2",  # Português
                "expected": "Chuva de Envelopes Vermelhos"
            },
            {
                "name": "红包雨活动 - 繁體中文",
                "template": "參與{活动名1}活動獲得獎勵！",
                "params": ["400"],
                "language": "12",  # 繁體中文
                "expected": "紅包雨"
            },
            {
                "name": "充值成功 - English",
                "template": "Congratulations on {活动名1}!",
                "params": ["100"],
                "language": "1",
                "expected": "Deposit Success"
            },
            {
                "name": "充值成功 - Português",
                "template": "Parabéns pelo {活动名1}!",
                "params": ["100"],
                "language": "2",
                "expected": "Sucesso do Depósito"
            },
            {
                "name": "充值成功 - 繁體中文",
                "template": "恭喜{活动名1}！",
                "params": ["100"],
                "language": "12",
                "expected": "充值成功"
            },
            {
                "name": "投注返利 - English",
                "template": "You received {活动名1} bonus!",
                "params": ["18000"],
                "language": "1",
                "expected": "Betting Cashback"
            },
            {
                "name": "投注返利 - Português",
                "template": "Você recebeu bônus de {活动名1}!",
                "params": ["18000"],
                "language": "2",
                "expected": "Cashback de Apostas"
            },
            {
                "name": "投注返利 - 繁體中文",
                "template": "您獲得了{活动名1}獎勵！",
                "params": ["18000"],
                "language": "12",
                "expected": "投注返利"
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {case['name']}")
            print(f"   模板: {case['template']}")
            print(f"   参数: {case['params']}")
            print(f"   语言ID: {case['language']}")
            
            try:
                result = translator.render_template(
                    case['template'], 
                    case['params'], 
                    case['language']
                )
                
                print(f"   结果: {result}")
                
                if case['expected'] in result:
                    print(f"   ✅ 映射正确: {case['expected']}")
                    success_count += 1
                else:
                    print(f"   ❌ 映射错误，期望: {case['expected']}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print(f"\n" + "=" * 70)
        print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过")
        
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_language_config_consistency():
    """测试语言配置一致性"""
    print(f"\n🔧 测试语言配置一致性")
    print("=" * 70)
    
    try:
        from config.settings import settings
        
        # 检查特殊参数映射配置
        mappings = settings.special_params_mappings
        activity_mappings = mappings.get("activity_name", {})
        
        print(f"📋 检查活动名映射的语言ID:")
        
        # 检查每个活动是否都有正确的语言ID
        expected_language_ids = ["1", "2", "12"]  # English, Português, 繁體中文
        
        for activity_id, translations in activity_mappings.items():
            print(f"   活动 {activity_id}:")
            
            for lang_id in expected_language_ids:
                if lang_id in translations:
                    lang_name = {
                        "1": "English",
                        "2": "Português", 
                        "12": "繁體中文"
                    }[lang_id]
                    print(f"     ✅ {lang_name} ({lang_id}): {translations[lang_id]}")
                else:
                    print(f"     ❌ 缺少语言 {lang_id}")
        
        # 显示语言映射对照
        print(f"\n🌐 正确的语言映射对照:")
        print(f"   1 = English (英语)")
        print(f"   2 = Português (葡萄牙语)")
        print(f"   12 = 繁體中文 (繁体中文)")
        
        print(f"\n💡 注意:")
        print(f"   • 这与 translation_config.json 中的 language_mapping 对齐")
        print(f"   • 不再使用错误的 1=中文, 2=英语, 3=葡萄牙语")
        print(f"   • 现在使用正确的 1=English, 2=Português, 12=繁體中文")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置检查失败: {e}")
        return False

def test_complex_multilingual_template():
    """测试复杂的多语言模板"""
    print(f"\n🎯 测试复杂的多语言模板")
    print("=" * 70)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 复杂模板测试
        template = "Player won {币种1} in {游戏名2} during {活动名3} event!"
        params = ["1", "1001", "400"]  # Gold, Slot Machine, Red Envelope Rain
        
        languages = [
            ("1", "English"),
            ("2", "Português"),
            ("12", "繁體中文")
        ]
        
        print(f"📋 测试模板: {template}")
        print(f"📋 测试参数: {params}")
        
        for lang_id, lang_name in languages:
            print(f"\n🔍 {lang_name} (ID: {lang_id}):")
            
            try:
                result = translator.render_template(template, params, lang_id)
                print(f"   结果: {result}")
                
                # 检查是否包含对应语言的翻译
                expected_words = {
                    "1": ["Gold", "Slot Machine", "Red Envelope Rain"],
                    "2": ["Ouro", "Máquina Caça-níqueis", "Chuva de Envelopes Vermelhos"],
                    "12": ["金幣", "老虎機", "紅包雨"]
                }
                
                words_found = sum(1 for word in expected_words[lang_id] if word in result)
                print(f"   匹配词汇: {words_found}/{len(expected_words[lang_id])}")
                
                if words_found == len(expected_words[lang_id]):
                    print(f"   ✅ {lang_name}完全正确")
                else:
                    print(f"   ⚠️ {lang_name}部分匹配")
                    
            except Exception as e:
                print(f"   ❌ {lang_name}异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 复杂模板测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 正确语言映射配置测试")
    print("=" * 80)
    
    # 1. 测试语言映射对齐
    alignment_ok = test_language_mapping_alignment()
    
    # 2. 测试配置一致性
    config_ok = test_language_config_consistency()
    
    # 3. 测试复杂模板
    complex_ok = test_complex_multilingual_template()
    
    print(f"\n" + "=" * 80)
    print("📊 总体测试结果:")
    print(f"   🌐 语言映射对齐: {'✅ 成功' if alignment_ok else '❌ 失败'}")
    print(f"   🔧 配置一致性: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"   🎯 复杂模板: {'✅ 成功' if complex_ok else '❌ 失败'}")
    
    if alignment_ok and config_ok and complex_ok:
        print(f"\n🎉 语言映射配置修正完成！")
        print(f"💡 现在的配置:")
        print(f"   • 与 translation_config.json 完全对齐")
        print(f"   • 1 = English (英语)")
        print(f"   • 2 = Português (葡萄牙语)")
        print(f"   • 12 = 繁體中文 (繁体中文)")
        print(f"   • 支持所有7种活动类型的正确翻译")
    else:
        print(f"\n⚠️ 部分功能异常，需要进一步检查")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
