#!/usr/bin/env python3
"""
测试动态适配能力
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_dynamic_templates():
    """测试不同模板的动态适配"""
    print("🔧 测试动态模板适配")
    print("=" * 80)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模拟排行榜数据
        rank_data = [
            {"ranking": 1, "playerId": 4743182, "currencyId": 1000, "wagered": 2092.21},
            {"ranking": 2, "playerId": 2268779, "currencyId": 1000, "wagered": 2035.44},
            {"ranking": 3, "playerId": 7731938, "currencyId": 1000, "wagered": 1821.96}
        ]
        
        # 测试不同的模板格式
        test_cases = [
            {
                "name": "当前数据库模板",
                "template": """Daily Ranking Revealed
Today's Top Players:
[repeat]{1}{2} – {3}{4}
[/repeat]""",
                "description": "4个占位符: 排名, 玩家ID, 货币, 投注额"
            },
            {
                "name": "简化模板",
                "template": """排行榜
[repeat]{rank1}: {player2}
[/repeat]""",
                "description": "2个占位符: 排名, 玩家ID"
            },
            {
                "name": "复杂模板",
                "template": """🏆 投注排行榜
[repeat]第{pos1}名 玩家{id2} 货币{curr3} 投注{amt4} 奖励{reward5}
[/repeat]
总计: {total6}""",
                "description": "6个占位符: 位置, ID, 货币, 投注, 奖励, 总计"
            },
            {
                "name": "参考代码格式",
                "template": """Daily Ranking Revealed  
Today's Top Players:
[repeat]{AAA1} {BBB2} – {CCC3}{DDD4}
[/repeat]""",
                "description": "4个占位符: AAA, BBB, CCC, DDD"
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}")
            print(f"   描述: {test_case['description']}")
            print(f"   模板: {test_case['template']}")
            
            # 动态准备参数
            try:
                # 模拟处理器的动态参数准备逻辑
                import re
                
                template_text = test_case['template']
                start_repeat = template_text.find("[repeat]")
                end_repeat = template_text.find("[/repeat]")
                
                if start_repeat != -1 and end_repeat != -1:
                    repeat_content = template_text[start_repeat + len("[repeat]"):end_repeat]
                    placeholders = re.findall(r'\{(\w+)\}', repeat_content)
                    
                    print(f"   占位符: {placeholders}")
                    print(f"   占位符数量: {len(placeholders)}")
                    
                    # 动态组装数据
                    rank_list = []
                    for record in rank_data:
                        rank_str = str(record["ranking"])
                        player_str = str(record["playerId"])
                        currency_str = str(record["currencyId"])
                        wagered_str = f"{float(record['wagered']):,.2f}"
                        
                        # 根据占位符数量动态组装
                        data_values = [rank_str, player_str, currency_str, wagered_str]
                        
                        if len(placeholders) <= len(data_values):
                            row_data = data_values[:len(placeholders)]
                        else:
                            row_data = data_values + [""] * (len(placeholders) - len(data_values))
                        
                        rank_list.append(row_data)
                    
                    params = [rank_list]
                    
                    print(f"   参数示例: {rank_list[0] if rank_list else []}")
                    
                    # 渲染测试
                    rendered = translation_manager.render_template(template_text, params, "1")
                    print(f"   渲染结果:")
                    for line in rendered.split('\n')[:5]:  # 只显示前5行
                        print(f"     {line}")
                    if len(rendered.split('\n')) > 5:
                        print(f"     ... (共{len(rendered.split('\n'))}行)")
                    
                    # 检查是否有未替换的占位符
                    if "{" in rendered and "}" in rendered:
                        print(f"   ❌ 仍有未替换的占位符")
                    else:
                        print(f"   ✅ 所有占位符已正确替换")
                else:
                    print(f"   ⚠️ 没有找到[repeat]块")
                
            except Exception as e:
                print(f"   ❌ 测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 动态适配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler_dynamic():
    """测试处理器的动态适配"""
    print(f"\n🎯 测试处理器动态适配")
    print("=" * 80)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            template_text = data.get('template_text', '')
            params = data.get('params', [])
            
            print(f"\n动态适配结果:")
            print(f"  模板: {template_text[:50]}...")
            print(f"  参数数量: {len(params)}")
            
            if params and isinstance(params[0], list):
                print(f"  嵌套数组: {len(params[0])} 条记录")
                print(f"  每行数据长度: {len(params[0][0]) if params[0] else 0}")
                
                # 显示前3条记录
                for i, rank_item in enumerate(params[0][:3], 1):
                    print(f"    {i}. {rank_item}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器动态适配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试动态适配能力")
    print("=" * 100)
    
    # 1. 测试不同模板的动态适配
    template_ok = await test_dynamic_templates()
    
    # 2. 测试处理器的动态适配
    handler_ok = await test_rebate_handler_dynamic()
    
    print(f"\n" + "=" * 100)
    print(f"📊 动态适配测试结果:")
    print(f"   模板适配: {'✅ 成功' if template_ok else '❌ 失败'}")
    print(f"   处理器适配: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if template_ok and handler_ok:
        print(f"\n🎉 动态适配功能完全正常！")
        print(f"💡 优势:")
        print(f"   1. 无硬编码，完全动态解析模板")
        print(f"   2. 支持任意数量的占位符")
        print(f"   3. 支持任意命名的占位符")
        print(f"   4. 自动适配数据长度")
        print(f"   5. 与参考代码逻辑完全一致")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 验证模板修改后的兼容性")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
