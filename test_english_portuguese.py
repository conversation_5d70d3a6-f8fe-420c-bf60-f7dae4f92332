#!/usr/bin/env python3
"""
测试英语和葡萄牙语的活动名映射
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_activity_names():
    """测试活动名的英语和葡萄牙语翻译"""
    print("🌐 测试活动名的英语和葡萄牙语翻译")
    print("=" * 70)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试用例 - 所有活动类型的多语言支持
        activities = [
            ("100", "充值成功", "Deposit Success", "Sucesso do Depósito"),
            ("200", "提现成功", "Withdrawal Success", "Sucesso do Saque"),
            ("300", "游戏获奖", "Game Winning", "Vitória no Jogo"),
            ("400", "红包雨", "Red Envelope Rain", "Chuva de Envelopes Vermelhos"),
            ("500", "投注返利", "Betting Cashback", "Cashback de Apostas"),
            ("18000", "投注返利", "Betting Cashback", "Cashback de Apostas"),
            ("19000", "首充拉新", "First Deposit Bonus", "Bônus de Primeiro Depósito")
        ]
        
        template = "参与{活动名1}活动获得奖励"
        
        success_count = 0
        total_tests = len(activities) * 3  # 3种语言
        
        for activity_id, chinese, english, portuguese in activities:
            print(f"\n📋 测试活动ID: {activity_id}")
            
            # 测试中文
            try:
                result_cn = translator.render_template(template, [activity_id], "1")
                if chinese in result_cn:
                    print(f"   ✅ 中文: {result_cn}")
                    success_count += 1
                else:
                    print(f"   ❌ 中文失败: {result_cn} (期望: {chinese})")
            except Exception as e:
                print(f"   ❌ 中文异常: {e}")
            
            # 测试英语
            try:
                result_en = translator.render_template(template, [activity_id], "2")
                if english in result_en:
                    print(f"   ✅ 英语: {result_en}")
                    success_count += 1
                else:
                    print(f"   ❌ 英语失败: {result_en} (期望: {english})")
            except Exception as e:
                print(f"   ❌ 英语异常: {e}")
            
            # 测试葡萄牙语
            try:
                result_pt = translator.render_template(template, [activity_id], "3")
                if portuguese in result_pt:
                    print(f"   ✅ 葡萄牙语: {result_pt}")
                    success_count += 1
                else:
                    print(f"   ❌ 葡萄牙语失败: {result_pt} (期望: {portuguese})")
            except Exception as e:
                print(f"   ❌ 葡萄牙语异常: {e}")
        
        print(f"\n" + "=" * 70)
        print(f"📊 测试结果: {success_count}/{total_tests} 通过")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complex_templates():
    """测试复杂模板的多语言支持"""
    print(f"\n🎯 测试复杂模板的多语言支持")
    print("=" * 70)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 复杂模板测试
        test_cases = [
            {
                "name": "充值成功通知",
                "template": "恭喜 {玩家名1} {活动名2} 获得 {币种3} 奖励！",
                "params": ["张三", "100", "1"],
                "expected": {
                    "1": ["张三", "充值成功", "金币"],
                    "2": ["张三", "Deposit Success", "Gold"],
                    "3": ["张三", "Sucesso do Depósito", "Ouro"]
                }
            },
            {
                "name": "红包雨活动",
                "template": "快来参与{活动名1}活动，赢取{币种2}大奖！",
                "params": ["400", "2"],
                "expected": {
                    "1": ["红包雨", "钻石"],
                    "2": ["Red Envelope Rain", "Diamond"],
                    "3": ["Chuva de Envelopes Vermelhos", "Diamante"]
                }
            },
            {
                "name": "游戏获奖通知",
                "template": "玩家在{游戏名1}中{活动名2}，获得{币种3}奖励！",
                "params": ["1001", "300", "3"],
                "expected": {
                    "1": ["老虎机", "游戏获奖", "积分"],
                    "2": ["Slot Machine", "Game Winning", "Points"],
                    "3": ["Máquina Caça-níqueis", "Vitória no Jogo", "Pontos"]
                }
            }
        ]
        
        success_count = 0
        total_tests = len(test_cases) * 3
        
        for case in test_cases:
            print(f"\n📋 测试: {case['name']}")
            print(f"   模板: {case['template']}")
            print(f"   参数: {case['params']}")
            
            for lang_id, expected_words in case['expected'].items():
                lang_name = {"1": "中文", "2": "英语", "3": "葡萄牙语"}[lang_id]
                
                try:
                    result = translator.render_template(case['template'], case['params'], lang_id)
                    print(f"   {lang_name}: {result}")
                    
                    # 检查是否包含所有期望的词汇
                    all_found = all(word in result for word in expected_words)
                    if all_found:
                        print(f"   ✅ {lang_name}测试通过")
                        success_count += 1
                    else:
                        missing = [word for word in expected_words if word not in result]
                        print(f"   ❌ {lang_name}测试失败，缺少: {missing}")
                        
                except Exception as e:
                    print(f"   ❌ {lang_name}异常: {e}")
        
        print(f"\n📊 复杂模板测试结果: {success_count}/{total_tests} 通过")
        
        return success_count == total_tests
        
    except Exception as e:
        print(f"❌ 复杂模板测试失败: {e}")
        return False

def test_config_validation():
    """验证配置文件的完整性"""
    print(f"\n🔧 验证配置文件的完整性")
    print("=" * 70)
    
    try:
        from config.settings import settings
        
        mappings = settings.special_params_mappings
        
        # 检查活动名映射
        activity_mappings = mappings.get("activity_name", {})
        required_activities = ["100", "200", "300", "400", "500", "18000", "19000"]
        required_languages = ["1", "2", "3"]
        
        print(f"📋 检查活动名映射:")
        missing_items = []
        
        for activity_id in required_activities:
            if activity_id not in activity_mappings:
                missing_items.append(f"活动{activity_id}")
                continue
            
            activity_langs = activity_mappings[activity_id]
            for lang_id in required_languages:
                if lang_id not in activity_langs:
                    missing_items.append(f"活动{activity_id}的语言{lang_id}")
                elif not activity_langs[lang_id].strip():
                    missing_items.append(f"活动{activity_id}的语言{lang_id}为空")
        
        if missing_items:
            print(f"❌ 缺少配置: {missing_items}")
            return False
        else:
            print(f"✅ 所有活动名映射配置完整")
        
        # 显示配置摘要
        print(f"\n📊 配置摘要:")
        print(f"   支持的活动类型: {len(required_activities)} 个")
        print(f"   支持的语言: {len(required_languages)} 种")
        print(f"   总映射条目: {len(required_activities) * len(required_languages)} 个")
        
        # 显示语言对照
        print(f"\n🌐 语言对照:")
        print(f"   1 = 中文 (Chinese)")
        print(f"   2 = 英语 (English)")
        print(f"   3 = 葡萄牙语 (Portuguese)")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 英语和葡萄牙语支持测试")
    print("=" * 80)
    
    # 1. 验证配置
    config_ok = test_config_validation()
    
    # 2. 测试活动名翻译
    activity_ok = test_activity_names() if config_ok else False
    
    # 3. 测试复杂模板
    complex_ok = test_complex_templates() if config_ok else False
    
    print(f"\n" + "=" * 80)
    print("📊 总体测试结果:")
    print(f"   🔧 配置验证: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"   🌐 活动名翻译: {'✅ 成功' if activity_ok else '❌ 失败'}")
    print(f"   🎯 复杂模板: {'✅ 成功' if complex_ok else '❌ 失败'}")
    
    if config_ok and activity_ok and complex_ok:
        print(f"\n🎉 英语和葡萄牙语支持完全正常！")
        print(f"💡 支持的功能:")
        print(f"   • 7种活动类型的完整翻译")
        print(f"   • 中文、英语、葡萄牙语三语支持")
        print(f"   • 复杂模板的多语言渲染")
        print(f"   • 配置文件驱动，易于维护")
        
        print(f"\n📋 活动类型对照:")
        activities = [
            ("100", "充值成功", "Deposit Success", "Sucesso do Depósito"),
            ("200", "提现成功", "Withdrawal Success", "Sucesso do Saque"),
            ("300", "游戏获奖", "Game Winning", "Vitória no Jogo"),
            ("400", "红包雨", "Red Envelope Rain", "Chuva de Envelopes Vermelhos"),
            ("500", "投注返利", "Betting Cashback", "Cashback de Apostas"),
            ("18000", "投注返利", "Betting Cashback", "Cashback de Apostas"),
            ("19000", "首充拉新", "First Deposit Bonus", "Bônus de Primeiro Depósito")
        ]
        
        for activity_id, cn, en, pt in activities:
            print(f"   {activity_id}: {cn} | {en} | {pt}")
    else:
        print(f"\n⚠️ 部分功能异常，请检查配置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
