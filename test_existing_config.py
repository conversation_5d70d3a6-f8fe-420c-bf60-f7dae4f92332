#!/usr/bin/env python3
"""
测试现有的通知配置
"""
import sys
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_existing_configs():
    """测试现有的通知配置"""
    print("🎯 测试现有的通知配置")
    print("=" * 50)
    
    # 测试API端点
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试不同的类型
    test_cases = [
        {
            "name": "投注返利 (18000)",
            "data": {
                "business_no": "39bac42a",
                "type": 18000,
                "params": [
                    "测试用户",
                    [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                    "感谢参与"
                ]
            }
        },
        {
            "name": "首充拉新 (19000)",
            "data": {
                "business_no": "39bac42a",
                "type": 19000,
                "params": [
                    "测试用户",
                    "19000"
                ]
            }
        },
        {
            "name": "充值成功 (100)",
            "data": {
                "business_no": "39bac42a",
                "type": 100,
                "params": [
                    "测试用户",
                    "100",
                    "USDT"
                ]
            }
        },
        {
            "name": "提现成功 (200)",
            "data": {
                "business_no": "39bac42a",
                "type": 200,
                "params": [
                    "测试用户",
                    "200",
                    "BRL"
                ]
            }
        }
    ]
    
    successful_tests = []
    
    async with aiohttp.ClientSession() as session:
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n{i}. 测试 {test_case['name']}")
            print(f"   数据: {test_case['data']}")
            
            try:
                async with session.post(api_url, json=test_case['data'], timeout=aiohttp.ClientTimeout(total=15)) as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"   状态码: {status}")
                    print(f"   响应: {text[:100]}...")
                    
                    if status == 200:
                        try:
                            import json
                            response_data = json.loads(text)
                            if response_data.get("status") == "success":
                                print(f"   ✅ 推送成功！")
                                successful_tests.append(test_case['name'])
                            else:
                                print(f"   ⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                        except json.JSONDecodeError:
                            print(f"   ⚠️ 响应格式异常")
                    else:
                        print(f"   ❌ API调用失败")
                        
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
    
    print(f"\n📊 测试结果:")
    print(f"   成功的配置: {len(successful_tests)}/{len(test_cases)}")
    for name in successful_tests:
        print(f"   ✅ {name}")
    
    return len(successful_tests) > 0

async def test_scheduled_task_with_working_type():
    """使用有效类型测试定时任务"""
    print(f"\n⏰ 使用有效类型测试定时任务")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 更新定时任务的类型为有效的类型
        tasks_collection = mongo.get_collection("c_tgScheduledPushTasks")
        
        # 查找当前的任务
        current_task = tasks_collection.find_one({"enabled": True})
        if not current_task:
            print("❌ 没有找到启用的任务")
            return False
        
        print(f"📋 当前任务:")
        print(f"   ID: {current_task.get('_id')}")
        print(f"   当前taskType: {current_task.get('taskType')}")
        
        # 临时更新为有效的类型 (18000 - 投注返利)
        print(f"\n🔄 临时更新任务类型为18000...")
        update_result = tasks_collection.update_one(
            {"_id": current_task.get('_id')},
            {"$set": {"taskType": 18000}}
        )
        
        if update_result.modified_count > 0:
            print(f"✅ 任务类型更新成功")
            
            # 测试推送
            api_url = "http://localhost:9005/api/realtime-push/template"
            test_data = {
                "business_no": current_task.get('business_no'),
                "type": 18000,
                "params": [
                    "定时任务测试",
                    [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                    "定时推送成功"
                ]
            }
            
            print(f"\n🚀 测试定时任务推送...")
            async with aiohttp.ClientSession() as session:
                async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"📊 推送结果:")
                    print(f"   状态码: {status}")
                    print(f"   响应: {text}")
                    
                    success = False
                    if status == 200:
                        try:
                            import json
                            response_data = json.loads(text)
                            if response_data.get("status") == "success":
                                print(f"🎉 定时任务推送成功！")
                                success = True
                            else:
                                print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                        except json.JSONDecodeError:
                            print(f"⚠️ 响应格式异常")
                    
                    # 恢复原来的类型
                    print(f"\n🔄 恢复原来的任务类型...")
                    tasks_collection.update_one(
                        {"_id": current_task.get('_id')},
                        {"$set": {"taskType": current_task.get('taskType')}}
                    )
                    
                    mongo.disconnect()
                    return success
        else:
            print(f"❌ 任务类型更新失败")
            mongo.disconnect()
            return False
        
    except Exception as e:
        print(f"❌ 定时任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 测试现有通知配置")
    print("=" * 60)
    
    # 1. 测试现有配置
    config_ok = await test_existing_configs()
    
    # 2. 如果有有效配置，测试定时任务
    task_ok = False
    if config_ok:
        task_ok = await test_scheduled_task_with_working_type()
    
    print(f"\n" + "=" * 60)
    print(f"📊 最终测试结果:")
    print(f"   通知配置: {'✅ 有效' if config_ok else '❌ 无效'}")
    print(f"   定时任务: {'✅ 正常' if task_ok else '❌ 异常'}")
    
    if config_ok and task_ok:
        print(f"\n🎉 定时推送功能正常！")
        print(f"💡 建议:")
        print(f"   1. 将定时任务的taskType改为18000 (投注返利)")
        print(f"   2. 或者为类型400添加正确的通知配置")
        print(f"   3. 定时任务将在00:55自动执行")
    else:
        print(f"\n⚠️ 定时推送功能需要修复")

if __name__ == "__main__":
    asyncio.run(main())
