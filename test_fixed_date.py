#!/usr/bin/env python3
"""
测试修复后的日期查询
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_fixed_date_query():
    """测试修复后的日期查询"""
    print("🔍 测试修复后的日期查询")
    print("=" * 60)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL
        await init_mysql()
        mysql_conn = get_mysql_connection()
        
        # 查询2025-07-10的数据
        business_no = "39bac42a"
        test_date = "2025-07-10"
        
        query = """
            SELECT playerId, ranking, currencyId, wagered 
            FROM ea_platform_wagered_rebate_rank_log 
            WHERE business_no = %s AND date = %s 
            ORDER BY ranking
        """
        
        print(f"查询SQL: {query}")
        print(f"参数: business_no={business_no}, date={test_date}")
        
        results = await mysql_conn.execute_query(query, (business_no, test_date))
        
        print(f"\n查询结果: {len(results)} 条记录")
        print(f"查询日期: {test_date}")
        
        if results:
            print(f"\n前5条记录:")
            for i, record in enumerate(results[:5], 1):
                ranking = record.get("ranking", 0)
                player_id = record.get("playerId", 0)
                currency_id = record.get("currencyId", 0)
                wagered = record.get("wagered", 0)
                
                print(f"  {i}. ranking={ranking}, playerId={player_id}, currencyId={currency_id}, wagered={wagered}")
                
                # 验证是否与您提供的数据一致
                if i == 1:
                    expected_player_id = 4743182
                    expected_ranking = 1
                    expected_wagered = 2092.21
                    if (player_id == expected_player_id and 
                        ranking == expected_ranking and 
                        abs(float(wagered) - expected_wagered) < 0.01):
                        print(f"     ✅ 第1条记录与您提供的数据完全一致！")
                    else:
                        print(f"     ❌ 第1条记录不一致")
                        print(f"        预期: playerId={expected_player_id}, ranking={expected_ranking}, wagered={expected_wagered}")
                        print(f"        实际: playerId={player_id}, ranking={ranking}, wagered={wagered}")
        else:
            print(f"❌ 没有查询到2025-07-10的数据")
        
        return results
        
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_handler_with_fixed_date():
    """测试修复后的处理器"""
    print(f"\n🎯 测试修复后的处理器")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        print(f"查询日期: 2025-07-10 (修复后)")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and isinstance(params[0], list) and len(params[0]) > 0:
                print(f"\n参数数据:")
                print(f"  记录数: {len(params[0])}")
                print(f"  第1条: {params[0][0] if params[0] else 'N/A'}")
                
                # 验证第1条数据
                if params[0] and len(params[0][0]) >= 4:
                    first_record = params[0][0]
                    ranking = first_record[0]
                    player_id = first_record[1]
                    currency = first_record[2]
                    wagered = first_record[3]
                    
                    print(f"  解析: 排名={ranking}, 玩家={player_id}, 货币={currency}, 投注={wagered}")
                    
                    if player_id == "4743182" and ranking == "1":
                        print(f"  ✅ 参数数据与预期一致！")
                    else:
                        print(f"  ❌ 参数数据不一致")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试修复后的日期查询")
    print("=" * 80)
    
    # 1. 测试MySQL查询
    mysql_results = await test_fixed_date_query()
    
    # 2. 测试处理器
    handler_ok = await test_handler_with_fixed_date()
    
    print(f"\n" + "=" * 80)
    print(f"📊 修复测试结果:")
    print(f"   MySQL查询: {len(mysql_results)} 条记录")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if mysql_results and len(mysql_results) > 0:
        first_record = mysql_results[0]
        print(f"\n💡 数据验证:")
        print(f"   查询到的第1条: playerId={first_record.get('playerId')}, ranking={first_record.get('ranking')}, wagered={first_record.get('wagered')}")
        print(f"   您提供的第1条: playerId=4743182, ranking=1, wagered=2092.21")
        
        if (first_record.get('playerId') == 4743182 and 
            first_record.get('ranking') == 1):
            print(f"   ✅ 数据源修复成功！")
        else:
            print(f"   ❌ 数据源仍有问题")
    
    if mysql_results and handler_ok:
        print(f"\n🎉 问题修复成功！")
        print(f"💡 修复内容:")
        print(f"   1. 查询日期从2025-07-11改为2025-07-10")
        print(f"   2. 数据源与您提供的数据一致")
        print(f"   3. 参数组装逻辑正确")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息是否正确")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
