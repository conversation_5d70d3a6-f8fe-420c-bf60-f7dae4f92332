#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 测试完全展开逻辑")
    
    translator = TranslationManager()
    
    # 测试参数展开
    print("=" * 60)
    print("1. 测试参数展开")
    
    original_params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    expanded = translator._fully_expand_params(original_params)
    print(f"原始参数: {original_params}")
    print(f"展开参数: {expanded}")
    
    expected_expanded = [
        "user_name_li",     # 参数1
        "10",               # 参数2
        "USDT",             # 参数3
        "50",               # 参数4
        "BRL",              # 参数5
        "100",              # 参数6
        "USDC",             # 参数7
        "thank_you_message" # 参数8
    ]
    
    print(f"期望展开: {expected_expanded}")
    print(f"展开正确: {expanded == expected_expanded}")
    
    # 测试完整模板渲染
    print("\n" + "=" * 60)
    print("2. 测试完整模板渲染")
    
    template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg8}"""
    
    print(f"模板: {repr(template)}")
    print(f"说明:")
    print(f"  {test1} -> 参数1: user_name_li")
    print(f"  {Currency2} -> 参数2: 10")
    print(f"  {Amount3} -> 参数3: USDT")
    print(f"  {gg8} -> 参数8: thank_you_message")
    
    result = translator.render_template(template, original_params, "2")
    
    print(f"\n实际结果:")
    print(f"{result}")
    
    print(f"\n期望结果:")
    expected = """Parabéns user_name_li
pelo depósito – 10USDT
thank_you_message"""
    print(f"{expected}")
    
    # 测试单次循环
    print("\n" + "=" * 60)
    print("3. 测试单次循环")
    
    single_params = [
        "user_name_li",
        [["10", "USDT"]],
        "thank_you_message"
    ]
    
    single_expanded = translator._fully_expand_params(single_params)
    print(f"单次循环参数: {single_params}")
    print(f"展开后: {single_expanded}")
    
    expected_single = ["user_name_li", "10", "USDT", "thank_you_message"]
    print(f"期望: {expected_single}")
    print(f"正确: {single_expanded == expected_single}")
    
    template_single = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
    
    result_single = translator.render_template(template_single, single_params, "2")
    print(f"\n单次循环结果:")
    print(f"{result_single}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
