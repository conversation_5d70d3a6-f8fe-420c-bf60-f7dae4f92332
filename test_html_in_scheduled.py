#!/usr/bin/env python3
"""
直接测试定时任务中的HTML标签
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def update_notify_config_with_html():
    """更新通知配置为HTML格式"""
    print("🔧 更新通知配置为HTML格式")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 查找notifyId=10的配置
        notify_collection = mongo.get_collection("c_tgNotify")
        config = notify_collection.find_one({"notifyId": 10, "business_no": "39bac42a"})

        if not config:
            print("❌ 未找到notifyId=10的配置")
            mongo.disconnect()
            return False
        
        print(f"📋 找到配置:")
        print(f"   notifyId: {config.get('notifyId')}")
        print(f"   原始文本: {config.get('text', '')}")
        
        # 保存原始文本
        original_text = config.get('text', '')
        
        # 创建HTML版本的文本
        html_text = f"""<p><b>🎉 HTML格式测试</b></p>
<p>这是一个包含 <i>HTML标签</i> 的定时任务消息</p>
<p>原始内容: <code>{original_text[:30]}...</code></p>
<p>测试时间: <u>现在</u></p>
<p>状态: <b>HTML功能已启用</b></p>"""
        
        print(f"\n📝 新的HTML文本:")
        print(f"{html_text}")
        
        # 更新配置
        update_result = notify_collection.update_one(
            {"notifyId": 10, "business_no": "39bac42a"},
            {"$set": {"text": html_text}}
        )
        
        if update_result.modified_count > 0:
            print(f"\n✅ 配置更新成功")
            print(f"💡 现在可以触发定时任务来测试HTML标签")
            
            # 显示恢复命令
            print(f"\n⚠️ 恢复原始文本的命令:")
            print(f"   可以手动在数据库中恢复，或者重新运行此脚本")
            
            mongo.disconnect()
            return True
        else:
            print(f"❌ 配置更新失败")
            mongo.disconnect()
            return False
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def trigger_scheduled_task():
    """触发定时任务"""
    print(f"\n🕐 触发定时任务")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        from datetime import datetime, timedelta
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 查找定时任务
        task_collection = mongo.get_collection("c_tgScheduledPushTasks")
        task = task_collection.find_one({"notifyId": 10, "business_no": "39bac42a"})

        if not task:
            print("❌ 未找到notifyId=10的定时任务")
            mongo.disconnect()
            return False
        
        print(f"📋 找到定时任务:")
        print(f"   notifyId: {task.get('notifyId')}")
        print(f"   当前时间: {task.get('scheduleTimes', [])}")
        
        # 设置为2分钟后执行
        now = datetime.now()
        future_time = now + timedelta(minutes=2)
        new_time = future_time.strftime("%H:%M")
        
        print(f"   新执行时间: {new_time}")
        
        # 更新任务时间
        update_result = task_collection.update_one(
            {"notifyId": 10, "business_no": "39bac42a"},
            {"$set": {"scheduleTimes": [new_time]}}
        )
        
        if update_result.modified_count > 0:
            print(f"✅ 任务时间更新成功")
            print(f"⏰ 任务将在 {new_time} 执行")
            print(f"💬 请观察Telegram频道中的HTML格式消息")
            
            mongo.disconnect()
            return True
        else:
            print(f"❌ 任务时间更新失败")
            mongo.disconnect()
            return False
        
    except Exception as e:
        print(f"❌ 触发失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试定时任务中的HTML标签")
    print("=" * 80)
    
    print("📋 测试步骤:")
    print("   1. 更新通知配置为HTML格式")
    print("   2. 触发定时任务")
    print("   3. 观察Telegram消息中的HTML效果")
    
    # 1. 更新配置
    config_ok = update_notify_config_with_html()
    
    if config_ok:
        # 2. 触发任务
        task_ok = trigger_scheduled_task()
        
        if task_ok:
            print(f"\n🎉 HTML测试准备完成！")
            print(f"💡 功能特点:")
            print(f"   1. 定时任务已支持HTML格式")
            print(f"   2. 消息使用parse_mode='HTML'")
            print(f"   3. 支持多种HTML标签")
            
            print(f"\n📱 预期效果:")
            print(f"   - <b>粗体文字</b> 显示为粗体")
            print(f"   - <i>斜体文字</i> 显示为斜体")
            print(f"   - <code>代码文字</code> 显示为等宽字体")
            print(f"   - <u>下划线文字</u> 显示为下划线")
            print(f"   - <p>段落</p> 正确分段")
            
            print(f"\n🚀 下一步:")
            print(f"   1. 等待定时任务执行")
            print(f"   2. 检查Telegram频道消息")
            print(f"   3. 确认HTML标签正确渲染")
        else:
            print(f"\n⚠️ 任务触发失败")
    else:
        print(f"\n⚠️ 配置更新失败")

if __name__ == "__main__":
    main()
