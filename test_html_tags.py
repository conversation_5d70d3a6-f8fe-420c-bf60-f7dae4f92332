#!/usr/bin/env python3
"""
测试HTML标签功能
"""
import sys
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_html_tags_in_template():
    """测试模板中的HTML标签"""
    print("🔧 测试HTML标签功能")
    print("=" * 60)
    
    try:
        # 测试API地址
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        # 测试数据 - 包含HTML标签
        test_cases = [
            {
                "name": "基础HTML标签测试",
                "data": {
                    "business_no": "39bac42a",
                    "type": 400,
                    "params": [
                        "<p>活动参与提醒-红包雨-PTR</p>",
                        "400"
                    ]
                },
                "description": "测试<p>标签"
            },
            {
                "name": "粗体和斜体测试",
                "data": {
                    "business_no": "39bac42a",
                    "type": 400,
                    "params": [
                        "<b>恭喜您获得奖励！</b>\n<i>活动类型：红包雨</i>",
                        "400"
                    ]
                },
                "description": "测试<b>和<i>标签"
            },
            {
                "name": "链接测试",
                "data": {
                    "business_no": "39bac42a",
                    "type": 400,
                    "params": [
                        "点击 <a href='https://example.com'>这里</a> 查看详情",
                        "400"
                    ]
                },
                "description": "测试<a>链接标签"
            },
            {
                "name": "代码和预格式化测试",
                "data": {
                    "business_no": "39bac42a",
                    "type": 400,
                    "params": [
                        "您的验证码是：<code>123456</code>\n<pre>请在5分钟内使用</pre>",
                        "400"
                    ]
                },
                "description": "测试<code>和<pre>标签"
            },
            {
                "name": "组合HTML标签测试",
                "data": {
                    "business_no": "39bac42a",
                    "type": 400,
                    "params": [
                        "<p><b>🎉 活动通知</b></p>\n<p>活动名称：<i>红包雨活动</i></p>\n<p>奖励金额：<code>100 BRL</code></p>\n<p>查看详情：<a href='https://example.com'>点击这里</a></p>",
                        "400"
                    ]
                },
                "description": "测试多种HTML标签组合"
            }
        ]
        
        print(f"📋 准备测试 {len(test_cases)} 个HTML标签用例")
        
        success_count = 0
        
        async with aiohttp.ClientSession() as session:
            for i, test_case in enumerate(test_cases, 1):
                print(f"\n{i}. {test_case['name']}")
                print(f"   描述: {test_case['description']}")
                print(f"   数据: {test_case['data']}")
                
                try:
                    async with session.post(
                        api_url, 
                        json=test_case['data'], 
                        timeout=aiohttp.ClientTimeout(total=30)
                    ) as response:
                        status = response.status
                        text = await response.text()
                        
                        print(f"   状态码: {status}")
                        
                        if status == 200:
                            try:
                                import json
                                response_data = json.loads(text)
                                if response_data.get("status") == "success":
                                    print(f"   ✅ 推送成功")
                                    success_count += 1
                                else:
                                    print(f"   ❌ 推送失败: {response_data.get('message', '未知错误')}")
                            except json.JSONDecodeError:
                                print(f"   ⚠️ 响应格式异常: {text}")
                        else:
                            print(f"   ❌ HTTP错误: {text}")
                
                except Exception as e:
                    print(f"   ❌ 请求异常: {e}")
                
                # 等待一下避免频繁请求
                await asyncio.sleep(2)
        
        print(f"\n📊 测试结果:")
        print(f"   成功: {success_count}/{len(test_cases)}")
        print(f"   成功率: {success_count/len(test_cases)*100:.1f}%")
        
        if success_count > 0:
            print(f"\n🎉 HTML标签功能测试成功！")
            print(f"💡 支持的HTML标签:")
            print(f"   - <p>段落标签</p>")
            print(f"   - <b>粗体标签</b>")
            print(f"   - <i>斜体标签</i>")
            print(f"   - <code>代码标签</code>")
            print(f"   - <pre>预格式化标签</pre>")
            print(f"   - <a href='url'>链接标签</a>")
            
            print(f"\n📱 请检查Telegram频道中的消息格式")
            print(f"   应该能看到HTML标签被正确渲染")
        else:
            print(f"\n⚠️ 所有测试都失败了")
        
        return success_count > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_html_in_scheduled_task():
    """测试定时任务中的HTML标签"""
    print(f"\n🕐 测试定时任务中的HTML标签")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 查找一个测试用的通知配置
        notify_collection = mongo.get_collection("c_tgNotify")
        notify_config = notify_collection.find_one({"notifyId": 8})  # 使用notifyId=8
        
        if not notify_config:
            print("❌ 未找到测试用的通知配置")
            mongo.disconnect()
            return False
        
        print(f"📋 找到通知配置:")
        print(f"   notifyId: {notify_config.get('notifyId')}")
        print(f"   原始文本: {notify_config.get('text', '')}")
        
        # 临时修改文本为包含HTML标签的版本
        html_text = "<p><b>🎉 定时任务HTML测试</b></p>\n<p>这是一个包含 <i>HTML标签</i> 的定时任务消息</p>\n<p>代码示例：<code>HTML_TEST_SUCCESS</code></p>"
        
        # 更新通知配置
        update_result = notify_collection.update_one(
            {"notifyId": 8},
            {"$set": {"text": html_text}}
        )
        
        if update_result.modified_count > 0:
            print(f"✅ 临时更新通知配置成功")
            print(f"   新文本: {html_text}")
            
            print(f"\n💡 现在可以触发定时任务来测试HTML标签")
            print(f"   或者等待下次定时执行")
            
            # 询问是否恢复原始文本
            print(f"\n⚠️ 注意：这会修改数据库中的配置")
            print(f"   建议测试完成后恢复原始文本")
            
            mongo.disconnect()
            return True
        else:
            print(f"❌ 更新通知配置失败")
            mongo.disconnect()
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试HTML标签功能")
    print("=" * 80)
    
    print("📋 测试说明:")
    print("   1. 即时推送API已修改为支持HTML格式")
    print("   2. 定时任务也已修改为支持HTML格式")
    print("   3. 现在测试各种HTML标签的效果")
    
    # 1. 测试即时推送中的HTML标签
    instant_ok = await test_html_tags_in_template()
    
    # 2. 询问是否测试定时任务
    if instant_ok:
        print(f"\n🤔 是否要测试定时任务中的HTML标签？")
        print(f"   这会临时修改数据库中的通知配置")
        
        try:
            choice = input("输入 'y' 继续，其他键跳过: ").strip().lower()
            if choice == 'y':
                scheduled_ok = await test_html_in_scheduled_task()
            else:
                scheduled_ok = True
                print("⏭️ 跳过定时任务测试")
        except:
            scheduled_ok = True
            print("⏭️ 跳过定时任务测试")
    else:
        scheduled_ok = False
    
    print(f"\n" + "=" * 80)
    print(f"📊 HTML标签测试结果:")
    print(f"   即时推送: {'✅ 成功' if instant_ok else '❌ 失败'}")
    print(f"   定时任务: {'✅ 准备就绪' if scheduled_ok else '❌ 失败'}")
    
    if instant_ok:
        print(f"\n🎉 HTML标签功能已成功启用！")
        print(f"💡 现在支持的功能:")
        print(f"   1. 即时推送API支持HTML标签")
        print(f"   2. 定时任务支持HTML标签")
        print(f"   3. 所有消息都使用parse_mode='HTML'")
        
        print(f"\n📱 Telegram支持的HTML标签:")
        print(f"   - <b>粗体</b> 或 <strong>粗体</strong>")
        print(f"   - <i>斜体</i> 或 <em>斜体</em>")
        print(f"   - <u>下划线</u>")
        print(f"   - <s>删除线</s> 或 <del>删除线</del>")
        print(f"   - <code>等宽字体</code>")
        print(f"   - <pre>预格式化文本</pre>")
        print(f"   - <a href='url'>链接</a>")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 在模板中使用HTML标签")
        print(f"   2. 测试不同标签的显示效果")
        print(f"   3. 观察Telegram中的消息格式")
    else:
        print(f"\n⚠️ 测试失败，请检查服务状态")

if __name__ == "__main__":
    asyncio.run(main())
