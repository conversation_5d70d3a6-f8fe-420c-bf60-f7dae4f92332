#!/usr/bin/env python3
"""
测试int类型的type支持
验证API接口支持int和str类型的type参数
"""
import requests
import json


def test_int_type_support():
    """测试int类型type支持"""
    print("🔢 测试int类型type支持")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 测试用例
    test_cases = [
        {
            "name": "int类型 - 18000",
            "data": {
                "business_no": "39bac42a",
                "type": 18000,  # int类型
                "params": ["user_name_zhang"]
            }
        },
        {
            "name": "str类型 - '18000'",
            "data": {
                "business_no": "39bac42a",
                "type": "18000",  # str类型
                "params": ["user_name_zhang"]
            }
        },
        {
            "name": "int类型 - 19000",
            "data": {
                "business_no": "39bac42a",
                "type": 19000,  # int类型
                "params": ["user_name_li"]
            }
        },
        {
            "name": "str类型 - 'chat'",
            "data": {
                "business_no": "39bac42a",
                "type": "chat",  # str类型（映射到18000）
                "params": ["user_name_zhang"]
            }
        },
        {
            "name": "参数不足测试",
            "data": {
                "business_no": "39bac42a",
                "type": 18000,
                "params": ["ttttt"]  # 只有1个参数
            }
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {case['name']}")
        print(f"   数据: {json.dumps(case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            # 测试预览接口
            response = requests.post(
                f"{base_url}/api/realtime-push/template/preview",
                json=case['data'],
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    print("   ✅ 预览成功")
                    preview = result.get("data", {})
                    print(f"   模板ID: {preview.get('template_id')}")
                    print(f"   模板名称: {preview.get('template_name')}")
                    print(f"   语言: {preview.get('language')}")
                    print(f"   渲染结果: {preview.get('rendered_message')}")
                else:
                    print(f"   ❌ 预览失败: {result.get('message')}")
                    all_passed = False
            else:
                print(f"   ❌ 请求失败: {response.status_code}")
                print(f"   错误: {response.text}")
                all_passed = False
                
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            all_passed = False
    
    return all_passed


def test_type_matching_logic():
    """测试type匹配逻辑"""
    print(f"\n🎯 测试type匹配逻辑")
    print("=" * 50)
    
    # 直接测试模板消息处理器
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from pusher.template_message_handler import template_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置
        import asyncio
        
        async def test_logic():
            await config_manager.initialize()
            
            # 测试不同类型的type
            test_types = [
                18000,      # int类型
                "18000",    # str数字
                19000,      # int类型
                "19000",    # str数字
                "chat",     # str文本
                99999       # 不存在的type
            ]
            
            for test_type in test_types:
                print(f"\n🔍 测试type: {test_type} (类型: {type(test_type).__name__})")
                
                config = template_handler._find_notify_config_by_type(test_type)
                if config:
                    print(f"   ✅ 找到配置: ID={config.get('id')}, 名称='{config.get('tgName')}'")
                    print(f"   types={config.get('types')}")
                else:
                    print(f"   ❌ 未找到配置")
        
        asyncio.run(test_logic())
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_api_health():
    """测试API健康状态"""
    print(f"\n💚 测试API健康状态")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常")
            return True
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False


def main():
    """主函数"""
    print("🚀 int类型type支持测试")
    print("=" * 60)
    
    # 1. 测试API健康状态
    health_ok = test_api_health()
    
    # 2. 测试type匹配逻辑
    logic_ok = test_type_matching_logic()
    
    # 3. 测试int类型支持
    int_ok = test_int_type_support() if health_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   💚 API健康: {'✅ 正常' if health_ok else '❌ 异常'}")
    print(f"   🎯 匹配逻辑: {'✅ 正常' if logic_ok else '❌ 异常'}")
    print(f"   🔢 int类型: {'✅ 支持' if int_ok else '❌ 不支持'}")
    
    if health_ok and logic_ok and int_ok:
        print(f"\n🎉 int类型type支持测试通过！")
        print(f"💡 支持的type格式:")
        print(f"   • int类型: 18000, 19000")
        print(f"   • str数字: '18000', '19000'")
        print(f"   • str文本: 'chat', 'deposit' (映射到对应数字)")
        print(f"   • 与MongoDB types字段完全匹配")
    else:
        print(f"\n⚠️ 部分测试失败，请检查配置")
    
    print("=" * 60)
    
    return 0 if (health_ok and logic_ok and int_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
