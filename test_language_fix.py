#!/usr/bin/env python3
"""
测试语言映射修复
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager

def test_language_mapping():
    """测试语言映射"""
    print("🌐 测试语言映射修复")
    print("=" * 50)
    
    # 加载翻译配置
    success = translation_manager.load_translations()
    print(f"📋 翻译配置加载: {'✅ 成功' if success else '❌ 失败'}")
    
    if not success:
        return False
    
    print(f"📊 语言映射数量: {len(translation_manager.language_mapping)}")
    print(f"📊 默认语言: {translation_manager.default_language}")
    
    # 显示语言映射
    print(f"\n📋 语言映射:")
    for lang_id, lang_name in translation_manager.language_mapping.items():
        print(f"   {lang_id}: {lang_name}")
    
    # 测试不同类型的语言ID
    test_cases = [
        {"input": 1, "desc": "int类型1"},
        {"input": 2, "desc": "int类型2"}, 
        {"input": "1", "desc": "str类型'1'"},
        {"input": "2", "desc": "str类型'2'"},
        {"input": 99, "desc": "不存在的int类型99"},
        {"input": "99", "desc": "不存在的str类型'99'"},
    ]
    
    print(f"\n🔍 测试语言ID转换:")
    all_passed = True
    
    for case in test_cases:
        input_val = case["input"]
        desc = case["desc"]
        
        result = translation_manager.get_language_id(input_val)
        lang_name = translation_manager.language_mapping.get(result, f"Unknown({result})")
        
        print(f"   {desc}: {input_val} -> {result} -> {lang_name}")
        
        # 验证结果
        if input_val in [1, "1"]:
            expected = "English"
        elif input_val in [2, "2"]:
            expected = "中文"
        else:
            expected = "English"  # 默认语言
        
        if lang_name == expected:
            print(f"      ✅ 正确")
        else:
            print(f"      ❌ 错误，期望: {expected}")
            all_passed = False
    
    return all_passed

def test_template_rendering():
    """测试模板渲染"""
    print(f"\n🎨 测试模板渲染")
    print("=" * 50)
    
    test_cases = [
        {
            "template": "test",
            "params": ["user_name_zhang"],
            "language": 2,  # int类型，应该对应中文
            "desc": "无占位符模板，int语言ID"
        },
        {
            "template": "用户 {arg1} 获得奖励",
            "params": ["user_name_zhang"],
            "language": 2,  # int类型，应该对应中文
            "desc": "有占位符模板，int语言ID"
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {case['desc']}")
        print(f"   模板: '{case['template']}'")
        print(f"   参数: {case['params']}")
        print(f"   语言: {case['language']} (类型: {type(case['language']).__name__})")
        
        # 转换语言ID
        language_id_str = translation_manager.get_language_id(case['language'])
        language_name = translation_manager.language_mapping.get(language_id_str, f"Unknown({language_id_str})")
        print(f"   转换后语言: {language_id_str} -> {language_name}")
        
        # 渲染模板
        result = translation_manager.render_template(
            case['template'], 
            case['params'], 
            language_id_str
        )
        
        print(f"   渲染结果: '{result}'")
        
        # 验证结果
        if case['template'] == "test":
            expected = "test"
        elif case['template'] == "用户 {arg1} 获得奖励":
            expected = "用户 张三 获得奖励"
        else:
            expected = case['template']
        
        if result == expected:
            print(f"   ✅ 正确")
        else:
            print(f"   ❌ 错误，期望: '{expected}'")
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🚀 语言映射修复测试")
    print("=" * 60)
    
    # 1. 测试语言映射
    mapping_ok = test_language_mapping()
    
    # 2. 测试模板渲染
    render_ok = test_template_rendering()
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   🌐 语言映射: {'✅ 通过' if mapping_ok else '❌ 失败'}")
    print(f"   🎨 模板渲染: {'✅ 通过' if render_ok else '❌ 失败'}")
    
    if mapping_ok and render_ok:
        print(f"\n🎉 语言映射修复成功！")
        print(f"💡 关键改进:")
        print(f"   • 支持int和str类型的语言ID")
        print(f"   • 统一转换为字符串处理")
        print(f"   • 语言ID 2 正确对应中文")
        print(f"   • 类型转换透明化")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步检查")
    
    print("=" * 60)
    
    return 0 if (mapping_ok and render_ok) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
