#!/usr/bin/env python3
"""
测试语言匹配
"""
from config.logging_config import app_logger
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_language_match():
    """测试语言匹配"""
    app_logger.info("🔧 测试语言匹配")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "1"  # 改为字符串"1"，匹配数据库中的language=1
        
        app_logger.info(f"📋 测试模板: {template}")
        app_logger.info(f"📋 语言: {language_id}")
        
        # 测试用例：activity_id=1, language_id="1"
        app_logger.info(f"\n🔍 测试activity_id=1, language_id='1'的情况:")
        params = ["xxx", "ttttt", "100", "BRL"]
        activity_id = 1
        message_type = 19000
        
        app_logger.info(f"   参数: {params}")
        app_logger.info(f"   activity_id: {activity_id}")
        app_logger.info(f"   language_id: '{language_id}'")
        app_logger.info(f"   期望: 匹配language=1，返回'充值排行'")
        
        app_logger.info(f"\n📋 开始渲染，查看详细匹配日志:")
        app_logger.info("=" * 50)
        
        result = translation_manager.render_template(
            template, params, language_id, 
            activity_id=activity_id, message_type=message_type
        )
        
        app_logger.info("=" * 50)
        app_logger.info(f"📊 最终结果: {result}")
        
        # 分析结果
        if "充值排行" in result:
            app_logger.info(f"✅ 成功：显示了期望的'充值排行'")
            return True
        elif "xxx" not in result:
            app_logger.info(f"⚠️ 参数被替换了，但不是期望的结果")
            return False
        else:
            app_logger.info(f"❌ 参数没有被替换")
            return False
        
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    app_logger.info("🔧 语言匹配调试测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 调试目标:")
    app_logger.info("   1. 查看详细的语言匹配过程")
    app_logger.info("   2. 确认为什么language=1没有匹配上")
    app_logger.info("   3. 期望看到'充值排行'而不是其他结果")
    
    success = test_language_match()
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📋 请重点关注日志中的:")
    app_logger.info(f"   1. 🔍 语言匹配调试:")
    app_logger.info(f"   2. 目标language_id和数据item_language的类型和值")
    app_logger.info(f"   3. str比较和int比较的结果")
    app_logger.info(f"   4. 最终是否找到匹配")
    
    if success:
        app_logger.info(f"\n🎉 语言匹配成功！")
    else:
        app_logger.info(f"\n⚠️ 语言匹配有问题，请根据日志调试")

if __name__ == "__main__":
    main()
