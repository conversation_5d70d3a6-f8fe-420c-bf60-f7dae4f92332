#!/usr/bin/env python3
"""
测试修复后的逻辑（不依赖服务器）
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import asyncio

async def test_config_lookup_logic():
    """测试配置查找逻辑"""
    print("🔍 测试修复后的配置查找逻辑")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        from pusher.template_message_handler import TemplateMessageHandler
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建处理器实例
        handler = TemplateMessageHandler()
        
        # 测试不同的type值
        test_types = [100, 18000, 19000, 999]
        
        for test_type in test_types:
            print(f"\n📋 测试type={test_type}:")
            
            try:
                config = handler._find_notify_config_by_type(test_type)
                
                if config:
                    print(f"   ✅ 找到配置:")
                    print(f"      ID: {config.get('id')}")
                    print(f"      名称: {config.get('tgName')}")
                    print(f"      类型: {config.get('types')}")
                    print(f"      通知类型: {config.get('notifyType')}")
                else:
                    print(f"   ❌ 未找到配置")
                    
            except Exception as e:
                print(f"   ❌ 查找异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_template_processing():
    """测试模板处理逻辑"""
    print(f"\n🎯 测试模板处理逻辑")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        from pusher.template_message_handler import TemplateMessageHandler
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建处理器实例
        handler = TemplateMessageHandler()
        
        # 测试type=100的完整处理流程
        print(f"📋 测试type=100的完整处理:")
        
        # 1. 查找配置
        config = handler._find_notify_config_by_type(100)
        if not config:
            print(f"   ❌ 未找到type=100的配置")
            return False
        
        print(f"   ✅ 找到配置: {config.get('tgName')}")
        
        # 2. 检查模板
        template = config.get("template", "")
        if not template:
            print(f"   ❌ 配置中没有模板")
            return False
        
        print(f"   ✅ 模板: {template[:100]}...")
        
        # 3. 检查语言
        language = config.get("language", "")
        print(f"   📋 语言: {language}")
        
        # 4. 检查聊天ID
        chat_ids = config.get("chatIds", [])
        print(f"   📋 聊天ID: {chat_ids}")
        
        if not chat_ids:
            print(f"   ⚠️ 没有配置聊天ID，无法发送消息")
        
        print(f"   ✅ 配置检查完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🚀 修复逻辑测试")
    print("=" * 60)
    
    # 1. 测试配置查找
    lookup_ok = await test_config_lookup_logic()
    
    # 2. 测试模板处理
    template_ok = await test_template_processing() if lookup_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   🔍 配置查找: {'✅ 成功' if lookup_ok else '❌ 失败'}")
    print(f"   🎯 模板处理: {'✅ 成功' if template_ok else '❌ 失败'}")
    
    if lookup_ok and template_ok:
        print(f"\n🎉 修复成功！")
        print(f"💡 修复内容:")
        print(f"   • 移除了notifyType=1的限制")
        print(f"   • 现在可以查找所有notify配置")
        print(f"   • type=100配置可以正常找到")
        print(f"   • 消除了type_mapping作用域错误")
        print(f"   • API参数简化为int类型")
        
        print(f"\n📋 下一步:")
        print(f"   • 确保服务器正常启动")
        print(f"   • 测试实际的API调用")
        print(f"   • 验证消息发送功能")
    else:
        print(f"\n⚠️ 部分功能异常，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
