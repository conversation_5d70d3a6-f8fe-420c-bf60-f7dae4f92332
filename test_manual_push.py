#!/usr/bin/env python3
"""
手动触发定时推送测试
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_manual_push():
    """手动触发推送测试"""
    print("🎯 手动触发定时推送测试")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        from scheduler.task_executor import TaskExecutor
        
        # 1. 连接数据库获取任务
        print("1. 获取测试任务")
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks = list(collection.find({"enabled": True}).limit(3))
        
        if not tasks:
            print("❌ 没有找到启用的任务")
            return False
        
        print(f"✅ 找到 {len(tasks)} 个启用的任务")
        
        # 2. 显示任务列表
        print(f"\n2. 可用任务列表:")
        for i, task in enumerate(tasks, 1):
            print(f"   {i}. 任务ID: {task.get('_id')}")
            print(f"      notifyId: {task.get('notifyId')}")
            print(f"      business_no: {task.get('business_no')}")
            print(f"      taskType: {task.get('taskType')}")
            print(f"      sendFrequency: {task.get('sendFrequency')}")
            print(f"      scheduleDays: {task.get('scheduleDays')}")
            print(f"      scheduleTimes: {task.get('scheduleTimes', [])[:3]}...")
            print()
        
        # 3. 选择任务进行测试
        print(f"3. 选择要测试的任务")
        try:
            choice = int(input(f"请选择任务 (1-{len(tasks)}): ").strip())
            if choice < 1 or choice > len(tasks):
                print("❌ 无效选择")
                return False
        except ValueError:
            print("❌ 请输入有效数字")
            return False
        
        selected_task = tasks[choice - 1]
        task_id = selected_task.get('_id')
        
        print(f"✅ 选择了任务: {task_id}")
        
        # 4. 创建执行器并执行任务
        print(f"\n4. 执行任务")
        executor = TaskExecutor()
        
        print(f"🚀 开始执行任务 {task_id}...")
        print(f"📅 执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行任务
        start_time = datetime.now()
        result = await executor.execute_task(selected_task)
        end_time = datetime.now()
        
        execution_time = (end_time - start_time).total_seconds()
        
        print(f"\n📊 执行结果:")
        print(f"   状态: {'✅ 成功' if result else '❌ 失败'}")
        print(f"   耗时: {execution_time:.2f} 秒")
        print(f"   开始时间: {start_time.strftime('%H:%M:%S')}")
        print(f"   结束时间: {end_time.strftime('%H:%M:%S')}")
        
        # 5. 显示任务详情
        print(f"\n5. 任务详情:")
        print(f"   任务ID: {task_id}")
        print(f"   通知ID: {selected_task.get('notifyId')}")
        print(f"   商户号: {selected_task.get('business_no')}")
        print(f"   任务类型: {selected_task.get('taskType')}")
        print(f"   目标频道: {selected_task.get('channelId')}")
        print(f"   消息文本: {selected_task.get('messageText', '')[:100]}...")
        
        mongo.disconnect()
        return result
        
    except Exception as e:
        print(f"❌ 手动推送测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_push_api():
    """测试推送API"""
    print(f"\n🌐 测试推送API")
    print("=" * 50)
    
    try:
        import aiohttp
        
        # 测试API端点
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        # 测试数据
        test_data = {
            "business_no": "39bac42a",
            "type": 19000,
            "params": [
                "tttttt",
                [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                "yyyyyy"
            ]
        }
        
        print(f"📡 API地址: {api_url}")
        print(f"📋 测试数据: {test_data}")
        
        async with aiohttp.ClientSession() as session:
            print(f"\n🚀 发送API请求...")
            
            async with session.post(api_url, json=test_data) as response:
                status = response.status
                text = await response.text()
                
                print(f"\n📊 API响应:")
                print(f"   状态码: {status}")
                print(f"   响应内容: {text}")
                
                return status == 200
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 定时推送功能测试")
    print("=" * 60)
    
    print("测试选项:")
    print("1. 手动触发定时任务")
    print("2. 测试推送API")
    print("3. 两个都测试")
    print("4. 退出")
    
    try:
        choice = input("\n请选择测试选项 (1-4): ").strip()
        
        if choice == "1":
            success = await test_manual_push()
        elif choice == "2":
            success = await test_push_api()
        elif choice == "3":
            print("🔄 执行完整测试...")
            success1 = await test_manual_push()
            success2 = await test_push_api()
            success = success1 and success2
        elif choice == "4":
            print("👋 退出测试")
            return
        else:
            print("❌ 无效选择")
            return
        
        print(f"\n" + "=" * 60)
        if success:
            print(f"🎉 测试完成 - 定时推送功能正常")
        else:
            print(f"⚠️ 测试失败 - 定时推送功能异常")
            
        print(f"\n💡 下一步建议:")
        print(f"   1. 检查Telegram Bot Token是否有效")
        print(f"   2. 确认目标频道ID是否正确")
        print(f"   3. 查看日志文件获取详细错误信息")
        print(f"   4. 在 00:55 时观察是否有自动推送")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
