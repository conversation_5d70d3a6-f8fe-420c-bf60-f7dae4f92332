#!/usr/bin/env python3
"""
测试Markdown解析问题
"""

def test_private_chat_text():
    """测试私聊引导文本"""
    user_first_name = "<PERSON>"
    bot_username = "ryanbobo321_bot"
    
    # 当前的文本格式
    guide_text = f"""
💬 **开始私聊**

👋 你好 {user_first_name}！

🎯 **私聊优势**
• 🔐 更私密的交流环境
• 📱 个性化服务体验
• 🚀 更快的响应速度
• 🎨 专属的私聊菜单

📱 **开始私聊的方法**
• 点击下方按钮直接开始私聊
• 手动搜索 @{bot_username}
• 复制链接使用

💡 **提示**: 私聊中发送 /start 可以看到专属的私聊菜单！
        """
    
    print("=== 当前文本内容 ===")
    print(repr(guide_text))
    print("\n=== 实际显示内容 ===")
    print(guide_text)
    print(f"\n=== 文本长度: {len(guide_text)} ===")
    
    # 检查可能的问题字符
    print("\n=== 检查特殊字符 ===")
    for i, char in enumerate(guide_text):
        if char in ['*', '_', '`', '[', ']', '(', ')', '@']:
            print(f"位置 {i}: '{char}'")
    
    return guide_text


def test_user_info_text():
    """测试用户信息文本"""
    user_info = {
        'user_id': 5535930411,
        'first_name': 'Ryan',
        'last_name': None,
        'username': 'ryan_test',
        'language_code': 'zh',
        'is_bot': False,
        'is_premium': None,
    }
    
    chat_info = {
        'chat_id': -1002316158105,
        'chat_type': 'supergroup',
        'chat_title': '测试群组',
    }
    
    bot_username = "ryanbobo321_bot"
    update_id = 123456
    message_id = 789
    
    # 当前的文本格式
    info_text = f"""
👤 **用户信息详情**

🆔 **基本信息**
• 用户ID: {user_info['user_id']}
• 姓名: {user_info['first_name']} {user_info['last_name'] or ''}
• 用户名: @{user_info['username'] or '未设置'}
• 语言: {user_info['language_code'] or '未知'}
• 机器人: {'是' if user_info['is_bot'] else '否'}
• 高级用户: {'是' if user_info['is_premium'] else '否' if user_info['is_premium'] is not None else '未知'}

💬 **聊天信息**
• 聊天ID: {chat_info['chat_id']}
• 聊天类型: {chat_info['chat_type']}
• 群组名称: {chat_info['chat_title'] or '私聊'}

⏰ **获取时间**: {bot_username or 'Bot'} 于刚刚获取

📊 **技术信息**
• Update ID: {update_id}
• Message ID: {message_id or '无'}
        """
    
    print("=== 用户信息文本内容 ===")
    print(repr(info_text))
    print("\n=== 实际显示内容 ===")
    print(info_text)
    print(f"\n=== 文本长度: {len(info_text)} ===")
    
    return info_text


def test_simple_working_format():
    """测试项目中正常工作的格式"""
    # 参考 handle_private_chat 的格式
    chat_text = """
💬 **智能问答助手**

🤖 **AI能力**
• 自然语言理解
• 智能对话交流
• 知识问答解答
• 多轮对话记忆

🎯 **专业领域**
• 技术问题解答
• 生活常识咨询
• 学习辅导支持
• 工作效率提升
    """
    
    print("=== 正常工作的格式 ===")
    print(repr(chat_text))
    print("\n=== 实际显示内容 ===")
    print(chat_text)
    
    return chat_text


def main():
    """主函数"""
    print("🔍 Markdown解析问题测试")
    print("=" * 60)
    
    # 测试1: 私聊引导文本
    print("\n1. 测试私聊引导文本")
    guide_text = test_private_chat_text()
    
    # 测试2: 用户信息文本
    print("\n" + "=" * 60)
    print("\n2. 测试用户信息文本")
    user_text = test_user_info_text()
    
    # 测试3: 正常工作的格式
    print("\n" + "=" * 60)
    print("\n3. 测试正常工作的格式")
    working_text = test_simple_working_format()
    
    # 分析问题
    print("\n" + "=" * 60)
    print("\n🔍 问题分析:")
    print("1. 检查是否有未闭合的Markdown标记")
    print("2. 检查是否有特殊字符冲突")
    print("3. 检查字符串格式是否正确")
    
    # 提供修复建议
    print("\n💡 修复建议:")
    print("1. 移除所有Markdown标记，使用纯文本")
    print("2. 或者使用HTML格式代替Markdown")
    print("3. 或者使用项目中已验证的格式")


if __name__ == "__main__":
    main()
