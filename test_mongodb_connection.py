#!/usr/bin/env python3
"""
MongoDB连接测试脚本
测试MongoDB连接是否正常，并显示数据库信息
"""
import asyncio
import sys
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from common.utils import setup_logging
from database.mongodb_connection import MongoDBConnection, mongodb_connection

import logging
logger = logging.getLogger(__name__)


def print_separator(title: str = ""):
    """打印分隔线"""
    if title:
        print(f"\n{'='*20} {title} {'='*20}")
    else:
        print("="*60)


def print_config_info():
    """打印配置信息"""
    print_separator("MongoDB配置信息")
    
    print(f"📍 主机地址: {settings.mongodb_host}")
    print(f"🔌 端口: {settings.mongodb_port}")
    print(f"👤 用户名: {settings.mongodb_username}")
    print(f"🔐 密码: {'*' * len(settings.mongodb_password) if settings.mongodb_password else '未设置'}")
    print(f"🗄️ 数据库: {settings.mongodb_database}")
    print(f"🔑 认证源: {settings.mongodb_auth_source}")
    print(f"🔗 连接字符串: {settings.mongodb_connection_string[:50]}...")
    
    print(f"\n⚙️ 连接选项:")
    options = settings.mongodb_options
    for key, value in options.items():
        print(f"   • {key}: {value}")


def test_basic_connection():
    """测试基本连接"""
    print_separator("基本连接测试")
    
    try:
        with MongoDBConnection() as mongo:
            if mongo.is_connected():
                print("✅ MongoDB连接成功！")
                return True
            else:
                print("❌ MongoDB连接失败！")
                return False
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False


def test_detailed_connection():
    """测试详细连接信息"""
    print_separator("详细连接测试")
    
    try:
        mongo = MongoDBConnection()
        result = mongo.test_connection()
        
        if result["success"]:
            print("✅ 详细连接测试成功！")
            
            # 服务器信息
            if result["server_info"]:
                print(f"\n🖥️ 服务器信息:")
                server_info = result["server_info"]
                print(f"   • MongoDB版本: {server_info.get('version', 'Unknown')}")
                print(f"   • Git版本: {server_info.get('git_version', 'Unknown')}")
                print(f"   • 平台: {server_info.get('platform', 'Unknown')}")
            
            # 数据库信息
            if result["database_info"]:
                print(f"\n🗄️ 数据库信息:")
                db_info = result["database_info"]
                print(f"   • 数据库名: {db_info.get('name', 'Unknown')}")
                print(f"   • 集合数量: {db_info.get('collections', 0)}")
                print(f"   • 数据大小: {db_info.get('data_size', 0)} bytes")
                print(f"   • 存储大小: {db_info.get('storage_size', 0)} bytes")
            
            # 集合列表
            if result["collections"]:
                print(f"\n📋 集合列表 ({len(result['collections'])} 个):")
                for i, collection in enumerate(result["collections"], 1):
                    print(f"   {i}. {collection}")
            else:
                print(f"\n📋 集合列表: 无集合")
            
            return True
        else:
            print(f"❌ 详细连接测试失败: {result['error']}")
            return False
            
    except Exception as e:
        print(f"❌ 详细连接测试异常: {e}")
        return False


def test_database_operations():
    """测试数据库操作"""
    print_separator("数据库操作测试")
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ 无法连接到数据库")
            return False
        
        # 获取测试集合
        test_collection = mongo.get_collection("test_connection")
        if test_collection is None:
            print("❌ 无法获取测试集合")
            return False
        
        # 测试文档
        test_doc = {
            "test_id": "connection_test",
            "timestamp": datetime.now().isoformat(),
            "message": "MongoDB连接测试",
            "from": "telegram_bot"
        }
        
        print("📝 测试写入操作...")
        # 插入测试文档
        result = test_collection.insert_one(test_doc)
        print(f"✅ 文档插入成功，ID: {result.inserted_id}")
        
        print("📖 测试读取操作...")
        # 查询测试文档
        found_doc = test_collection.find_one({"test_id": "connection_test"})
        if found_doc:
            print(f"✅ 文档查询成功: {found_doc['message']}")
        else:
            print("❌ 文档查询失败")
            return False
        
        print("🗑️ 测试删除操作...")
        # 删除测试文档
        delete_result = test_collection.delete_one({"test_id": "connection_test"})
        print(f"✅ 文档删除成功，删除数量: {delete_result.deleted_count}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 数据库操作测试异常: {e}")
        return False


def test_collection_access():
    """测试集合访问"""
    print_separator("集合访问测试")
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ 无法连接到数据库")
            return False
        
        database = mongo.get_database()
        if database is None:
            print("❌ 无法获取数据库")
            return False
        
        # 获取所有集合
        collections = database.list_collection_names()
        print(f"📋 发现 {len(collections)} 个集合")
        
        # 测试访问每个集合
        for collection_name in collections[:5]:  # 只测试前5个集合
            try:
                collection = database[collection_name]
                count = collection.count_documents({})
                print(f"   • {collection_name}: {count} 个文档")
            except Exception as e:
                print(f"   • {collection_name}: 访问失败 - {e}")
        
        if len(collections) > 5:
            print(f"   ... 还有 {len(collections) - 5} 个集合未显示")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 集合访问测试异常: {e}")
        return False


def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    print("🚀 MongoDB连接测试开始")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 打印配置信息
    print_config_info()
    
    # 测试结果
    test_results = []
    
    # 1. 基本连接测试
    result1 = test_basic_connection()
    test_results.append(("基本连接测试", result1))
    
    # 2. 详细连接测试
    result2 = test_detailed_connection()
    test_results.append(("详细连接测试", result2))
    
    # 3. 数据库操作测试
    result3 = test_database_operations()
    test_results.append(("数据库操作测试", result3))
    
    # 4. 集合访问测试
    result4 = test_collection_access()
    test_results.append(("集合访问测试", result4))
    
    # 打印测试结果汇总
    print_separator("测试结果汇总")
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试统计: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MongoDB连接正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查MongoDB配置和网络连接。")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
