### 测试多语言活动名映射的API接口

### 1. 充值成功 - 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 100,
  "params": ["张三", "100"]
}

### 2. 充值成功 - 英语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 100,
  "params": ["<PERSON>", "100"]
}

### 3. 充值成功 - 葡萄牙语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 100,
  "params": ["João", "100"]
}

### 4. 红包雨活动 - 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 400,
  "params": ["400"]
}

### 5. 红包雨活动 - 英语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 400,
  "params": ["400"]
}

### 6. 红包雨活动 - 葡萄牙语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 400,
  "params": ["400"]
}

### 7. 投注返利 - 复杂模板 - 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": ["李四", "18000", "1", "100"]
}

### 8. 投注返利 - 复杂模板 - 英语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": ["Mike", "18000", "2", "200"]
}

### 9. 投注返利 - 复杂模板 - 葡萄牙语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": ["Carlos", "18000", "3", "300"]
}

### 10. 首充拉新 - 英语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 19000,
  "params": ["Alice", "19000", "2", "500"]
}

### 11. 首充拉新 - 葡萄牙语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 19000,
  "params": ["Maria", "19000", "3", "1000"]
}

### 12. 游戏获奖 - 多种元素 - 葡萄牙语
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 300,
  "params": ["Pedro", "1001", "300", "3"]
}
