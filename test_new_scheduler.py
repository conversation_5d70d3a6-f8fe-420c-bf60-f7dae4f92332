#!/usr/bin/env python3
"""
测试新的定时任务功能
验证新表结构的调度器是否正常工作
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, date

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager
from scheduler.task_scheduler import TaskScheduler


async def test_config_manager():
    """测试配置管理器"""
    print("🔧 测试配置管理器")
    print("=" * 50)
    
    # 初始化配置管理器
    success = await config_manager.initialize()
    if not success:
        print("❌ 配置管理器初始化失败")
        return False
    
    print("✅ 配置管理器初始化成功")
    
    # 获取所有定时任务
    all_tasks = config_manager.get_all_scheduled_tasks()
    print(f"📋 总任务数: {len(all_tasks)}")
    
    # 显示任务分布
    distribution = {}
    for task in all_tasks:
        frequency = task.get("sendFrequency", "unknown")
        if frequency not in distribution:
            distribution[frequency] = 0
        distribution[frequency] += 1
    
    print(f"📊 任务分布: {distribution}")
    
    # 显示前几个任务的详情
    print(f"\n📋 任务详情 (前3个):")
    for i, task in enumerate(all_tasks[:3], 1):
        print(f"  {i}. ID: {task.get('_id')}")
        print(f"     notifyId: {task.get('notifyId')}")
        print(f"     business_no: {task.get('business_no')}")
        print(f"     sendFrequency: {task.get('sendFrequency')}")
        print(f"     scheduleDays: {task.get('scheduleDays')}")
        print(f"     scheduleTimes: {task.get('scheduleTimes')}")
        print(f"     taskType: {task.get('taskType')}")
        print(f"     enabled: {task.get('enabled')}")
        print()
    
    return True


async def test_date_matching():
    """测试日期匹配功能"""
    print(f"\n📅 测试日期匹配功能")
    print("=" * 50)
    
    # 测试今天的任务
    today = date.today()
    today_tasks = config_manager.get_scheduled_tasks_for_date(today)
    
    print(f"📅 今天 ({today}) 的任务: {len(today_tasks)} 个")
    
    for i, task in enumerate(today_tasks, 1):
        print(f"  {i}. ID: {task.get('_id')}")
        print(f"     频率: {task.get('sendFrequency')}")
        print(f"     天数: {task.get('scheduleDays')}")
        print(f"     时间: {task.get('scheduleTimes')}")
        print(f"     消息: {task.get('messageText', '')[:50]}...")
        print()
    
    # 测试特定日期
    test_dates = [
        date(2025, 7, 15),  # 15号 (每月)
        date(2025, 7, 16),  # 周三 (每周)
        date(2025, 7, 17),  # 周四 (每周)
    ]
    
    for test_date in test_dates:
        tasks = config_manager.get_scheduled_tasks_for_date(test_date)
        weekday = test_date.strftime('%A')
        print(f"📅 {test_date} ({weekday}) 的任务: {len(tasks)} 个")
        
        for task in tasks:
            frequency = task.get('sendFrequency')
            days = task.get('scheduleDays')
            print(f"   - {frequency}: {days}")
    
    return True


async def test_scheduler():
    """测试调度器"""
    print(f"\n⏰ 测试调度器")
    print("=" * 50)
    
    try:
        # 创建调度器实例
        scheduler = TaskScheduler()
        
        # 初始化调度器
        await scheduler.initialize()
        print("✅ 调度器初始化成功")
        
        # 手动检查当前时间的任务
        current_time = datetime.now()
        print(f"🕐 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 获取当前日期的任务
        current_date = current_time.date()
        matched_tasks = config_manager.get_scheduled_tasks_for_date(current_date)
        
        print(f"📋 今天匹配的任务: {len(matched_tasks)} 个")
        
        # 检查每个任务是否应该在当前时间执行
        for task in matched_tasks:
            should_execute = await scheduler._should_execute_task(task, current_time)
            task_id = task.get('_id')
            schedule_times = task.get('scheduleTimes', [])
            
            print(f"  任务 {task_id}:")
            print(f"    调度时间: {schedule_times}")
            print(f"    当前时间: {current_time.strftime('%H:%M')}")
            print(f"    应该执行: {'✅ 是' if should_execute else '❌ 否'}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_task_execution():
    """测试任务执行 (模拟)"""
    print(f"\n🎯 测试任务执行 (模拟)")
    print("=" * 50)
    
    try:
        # 获取一个示例任务
        all_tasks = config_manager.get_all_scheduled_tasks()
        if not all_tasks:
            print("❌ 没有可用的任务进行测试")
            return False
        
        test_task = all_tasks[0]
        print(f"📋 测试任务:")
        print(f"   ID: {test_task.get('_id')}")
        print(f"   notifyId: {test_task.get('notifyId')}")
        print(f"   business_no: {test_task.get('business_no')}")
        print(f"   消息: {test_task.get('messageText', '')[:100]}...")
        print(f"   目标聊天: {test_task.get('channelId')}")
        print(f"   Bot Token: {test_task.get('botToken', '')[:20]}...")
        
        # 检查Bot Token
        business_no = test_task.get('business_no')
        bot_token = config_manager.get_bot_token(business_no)
        
        if bot_token:
            print(f"✅ 成功获取Bot Token: {bot_token[:20]}...")
        else:
            print(f"❌ 无法获取Bot Token，商户号: {business_no}")
        
        print(f"\n💡 任务执行准备就绪，实际执行需要在调度器中进行")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务执行测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 新定时任务功能测试")
    print("=" * 60)
    
    # 1. 测试配置管理器
    config_ok = await test_config_manager()
    
    # 2. 测试日期匹配
    date_ok = await test_date_matching() if config_ok else False
    
    # 3. 测试调度器
    scheduler_ok = await test_scheduler() if config_ok else False
    
    # 4. 测试任务执行
    execution_ok = await test_task_execution() if config_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   🔧 配置管理器: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"   📅 日期匹配: {'✅ 正常' if date_ok else '❌ 异常'}")
    print(f"   ⏰ 调度器: {'✅ 正常' if scheduler_ok else '❌ 异常'}")
    print(f"   🎯 任务执行: {'✅ 正常' if execution_ok else '❌ 异常'}")
    
    if config_ok and date_ok and scheduler_ok and execution_ok:
        print(f"\n🎉 新定时任务功能测试通过！")
        print(f"💡 关键特性:")
        print(f"   • 支持新的表结构 (sendFrequency, scheduleDays, scheduleTimes)")
        print(f"   • 支持多种调度频率 (daily, weekly, monthly)")
        print(f"   • 支持多时间点执行")
        print(f"   • 支持优先级冲突解决")
        print(f"   • 支持同一notifyId的多种调度策略")
    else:
        print(f"\n⚠️ 部分测试失败，需要检查配置")
    
    print("=" * 60)
    
    return 0 if (config_ok and date_ok and scheduler_ok and execution_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
