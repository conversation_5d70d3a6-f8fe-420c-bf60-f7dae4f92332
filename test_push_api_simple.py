#!/usr/bin/env python3
"""
简单测试推送API
"""
import sys
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_push_api():
    """测试推送API"""
    print("🌐 测试推送API")
    print("=" * 50)
    
    try:
        # 测试API端点
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        # 测试数据
        test_data = {
            "business_no": "39bac42a",
            "type": 19000,
            "params": [
                "tttttt",
                [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                "yyyyyy"
            ]
        }
        
        print(f"📡 API地址: {api_url}")
        print(f"📋 测试数据: {test_data}")
        
        async with aiohttp.ClientSession() as session:
            print(f"\n🚀 发送API请求...")
            
            try:
                async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"\n📊 API响应:")
                    print(f"   状态码: {status}")
                    print(f"   响应内容: {text}")
                    
                    if status == 200:
                        print(f"✅ API调用成功")
                        return True
                    else:
                        print(f"❌ API调用失败")
                        return False
                        
            except aiohttp.ClientConnectorError:
                print(f"❌ 连接失败 - 服务可能未启动")
                return False
            except asyncio.TimeoutError:
                print(f"❌ 请求超时")
                return False
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_service_status():
    """检查服务状态"""
    print(f"\n🔍 检查服务状态")
    print("=" * 50)
    
    try:
        async with aiohttp.ClientSession() as session:
            # 检查健康状态端点
            health_url = "http://localhost:9005/health"
            
            try:
                async with session.get(health_url, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"📊 健康检查:")
                    print(f"   状态码: {status}")
                    print(f"   响应: {text}")
                    
                    return status == 200
                    
            except aiohttp.ClientConnectorError:
                print(f"❌ 服务未启动 (端口 9005)")
                return False
            except Exception as e:
                print(f"❌ 健康检查失败: {e}")
                return False
        
    except Exception as e:
        print(f"❌ 服务状态检查失败: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 定时推送功能测试")
    print("=" * 60)
    
    # 1. 检查服务状态
    service_ok = await check_service_status()
    
    if not service_ok:
        print(f"\n⚠️ 服务未启动，请先启动推送服务")
        print(f"💡 启动命令: python main.py")
        return
    
    # 2. 测试推送API
    api_ok = await test_push_api()
    
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果:")
    print(f"   服务状态: {'✅ 正常' if service_ok else '❌ 异常'}")
    print(f"   推送API: {'✅ 正常' if api_ok else '❌ 异常'}")
    
    if service_ok and api_ok:
        print(f"\n🎉 定时推送功能正常")
        print(f"💡 下一步:")
        print(f"   1. 定时任务将在 00:55 自动执行")
        print(f"   2. 可以查看Telegram频道确认推送结果")
        print(f"   3. 检查日志文件获取详细信息")
    else:
        print(f"\n⚠️ 定时推送功能异常")
        print(f"💡 排查建议:")
        print(f"   1. 确认服务已启动: python main.py")
        print(f"   2. 检查端口9005是否被占用")
        print(f"   3. 查看日志文件获取错误信息")

if __name__ == "__main__":
    asyncio.run(main())
