#!/usr/bin/env python3
"""
测试更新后的投注返利排行榜处理器
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_query():
    """测试MySQL查询"""
    print("🔍 测试MySQL查询")
    print("=" * 50)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL
        await init_mysql()
        mysql_conn = get_mysql_connection()
        
        # 测试查询39bac42a商户今天的数据
        today = "2025-07-10"
        business_no = "39bac42a"
        
        query = """
            SELECT 
                id,
                playerId,
                business_no,
                ranking,
                wagered,
                reward,
                prize,
                currencyId,
                date,
                logTime
            FROM ea_platform_wagered_rebate_rank_log 
            WHERE business_no = %s 
            AND date = %s
            AND robot = 0
            ORDER BY ranking ASC
            LIMIT 10
        """
        
        print(f"查询商户: {business_no}")
        print(f"查询日期: {today}")
        
        results = await mysql_conn.execute_query(query, (business_no, today))
        
        print(f"查询结果: {len(results)} 条记录")
        
        for i, record in enumerate(results, 1):
            ranking = record.get("ranking", 0)
            player_id = record.get("playerId", 0)
            wagered = record.get("wagered", 0)
            reward = record.get("reward", 0)
            
            print(f"  {i}. 排名: {ranking}, 玩家: {player_id}, 投注: {wagered}, 奖励: {reward}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler():
    """测试投注返利处理器"""
    print(f"\n🎯 测试投注返利处理器")
    print("=" * 50)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from database.mongodb_connection import MongoDBConnection
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "test_task_500",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 1,  # 使用已存在的通知配置
            "enabled": True
        }
        
        print(f"测试任务:")
        print(f"  商户: {test_task['business_no']}")
        print(f"  类型: {test_task['taskType']}")
        print(f"  通知ID: {test_task['notifyId']}")
        
        # 执行处理器
        print(f"\n执行处理器...")
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            print(f"\n数据详情:")
            print(f"  模板文本长度: {len(data.get('template_text', ''))}")
            print(f"  参数数量: {len(data.get('params', []))}")
            print(f"  目标聊天数: {len(data.get('target_chats', []))}")
            print(f"  排行榜记录数: {data.get('rank_count', 0)}")
            
            # 显示参数详情
            params = data.get('params', [])
            if len(params) >= 3:
                print(f"\n参数详情:")
                print(f"  参数1 (标题): {params[0]}")
                
                if isinstance(params[1], list):
                    print(f"  参数2 (排行榜): {len(params[1])} 条记录")
                    for i, rank_item in enumerate(params[1][:3], 1):
                        if isinstance(rank_item, list) and len(rank_item) >= 4:
                            print(f"    {i}. 排名:{rank_item[0]}, 玩家:{rank_item[1]}, 投注:{rank_item[2]}, 奖励:{rank_item[3]}")
                
                print(f"  参数3 (总结): {params[2]}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_template_rendering():
    """测试模板渲染"""
    print(f"\n📝 测试模板渲染")
    print("=" * 50)
    
    try:
        from pusher.template_message_handler import TemplateMessageHandler
        
        # 创建模板处理器
        handler = TemplateMessageHandler()
        
        # 模拟模板和参数
        template_text = """
🏆 {arg1}

[repeat]
{arg1}. {arg2} - 投注: {arg3}, 奖励: {arg4}
[/repeat]

📊 {arg3}
        """.strip()
        
        # 模拟参数
        params = [
            "投注返利排行榜 - 2025-07-10",
            [
                ["1", "玩家1234", "1000.00", "50.00"],
                ["2", "玩家5678", "800.00", "40.00"],
                ["3", "玩家9012", "600.00", "30.00"]
            ],
            "共 3 位玩家，总投注 2400.00，总奖励 120.00"
        ]
        
        print(f"模板文本:")
        print(f"  {template_text}")
        
        print(f"\n参数:")
        for i, param in enumerate(params, 1):
            if isinstance(param, list):
                print(f"  参数{i}: 列表 ({len(param)} 项)")
            else:
                print(f"  参数{i}: {param}")
        
        # 渲染模板
        rendered_text = handler._render_template_text(template_text, params)
        
        print(f"\n渲染结果:")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔗 投注返利排行榜功能完整测试")
    print("=" * 80)
    
    # 1. 测试MySQL查询
    query_ok = await test_mysql_query()
    
    # 2. 测试处理器
    handler_ok = False
    if query_ok:
        handler_ok = await test_rebate_handler()
    
    # 3. 测试模板渲染
    template_ok = await test_template_rendering()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   MySQL查询: {'✅ 成功' if query_ok else '❌ 失败'}")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    print(f"   模板渲染: {'✅ 成功' if template_ok else '❌ 失败'}")
    
    if query_ok and handler_ok and template_ok:
        print(f"\n🎉 投注返利排行榜功能测试成功！")
        print(f"💡 功能特点:")
        print(f"   1. 查询MySQL数据库ea_platform_wagered_rebate_rank_log表")
        print(f"   2. 根据实际表结构处理数据")
        print(f"   3. 生成排行榜消息模板")
        print(f"   4. 支持嵌套数组参数")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 等待taskType=500任务执行")
        print(f"   3. 观察排行榜消息推送")
    else:
        print(f"\n⚠️ 测试中发现问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
