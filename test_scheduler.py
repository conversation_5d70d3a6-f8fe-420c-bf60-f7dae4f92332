#!/usr/bin/env python3
"""
测试定时任务调度器
"""
import asyncio
import sys
from pathlib import Path
from datetime import datetime, time

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.task_scheduler import TaskScheduler
from common.utils import setup_logging

import logging
logger = logging.getLogger(__name__)


async def test_task_loading():
    """测试任务加载"""
    print("🧪 测试任务加载")
    print("=" * 50)
    
    scheduler = TaskScheduler()
    
    if not scheduler.mongo.connect():
        print("❌ MongoDB连接失败")
        return False
    
    try:
        await scheduler._load_tasks()
        
        print(f"📋 加载的任务数量: {len(scheduler.tasks)}")
        
        for i, task in enumerate(scheduler.tasks, 1):
            print(f"\n任务 {i}:")
            print(f"  ID: {task.get('notifyId')}")
            print(f"  消息: {task.get('messageText')}")
            print(f"  时间: {task.get('sendTime')}")
            print(f"  频率: {task.get('sendFrequency')}")
            print(f"  目标: {task.get('channelId')}")
            print(f"  启用: {task.get('enabled')}")
            
            if task.get('sendFrequency') == 'weekly':
                print(f"  星期: {task.get('weekDays')}")
            elif task.get('sendFrequency') == 'monthly':
                print(f"  日期: {task.get('monthDays')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        scheduler.mongo.disconnect()


async def test_task_execution_logic():
    """测试任务执行逻辑"""
    print("\n🧪 测试任务执行逻辑")
    print("=" * 50)
    
    scheduler = TaskScheduler()
    
    if not scheduler.mongo.connect():
        print("❌ MongoDB连接失败")
        return False
    
    try:
        await scheduler._load_tasks()
        
        if not scheduler.tasks:
            print("ℹ️ 没有任务可测试")
            return True
        
        current_time = datetime.now()
        current_weekday = current_time.weekday() + 1
        current_day = current_time.day
        
        print(f"⏰ 当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📅 当前星期: {current_weekday} (1=周一, 7=周日)")
        print(f"📆 当前日期: {current_day}")
        
        for task in scheduler.tasks:
            notify_id = task.get('notifyId')
            should_execute = await scheduler._should_execute_task(
                task, current_time, current_weekday, current_day
            )
            
            print(f"\n任务 {notify_id}:")
            print(f"  发送时间: {task.get('sendTime')}")
            print(f"  发送频率: {task.get('sendFrequency')}")
            
            if task.get('sendFrequency') == 'weekly':
                week_days = task.get('weekDays', [])
                print(f"  配置星期: {week_days}")
                print(f"  当前星期匹配: {current_weekday in week_days}")
            elif task.get('sendFrequency') == 'monthly':
                month_days = task.get('monthDays', [])
                print(f"  配置日期: {month_days}")
                print(f"  当前日期匹配: {current_day in month_days}")
            
            print(f"  应该执行: {'✅ 是' if should_execute else '❌ 否'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        scheduler.mongo.disconnect()


async def test_manual_execution():
    """测试手动执行任务"""
    print("\n🧪 测试手动执行任务")
    print("=" * 50)
    
    scheduler = TaskScheduler()
    
    if not scheduler.mongo.connect():
        print("❌ MongoDB连接失败")
        return False
    
    try:
        await scheduler._load_tasks()
        
        if not scheduler.tasks:
            print("ℹ️ 没有任务可测试")
            return True
        
        task = scheduler.tasks[0]
        notify_id = task.get('notifyId')
        
        print(f"🎯 手动执行任务: {notify_id}")
        print(f"消息内容: {task.get('messageText')}")
        print(f"目标聊天: {task.get('channelId')}")
        
        # 询问是否真的要执行
        response = input("\n是否真的要执行这个任务？(y/N): ")
        if response.lower() != 'y':
            print("❌ 用户取消执行")
            return True
        
        await scheduler._execute_task(task)
        print("✅ 任务执行完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        scheduler.mongo.disconnect()


async def main():
    """主函数"""
    setup_logging()
    
    print("🚀 定时任务调度器测试")
    print("=" * 60)
    
    # 测试1: 任务加载
    result1 = await test_task_loading()
    
    # 测试2: 执行逻辑
    result2 = await test_task_execution_logic()
    
    # 测试3: 手动执行（可选）
    print("\n" + "=" * 60)
    response = input("是否要测试手动执行任务？(y/N): ")
    result3 = True
    if response.lower() == 'y':
        result3 = await test_manual_execution()
    
    # 结果汇总
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    tests = [
        ("任务加载", result1),
        ("执行逻辑", result2),
        ("手动执行", result3)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！调度器可以正常工作。")
        print("\n💡 下一步:")
        print("1. 运行 python start_scheduler.py 启动调度器")
        print("2. 调度器会每分钟检查一次任务")
        print("3. 符合条件的任务会自动执行")
    else:
        print("⚠️ 部分测试失败，请检查配置。")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        sys.exit(1)
