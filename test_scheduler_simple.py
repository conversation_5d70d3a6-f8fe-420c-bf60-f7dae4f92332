#!/usr/bin/env python3
"""
简单测试定时任务调度器
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_scheduler_simple():
    """简单测试调度器"""
    print("🔍 简单测试定时任务调度器")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        
        # 1. 加载定时任务
        print("1. 加载定时任务")
        success = await config_manager.load_scheduled_tasks()
        print(f"   加载结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 2. 获取所有任务
        print("\n2. 获取所有任务")
        all_tasks = config_manager.get_all_scheduled_tasks()
        print(f"   任务数量: {len(all_tasks)}")
        print(f"   任务类型: {type(all_tasks)}")
        
        # 3. 显示前3个任务
        if all_tasks and len(all_tasks) > 0:
            print("\n3. 前3个任务详情:")
            for i, task in enumerate(all_tasks[:3], 1):
                print(f"   任务 {i}:")
                print(f"     ID: {task.get('_id')}")
                print(f"     notifyId: {task.get('notifyId')}")
                print(f"     sendFrequency: {task.get('sendFrequency')}")
                print(f"     scheduleDays: {task.get('scheduleDays')}")
                print(f"     scheduleTimes: {task.get('scheduleTimes')}")
                print(f"     enabled: {task.get('enabled')}")
                print()
        
        # 4. 测试当前时间
        print("4. 当前时间信息")
        current_time = datetime.now()
        current_date = current_time.date()
        
        print(f"   当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   星期几: {current_date.weekday() + 1} (1=周一, 7=周日)")
        print(f"   月份日期: {current_date.day}")
        
        # 5. UTC时间对比
        print(f"\n5. UTC时间对比")
        utc_now = datetime.now(timezone.utc)
        local_now = datetime.now()
        
        print(f"   UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"   本地时间: {local_now.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 6. 检查匹配的任务
        print(f"\n6. 检查今天匹配的任务")
        matched_tasks = config_manager.get_scheduled_tasks_for_date(current_date)
        print(f"   匹配数量: {len(matched_tasks)}")
        
        if matched_tasks:
            for i, task in enumerate(matched_tasks, 1):
                print(f"   匹配任务 {i}:")
                print(f"     ID: {task.get('_id')}")
                print(f"     sendFrequency: {task.get('sendFrequency')}")
                print(f"     scheduleDays: {task.get('scheduleDays')}")
                print(f"     scheduleTimes: {task.get('scheduleTimes')[:3]}...")  # 只显示前3个时间
        
        # 7. 检查当前时间是否有任务
        print(f"\n7. 检查当前时间是否有任务应该执行")
        current_time_str = current_time.strftime("%H:%M")
        print(f"   当前时间: {current_time_str}")
        
        should_execute = []
        for task in matched_tasks:
            schedule_times = task.get("scheduleTimes", [])
            for schedule_time in schedule_times:
                # 处理时间格式
                if len(schedule_time.split(':')) == 3:
                    schedule_time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    schedule_time_str = schedule_time
                
                if current_time_str == schedule_time_str:
                    should_execute.append((task, schedule_time))
        
        print(f"   应该执行的任务: {len(should_execute)}")
        for task, schedule_time in should_execute:
            print(f"     ✅ 任务 {task.get('_id')} 在 {schedule_time}")
        
        # 8. 显示接下来的几个时间点
        print(f"\n8. 显示接下来可能执行的时间点")
        next_times = set()
        for task in matched_tasks:
            schedule_times = task.get("scheduleTimes", [])
            for schedule_time in schedule_times:
                if len(schedule_time.split(':')) == 3:
                    time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    time_str = schedule_time
                next_times.add(time_str)
        
        sorted_times = sorted(list(next_times))
        current_hour_min = current_time.strftime("%H:%M")
        
        print(f"   今天的所有调度时间点:")
        for time_str in sorted_times[:10]:  # 只显示前10个
            status = "🔥 当前" if time_str == current_hour_min else "⏰ 待执行"
            print(f"     {status} {time_str}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_scheduler_simple()
    
    if success:
        print(f"\n🎉 调度器状态检查完成")
        
        # 建议下一步
        print(f"\n💡 下一步建议:")
        print(f"   1. 如果当前时间有匹配的任务，可以手动触发测试")
        print(f"   2. 检查定时任务服务是否正在运行")
        print(f"   3. 查看日志文件确认调度器状态")
    else:
        print(f"\n⚠️ 调度器状态检查失败")

if __name__ == "__main__":
    asyncio.run(main())
