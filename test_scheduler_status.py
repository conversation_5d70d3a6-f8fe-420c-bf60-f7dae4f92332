#!/usr/bin/env python3
"""
测试定时任务调度器状态
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_scheduler_status():
    """测试调度器状态"""
    print("🔍 测试定时任务调度器状态")
    print("=" * 60)
    
    try:
        from scheduler.config_manager import config_manager
        
        # 1. 检查配置管理器状态
        print("1. 检查配置管理器状态")
        print(f"   MongoDB连接状态: {config_manager.mongo.is_connected() if config_manager.mongo else '未连接'}")
        
        # 2. 加载定时任务
        print("\n2. 加载定时任务")
        success = await config_manager.load_scheduled_tasks()
        print(f"   加载结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 3. 获取所有任务
        print("\n3. 获取所有任务")
        all_tasks = config_manager.get_all_scheduled_tasks()
        print(f"   任务数量: {len(all_tasks)}")
        
        # 4. 显示任务详情
        if all_tasks:
            print("\n4. 任务详情:")
            for i, (task_id, task) in enumerate(all_tasks.items(), 1):
                print(f"   任务 {i}:")
                print(f"     ID: {task_id}")
                print(f"     notifyId: {task.get('notifyId')}")
                print(f"     business_no: {task.get('business_no')}")
                print(f"     sendFrequency: {task.get('sendFrequency')}")
                print(f"     scheduleDays: {task.get('scheduleDays')}")
                print(f"     scheduleTimes: {task.get('scheduleTimes')}")
                print(f"     enabled: {task.get('enabled')}")
                print()
        
        # 5. 测试当前时间匹配
        print("5. 测试当前时间匹配")
        current_time = datetime.now()
        current_date = current_time.date()
        
        print(f"   当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   当前日期: {current_date}")
        print(f"   星期几: {current_date.weekday() + 1} (1=周一, 7=周日)")
        print(f"   月份日期: {current_date.day}")
        
        # 6. 获取匹配的任务
        matched_tasks = config_manager.get_scheduled_tasks_for_date(current_date)
        print(f"\n6. 匹配的任务")
        print(f"   匹配数量: {len(matched_tasks)}")
        
        if matched_tasks:
            for i, task in enumerate(matched_tasks, 1):
                print(f"   匹配任务 {i}:")
                print(f"     ID: {task.get('_id')}")
                print(f"     sendFrequency: {task.get('sendFrequency')}")
                print(f"     scheduleDays: {task.get('scheduleDays')}")
                print(f"     scheduleTimes: {task.get('scheduleTimes')}")
        
        # 7. 测试UTC时间处理
        print(f"\n7. UTC时间处理")
        utc_now = datetime.now(timezone.utc)
        local_now = datetime.now()
        
        print(f"   UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"   本地时间: {local_now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   时差: {(local_now - utc_now.replace(tzinfo=None)).total_seconds() / 3600:.1f} 小时")
        
        # 8. 检查是否有任务应该在当前时间执行
        print(f"\n8. 检查当前时间是否有任务应该执行")
        current_time_str = current_time.strftime("%H:%M")
        print(f"   当前时间字符串: {current_time_str}")
        
        should_execute_tasks = []
        for task in matched_tasks:
            schedule_times = task.get("scheduleTimes", [])
            for schedule_time in schedule_times:
                # 处理时间格式
                if len(schedule_time.split(':')) == 3:
                    schedule_time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    schedule_time_str = schedule_time
                
                if current_time_str == schedule_time_str:
                    should_execute_tasks.append((task, schedule_time))
        
        print(f"   应该执行的任务数: {len(should_execute_tasks)}")
        for task, schedule_time in should_execute_tasks:
            print(f"     任务 {task.get('_id')} 在 {schedule_time} 应该执行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_scheduler_status()
    
    if success:
        print(f"\n🎉 调度器状态检查完成")
    else:
        print(f"\n⚠️ 调度器状态检查失败")

if __name__ == "__main__":
    asyncio.run(main())
