#!/usr/bin/env python3
"""
简单测试动态适配
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_dynamic_parsing():
    """测试动态解析逻辑"""
    print("🔧 测试动态解析逻辑")
    print("=" * 60)
    
    import re
    
    # 测试模板
    templates = [
        "Daily Ranking Revealed\nToday's Top Players:\n[repeat]{1}{2} – {3}{4}\n[/repeat]",
        "排行榜\n[repeat]{rank1}: {player2}\n[/repeat]",
        "🏆 投注排行榜\n[repeat]第{pos1}名 玩家{id2} 货币{curr3} 投注{amt4}\n[/repeat]"
    ]
    
    for i, template in enumerate(templates, 1):
        print(f"\n{i}. 测试模板:")
        print(f"   {template}")
        
        # 解析repeat部分
        start_repeat = template.find("[repeat]")
        end_repeat = template.find("[/repeat]")
        
        if start_repeat != -1 and end_repeat != -1:
            repeat_content = template[start_repeat + len("[repeat]"):end_repeat]
            placeholders = re.findall(r'\{(\w+)\}', repeat_content)
            
            print(f"   repeat内容: {repeat_content}")
            print(f"   占位符: {placeholders}")
            print(f"   占位符数量: {len(placeholders)}")
            
            # 模拟数据组装
            test_data = ["1", "4743182", "1000", "2,092.21"]
            if len(placeholders) <= len(test_data):
                row_data = test_data[:len(placeholders)]
            else:
                row_data = test_data + [""] * (len(placeholders) - len(test_data))
            
            print(f"   组装数据: {row_data}")
            print(f"   ✅ 解析成功")
        else:
            print(f"   ❌ 没有找到[repeat]块")

def test_translation_manager():
    """测试翻译管理器"""
    print(f"\n📝 测试翻译管理器")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 简单测试
        template = "Daily Ranking Revealed\nToday's Top Players:\n[repeat]{1}{2} – {3}{4}\n[/repeat]"
        params = [
            [
                ["1", "4743182", "1000", "2,092.21"],
                ["2", "2268779", "1000", "2,035.44"]
            ]
        ]
        
        print(f"模板: {template}")
        print(f"参数: {params}")
        
        result = translation_manager.render_template(template, params, "1")
        print(f"\n渲染结果:")
        print(f"{result}")
        
        if "{" not in result:
            print(f"✅ 渲染成功")
            return True
        else:
            print(f"❌ 仍有未替换的占位符")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 简单动态适配测试")
    print("=" * 80)
    
    # 1. 测试动态解析
    test_dynamic_parsing()
    
    # 2. 测试翻译管理器
    tm_ok = test_translation_manager()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   翻译管理器: {'✅ 正常' if tm_ok else '❌ 异常'}")
    
    if tm_ok:
        print(f"\n🎉 动态适配基础功能正常！")
        print(f"💡 关键改进:")
        print(f"   1. 动态解析模板中的占位符")
        print(f"   2. 根据占位符数量组装数据")
        print(f"   3. 无硬编码，完全适配")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 测试实际处理器")
        print(f"   3. 验证不同模板的兼容性")
    else:
        print(f"\n⚠️ 需要进一步调试")

if __name__ == "__main__":
    main()
