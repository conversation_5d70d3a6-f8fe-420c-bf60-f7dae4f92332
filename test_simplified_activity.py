#!/usr/bin/env python3
"""
测试简化的活动名称功能
"""
from config.logging_config import app_logger
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_activity_id_override():
    """测试activity_id覆盖功能"""
    app_logger.info("🔧 测试activity_id覆盖功能")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板 - 包含活动名占位符
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"📋 测试模板: {template}")
        app_logger.info(f"📋 语言: {language_id} (葡萄牙语)")
        
        # 测试用例1: 有activity_id
        app_logger.info(f"\n1. 测试有activity_id的情况:")
        params1 = ["xxx", "ttttt", "100", "BRL"]  # params[0]应该被无效化
        activity_id = 12
        
        app_logger.info(f"   参数: {params1}")
        app_logger.info(f"   activity_id: {activity_id}")
        app_logger.info(f"   期望: 'xxx'被无效化，使用activity_id=12查询的活动名称")
        
        result1 = translation_manager.render_template(
            template, params1, language_id, activity_id=activity_id
        )
        
        app_logger.info(f"   结果: {result1}")
        
        if "xxx" not in result1:
            app_logger.info(f"   ✅ 成功：params[0]='xxx'被无效化")
        else:
            app_logger.info(f"   ❌ 失败：仍包含'xxx'")
        
        # 测试用例2: 无activity_id，使用现有特殊参数处理
        app_logger.info(f"\n2. 测试无activity_id的情况:")
        params2 = ["19000", "ttttt", "100", "BRL"]  # params[0]应该通过特殊参数处理
        
        app_logger.info(f"   参数: {params2}")
        app_logger.info(f"   activity_id: None")
        app_logger.info(f"   期望: '19000'通过现有特殊参数系统处理")
        
        result2 = translation_manager.render_template(
            template, params2, language_id, activity_id=None
        )
        
        app_logger.info(f"   结果: {result2}")
        
        if "19000" not in result2 and ("Bônus" in result2 or "Primeiro" in result2):
            app_logger.info(f"   ✅ 成功：使用了现有特殊参数处理")
        else:
            app_logger.info(f"   ❌ 失败：特殊参数处理不正常")
        
        return True
        
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    app_logger.info("🔧 测试简化的活动名称功能")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 功能说明:")
    app_logger.info("   1. 有activity_id时：查询数据库，无效化params中的活动名参数")
    app_logger.info("   2. 无activity_id时：使用现有的特殊参数处理系统")
    app_logger.info("   3. 保持现有映射配置不变")
    
    success = test_activity_id_override()
    
    if success:
        app_logger.info(f"\n🎉 简化的活动名称功能正常！")
        app_logger.info(f"💡 功能特点:")
        app_logger.info(f"   1. 只在有activity_id时进行数据库查询")
        app_logger.info(f"   2. 复用现有的特殊参数处理系统")
        app_logger.info(f"   3. 保持代码简洁，避免重复实现")
        
        app_logger.info(f"\n📱 现在的行为:")
        app_logger.info(f"   有activity_id: params被无效化，使用数据库查询结果")
        app_logger.info(f"   无activity_id: 使用现有config.yaml中的映射")
    else:
        app_logger.info(f"\n⚠️ 功能有问题，请检查代码")

if __name__ == "__main__":
    main()
