#!/usr/bin/env python3
"""
测试智能活动名称功能
"""
from config.logging_config import app_logger
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_smart_activity_name():
    """测试智能活动名称功能"""
    app_logger.info("🔧 测试智能活动名称功能")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 重新加载配置以获取新的活动名称翻译
        translation_manager.load_config()
        
        # 测试模板
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"📋 测试模板: {template}")
        app_logger.info(f"📋 语言: {language_id} (葡萄牙语)")
        
        # 测试用例
        test_cases = [
            {
                "name": "有activity_id的情况",
                "params": ["xxx", "ttttt", "100", "BRL"],
                "activity_id": 12,
                "message_type": 19000,
                "description": "应该使用activity_id查询到的活动名称，忽略params[0]='xxx'"
            },
            {
                "name": "无activity_id，有type的情况", 
                "params": ["yyy", "ttttt", "100", "BRL"],
                "activity_id": None,
                "message_type": 19000,
                "description": "应该使用type=19000对应的'首充拉新'翻译，忽略params[0]='yyy'"
            },
            {
                "name": "都没有的情况",
                "params": ["zzz", "ttttt", "100", "BRL"],
                "activity_id": None,
                "message_type": 999,  # 不存在的type
                "description": "应该使用空字符串，忽略params[0]='zzz'"
            }
        ]
        
        success_count = 0
        
        for i, test_case in enumerate(test_cases, 1):
            app_logger.info(f"\n{i}. {test_case['name']}")
            app_logger.info(f"   描述: {test_case['description']}")
            app_logger.info(f"   参数: {test_case['params']}")
            app_logger.info(f"   activity_id: {test_case['activity_id']}")
            app_logger.info(f"   message_type: {test_case['message_type']}")
            
            try:
                result = translation_manager.render_template(
                    template,
                    test_case['params'],
                    language_id,
                    activity_id=test_case['activity_id'],
                    message_type=test_case['message_type']
                )
                
                app_logger.info(f"   结果: {result}")
                
                # 检查结果
                if i == 1:
                    # 第一个测试：应该包含从activity_id查询的活动名称，不包含'xxx'
                    if "xxx" not in result and result != template:
                        app_logger.info(f"   ✅ 成功：params中的'xxx'被无效化")
                        success_count += 1
                    else:
                        app_logger.info(f"   ❌ 失败：仍包含'xxx'或未替换")
                elif i == 2:
                    # 第二个测试：应该包含'首充拉新'的翻译，不包含'yyy'
                    if "yyy" not in result and ("Bônus" in result or "Primeiro" in result):
                        app_logger.info(f"   ✅ 成功：使用了type对应的活动名称")
                        success_count += 1
                    else:
                        app_logger.info(f"   ❌ 失败：未正确使用type对应的活动名称")
                elif i == 3:
                    # 第三个测试：应该使用空字符串，不包含'zzz'
                    if "zzz" not in result:
                        app_logger.info(f"   ✅ 成功：使用了空字符串")
                        success_count += 1
                    else:
                        app_logger.info(f"   ❌ 失败：仍包含'zzz'")
                        
            except Exception as e:
                app_logger.info(f"   ❌ 异常: {e}")
        
        app_logger.info(f"\n📊 测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
        
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_smart_activity():
    """测试API智能活动名称功能"""
    app_logger.info(f"\n🔧 测试API智能活动名称功能")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试用例
    test_cases = [
        {
            "name": "有activity_id",
            "data": {
                "business_no": "39bac42a",
                "type": 19000,
                "params": ["xxx", "ttttt", "100", "BRL"],
                "activity_id": 12
            },
            "description": "应该忽略params[0]='xxx'，使用activity_id=12的活动名称"
        },
        {
            "name": "无activity_id",
            "data": {
                "business_no": "39bac42a", 
                "type": 19000,
                "params": ["yyy", "ttttt", "100", "BRL"]
            },
            "description": "应该忽略params[0]='yyy'，使用type=19000对应的活动名称"
        }
    ]
    
    success_count = 0
    
    for i, test_case in enumerate(test_cases, 1):
        app_logger.info(f"\n{i}. {test_case['name']}")
        app_logger.info(f"   描述: {test_case['description']}")
        app_logger.info(f"   数据: {json.dumps(test_case['data'], indent=2, ensure_ascii=False)}")
        
        try:
            response = requests.post(
                api_url,
                json=test_case['data'],
                timeout=15
            )
            
            app_logger.info(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if result.get("status") == "success":
                        app_logger.info(f"   ✅ API调用成功")
                        app_logger.info(f"   💬 请检查Telegram消息是否正确显示活动名称")
                        success_count += 1
                    else:
                        app_logger.info(f"   ❌ API失败: {result.get('message', '未知错误')}")
                except json.JSONDecodeError:
                    app_logger.info(f"   ⚠️ 响应格式异常: {response.text}")
            else:
                app_logger.info(f"   ❌ HTTP错误: {response.text}")
                
        except Exception as e:
            app_logger.info(f"   ❌ 请求异常: {e}")
    
    return success_count > 0

def main():
    """主函数"""
    app_logger.info("🔧 测试智能活动名称功能")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 功能说明:")
    app_logger.info("   1. 优先使用activity_id查询活动名称")
    app_logger.info("   2. 无activity_id时使用type对应的活动名称")
    app_logger.info("   3. 都没有时使用空字符串")
    app_logger.info("   4. params中的活动名参数被无效化")
    
    # 1. 测试直接渲染
    direct_ok = test_smart_activity_name()
    
    # 2. 测试API
    if direct_ok:
        api_ok = test_api_smart_activity()
    else:
        api_ok = False
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📊 测试结果:")
    app_logger.info(f"   直接渲染: {'✅ 成功' if direct_ok else '❌ 失败'}")
    app_logger.info(f"   API测试: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if direct_ok:
        app_logger.info(f"\n🎉 智能活动名称功能已实现！")
        app_logger.info(f"💡 功能特点:")
        app_logger.info(f"   1. 智能查找：activity_id > type > 空字符串")
        app_logger.info(f"   2. 参数无效化：忽略params中的活动名参数")
        app_logger.info(f"   3. 减少错误：调用方不需要关心活动名称的准确性")
        
        app_logger.info(f"\n📱 现在您的模板会正确显示:")
        app_logger.info(f"   不管params[0]传什么，都会被智能替换")
        app_logger.info(f"   'xxx' -> 'First Deposit Bonus' (从activity_id查询)")
        app_logger.info(f"   'yyy' -> 'Bônus de Primeiro Depósito' (从type查询)")
    else:
        app_logger.info(f"\n⚠️ 功能实现有问题，请检查代码")

if __name__ == "__main__":
    main()
