### 测试特殊参数替换功能的API接口

### 1. 测试活动名替换 - 中文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "张三",
    "18000",
    "1", 
    "100"
  ]
}

### 2. 测试活动名替换 - 英文
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "John",
    "19000",
    "2",
    "200"
  ]
}

### 3. 测试游戏名替换
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "王五",
    "1001"
  ]
}

### 4. 测试未知ID处理
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "测试玩家",
    "99999"
  ]
}

### 5. 实际发送测试 - 活动名替换
POST http://localhost:9005/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "张三",
    "18000",
    "1",
    "100"
  ]
}
