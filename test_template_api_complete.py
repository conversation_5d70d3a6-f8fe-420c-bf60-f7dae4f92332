#!/usr/bin/env python3
"""
完整测试模板消息API
测试 /api/realtime-push/template 接口的完整流程
"""
import asyncio
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager
from common.translation_manager import translation_manager
from pusher.template_message_handler import template_handler


async def test_config_loading():
    """测试配置加载"""
    print("🔧 测试配置加载")
    print("=" * 50)
    
    # 1. 测试配置管理器
    config_success = await config_manager.initialize()
    print(f"📋 配置管理器: {'✅ 成功' if config_success else '❌ 失败'}")
    
    if config_success:
        stats = config_manager.get_cache_stats()
        print(f"   Bot配置: {stats['robot_configs']['total']} 个")
        print(f"   通知配置: {stats['notify_configs']['total']} 个")
        print(f"   定时任务: {stats['scheduled_tasks']['total']} 个")
    
    # 2. 测试翻译管理器
    translation_success = translation_manager.load_translations()
    print(f"🌐 翻译管理器: {'✅ 成功' if translation_success else '❌ 失败'}")
    
    if translation_success:
        trans_stats = translation_manager.get_translation_stats()
        print(f"   翻译分类: {trans_stats['categories']} 个")
        print(f"   翻译词条: {trans_stats['total_keys']} 个")
        print(f"   支持语言: {trans_stats['languages']} 种")
    
    return config_success and translation_success


async def test_notify_config_lookup():
    """测试notify配置查找"""
    print(f"\n🔍 测试notify配置查找")
    print("=" * 50)
    
    # 获取所有实时通知配置
    realtime_configs = config_manager.get_all_notify_configs(notify_type=1)
    print(f"📋 实时通知配置: {len(realtime_configs)} 个")
    
    for i, config in enumerate(realtime_configs, 1):
        config_id = config.get("id")
        config_name = config.get("tgName", "")
        config_types = config.get("types", [])
        language = config.get("language", 1)
        target = config.get("notifyTarget", [])
        
        print(f"   {i}. ID={config_id}, 名称='{config_name}'")
        print(f"      触发类型: {config_types}")
        print(f"      语言: {language}")
        print(f"      目标: {target}")
        print()
    
    # 测试type查找
    test_types = ["chat", "1", "18000", "deposit"]
    
    print("🔍 测试type查找:")
    for test_type in test_types:
        config = template_handler._find_notify_config_by_type(test_type)
        if config:
            print(f"   ✅ {test_type} -> 配置ID={config.get('id')}, 名称='{config.get('tgName')}'")
        else:
            print(f"   ❌ {test_type} -> 未找到配置")
    
    return len(realtime_configs) > 0


async def test_template_rendering():
    """测试模板渲染"""
    print(f"\n📝 测试模板渲染")
    print("=" * 50)
    
    # 测试参数
    test_params = [
        "user_name_zhang",
        ["game_a", "game_b"],
        ["100", "50"],
        "thank_you_message"
    ]
    
    print(f"测试参数: {test_params}")
    
    # 测试不同语言的翻译
    test_languages = [1, 2, 7, 12]  # 英文、中文、日文、繁中
    
    for lang_id in test_languages:
        lang_name = translation_manager.language_mapping.get(str(lang_id), f"Unknown({lang_id})")
        translated_params = translation_manager.translate_params(test_params, str(lang_id))
        print(f"\n🌐 语言 {lang_id} ({lang_name}):")
        print(f"   翻译后参数: {translated_params}")
    
    return True


def test_api_preview():
    """测试API预览功能"""
    print(f"\n👁️ 测试API预览功能")
    print("=" * 50)
    
    try:
        # 测试预览请求
        preview_request = {
            "business_no": "39bac42a",
            "type": "chat",
            "params": [
                "user_name_zhang",
                ["game_a", "game_b"],
                ["100", "50"],
                "thank_you_message"
            ]
        }
        
        print(f"📤 发送预览请求:")
        print(json.dumps(preview_request, indent=2, ensure_ascii=False))
        
        response = requests.post(
            "http://localhost:8000/api/realtime-push/template/preview",
            json=preview_request,
            timeout=10
        )
        
        print(f"\n📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 预览请求成功")
            
            if result.get("success"):
                preview_data = result.get("data", {})
                print(f"\n📋 预览结果:")
                print(f"   模板ID: {preview_data.get('template_id')}")
                print(f"   模板名称: {preview_data.get('template_name')}")
                print(f"   原始模板: {preview_data.get('original_template')}")
                print(f"   语言: {preview_data.get('language')}")
                print(f"   翻译参数: {preview_data.get('translated_params')}")
                print(f"   渲染结果: {preview_data.get('rendered_message')}")
                print(f"   目标聊天: {preview_data.get('target_chats')}")
                print(f"   图片URL: {preview_data.get('image_url')}")
                print(f"   链接: {preview_data.get('link')}")
                return True
            else:
                print(f"❌ 预览失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 预览请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False


def test_api_send():
    """测试API发送功能"""
    print(f"\n📤 测试API发送功能")
    print("=" * 50)
    
    try:
        # 测试发送请求
        send_request = {
            "business_no": "39bac42a",
            "type": "chat",
            "params": [
                "user_name_zhang",
                ["game_a"],
                ["100"],
                "thank_you_message"
            ],
            "target_chats": [-1002316158105]  # 指定测试群组
        }
        
        print(f"📤 发送消息请求:")
        print(json.dumps(send_request, indent=2, ensure_ascii=False))
        
        response = requests.post(
            "http://localhost:8000/api/realtime-push/template",
            json=send_request,
            timeout=15
        )
        
        print(f"\n📥 响应状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 发送请求成功")
            print(f"   响应: {result.get('message')}")
            
            # 等待后台处理完成
            print("⏳ 等待后台处理...")
            import time
            time.sleep(5)
            
            return True
        else:
            print(f"❌ 发送请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        return False


def test_api_health():
    """测试API健康状态"""
    print(f"\n💚 测试API健康状态")
    print("=" * 50)
    
    try:
        # 健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务运行正常")
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
        
        # 统一状态
        response = requests.get("http://localhost:8000/api/unified-status", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ 统一状态获取成功")
            
            data = result.get("data", {})
            print(f"   服务类型: {data.get('server_type')}")
            print(f"   FastAPI: {data.get('services', {}).get('fastapi')}")
            print(f"   调度器: {data.get('services', {}).get('scheduler')}")
            print(f"   内存共享: {data.get('memory_shared')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        return False


async def main():
    """主函数"""
    print("🚀 模板消息API完整测试")
    print("=" * 80)
    
    # 1. 测试配置加载
    config_ok = await test_config_loading()
    
    # 2. 测试notify配置查找
    notify_ok = await test_notify_config_lookup() if config_ok else False
    
    # 3. 测试模板渲染
    render_ok = await test_template_rendering() if config_ok else False
    
    # 4. 测试API健康状态
    health_ok = test_api_health()
    
    # 5. 测试API预览
    preview_ok = test_api_preview() if health_ok else False
    
    # 6. 测试API发送
    send_ok = test_api_send() if health_ok and preview_ok else False
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果汇总:")
    print(f"   🔧 配置加载: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   🔍 配置查找: {'✅ 通过' if notify_ok else '❌ 失败'}")
    print(f"   📝 模板渲染: {'✅ 通过' if render_ok else '❌ 失败'}")
    print(f"   💚 API健康: {'✅ 通过' if health_ok else '❌ 失败'}")
    print(f"   👁️ API预览: {'✅ 通过' if preview_ok else '❌ 失败'}")
    print(f"   📤 API发送: {'✅ 通过' if send_ok else '❌ 失败'}")
    
    if all([config_ok, notify_ok, render_ok, health_ok, preview_ok]):
        print(f"\n🎉 模板消息API功能完整！")
        print(f"💡 接口特性:")
        print(f"   • 根据business_no和type查找notify配置")
        print(f"   • 多语言翻译和模板渲染")
        print(f"   • 与定时任务相同的推送格式")
        print(f"   • 支持文本和图片消息")
        print(f"   • 支持链接和按钮")
        
        if send_ok:
            print(f"   • 实际发送测试通过")
        else:
            print(f"   ⚠️ 实际发送需要检查Bot Token和群组权限")
    else:
        print(f"\n⚠️ 部分功能需要检查")
    
    print("=" * 80)
    
    return 0 if all([config_ok, notify_ok, render_ok, health_ok, preview_ok]) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
