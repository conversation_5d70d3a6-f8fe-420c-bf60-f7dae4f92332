#!/usr/bin/env python3
"""
测试模板修复
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_template_rendering():
    """测试模板渲染修复"""
    print("📝 测试模板渲染修复")
    print("=" * 50)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模板
        template_text = """Daily Ranking Revealed
Today's Top Players:
[repeat]{1}{2} – {3}{4}
[/repeat]"""
        
        # 修复后的参数格式 (添加索引0占位符)
        rank_list = [
            ["", "1", "4743182", "1000", "2,092.21"],  # 索引0是占位符
            ["", "2", "2268779", "1000", "2,035.44"],
            ["", "3", "7731938", "1000", "1,821.96"]
        ]
        
        params = [rank_list]
        
        print(f"模板:")
        print(f"  {template_text}")
        
        print(f"\n参数 (修复后):")
        print(f"  记录数: {len(rank_list)}")
        for i, item in enumerate(rank_list, 1):
            print(f"    {i}. {item} (索引0是占位符)")
        
        # 渲染模板
        language_id = "1"  # 英语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n渲染结果:")
        print(f"  语言: {language_id}")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        # 检查是否还有占位符
        if "{" in rendered_text and "}" in rendered_text:
            print(f"  ❌ 仍有未替换的占位符")
            return False
        else:
            print(f"  ✅ 所有占位符已正确替换")
            return True
        
    except Exception as e:
        print(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler_fixed():
    """测试修复后的投注返利处理器"""
    print(f"\n🎯 测试修复后的投注返利处理器")
    print("=" * 50)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and isinstance(params[0], list):
                print(f"\n参数格式检查:")
                print(f"  嵌套数组: {len(params[0])} 条记录")
                
                # 检查前3条记录的格式
                for i, rank_item in enumerate(params[0][:3], 1):
                    if isinstance(rank_item, list):
                        print(f"    {i}. 长度={len(rank_item)}, 内容={rank_item}")
                        if len(rank_item) >= 5:
                            print(f"       索引0(占位符)='{rank_item[0]}', 索引1(排名)='{rank_item[1]}', 索引2(玩家)='{rank_item[2]}', 索引3(货币)='{rank_item[3]}', 索引4(投注)='{rank_item[4]}'")
                        else:
                            print(f"       ❌ 数组长度不足5")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_priority_filter():
    """测试优先级过滤修复"""
    print(f"\n📅 测试优先级过滤修复")
    print("=" * 50)
    
    try:
        from scheduler.timezone_manager import TimeZoneManager
        from datetime import datetime, timezone
        
        # 创建时区管理器
        tz_manager = TimeZoneManager()
        
        # 模拟任务
        test_tasks = [
            {"_id": "daily1", "sendFrequency": "daily", "business_no": "39bac42a", "notifyId": 10},
            {"_id": "weekly1", "sendFrequency": "weekly", "business_no": "39bac42a", "notifyId": 8},
            {"_id": "monthly1", "sendFrequency": "monthly", "business_no": "39bac42a", "notifyId": 9}
        ]
        
        print(f"测试任务:")
        for task in test_tasks:
            print(f"  {task['_id']}: {task['sendFrequency']}")
        
        # 测试优先级过滤
        current_utc = datetime.now(timezone.utc)
        filtered_tasks = tz_manager.filter_tasks_by_priority(test_tasks, current_utc)
        
        print(f"\n过滤结果:")
        print(f"  输入任务数: {len(test_tasks)}")
        print(f"  输出任务数: {len(filtered_tasks)}")
        
        for task in filtered_tasks:
            print(f"    {task['_id']}: {task['sendFrequency']}")
        
        # 检查每日任务是否被保留
        daily_tasks = [t for t in filtered_tasks if t['sendFrequency'] == 'daily']
        print(f"\n每日任务检查:")
        print(f"  每日任务数: {len(daily_tasks)}")
        
        if len(daily_tasks) > 0:
            print(f"  ✅ 每日任务正确保留")
            return True
        else:
            print(f"  ❌ 每日任务被错误过滤")
            return False
        
    except Exception as e:
        print(f"❌ 优先级过滤测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 模板和优先级修复测试")
    print("=" * 80)
    
    # 1. 测试模板渲染
    template_ok = await test_template_rendering()
    
    # 2. 测试处理器
    handler_ok = await test_rebate_handler_fixed()
    
    # 3. 测试优先级过滤
    priority_ok = await test_priority_filter()
    
    print(f"\n" + "=" * 80)
    print(f"📊 修复测试结果:")
    print(f"   模板渲染: {'✅ 修复成功' if template_ok else '❌ 仍有问题'}")
    print(f"   处理器测试: {'✅ 正常' if handler_ok else '❌ 异常'}")
    print(f"   优先级过滤: {'✅ 修复成功' if priority_ok else '❌ 仍有问题'}")
    
    if template_ok and handler_ok and priority_ok:
        print(f"\n🎉 所有问题修复成功！")
        print(f"💡 修复内容:")
        print(f"   1. 模板参数索引修复 (添加索引0占位符)")
        print(f"   2. 每日任务优先级修复 (独立执行，不参与过滤)")
        print(f"   3. 模板渲染正确显示排行榜数据")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息格式")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
