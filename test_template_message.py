#!/usr/bin/env python3
"""
测试模板消息功能
验证翻译管理器和模板消息处理器
"""
import asyncio
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager
from pusher.template_message_handler import template_handler


async def test_translation_manager():
    """测试翻译管理器"""
    print("🌐 测试翻译管理器")
    print("=" * 50)
    
    # 加载翻译配置
    success = translation_manager.load_translations()
    if not success:
        print("❌ 翻译配置加载失败")
        return False
    
    print("✅ 翻译配置加载成功")
    
    # 获取统计信息
    stats = translation_manager.get_translation_stats()
    print(f"📊 翻译统计:")
    print(f"   分类数: {stats['categories']}")
    print(f"   总词条: {stats['total_keys']}")
    print(f"   支持语言: {stats['languages']} 种")
    print(f"   语言代码: {stats['language_codes']}")
    
    # 测试单词翻译
    print(f"\n🔤 单词翻译测试:")
    test_words = ["user_name_zhang", "game_a", "thank_you_message", "123", "unknown_word"]
    
    for word in test_words:
        zh_translation = translation_manager.translate_text(word, "zh")
        en_translation = translation_manager.translate_text(word, "en")
        print(f"   {word}: zh='{zh_translation}', en='{en_translation}'")
    
    # 测试参数翻译
    print(f"\n📋 参数翻译测试:")
    test_params = [
        "user_name_zhang",
        ["game_a", "game_b", "game_c"],
        ["100", "50", "200"],
        "thank_you_message"
    ]
    
    zh_params = translation_manager.translate_params(test_params, "zh")
    en_params = translation_manager.translate_params(test_params, "en")
    
    print(f"   原始参数: {test_params}")
    print(f"   中文翻译: {zh_params}")
    print(f"   英文翻译: {en_params}")
    
    return True


async def test_template_rendering():
    """测试模板渲染"""
    print(f"\n📝 测试模板渲染")
    print("=" * 50)
    
    # 测试模板
    template = """您好 {arg1}，您本次获得以下奖励： 
[repeat3]
{index2}. 在 {list3} 中获得 {list4}
[/repeat]
{test5}"""
    
    # 测试参数
    params = [
        "user_name_zhang",
        [1, 2, 3],
        ["game_a", "game_b", "game_c"],
        ["100 USDT", "50 USDT", "200 USDT"],
        "thank_you_message"
    ]
    
    print(f"📄 原始模板:")
    print(template)
    print(f"\n📋 参数:")
    print(json.dumps(params, indent=2, ensure_ascii=False))
    
    # 渲染中文版本
    zh_result = translation_manager.render_template(template, params, "zh")
    print(f"\n🇨🇳 中文渲染结果:")
    print(zh_result)
    
    # 渲染英文版本
    en_result = translation_manager.render_template(template, params, "en")
    print(f"\n🇺🇸 英文渲染结果:")
    print(en_result)
    
    return True


def test_api_calls():
    """测试API调用"""
    print(f"\n🔗 测试API调用")
    print("=" * 50)
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ API服务异常: {response.status_code}")
            return False
        
        print("✅ API服务运行正常")
        
        # 测试模板消息预览
        print(f"\n📋 测试模板消息预览:")
        preview_request = {
            "business_no": "39bac42a",
            "type": "chat",
            "params": [
                "user_name_zhang",
                [1, 2, 3],
                ["game_a", "game_b", "game_c"],
                ["100", "50", "200"],
                "thank_you_message"
            ]
        }
        
        response = requests.post(
            "http://localhost:8000/api/realtime-push/template/preview",
            json=preview_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 模板消息预览成功")
            if result.get("success"):
                preview_data = result.get("data", {})
                print(f"   模板ID: {preview_data.get('template_id')}")
                print(f"   模板名称: {preview_data.get('template_name')}")
                print(f"   语言: {preview_data.get('language')}")
                print(f"   渲染结果: {preview_data.get('rendered_message')}")
            else:
                print(f"   预览失败: {result.get('message')}")
        else:
            print(f"❌ 预览请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
        
        # 测试模板消息发送
        print(f"\n📤 测试模板消息发送:")
        send_request = {
            "business_no": "39bac42a",
            "type": "chat",
            "params": [
                "user_name_zhang",
                ["game_a", "game_b"],
                ["100", "50"],
                "thank_you_message"
            ],
            "target_chats": [-1002316158105]
        }
        
        response = requests.post(
            "http://localhost:8000/api/realtime-push/template",
            json=send_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 模板消息发送请求成功")
            print(f"   响应: {result.get('message')}")
        else:
            print(f"❌ 发送请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False


async def main():
    """主函数"""
    print("🚀 模板消息功能测试")
    print("=" * 60)
    
    # 1. 测试翻译管理器
    translation_ok = await test_translation_manager()
    
    # 2. 测试模板渲染
    rendering_ok = await test_template_rendering()
    
    # 3. 测试API调用
    api_ok = test_api_calls()
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果汇总:")
    print(f"   🌐 翻译管理器: {'✅ 通过' if translation_ok else '❌ 失败'}")
    print(f"   📝 模板渲染: {'✅ 通过' if rendering_ok else '❌ 失败'}")
    print(f"   🔗 API调用: {'✅ 通过' if api_ok else '❌ 失败'}")
    
    if translation_ok and rendering_ok:
        print(f"\n🎉 模板消息功能基本正常！")
        print(f"💡 特性:")
        print(f"   • 支持多语言翻译")
        print(f"   • 支持复杂模板渲染")
        print(f"   • 支持循环和嵌套参数")
        print(f"   • 提供预览和发送接口")
        
        if not api_ok:
            print(f"\n⚠️ API测试失败，请检查:")
            print(f"   • 统一服务器是否启动")
            print(f"   • notify表是否有对应的模板配置")
            print(f"   • 模板文本是否已配置")
    else:
        print(f"\n⚠️ 部分功能需要检查")
    
    print("=" * 60)
    
    return 0 if (translation_ok and rendering_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
