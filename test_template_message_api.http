# TG Bot模板消息API测试

### 1. 健康检查
GET http://localhost:9005/health

### 2. 统一服务器状态
GET http://localhost:9005/api/unified-status

### 3. 配置状态检查
GET http://localhost:9005/api/config-status

### 4. 模板消息预览 - 基础测试
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": [
    "user_name_zhang"
  ]
}

### 5. 模板消息预览 - 数字type测试 (精确匹配)
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "user_name_zhang",
    ["game_a", "game_b"],
    ["100", "50"],
    "thank_you_message"
  ]
}

### 6. 模板消息预览 - 复杂参数测试
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "19000",
  "params": [
    "user_name_li",
    [1, 2, 3],
    ["game_a", "game_b", "game_c"],
    ["100", "200", "300"],
    "thank_you_message"
  ]
}

### 7. 模板消息预览 - 兜底语言测试
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": [
    "user_name_wang",
    ["unknown_game"],
    ["999"],
    "unknown_message"
  ]
}

### 8. 模板消息发送 - 基础测试 (不会实际发送，只是测试流程)
POST http://localhost:9005/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": 18000,
  "params": [
    "18000",
    "ttttt"
  ]
}

### 9. 模板消息发送 - 使用notify配置的目标群组
POST http://localhost:9005/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "19000",
  "params": [
    "user_name_li",
    ["game_b"， ],
    ["200"],
    "thank_you_message"
  ]
}

### 10. 模板消息发送 - 复杂循环测试
POST http://localhost:9005/api/realtime-push/template
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": [
    "tttttt",
    ["10", "USDT"],
    ["20", "BRL"],
    ["100", "USDC"],
    "thank_you_message"
  ]
}

### 11. 错误测试 - 不存在的商户号
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "nonexistent",
  "type": "chat",
  "params": ["user_name_zhang"]
}

### 12. 错误测试 - 空参数
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": []
}

### 13. 错误测试 - 无效的type
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "invalid_type_12345",
  "params": ["user_name_zhang"]
}

### 14. 性能测试 - 大量参数
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "user_name_zhang",
    [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20],
    ["game_a", "game_b", "game_c", "game_a", "game_b", "game_c", "game_a", "game_b", "game_c", "game_a", "game_b", "game_c", "game_a", "game_b", "game_c", "game_a", "game_b", "game_c", "game_a", "game_b"],
    ["100", "200", "300", "400", "500", "600", "700", "800", "900", "1000", "1100", "1200", "1300", "1400", "1500", "1600", "1700", "1800", "1900", "2000"],
    "thank_you_message"
  ]
}

### 15. 实际发送测试 - 小心使用，会真实发送消息到notify配置的目标群组
# POST http://localhost:9005/api/realtime-push/template
# Content-Type: application/json
#
# {
#   "business_no": "39bac42a",
#   "type": "18000",
#   "params": [
#     "user_name_zhang",
#     ["game_a"],
#     ["100"],
#     "thank_you_message"
#   ]
# }

### 16. 批量测试 - 多种语言效果
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "18000",
  "params": [
    "user_name_zhang",
    ["game_a", "game_b", "game_c"],
    ["100", "200", "300"],
    "thank_you_message"
  ]
}

### 17. 边界测试 - 单个参数
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": ["user_name_zhang"]
}

### 18. 边界测试 - 嵌套数组
POST http://localhost:9005/api/realtime-push/template/preview
Content-Type: application/json

{
  "business_no": "39bac42a",
  "type": "chat",
  "params": [
    "user_name_zhang",
    [["nested_array_not_supported"]],
    "thank_you_message"
  ]
}
