#!/usr/bin/env python3
"""
简单模板测试
测试无占位符模板和有占位符模板的处理
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager


async def test_template_rendering():
    """测试模板渲染"""
    print("🎨 测试模板渲染")
    print("=" * 50)
    
    # 初始化翻译管理器
    success = translation_manager.load_translations()
    if not success:
        print("❌ 翻译管理器初始化失败")
        return False
    
    print("✅ 翻译管理器初始化成功")
    
    # 测试用例
    test_cases = [
        {
            "name": "无占位符模板",
            "template": "test",
            "params": ["user_name_zhang", ["game_a"], ["100"], "thank_you_message"],
            "language": "1",
            "expected": "test"
        },
        {
            "name": "简单占位符模板",
            "template": "用户 {arg1} 获得奖励",
            "params": ["user_name_zhang"],
            "language": "2",
            "expected": "用户 张三 获得奖励"
        },
        {
            "name": "复杂占位符模板",
            "template": "您好 {arg1}，您在 {list2} 游戏中获得了 {list3} 积分！{arg4}",
            "params": ["user_name_zhang", ["game_a"], ["100"], "thank_you_message"],
            "language": "2",
            "expected": "您好 张三，您在 老虎机 游戏中获得了 100 积分！感谢您的参与！"
        },
        {
            "name": "纯文本模板（运营定义）",
            "template": "系统维护通知：服务器将在今晚进行维护",
            "params": ["ignored_param1", "ignored_param2"],
            "language": "1",
            "expected": "系统维护通知：服务器将在今晚进行维护"
        }
    ]
    
    print(f"\n📋 测试用例: {len(test_cases)} 个")
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {case['name']}")
        print(f"   模板: '{case['template']}'")
        print(f"   参数: {case['params']}")
        print(f"   语言: {case['language']}")
        
        result = translation_manager.render_template(
            case['template'], 
            case['params'], 
            case['language']
        )
        
        print(f"   结果: '{result}'")
        print(f"   期望: '{case['expected']}'")
        
        if result == case['expected']:
            print(f"   ✅ 通过")
        else:
            print(f"   ❌ 失败")
            all_passed = False
    
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    
    return all_passed


async def test_language_mapping():
    """测试语言映射"""
    print(f"\n🌐 测试语言映射")
    print("=" * 50)
    
    # 检查语言映射
    if not translation_manager.language_mapping:
        print("❌ 语言映射为空")
        return False
    
    print(f"📋 可用语言: {len(translation_manager.language_mapping)} 种")
    
    # 显示前几种语言
    for lang_id, lang_name in list(translation_manager.language_mapping.items())[:5]:
        print(f"   {lang_id}: {lang_name}")
    
    # 测试特定语言
    test_languages = ["1", "2", "7"]
    
    for lang_id in test_languages:
        lang_name = translation_manager.language_mapping.get(lang_id, f"Unknown({lang_id})")
        print(f"   语言 {lang_id}: {lang_name}")
        
        if lang_name.startswith("Unknown"):
            print(f"   ⚠️ 语言 {lang_id} 未找到映射")
        else:
            print(f"   ✅ 语言 {lang_id} 映射正常")
    
    return True


async def main():
    """主函数"""
    print("🚀 简单模板测试")
    print("=" * 60)
    
    # 1. 测试语言映射
    lang_ok = await test_language_mapping()
    
    # 2. 测试模板渲染
    template_ok = await test_template_rendering()
    
    print(f"\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   🌐 语言映射: {'✅ 正常' if lang_ok else '❌ 异常'}")
    print(f"   🎨 模板渲染: {'✅ 正常' if template_ok else '❌ 异常'}")
    
    if lang_ok and template_ok:
        print(f"\n🎉 所有测试通过！")
        print(f"💡 关键特性:")
        print(f"   • 无占位符模板直接返回原文")
        print(f"   • 多余参数自动忽略")
        print(f"   • 支持复杂占位符和循环")
        print(f"   • 多语言翻译正常")
    else:
        print(f"\n⚠️ 部分测试失败，需要检查")
    
    print("=" * 60)
    
    return 0 if (lang_ok and template_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
