#!/usr/bin/env python3
"""
简单测试翻译功能
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager


async def test_translation():
    """测试翻译功能"""
    print("🌐 测试翻译功能")
    print("=" * 50)
    
    # 加载翻译配置
    success = translation_manager.load_translations()
    if not success:
        print("❌ 翻译配置加载失败")
        return False
    
    print("✅ 翻译配置加载成功")
    
    # 显示语言映射
    print(f"\n📋 语言映射:")
    for lang_id, lang_name in translation_manager.language_mapping.items():
        print(f"  {lang_id}: {lang_name}")
    
    # 测试翻译
    print(f"\n🔤 翻译测试:")
    test_words = ["user_name_zhang", "game_a", "thank_you_message"]
    test_languages = ["1", "2", "7", "12"]  # English, Português, 日本語, 繁體中文
    
    for word in test_words:
        print(f"\n  {word}:")
        for lang_id in test_languages:
            lang_name = translation_manager.language_mapping.get(lang_id, "Unknown")
            translation = translation_manager.translate_text(word, lang_id)
            print(f"    {lang_id} ({lang_name}): {translation}")
    
    # 测试模板渲染
    print(f"\n📝 模板渲染测试:")
    template = "您好 {arg1}，您获得了 {list2} 奖励！{test3}"
    params = ["user_name_zhang", ["game_a"], "thank_you_message"]
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    
    for lang_id in ["1", "2", "7"]:
        lang_name = translation_manager.language_mapping.get(lang_id, "Unknown")
        result = translation_manager.render_template(template, params, lang_id)
        print(f"\n{lang_id} ({lang_name}): {result}")
    
    return True


async def main():
    """主函数"""
    print("🚀 简单翻译测试")
    
    success = await test_translation()
    
    if success:
        print("\n✅ 翻译功能正常")
        print("💡 现在使用语言ID而不是语言代码")
        print("💡 与c_baseLanguage表保持一致")
    else:
        print("\n❌ 翻译功能异常")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        sys.exit(1)
