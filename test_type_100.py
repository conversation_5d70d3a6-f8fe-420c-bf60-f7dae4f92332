#!/usr/bin/env python3
"""
测试type=100的配置查找
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

import asyncio
import requests
import json

async def test_config_lookup():
    """测试配置查找"""
    print("🔍 测试type=100配置查找")
    print("=" * 50)
    
    try:
        from scheduler.config_manager import config_manager
        from pusher.template_message_handler import TemplateMessageHandler
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建处理器实例
        handler = TemplateMessageHandler()
        
        # 测试查找type=100的配置
        config = handler._find_notify_config_by_type(100)
        
        if config:
            print(f"✅ 找到type=100的配置:")
            print(f"   ID: {config.get('id')}")
            print(f"   名称: {config.get('tgName')}")
            print(f"   类型: {config.get('types')}")
            print(f"   通知类型: {config.get('notifyType')}")
            print(f"   模板: {config.get('template', '')[:100]}...")
            return True
        else:
            print(f"❌ 未找到type=100的配置")
            
            # 显示所有配置
            all_configs = config_manager.get_all_notify_configs()
            print(f"\n📋 所有可用配置:")
            for cfg in all_configs:
                types = cfg.get("types", [])
                print(f"   ID {cfg.get('id')}: {cfg.get('tgName')}, types={types}")
            
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_call():
    """测试API调用"""
    print(f"\n🔧 测试type=100的API调用")
    print("=" * 50)
    
    # 测试数据
    data = {
        "business_no": "39bac42a",
        "type": 100,
        "params": ["ttttt"]
    }
    
    print(f"📋 测试数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        # 测试预览接口
        print(f"\n🔍 测试预览接口...")
        response = requests.post(
            "http://localhost:9005/api/realtime-push/template/preview",
            json=data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"状态: {result.get('status')}")
            print(f"消息: {result.get('message')}")
            
            if result.get('status') == 'success':
                data_part = result.get('data', {})
                if 'rendered_message' in data_part:
                    rendered = data_part['rendered_message']
                    print(f"渲染结果: {rendered}")
                
                print(f"✅ API调用成功")
                return True
            else:
                print(f"❌ API调用失败: {result.get('message')}")
                return False
        else:
            print(f"❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def main():
    """主函数"""
    print("🚀 type=100配置测试")
    print("=" * 60)
    
    # 1. 测试配置查找
    config_ok = await test_config_lookup()
    
    # 2. 测试API调用
    if config_ok:
        api_ok = test_api_call()
    else:
        print("⚠️ 配置查找失败，跳过API测试")
        api_ok = False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   🔍 配置查找: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"   🔧 API调用: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if config_ok and api_ok:
        print(f"\n🎉 type=100功能正常工作！")
        print(f"💡 修复说明:")
        print(f"   • 移除了notifyType=1的限制")
        print(f"   • 现在可以查找所有notify配置")
        print(f"   • type=100配置已正常工作")
    else:
        print(f"\n⚠️ 功能异常，需要进一步检查")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(main())
