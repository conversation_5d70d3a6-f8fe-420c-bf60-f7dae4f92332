#!/usr/bin/env python3
"""
测试统一日志功能
"""
import requests
import json
from config.logging_config import app_logger

def test_unified_logging():
    """测试统一日志功能"""
    app_logger.info("🔧 测试统一日志功能")
    app_logger.info("=" * 60)
    
    # 测试不同模块的日志
    app_logger.info("📱 应用模块日志测试")
    
    # 模拟数据库日志
    from config.logging_config import db_logger
    db_logger.info("🗄️ 数据库连接成功")
    
    # 模拟推送日志
    from config.logging_config import pusher_logger
    pusher_logger.info("📤 消息推送测试")
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,
        "params": ["统一日志测试", "ttttt", "100", "BRL"],
        "activity_id": 1
    }
    
    app_logger.info(f"📡 准备测试API: {api_url}")
    app_logger.info(f"📋 测试数据: {json.dumps(test_data, ensure_ascii=False)}")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"📊 API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                app_logger.info(f"📋 API响应内容: {json.dumps(result, ensure_ascii=False)}")
                
                if result.get("status") == "success":
                    app_logger.info("✅ API调用成功！")
                    return True
                else:
                    app_logger.warning(f"⚠️ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.error(f"❌ 响应格式异常: {response.text}")
                return False
        else:
            app_logger.error(f"❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        app_logger.error(f"❌ 请求失败: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 统一日志系统测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 测试目标:")
    app_logger.info("   1. 验证所有模块日志写入同一个文件")
    app_logger.info("   2. 检查API请求/响应日志记录")
    app_logger.info("   3. 确认日志格式统一")
    
    success = test_unified_logging()
    
    app_logger.info("=" * 80)
    app_logger.info(f"📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        app_logger.info("🎉 统一日志系统正常工作！")
        app_logger.info("💡 功能特点:")
        app_logger.info("   1. 所有模块日志写入同一个文件")
        app_logger.info("   2. API请求/响应完整记录")
        app_logger.info("   3. 按日期分目录存储")
        app_logger.info("   4. 同时输出到控制台和文件")
        
        app_logger.info("📁 日志文件位置:")
        app_logger.info("   - 统一日志: logs/2025-07-12/app_2025-07-12.log")
        app_logger.info("   - 包含: API、应用、数据库、推送所有日志")
    else:
        app_logger.warning("⚠️ 统一日志系统有问题，请检查配置")

if __name__ == "__main__":
    main()
