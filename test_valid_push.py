#!/usr/bin/env python3
"""
测试有效的推送类型
"""
import sys
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_valid_push():
    """测试有效的推送类型"""
    print("🎯 测试有效的推送类型")
    print("=" * 50)
    
    try:
        # 测试API端点
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        # 使用定时任务中的实际类型 (taskType: 400)
        test_data = {
            "business_no": "39bac42a",
            "type": 400,  # 红包雨活动
            "params": [
                "测试用户",
                [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
                "感谢参与"
            ]
        }
        
        print(f"📡 API地址: {api_url}")
        print(f"📋 测试数据: {test_data}")
        
        async with aiohttp.ClientSession() as session:
            print(f"\n🚀 发送API请求...")
            
            try:
                async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    status = response.status
                    text = await response.text()
                    
                    print(f"\n📊 API响应:")
                    print(f"   状态码: {status}")
                    print(f"   响应内容: {text}")
                    
                    if status == 200:
                        print(f"✅ API调用成功")
                        
                        # 解析响应
                        try:
                            import json
                            response_data = json.loads(text)
                            if response_data.get("status") == "success":
                                print(f"🎉 推送成功！")
                                return True
                            else:
                                print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                                return False
                        except json.JSONDecodeError:
                            print(f"⚠️ 响应格式异常")
                            return False
                    else:
                        print(f"❌ API调用失败")
                        return False
                        
            except aiohttp.ClientConnectorError:
                print(f"❌ 连接失败 - 服务可能未启动")
                return False
            except asyncio.TimeoutError:
                print(f"❌ 请求超时")
                return False
        
    except Exception as e:
        print(f"❌ 推送测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scheduled_task_execution():
    """测试定时任务执行"""
    print(f"\n⏰ 测试定时任务执行")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取一个启用的任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        task = collection.find_one({"enabled": True, "taskType": 400})
        
        if not task:
            print("❌ 没有找到类型为400的启用任务")
            return False
        
        print(f"📋 找到任务:")
        print(f"   ID: {task.get('_id')}")
        print(f"   notifyId: {task.get('notifyId')}")
        print(f"   taskType: {task.get('taskType')}")
        print(f"   business_no: {task.get('business_no')}")
        print(f"   channelId: {task.get('channelId')}")
        
        # 构造API请求
        api_url = "http://localhost:9005/api/realtime-push/template"
        
        # 使用任务中的实际配置
        test_data = {
            "business_no": task.get('business_no'),
            "type": task.get('taskType'),
            "params": [
                "定时任务测试",
                "400"  # 活动类型参数
            ]
        }
        
        print(f"\n🚀 模拟定时任务推送...")
        print(f"📡 API地址: {api_url}")
        print(f"📋 请求数据: {test_data}")
        
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=30)) as response:
                status = response.status
                text = await response.text()
                
                print(f"\n📊 推送结果:")
                print(f"   状态码: {status}")
                print(f"   响应: {text}")
                
                if status == 200:
                    try:
                        import json
                        response_data = json.loads(text)
                        if response_data.get("status") == "success":
                            print(f"🎉 定时任务推送成功！")
                            print(f"💬 消息已发送到频道: {task.get('channelId')}")
                            return True
                        else:
                            print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"⚠️ 响应格式异常")
                        return False
                else:
                    print(f"❌ 推送失败")
                    return False
        
        mongo.disconnect()
        
    except Exception as e:
        print(f"❌ 定时任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 定时推送功能完整测试")
    print("=" * 60)
    
    # 1. 测试有效的推送类型
    push_ok = await test_valid_push()
    
    # 2. 测试定时任务执行
    task_ok = await test_scheduled_task_execution()
    
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果:")
    print(f"   推送API: {'✅ 正常' if push_ok else '❌ 异常'}")
    print(f"   定时任务: {'✅ 正常' if task_ok else '❌ 异常'}")
    
    if push_ok and task_ok:
        print(f"\n🎉 定时推送功能完全正常！")
        print(f"💡 下一步:")
        print(f"   1. 定时任务将在 00:55 自动执行 (约8分钟后)")
        print(f"   2. 可以查看Telegram频道确认推送结果")
        print(f"   3. 监控日志文件获取执行详情")
    else:
        print(f"\n⚠️ 定时推送功能存在问题")
        print(f"💡 排查建议:")
        print(f"   1. 检查通知配置表 c_tgNotify")
        print(f"   2. 确认Bot Token和频道ID正确")
        print(f"   3. 查看详细日志信息")

if __name__ == "__main__":
    asyncio.run(main())
