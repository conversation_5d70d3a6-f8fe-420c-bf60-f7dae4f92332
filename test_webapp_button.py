#!/usr/bin/env python3
"""
测试WebApp按钮功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from pusher.menu.menu_builder import menu_builder
from telegram import WebApp


def test_webapp_button():
    """测试WebApp按钮创建"""
    print("🧪 测试WebApp按钮功能")
    print("=" * 50)
    
    # 测试按钮配置
    test_buttons = [
        [
            {"text": "🌐 普通链接", "url": "https://www.google.com"},
            {"text": "📱 WebApp按钮", "web_app": {"url": "https://t.me/telegram"}}
        ],
        [
            {"text": "🔄 回调按钮", "callback_data": "test_callback"}
        ]
    ]
    
    try:
        print("📋 测试按钮配置:")
        for i, row in enumerate(test_buttons):
            print(f"  第{i+1}行:")
            for j, button in enumerate(row):
                print(f"    按钮{j+1}: {button}")
        
        print("\n🔧 创建内联键盘...")
        keyboard = menu_builder.create_inline_keyboard(test_buttons)
        
        print("✅ 内联键盘创建成功！")
        print(f"📊 键盘行数: {len(keyboard.inline_keyboard)}")
        
        # 检查每个按钮
        for i, row in enumerate(keyboard.inline_keyboard):
            print(f"\n第{i+1}行按钮:")
            for j, button in enumerate(row):
                print(f"  按钮{j+1}:")
                print(f"    文字: {button.text}")
                if button.url:
                    print(f"    URL: {button.url}")
                elif button.callback_data:
                    print(f"    回调: {button.callback_data}")
                elif button.web_app:
                    print(f"    WebApp: {button.web_app.url}")
                else:
                    print(f"    类型: 未知")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_menu_loading():
    """测试菜单加载"""
    print("\n🧪 测试菜单加载")
    print("=" * 50)
    
    try:
        print("📂 加载定制测试群组菜单...")
        menu_data = menu_builder.load_menu_template("custom_test_group")
        
        if menu_data:
            print("✅ 菜单加载成功！")
            print(f"📋 菜单标题: {menu_data.get('title')}")
            print(f"🖼️ 菜单图片: {menu_data.get('image')}")
            print(f"🔘 按钮数量: {len(menu_data.get('buttons', []))}")
            
            # 检查按钮配置
            buttons = menu_data.get('buttons', [])
            for i, row in enumerate(buttons):
                print(f"\n第{i+1}行按钮:")
                for j, button in enumerate(row):
                    print(f"  按钮{j+1}: {button.get('text')}")
                    if 'url' in button:
                        print(f"    类型: URL链接")
                        print(f"    链接: {button['url']}")
                    elif 'web_app' in button:
                        print(f"    类型: WebApp")
                        web_app_config = button['web_app']
                        if isinstance(web_app_config, dict):
                            print(f"    WebApp URL: {web_app_config.get('url')}")
                        else:
                            print(f"    WebApp URL: {web_app_config}")
                    elif 'callback_data' in button:
                        print(f"    类型: 回调")
                        print(f"    回调数据: {button['callback_data']}")
            
            # 测试创建键盘
            print(f"\n🔧 测试创建键盘...")
            keyboard = menu_builder.create_inline_keyboard(buttons)
            print(f"✅ 键盘创建成功！")
            
            return True
        else:
            print("❌ 菜单加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 WebApp按钮测试开始")
    
    # 测试1: WebApp按钮创建
    result1 = test_webapp_button()
    
    # 测试2: 菜单加载
    result2 = test_menu_loading()
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print("=" * 50)
    
    tests = [
        ("WebApp按钮创建", result1),
        ("菜单加载测试", result2)
    ]
    
    passed = 0
    for test_name, result in tests:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📈 测试统计: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！WebApp按钮应该可以正常工作。")
        print("\n💡 下一步:")
        print("1. 重启Bot服务: python start_bot_interactive.py")
        print("2. 在群组中发送 /start")
        print("3. 查看是否有4个按钮（包括WebApp按钮）")
    else:
        print("⚠️ 部分测试失败，请检查配置。")
    
    return 0 if passed == len(tests) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        sys.exit(1)
