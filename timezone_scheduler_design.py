#!/usr/bin/env python3
"""
多时区定时任务调度器设计
"""
import sys
from pathlib import Path
from datetime import datetime, timezone, timedelta
import pytz

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def analyze_timezone_requirements():
    """分析时区需求"""
    print("🌍 多时区定时任务需求分析")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 1. 检查商户时区配置
        print("1. 检查商户时区配置")
        merchant_collection = mongo.get_collection("c_baseMerchant")
        merchants = list(merchant_collection.find({}))
        
        print(f"   商户数量: {len(merchants)}")
        
        timezone_stats = {}
        for merchant in merchants:
            business_no = merchant.get("business_no", "")
            timezone_str = merchant.get("timeZone", "UTC")
            
            if timezone_str not in timezone_stats:
                timezone_stats[timezone_str] = []
            timezone_stats[timezone_str].append(business_no)
            
            print(f"   商户 {business_no}: {timezone_str}")
        
        print(f"\n   时区分布:")
        for tz, merchants in timezone_stats.items():
            print(f"     {tz}: {len(merchants)} 个商户")
        
        # 2. 检查定时任务分布
        print(f"\n2. 检查定时任务分布")
        task_collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks = list(task_collection.find({"enabled": True}))
        
        print(f"   启用任务数: {len(tasks)}")
        
        # 按商户和频率分组
        task_stats = {}
        for task in tasks:
            business_no = task.get("business_no", "")
            frequency = task.get("sendFrequency", "")
            
            if business_no not in task_stats:
                task_stats[business_no] = {"weekly": 0, "monthly": 0}
            
            if frequency in task_stats[business_no]:
                task_stats[business_no][frequency] += 1
        
        print(f"\n   任务分布:")
        for business_no, stats in task_stats.items():
            merchant_tz = next((m.get("timeZone", "UTC") for m in merchants if m.get("business_no") == business_no), "UTC")
            print(f"     商户 {business_no} ({merchant_tz}): 周任务={stats['weekly']}, 月任务={stats['monthly']}")
        
        # 3. 分析优先级冲突
        print(f"\n3. 分析优先级冲突情况")
        
        # 模拟当前日期检查
        current_date = datetime.now().date()
        day_of_week = current_date.weekday() + 1  # 1=周一
        day_of_month = current_date.day
        
        print(f"   当前日期: {current_date}")
        print(f"   星期几: {day_of_week}")
        print(f"   月份日期: {day_of_month}")
        
        conflicts = []
        for business_no in task_stats.keys():
            # 查找该商户的任务
            merchant_tasks = [t for t in tasks if t.get("business_no") == business_no]
            
            weekly_tasks = [t for t in merchant_tasks if t.get("sendFrequency") == "weekly" and day_of_week in t.get("scheduleDays", [])]
            monthly_tasks = [t for t in merchant_tasks if t.get("sendFrequency") == "monthly" and day_of_month in t.get("scheduleDays", [])]
            
            if weekly_tasks and monthly_tasks:
                conflicts.append({
                    "business_no": business_no,
                    "weekly_count": len(weekly_tasks),
                    "monthly_count": len(monthly_tasks)
                })
        
        print(f"   今天有冲突的商户: {len(conflicts)}")
        for conflict in conflicts:
            print(f"     商户 {conflict['business_no']}: 周任务={conflict['weekly_count']}, 月任务={conflict['monthly_count']} (月任务优先)")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def design_timezone_scheduler():
    """设计时区调度器"""
    print(f"\n🏗️ 时区调度器设计方案")
    print("=" * 60)
    
    print(f"核心设计思路:")
    print(f"1. 实时时区转换")
    print(f"   - 获取商户时区配置")
    print(f"   - 将服务器时间转换为商户本地时间")
    print(f"   - 按商户本地时间匹配任务")
    
    print(f"\n2. 优先级处理")
    print(f"   - 同一商户&语言&群组，月任务优先于周任务")
    print(f"   - 当天有月任务时，忽略周任务")
    
    print(f"\n3. 调度流程")
    print(f"   - 每分钟检查一次")
    print(f"   - 遍历所有启用任务")
    print(f"   - 按商户分组处理")
    print(f"   - 应用优先级规则")
    
    print(f"\n4. 实现要点")
    print(f"   - 使用pytz处理时区转换")
    print(f"   - 缓存商户时区配置")
    print(f"   - 处理夏令时变化")
    print(f"   - 避免重复执行")

def test_timezone_conversion():
    """测试时区转换"""
    print(f"\n🧪 测试时区转换")
    print("=" * 60)
    
    try:
        # 模拟不同时区
        timezones = [
            "UTC",
            "Asia/Shanghai",  # UTC+8
            "America/New_York",  # UTC-5/-4
            "Europe/London",  # UTC+0/+1
            "Asia/Tokyo"  # UTC+9
        ]
        
        # 当前服务器时间
        server_time = datetime.now()
        server_utc = datetime.now(timezone.utc)
        
        print(f"服务器时间: {server_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"服务器UTC: {server_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        print(f"\n各时区对应时间:")
        for tz_name in timezones:
            try:
                tz = pytz.timezone(tz_name)
                local_time = server_utc.astimezone(tz)
                
                print(f"  {tz_name:20}: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
                
                # 检查是否跨日期
                if local_time.date() != server_utc.date():
                    print(f"    ⚠️ 与UTC日期不同")
                
            except Exception as e:
                print(f"  {tz_name:20}: ❌ 错误 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🌍 多时区定时任务调度器分析")
    print("=" * 80)
    
    # 1. 分析时区需求
    analysis_ok = analyze_timezone_requirements()
    
    # 2. 设计调度器
    if analysis_ok:
        design_timezone_scheduler()
    
    # 3. 测试时区转换
    test_ok = test_timezone_conversion()
    
    print(f"\n" + "=" * 80)
    print(f"📊 分析结果:")
    print(f"   需求分析: {'✅ 完成' if analysis_ok else '❌ 失败'}")
    print(f"   时区测试: {'✅ 成功' if test_ok else '❌ 失败'}")
    
    if analysis_ok and test_ok:
        print(f"\n💡 推荐实现方案:")
        print(f"1. 修改TaskScheduler类，增加时区处理")
        print(f"2. 添加商户时区缓存机制")
        print(f"3. 实现优先级过滤逻辑")
        print(f"4. 处理跨时区的边界情况")
        
        print(f"\n🚀 下一步:")
        print(f"1. 实现TimeZoneManager类")
        print(f"2. 修改任务匹配逻辑")
        print(f"3. 添加优先级处理")
        print(f"4. 测试多时区场景")

if __name__ == "__main__":
    main()
