# tmp 目录说明

## 📁 **目录用途**

这个 `tmp/` 目录包含了项目开发过程中的所有临时文件、测试文件、调试脚本和开发文档。

## 🗂️ **文件分类**

### **1. 测试文件 (test_*.py)**
- `test_*.py` - 各种功能测试脚本
- 包含API测试、数据库测试、功能验证等
- 用于开发和调试过程中的功能验证

### **2. 调试文件 (debug_*.py)**
- `debug_*.py` - 调试和排错脚本
- 用于定位问题、分析数据、验证逻辑
- 开发过程中的问题诊断工具

### **3. 检查文件 (check_*.py)**
- `check_*.py` - 配置和数据检查脚本
- 验证数据库连接、配置正确性、数据完整性
- 部署前的环境检查工具

### **4. 简单测试 (simple_*.py)**
- `simple_*.py` - 简化的测试脚本
- 快速验证单一功能
- 轻量级的功能测试

### **5. HTTP测试文件 (*.http)**
- `*.http` - API接口测试文件
- 用于测试REST API接口
- 可以在IDE中直接执行

### **6. 开发文档 (*.md)**
- 各种开发过程中的文档和说明
- 功能设计文档、部署指南、配置说明等
- 项目开发历史记录

### **7. 其他临时文件**
- `*.py` - 各种临时脚本和工具
- `*.log` - 历史日志文件
- 其他开发过程中产生的临时文件

## 🔧 **使用建议**

### **运行测试文件**
```bash
# 进入项目根目录
cd /path/to/tgbot

# 运行测试文件
python tmp/test_*.py

# 运行调试脚本
python tmp/debug_*.py

# 运行检查脚本
python tmp/check_*.py
```

### **查看文档**
```bash
# 查看开发文档
cat tmp/*.md

# 查看特定功能文档
cat tmp/DEPLOYMENT_CHECKLIST.md
```

## 🗑️ **清理建议**

### **可以删除的文件**
- 过期的测试文件
- 已解决问题的调试脚本
- 临时的验证脚本

### **建议保留的文件**
- 重要的功能测试脚本
- 部署和配置检查脚本
- 有价值的开发文档

### **定期清理**
```bash
# 删除超过30天的临时文件（示例）
find tmp/ -name "*.py" -mtime +30 -type f

# 清理特定类型的文件
rm tmp/test_old_*.py
```

## 📋 **文件索引**

### **重要测试文件**
- `test_robot_name_fix.py` - Robot名称修复测试
- `test_api_logging.py` - API日志测试
- `test_unified_logging.py` - 统一日志测试

### **重要调试文件**
- `debug_task_500.py` - 任务500调试
- `debug_config_cache.py` - 配置缓存调试

### **重要检查文件**
- `check_dependencies.py` - 依赖检查
- `check_mysql_data_again.py` - MySQL数据检查
- `check_scheduled_tasks.py` - 定时任务检查

### **重要文档**
- `DEPLOYMENT_CHECKLIST.md` - 部署检查清单
- `SERVICE_STARTUP_GUIDE.md` - 服务启动指南
- `PROJECT_STRUCTURE.md` - 项目结构说明

## 💡 **开发提示**

1. **新的测试文件**：建议放在 `tmp/` 目录中
2. **临时脚本**：开发过程中的临时脚本都放在这里
3. **调试工具**：问题排查工具统一放在这里
4. **文档草稿**：开发文档的草稿版本可以放在这里
5. **实验代码**：实验性的代码和功能验证放在这里

## 🔄 **与主项目的关系**

- **主项目**：`tmp/` 目录外的文件是正式的项目代码
- **测试代码**：`tmp/` 目录中的文件用于测试和验证
- **开发工具**：`tmp/` 目录提供开发和调试工具
- **文档支持**：`tmp/` 目录包含详细的开发文档

这样的结构保持了主项目的整洁，同时保留了所有有用的开发工具和文档。
