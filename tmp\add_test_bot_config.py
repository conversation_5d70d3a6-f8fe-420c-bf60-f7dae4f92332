#!/usr/bin/env python3
"""
添加测试Bot配置数据
向c_tbRobotConfig表中添加测试数据
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def add_test_bot_config():
    """添加测试Bot配置"""
    print("🔧 添加测试Bot配置数据")
    print("=" * 50)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tbRobotConfig")
        if collection is None:
            print("❌ 无法获取c_tbRobotConfig集合")
            return False
        
        # 测试Bot配置数据
        test_configs = [
            {
                "business_no": "39bac42a",
                "bot_token": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
                "bot_name": "测试Bot1",
                "bot_username": "test_bot_1",
                "status": "active",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            },
            {
                "business_no": "40bac42b",
                "bot_token": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
                "bot_name": "测试Bot2", 
                "bot_username": "test_bot_2",
                "status": "active",
                "created_at": datetime.now(),
                "updated_at": datetime.now()
            }
        ]
        
        print(f"📋 准备添加 {len(test_configs)} 个Bot配置:")
        for config in test_configs:
            print(f"   商户号: {config['business_no']}")
            print(f"   Bot名称: {config['bot_name']}")
            print(f"   状态: {config['status']}")
            print()
        
        # 检查是否已存在
        existing_count = collection.count_documents({})
        if existing_count > 0:
            print(f"⚠️ 表中已有 {existing_count} 条记录")
            choice = input("是否清空现有数据并重新添加？(y/N): ")
            if choice.lower() == 'y':
                collection.delete_many({})
                print("🗑️ 已清空现有数据")
            else:
                print("❌ 取消操作")
                return False
        
        # 插入测试数据
        result = collection.insert_many(test_configs)
        
        if result.inserted_ids:
            print(f"✅ 成功添加 {len(result.inserted_ids)} 个Bot配置")
            
            # 验证插入结果
            total_count = collection.count_documents({})
            print(f"📊 表中现有记录数: {total_count}")
            
            # 显示插入的记录
            print(f"\n📋 插入的记录:")
            for config in collection.find({}):
                print(f"   business_no: {config.get('business_no')}")
                print(f"   bot_name: {config.get('bot_name')}")
                print(f"   status: {config.get('status')}")
                print(f"   _id: {config.get('_id')}")
                print()
        else:
            print("❌ 插入失败")
            return False
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 添加测试数据失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def verify_bot_config():
    """验证Bot配置"""
    print(f"\n🔍 验证Bot配置")
    print("=" * 50)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tbRobotConfig")
        if collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 查询所有记录
        all_configs = list(collection.find({}))
        print(f"📊 总记录数: {len(all_configs)}")
        
        # 查询特定商户
        target_business_no = "39bac42a"
        target_config = collection.find_one({"business_no": target_business_no})
        
        if target_config:
            print(f"✅ 找到商户 '{target_business_no}' 的配置:")
            print(f"   bot_token: {target_config.get('bot_token', '')[:20]}...")
            print(f"   bot_name: {target_config.get('bot_name', '')}")
            print(f"   status: {target_config.get('status', '')}")
        else:
            print(f"❌ 未找到商户 '{target_business_no}' 的配置")
        
        mongo.disconnect()
        return len(all_configs) > 0
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 Bot配置数据管理")
    print("=" * 60)
    
    # 1. 添加测试数据
    add_ok = add_test_bot_config()
    
    # 2. 验证数据
    verify_ok = verify_bot_config() if add_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 操作结果:")
    print(f"   🔧 添加数据: {'✅ 成功' if add_ok else '❌ 失败'}")
    print(f"   🔍 验证数据: {'✅ 成功' if verify_ok else '❌ 失败'}")
    
    if add_ok and verify_ok:
        print(f"\n🎉 Bot配置数据添加成功！")
        print(f"💡 现在可以重新测试模板消息API:")
        print(f"   1. 重启统一服务器: python start_unified_server.py")
        print(f"   2. 运行测试: python quick_test.py")
    else:
        print(f"\n⚠️ 操作失败，请检查数据库连接和权限")
    
    print("=" * 60)
    
    return 0 if (add_ok and verify_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 操作过程中发生异常: {e}")
        sys.exit(1)
