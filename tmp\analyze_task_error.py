#!/usr/bin/env python3
"""
分析定时任务错误
专门分析任务4的时间格式问题
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_task_4_error():
    """分析任务4的错误"""
    print("🔍 分析任务4的时间格式错误")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取定时任务集合
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        if collection is None:
            print("❌ 无法获取c_tgScheduledPushTasks集合")
            return False
        
        # 查找任务4
        task_4 = collection.find_one({"notifyId": 4})
        
        if not task_4:
            print("❌ 未找到任务ID为4的任务")
            # 查看所有任务ID
            all_tasks = list(collection.find({}, {"notifyId": 1, "_id": 0}))
            task_ids = [task.get("notifyId") for task in all_tasks]
            print(f"📋 现有任务ID: {task_ids}")
            return False
        
        print("📄 任务4的完整数据:")
        print("-" * 40)
        for key, value in task_4.items():
            if key == '_id':
                print(f"  {key}: {value}")
            else:
                print(f"  {key}: {value}")
        
        # 重点分析时间相关字段
        print("\n🕐 时间字段分析:")
        print("-" * 40)
        
        time_fields = ["sendTime", "pushTime", "pushTimeString", "createTime", "updateTime"]
        for field in time_fields:
            if field in task_4:
                value = task_4[field]
                value_type = type(value).__name__
                print(f"  {field}: {value} (类型: {value_type})")
                
                # 特别检查sendTime字段
                if field == "sendTime":
                    print(f"    ❌ 问题字段: sendTime = '{value}'")
                    print(f"    📝 期望格式: 'HH:MM:SS' (如: '05:00:00')")
                    print(f"    🔧 实际格式: '{value}' (只有数字，缺少时分秒格式)")
        
        # 检查其他可能的问题
        print("\n🔍 其他字段检查:")
        print("-" * 40)
        
        # 检查必要字段
        required_fields = ["notifyId", "messageText", "channelId", "enabled", "sendTime", "sendFrequency"]
        missing_fields = []
        invalid_fields = []
        
        for field in required_fields:
            if field not in task_4:
                missing_fields.append(field)
            elif field == "sendTime":
                # 特别检查sendTime格式
                send_time = task_4[field]
                if not isinstance(send_time, str) or ":" not in str(send_time):
                    invalid_fields.append(f"{field}: '{send_time}' (应为 'HH:MM:SS' 格式)")
        
        if missing_fields:
            print(f"  ❌ 缺失字段: {missing_fields}")
        
        if invalid_fields:
            print(f"  ❌ 无效字段: {invalid_fields}")
        
        if not missing_fields and not invalid_fields:
            print("  ✅ 所有必要字段都存在")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_all_tasks_time_format():
    """分析所有任务的时间格式"""
    print("\n📊 分析所有任务的时间格式")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        if collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 获取所有任务
        all_tasks = list(collection.find({}))
        
        print(f"📋 总任务数: {len(all_tasks)}")
        print("\n时间格式检查:")
        print("-" * 40)
        
        valid_tasks = []
        invalid_tasks = []
        
        for task in all_tasks:
            task_id = task.get("notifyId")
            send_time = task.get("sendTime")
            
            # 检查时间格式
            is_valid = False
            error_msg = ""
            
            if not send_time:
                error_msg = "sendTime字段缺失"
            elif not isinstance(send_time, str):
                error_msg = f"sendTime不是字符串类型: {type(send_time).__name__}"
            elif ":" not in send_time:
                error_msg = f"sendTime格式错误: '{send_time}' (缺少冒号)"
            else:
                try:
                    # 尝试解析时间
                    datetime.strptime(send_time, "%H:%M:%S")
                    is_valid = True
                except ValueError as e:
                    error_msg = f"sendTime格式错误: '{send_time}' ({e})"
            
            if is_valid:
                valid_tasks.append(task_id)
                print(f"  ✅ 任务 {task_id}: sendTime = '{send_time}'")
            else:
                invalid_tasks.append((task_id, send_time, error_msg))
                print(f"  ❌ 任务 {task_id}: {error_msg}")
        
        print(f"\n📊 统计结果:")
        print(f"  ✅ 有效任务: {len(valid_tasks)} 个")
        print(f"  ❌ 无效任务: {len(invalid_tasks)} 个")
        
        if invalid_tasks:
            print(f"\n🔧 需要修复的任务:")
            for task_id, send_time, error_msg in invalid_tasks:
                print(f"  任务 {task_id}: {error_msg}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def suggest_fix_sql():
    """建议修复SQL"""
    print("\n🔧 建议的修复方案")
    print("=" * 60)
    
    print("基于分析结果，建议的修复方案:")
    print()
    
    print("1️⃣ 如果sendTime是数字(如: 5)，需要转换为时间格式:")
    print("   MongoDB修复脚本:")
    print("   ```javascript")
    print("   // 修复任务4的sendTime字段")
    print("   db.c_tgScheduledPushTasks.updateOne(")
    print("     { notifyId: 4 },")
    print("     { $set: { sendTime: '05:00:00' } }")
    print("   );")
    print("   ```")
    print()
    
    print("2️⃣ 批量修复所有格式错误的sendTime:")
    print("   ```javascript")
    print("   // 查找所有sendTime不包含冒号的记录")
    print("   db.c_tgScheduledPushTasks.find({ sendTime: { $not: /.*:.*/ } });")
    print()
    print("   // 如果sendTime是纯数字，转换为HH:00:00格式")
    print("   db.c_tgScheduledPushTasks.find().forEach(function(doc) {")
    print("     if (doc.sendTime && typeof doc.sendTime === 'string' && !doc.sendTime.includes(':')) {")
    print("       var hour = parseInt(doc.sendTime);")
    print("       if (!isNaN(hour) && hour >= 0 && hour <= 23) {")
    print("         var newTime = hour.toString().padStart(2, '0') + ':00:00';")
    print("         db.c_tgScheduledPushTasks.updateOne(")
    print("           { _id: doc._id },")
    print("           { $set: { sendTime: newTime } }")
    print("         );")
    print("         print('Updated task ' + doc.notifyId + ': ' + doc.sendTime + ' -> ' + newTime);")
    print("       }")
    print("     }")
    print("   });")
    print("   ```")
    print()
    
    print("3️⃣ 验证修复结果:")
    print("   ```javascript")
    print("   // 检查所有任务的sendTime格式")
    print("   db.c_tgScheduledPushTasks.find({}, { notifyId: 1, sendTime: 1, _id: 0 });")
    print("   ```")


def main():
    """主函数"""
    print("🚀 定时任务错误分析")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 分析任务4的具体错误
    success1 = analyze_task_4_error()
    
    # 2. 分析所有任务的时间格式
    success2 = analyze_all_tasks_time_format()
    
    # 3. 提供修复建议
    suggest_fix_sql()
    
    print(f"\n" + "=" * 60)
    print("📊 分析完成")
    print("=" * 60)
    
    if success1 and success2:
        print("✅ 错误分析成功")
        print("💡 请根据上述建议修复数据库中的时间格式问题")
        print("🔧 修复后重启服务: python start_unified_server.py")
    else:
        print("❌ 分析过程中出现问题")
        print("💡 请检查MongoDB连接和表结构")
    
    return 0 if (success1 and success2) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
