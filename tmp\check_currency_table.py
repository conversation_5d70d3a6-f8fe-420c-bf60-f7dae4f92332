#!/usr/bin/env python3
"""
查看c_currency表结构
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from database.mongodb_connection import MongoDBConnection


def check_currency_table():
    """查看c_currency表结构"""
    app_logger.info("🔍 查看c_currency表结构")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 获取集合
        collection = mongo_conn.get_collection("c_currency")
        if collection is None:
            app_logger.error("❌ 无法获取c_currency集合")
            return False
        
        # 查询前几条记录
        app_logger.info("📋 查询前10条记录:")
        records = list(collection.find({}).limit(10))
        
        if not records:
            app_logger.info("📊 表中没有数据")
            return True
        
        app_logger.info(f"📊 找到 {len(records)} 条记录:")
        
        for i, record in enumerate(records):
            app_logger.info(f"\n📋 记录 {i+1}:")
            app_logger.info("-" * 60)
            
            # 显示所有字段
            for key, value in record.items():
                value_type = type(value).__name__
                if isinstance(value, str) and len(value) > 100:
                    display_value = value[:100] + "..."
                else:
                    display_value = value
                app_logger.info(f"   {key:<20}: {display_value} ({value_type})")
        
        # 查询总数
        total_count = collection.count_documents({})
        app_logger.info(f"\n📊 总记录数: {total_count}")
        
        # 查看是否有currencyId和currencyName字段
        app_logger.info(f"\n🔍 字段分析:")
        sample_record = records[0]
        
        key_fields = ['currencyId', 'currencyName', 'id', 'name', 'symbol', 'code']
        for field in key_fields:
            if field in sample_record:
                value = sample_record[field]
                value_type = type(value).__name__
                app_logger.info(f"   {field:<15}: {value_type:<10} = {value}")
            else:
                app_logger.info(f"   {field:<15}: 不存在")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


def search_currency_by_id():
    """根据currencyId搜索币种"""
    app_logger.info("\n🔍 根据currencyId搜索币种")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        collection = mongo_conn.get_collection("c_currency")
        if collection is None:
            app_logger.error("❌ 无法获取c_currency集合")
            return False
        
        # 测试一些currencyId
        test_currency_ids = [1, 2, 3, 4, 5, 999]
        
        app_logger.info(f"💰 测试currencyId: {test_currency_ids}")
        
        for currency_id in test_currency_ids:
            # 尝试不同的字段名查询
            queries = [
                {"currencyId": currency_id},
                {"id": currency_id},
                {"_id": currency_id}
            ]
            
            found = False
            for query in queries:
                record = collection.find_one(query)
                if record:
                    app_logger.info(f"\n💰 currencyId={currency_id} 找到记录 (查询: {query}):")
                    
                    # 显示关键字段
                    key_fields = ['id', 'currencyId', 'currencyName', 'name', 'symbol', 'code']
                    for field in key_fields:
                        if field in record:
                            app_logger.info(f"   {field}: {record[field]}")
                    
                    found = True
                    break
            
            if not found:
                app_logger.info(f"❌ currencyId={currency_id} 未找到")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


async def check_game_log_currency():
    """检查游戏日志表中的currencyId"""
    app_logger.info("\n🔍 检查游戏日志表中的currencyId")
    app_logger.info("=" * 80)

    try:
        from database.mysql_connection import MySQLConnection

        mysql_conn = MySQLConnection()
        if not mysql_conn.connect():
            app_logger.error("❌ MySQL连接失败")
            return False

        # 查询当前月份的游戏日志表
        from datetime import datetime
        current_month = datetime.now().strftime("%Y-%m")
        table_name = f"ea_platform_agent_game_log_{current_month}"

        app_logger.info(f"📋 查询表: {table_name}")

        # 查询前几条记录的currencyId
        query = f"""
        SELECT number, playerId, gameId, currencyId, betAmount, winAmount
        FROM `{table_name}`
        WHERE currencyId IS NOT NULL
        ORDER BY number DESC
        LIMIT 10
        """

        results = await mysql_conn.execute_query(query)

        if results:
            app_logger.info(f"📊 找到 {len(results)} 条记录:")

            currency_ids = set()
            for result in results:
                currency_id = result.get('currencyId')
                currency_ids.add(currency_id)
                app_logger.info(f"   number={result.get('number')}, currencyId={currency_id}, "
                              f"betAmount={result.get('betAmount')}, winAmount={result.get('winAmount')}")

            app_logger.info(f"\n💰 发现的currencyId: {sorted(currency_ids)}")
        else:
            app_logger.info("📊 没有找到记录")

        mysql_conn.disconnect()
        return True

    except Exception as e:
        app_logger.error(f"❌ 检查游戏日志失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🔍 c_currency表结构分析")
    app_logger.info("=" * 100)

    # 查看表结构
    success1 = check_currency_table()

    # 搜索测试
    success2 = search_currency_by_id()

    # 检查游戏日志中的currencyId
    success3 = await check_game_log_currency()

    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 分析总结:")
    app_logger.info(f"   表结构查看: {'✅ 成功' if success1 else '❌ 失败'}")
    app_logger.info(f"   currencyId搜索: {'✅ 成功' if success2 else '❌ 失败'}")
    app_logger.info(f"   游戏日志检查: {'✅ 成功' if success3 else '❌ 失败'}")


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
