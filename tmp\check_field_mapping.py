#!/usr/bin/env python3
"""
检查配置表字段是否满足定时任务表的需求
分析从c_tgNotify同步到c_tgScheduledPushTasks的字段映射
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_field_mapping():
    """分析字段映射关系"""
    print("🔍 分析配置表到定时任务表的字段映射")
    print("=" * 70)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取两个集合的样本数据
        notify_collection = mongo.get_collection("c_tgNotify")
        tasks_collection = mongo.get_collection("c_tgScheduledPushTasks")
        
        if notify_collection is None or tasks_collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 获取样本数据
        notify_sample = notify_collection.find_one({"notifyType": 2})  # 定时通知
        tasks_sample = tasks_collection.find_one()
        
        print("📋 配置表 (c_tgNotify) 定时通知样本:")
        if notify_sample:
            for key, value in notify_sample.items():
                if key != '_id':
                    print(f"  {key}: {value}")
        else:
            print("  ❌ 没有找到定时通知配置")
        
        print(f"\n📋 定时任务表 (c_tgScheduledPushTasks) 样本:")
        if tasks_sample:
            for key, value in tasks_sample.items():
                if key != '_id':
                    print(f"  {key}: {value}")
        else:
            print("  ❌ 没有找到定时任务")
        
        mongo.disconnect()
        return True, notify_sample, tasks_sample
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False, None, None


def create_field_mapping_table():
    """创建字段映射表"""
    print(f"\n📊 字段映射对照表:")
    print("=" * 70)
    
    # 定义字段映射关系
    field_mapping = [
        # (定时任务表字段, 配置表字段, 是否必需, 转换说明)
        ("notifyId", "id", "✅", "直接映射"),
        ("business_no", "business_no", "✅", "直接映射"),
        ("botToken", "❌缺失", "✅", "需要添加botToken字段"),
        ("channelId", "notifyTarget", "✅", "直接映射"),
        ("bannerUrl", "msgBanner", "✅", "直接映射"),
        ("messageText", "text", "✅", "直接映射"),
        ("externalUrl", "jumpLink", "✅", "直接映射"),
        ("internalUrl", "tgLink", "✅", "直接映射"),
        ("urlLabel", "❌缺失", "⚠️", "需要添加urlLabel字段"),
        ("urlType", "❌缺失", "⚠️", "需要添加urlType字段"),
        ("sendTime", "pushTimeString", "✅", "直接映射"),
        ("sendFrequency", "❌缺失", "✅", "需要根据cycle推导"),
        ("weekDays", "cycle", "✅", "直接映射"),
        ("monthDays", "❌缺失", "⚠️", "需要添加monthDays字段"),
        ("enabled", "open", "✅", "直接映射"),
        ("createTime", "createTime", "✅", "直接映射"),
        ("updateTime", "updateTime", "✅", "直接映射")
    ]
    
    print("| 定时任务表字段 | 配置表字段 | 状态 | 说明 |")
    print("|---------------|-----------|------|------|")
    
    missing_fields = []
    for task_field, notify_field, required, description in field_mapping:
        status = "✅ 有" if "❌" not in notify_field else "❌ 缺失"
        print(f"| {task_field} | {notify_field} | {status} | {description} |")
        
        if "❌" in notify_field and required == "✅":
            missing_fields.append((task_field, description))
    
    return missing_fields


def suggest_sync_logic():
    """建议同步逻辑"""
    print(f"\n🔄 建议的同步逻辑:")
    print("=" * 70)
    
    sync_logic = '''
def sync_notify_to_tasks(notify_record):
    """将配置表记录同步到定时任务表"""
    
    # 只同步定时通知 (notifyType=2)
    if notify_record.get("notifyType") != 2:
        return None
    
    # 构建定时任务记录
    task_record = {
        "notifyId": notify_record.get("id"),
        "business_no": notify_record.get("business_no"),
        "botToken": notify_record.get("botToken", ""),  # 需要添加
        "channelId": notify_record.get("notifyTarget", []),
        "bannerUrl": notify_record.get("msgBanner", ""),
        "messageText": notify_record.get("text", ""),
        "externalUrl": notify_record.get("jumpLink", ""),
        "internalUrl": notify_record.get("tgLink", ""),
        "urlLabel": notify_record.get("urlLabel", ""),  # 需要添加
        "urlType": notify_record.get("urlType", "browser"),  # 需要添加
        "sendTime": notify_record.get("pushTimeString", ""),
        "sendFrequency": derive_frequency(notify_record),  # 推导逻辑
        "weekDays": notify_record.get("cycle", []),
        "monthDays": notify_record.get("monthDays", []),  # 需要添加
        "enabled": notify_record.get("open", False),
        "createTime": notify_record.get("createTime"),
        "updateTime": notify_record.get("updateTime")
    }
    
    return task_record

def derive_frequency(notify_record):
    """根据配置推导发送频率"""
    cycle = notify_record.get("cycle", [])
    
    if not cycle:
        return "once"  # 单次执行
    elif len(cycle) == 7 and set(cycle) == {1,2,3,4,5,6,7}:
        return "daily"  # 每天
    elif len(cycle) < 7:
        return "weekly"  # 每周指定天
    else:
        return "weekly"  # 默认每周
'''
    
    print(sync_logic)


def check_missing_fields():
    """检查缺失字段"""
    print(f"\n⚠️ 缺失字段分析:")
    print("=" * 70)
    
    missing_fields = [
        {
            "field": "botToken",
            "type": "string",
            "required": True,
            "description": "Bot令牌，用于发送消息",
            "default_value": "",
            "suggestion": "从系统配置或环境变量获取"
        },
        {
            "field": "urlLabel", 
            "type": "string",
            "required": False,
            "description": "链接按钮显示文本",
            "default_value": "点击查看",
            "suggestion": "可以为空，使用默认文本"
        },
        {
            "field": "urlType",
            "type": "string", 
            "required": False,
            "description": "链接类型：browser/webapp",
            "default_value": "browser",
            "suggestion": "根据jumpLink和tgLink判断"
        },
        {
            "field": "monthDays",
            "type": "array",
            "required": False, 
            "description": "月日配置，用于monthly频率",
            "default_value": "[]",
            "suggestion": "暂时可以为空数组"
        }
    ]
    
    print("📋 需要添加的字段:")
    for field_info in missing_fields:
        print(f"\n字段: {field_info['field']}")
        print(f"  类型: {field_info['type']}")
        print(f"  必需: {'是' if field_info['required'] else '否'}")
        print(f"  说明: {field_info['description']}")
        print(f"  默认值: {field_info['default_value']}")
        print(f"  建议: {field_info['suggestion']}")
    
    return missing_fields


def generate_migration_script():
    """生成配置表迁移脚本"""
    print(f"\n🚀 配置表迁移脚本:")
    print("=" * 70)
    
    script = '''
# MongoDB 迁移脚本 - 为 c_tgNotify 添加缺失字段

# 1. 添加缺失的字段
db.c_tgNotify.updateMany(
    {},
    {
        $set: {
            "botToken": "",                    // Bot令牌 (需要手动配置)
            "urlLabel": "",                    // 链接按钮文本
            "urlType": "browser",              // 链接类型
            "monthDays": []                    // 月日配置
        }
    }
)

# 2. 为定时通知设置默认的botToken (如果有统一的Bot)
db.c_tgNotify.updateMany(
    { "notifyType": 2 },
    {
        $set: {
            "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw"
        }
    }
)

# 3. 根据链接类型设置urlType
db.c_tgNotify.updateMany(
    { "tgLink": { $ne: "" } },
    {
        $set: {
            "urlType": "webapp"
        }
    }
)

db.c_tgNotify.updateMany(
    { "jumpLink": { $ne: "" }, "tgLink": "" },
    {
        $set: {
            "urlType": "browser"
        }
    }
)
'''
    
    print(script)


def create_sync_function():
    """创建同步函数示例"""
    print(f"\n💻 Python同步函数示例:")
    print("=" * 70)
    
    function_code = '''
async def sync_notify_to_scheduled_tasks(notify_id: int = None):
    """
    将配置表的定时通知同步到定时任务表
    
    Args:
        notify_id: 指定通知ID，如果为None则同步所有定时通知
    """
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            return False
        
        notify_collection = mongo.get_collection("c_tgNotify")
        tasks_collection = mongo.get_collection("c_tgScheduledPushTasks")
        
        # 查询条件
        query = {"notifyType": 2, "open": True}  # 启用的定时通知
        if notify_id:
            query["id"] = notify_id
        
        # 获取配置记录
        notify_records = list(notify_collection.find(query))
        
        for notify_record in notify_records:
            # 构建任务记录
            task_record = {
                "notifyId": notify_record.get("id"),
                "business_no": notify_record.get("business_no"),
                "botToken": notify_record.get("botToken", ""),
                "channelId": notify_record.get("notifyTarget", []),
                "bannerUrl": notify_record.get("msgBanner", ""),
                "messageText": notify_record.get("text", ""),
                "externalUrl": notify_record.get("jumpLink", ""),
                "internalUrl": notify_record.get("tgLink", ""),
                "urlLabel": notify_record.get("urlLabel", ""),
                "urlType": notify_record.get("urlType", "browser"),
                "sendTime": notify_record.get("pushTimeString", ""),
                "sendFrequency": "weekly",  # 根据cycle推导
                "weekDays": notify_record.get("cycle", []),
                "monthDays": notify_record.get("monthDays", []),
                "enabled": notify_record.get("open", False),
                "createTime": notify_record.get("createTime"),
                "updateTime": notify_record.get("updateTime")
            }
            
            # 更新或插入任务记录
            tasks_collection.update_one(
                {"notifyId": task_record["notifyId"]},
                {"$set": task_record},
                upsert=True
            )
            
            print(f"✅ 同步通知 {notify_record.get('id')} 完成")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 同步失败: {e}")
        return False
'''
    
    print(function_code)


def main():
    """主函数"""
    print("🚀 配置表字段检查")
    print(f"⏰ 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 分析字段映射
    success, notify_sample, tasks_sample = analyze_field_mapping()
    
    # 2. 创建字段映射表
    missing_fields = create_field_mapping_table()
    
    # 3. 检查缺失字段
    missing_details = check_missing_fields()
    
    # 4. 建议同步逻辑
    suggest_sync_logic()
    
    # 5. 生成迁移脚本
    generate_migration_script()
    
    # 6. 创建同步函数
    create_sync_function()
    
    print(f"\n" + "=" * 70)
    print("📊 字段检查完成")
    print("=" * 70)
    
    if success:
        if missing_fields:
            print("⚠️ 配置表缺少部分字段，需要添加:")
            for field, desc in missing_fields:
                print(f"  • {field}: {desc}")
            print("\n💡 建议:")
            print("1. 执行MongoDB迁移脚本添加缺失字段")
            print("2. 实现同步函数")
            print("3. 在配置更新时调用同步函数")
        else:
            print("✅ 配置表字段完全满足定时任务表需求")
    else:
        print("❌ 检查失败，请检查数据库连接")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 检查被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 检查过程中发生异常: {e}")
        sys.exit(1)
