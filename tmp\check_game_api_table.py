#!/usr/bin/env python3
"""
查看c_gameApi表结构
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from database.mongodb_connection import MongoDBConnection


def check_game_api_table():
    """查看c_gameApi表结构"""
    app_logger.info("🔍 查看c_gameApi表结构")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 获取集合
        collection = mongo_conn.get_collection("c_gameApi")
        if collection is None:
            app_logger.error("❌ 无法获取c_gameApi集合")
            return False
        
        # 查询前几条记录
        app_logger.info("📋 查询前5条记录:")
        records = list(collection.find({}).limit(5))
        
        if not records:
            app_logger.info("📊 表中没有数据")
            return True
        
        app_logger.info(f"📊 找到 {len(records)} 条记录:")
        
        for i, record in enumerate(records):
            app_logger.info(f"\n📋 记录 {i+1}:")
            app_logger.info("-" * 60)
            
            # 显示所有字段
            for key, value in record.items():
                value_type = type(value).__name__
                if isinstance(value, str) and len(value) > 100:
                    display_value = value[:100] + "..."
                else:
                    display_value = value
                app_logger.info(f"   {key:<20}: {display_value} ({value_type})")
        
        # 查询总数
        total_count = collection.count_documents({})
        app_logger.info(f"\n📊 总记录数: {total_count}")
        
        # 查看是否有gameId和gameName字段
        app_logger.info(f"\n🔍 字段分析:")
        sample_record = records[0]
        
        key_fields = ['gameId', 'gameName', 'gameApiInfoList', 'id', 'name']
        for field in key_fields:
            if field in sample_record:
                value = sample_record[field]
                value_type = type(value).__name__
                app_logger.info(f"   {field:<15}: {value_type:<10} = {value}")
            else:
                app_logger.info(f"   {field:<15}: 不存在")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


def search_game_by_id():
    """根据gameId搜索游戏"""
    app_logger.info("\n🔍 根据gameId搜索游戏")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        collection = mongo_conn.get_collection("c_gameApi")
        if collection is None:
            app_logger.error("❌ 无法获取c_gameApi集合")
            return False
        
        # 测试一些gameId
        test_game_ids = [200004, 300003, 100001, 999999]
        
        app_logger.info(f"🎮 测试gameId: {test_game_ids}")
        
        for game_id in test_game_ids:
            # 尝试不同的字段名查询
            queries = [
                {"gameId": game_id},
                {"id": game_id},
                {"gameApiInfoList.gameId": game_id}
            ]
            
            found = False
            for query in queries:
                record = collection.find_one(query)
                if record:
                    app_logger.info(f"\n🎯 gameId={game_id} 找到记录 (查询: {query}):")
                    
                    # 显示关键字段
                    key_fields = ['id', 'gameId', 'gameName', 'name']
                    for field in key_fields:
                        if field in record:
                            app_logger.info(f"   {field}: {record[field]}")
                    
                    # 检查gameApiInfoList
                    if 'gameApiInfoList' in record:
                        game_api_list = record['gameApiInfoList']
                        if isinstance(game_api_list, list) and game_api_list:
                            app_logger.info(f"   gameApiInfoList: {len(game_api_list)} 项")
                            # 显示第一项
                            first_item = game_api_list[0]
                            if isinstance(first_item, dict):
                                for k, v in first_item.items():
                                    app_logger.info(f"     {k}: {v}")
                    
                    found = True
                    break
            
            if not found:
                app_logger.info(f"❌ gameId={game_id} 未找到")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


def main():
    """主函数"""
    app_logger.info("🔍 c_gameApi表结构分析")
    app_logger.info("=" * 100)
    
    # 查看表结构
    success1 = check_game_api_table()
    
    # 搜索测试
    success2 = search_game_by_id()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 分析总结:")
    app_logger.info(f"   表结构查看: {'✅ 成功' if success1 else '❌ 失败'}")
    app_logger.info(f"   gameId搜索: {'✅ 成功' if success2 else '❌ 失败'}")


if __name__ == "__main__":
    main()
