#!/usr/bin/env python3
"""
查询投注日志表结构
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from database.mysql_connection import MySQLConnection

async def check_game_log_table():
    """查询投注日志表结构"""
    app_logger.info("🔧 查询投注日志表结构")
    app_logger.info("=" * 60)
    
    mysql_conn = MySQLConnection()
    
    try:
        # 连接数据库
        await mysql_conn.connect()
        app_logger.info("✅ MySQL连接成功")
        
        # 查询当前月份的表名
        current_table = "`ea_platform_agent_game_log_2025-07`"
        app_logger.info(f"📋 查询表: {current_table}")

        # 查询表结构
        describe_query = f"DESCRIBE {current_table}"
        app_logger.info(f"🔍 执行查询: {describe_query}")
        
        results = await mysql_conn.execute_query(describe_query)
        
        if results:
            app_logger.info(f"📊 表结构 ({len(results)} 个字段):")
            app_logger.info("-" * 80)
            app_logger.info(f"{'字段名':<25} {'类型':<20} {'NULL':<8} {'键':<8} {'默认值':<15} {'额外'}")
            app_logger.info("-" * 80)
            
            for row in results:
                field = row.get('Field', '')
                type_info = row.get('Type', '')
                null_info = row.get('Null', '')
                key_info = row.get('Key', '')
                default_info = str(row.get('Default', '')) if row.get('Default') is not None else 'NULL'
                extra_info = row.get('Extra', '')
                
                app_logger.info(f"{field:<25} {type_info:<20} {null_info:<8} {key_info:<8} {default_info:<15} {extra_info}")
        
        # 查询表中的数据量
        count_query = f"SELECT COUNT(*) as total FROM {current_table}"
        count_result = await mysql_conn.execute_query(count_query)
        
        if count_result:
            total_count = count_result[0].get('total', 0)
            app_logger.info(f"\n📊 表数据量: {total_count:,} 条记录")
        
        # 查询最新的几条记录
        sample_query = f"""
        SELECT number, create_time, gameType, platformId, gameId,
               playerId, betAmount, winAmount, status
        FROM {current_table}
        ORDER BY create_time DESC
        LIMIT 5
        """
        
        app_logger.info(f"\n🔍 查询最新5条记录:")
        sample_results = await mysql_conn.execute_query(sample_query)
        
        if sample_results:
            app_logger.info("-" * 120)
            app_logger.info(f"{'Number':<15} {'创建时间':<15} {'游戏类型':<10} {'平台ID':<10} {'游戏ID':<10} {'玩家ID':<10} {'投注':<12} {'赢取':<12} {'状态'}")
            app_logger.info("-" * 120)

            for row in sample_results:
                number_val = row.get('number', '')
                create_time = str(row.get('create_time', ''))  # Unix时间戳
                game_type = row.get('gameType', '')
                platform_id = row.get('platformId', '')
                game_id = row.get('gameId', '')
                player_id = row.get('playerId', '')
                bet_amount = row.get('betAmount', 0)
                win_amount = row.get('winAmount', 0)
                status = row.get('status', '')
                
                app_logger.info(f"{number_val:<15} {create_time:<15} {game_type:<10} {platform_id:<10} {game_id:<10} {player_id:<10} {bet_amount:<12} {win_amount:<12} {status}")
        
        # 查询时间范围
        time_range_query = f"""
        SELECT
            MIN(create_time) as earliest,
            MAX(create_time) as latest,
            MAX(number) as max_number
        FROM {current_table}
        """
        
        time_results = await mysql_conn.execute_query(time_range_query)
        
        if time_results:
            time_info = time_results[0]
            app_logger.info(f"\n📅 时间范围:")
            app_logger.info(f"   最早记录: {time_info.get('earliest', 'N/A')}")
            app_logger.info(f"   最新记录: {time_info.get('latest', 'N/A')}")
            app_logger.info(f"   最大Number: {time_info.get('max_number', 'N/A')}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await mysql_conn.disconnect()

async def main():
    """主函数"""
    app_logger.info("🔧 投注日志表结构分析")
    app_logger.info("=" * 80)
    
    success = await check_game_log_table()
    
    app_logger.info("=" * 80)
    app_logger.info(f"📊 分析结果: {'✅ 成功' if success else '❌ 失败'}")
    
    if success:
        app_logger.info("💡 关键信息:")
        app_logger.info("   1. 表名格式: ea_platform_agent_game_log_YYYY-MM")
        app_logger.info("   2. 主要字段: number(主键), create_time, gameType, platformId, gameId")
        app_logger.info("   3. 查询策略: 使用 number > last_number AND create_time < NOW() - 4分钟")
        app_logger.info("   4. 周期性任务: 每分钟轮询一次")
        app_logger.info("   5. create_time是Unix时间戳格式")

if __name__ == "__main__":
    asyncio.run(main())
