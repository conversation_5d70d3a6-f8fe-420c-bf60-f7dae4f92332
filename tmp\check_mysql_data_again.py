#!/usr/bin/env python3
"""
重新检查MySQL数据
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def check_mysql_data():
    """检查MySQL数据"""
    print("🔍 重新检查MySQL数据")
    print("=" * 50)
    
    try:
        import aiomysql
        
        # 连接参数
        config = {
            'host': '*************',
            'port': 3306,
            'user': 'wingame',
            'password': 'ws82H4HRFbzjmNtD',
            'db': 'wingame',
            'charset': 'utf8mb4'
        }
        
        # 创建连接池
        pool = await aiomysql.create_pool(**config)
        
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 1. 按您的查询检查39bac42a商户的数据
                print("1. 检查商户39bac42a的数据 (按日期倒序)")
                await cursor.execute("""
                    SELECT * FROM ea_platform_wagered_rebate_rank_log 
                    WHERE business_no = '39bac42a' 
                    ORDER BY date DESC
                    LIMIT 10
                """)
                records = await cursor.fetchall()
                
                print(f"   找到 {len(records)} 条记录:")
                for i, record in enumerate(records, 1):
                    # 获取列名
                    await cursor.execute("SHOW COLUMNS FROM ea_platform_wagered_rebate_rank_log")
                    column_info = await cursor.fetchall()
                    column_names = [col[0] for col in column_info]
                    
                    print(f"\n   记录 {i}:")
                    for j, value in enumerate(record):
                        if j < len(column_names):
                            print(f"     {column_names[j]:15}: {value}")
                    
                    if i >= 3:  # 只显示前3条详细信息
                        break
                
                # 2. 专门检查2025-07-10的数据
                print(f"\n2. 专门检查2025-07-10的数据")
                await cursor.execute("""
                    SELECT id, playerId, ranking, wagered, reward, robot, date
                    FROM ea_platform_wagered_rebate_rank_log 
                    WHERE business_no = '39bac42a' 
                    AND date = '2025-07-10'
                    ORDER BY ranking ASC
                """)
                today_records = await cursor.fetchall()
                
                print(f"   2025-07-10的记录数: {len(today_records)}")
                for record in today_records:
                    print(f"     ID: {record[0]}, 玩家: {record[1]}, 排名: {record[2]}, 投注: {record[3]}, 奖励: {record[4]}, robot: {record[5]}, 日期: {record[6]}")
                
                # 3. 检查robot字段的分布
                print(f"\n3. 检查robot字段分布 (2025-07-10)")
                await cursor.execute("""
                    SELECT robot, COUNT(*) as count
                    FROM ea_platform_wagered_rebate_rank_log 
                    WHERE business_no = '39bac42a' 
                    AND date = '2025-07-10'
                    GROUP BY robot
                """)
                robot_stats = await cursor.fetchall()
                
                print(f"   robot字段分布:")
                for stat in robot_stats:
                    robot_value = stat[0]
                    count = stat[1]
                    print(f"     robot={robot_value}: {count} 条记录")
        
        # 关闭连接池
        pool.close()
        await pool.wait_closed()
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def check_scheduled_tasks():
    """检查定时任务表中的taskType=500"""
    print(f"\n📋 检查定时任务表中的taskType=500")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 查询taskType=500的任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks = list(collection.find({"taskType": 500}))
        
        print(f"   找到 {len(tasks)} 个taskType=500的任务:")
        
        for i, task in enumerate(tasks, 1):
            print(f"\n   任务 {i}:")
            print(f"     ID: {task.get('_id')}")
            print(f"     商户: {task.get('business_no')}")
            print(f"     taskType: {task.get('taskType')}")
            print(f"     notifyId: {task.get('notifyId')}")
            print(f"     启用: {task.get('enabled')}")
            print(f"     频率: {task.get('sendFrequency')}")
            print(f"     调度日期: {task.get('scheduleDays')}")
            print(f"     调度时间: {task.get('scheduleTimes')}")
        
        mongo.disconnect()
        return len(tasks) > 0
        
    except Exception as e:
        print(f"❌ 检查定时任务失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 重新检查MySQL数据和定时任务")
    print("=" * 80)
    
    # 1. 检查MySQL数据
    mysql_ok = await check_mysql_data()
    
    # 2. 检查定时任务
    task_ok = await check_scheduled_tasks()
    
    print(f"\n" + "=" * 80)
    print(f"📊 检查结果:")
    print(f"   MySQL数据: {'✅ 正常' if mysql_ok else '❌ 异常'}")
    print(f"   定时任务: {'✅ 找到taskType=500' if task_ok else '❌ 未找到taskType=500'}")
    
    if mysql_ok and task_ok:
        print(f"\n💡 下一步:")
        print(f"   1. 确认处理器查询逻辑")
        print(f"   2. 重新测试投注返利排行榜功能")
        print(f"   3. 重启定时任务服务")

if __name__ == "__main__":
    asyncio.run(main())
