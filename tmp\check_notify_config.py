#!/usr/bin/env python3
"""
检查通知配置表
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_notify_config():
    """检查通知配置表"""
    print("🔍 检查通知配置表 c_tgNotify")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取通知配置集合
        collection = mongo.get_collection("c_tgNotify")
        if collection is None:
            print("❌ 无法获取集合 c_tgNotify")
            return False
        
        # 1. 统计信息
        total_count = collection.count_documents({})
        print(f"📊 总记录数: {total_count}")
        
        if total_count == 0:
            print("ℹ️ 通知配置表为空")
            return True
        
        # 2. 查看所有配置
        print(f"\n📋 所有通知配置:")
        configs = list(collection.find())
        
        for i, config in enumerate(configs, 1):
            print(f"\n配置 {i}:")
            for key, value in config.items():
                if key == '_id':
                    print(f"  {key}: {value}")
                elif isinstance(value, dict):
                    print(f"  {key}: {value}")
                elif isinstance(value, list):
                    print(f"  {key}: {value}")
                else:
                    print(f"  {key}: {value}")
            print("-" * 40)
        
        # 3. 检查特定类型
        print(f"\n🔍 检查类型400的配置:")
        type_400_config = collection.find_one({"type": 400})
        if type_400_config:
            print(f"✅ 找到类型400的配置:")
            for key, value in type_400_config.items():
                print(f"  {key}: {value}")
        else:
            print(f"❌ 未找到类型400的配置")
        
        # 4. 检查商户39bac42a的配置
        print(f"\n🔍 检查商户39bac42a的配置:")
        merchant_configs = list(collection.find({"business_no": "39bac42a"}))
        if merchant_configs:
            print(f"✅ 找到 {len(merchant_configs)} 个配置:")
            for config in merchant_configs:
                print(f"  类型: {config.get('type')}, ID: {config.get('_id')}")
        else:
            print(f"❌ 未找到商户39bac42a的配置")
        
        # 5. 显示所有类型
        print(f"\n📋 所有配置的类型:")
        types = collection.distinct("type")
        print(f"  可用类型: {types}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fix():
    """建议修复方案"""
    print(f"\n💡 修复建议:")
    print("=" * 50)
    
    print(f"问题: 定时任务表中有taskType=400的任务，但c_tgNotify表中没有对应配置")
    print(f"\n解决方案:")
    print(f"1. 在c_tgNotify表中添加类型400的通知配置")
    print(f"2. 或者修改定时任务表中的taskType为已存在的类型")
    print(f"3. 确保business_no匹配")
    
    print(f"\n示例配置 (需要添加到c_tgNotify表):")
    example_config = {
        "type": 400,
        "business_no": "39bac42a",
        "template": "红包雨活动通知: {活动名1}",
        "language": "2",
        "enabled": True
    }
    
    for key, value in example_config.items():
        print(f"  {key}: {value}")

def main():
    """主函数"""
    success = check_notify_config()
    
    if success:
        suggest_fix()
    
    print(f"\n🎯 下一步:")
    print(f"1. 根据检查结果添加缺失的通知配置")
    print(f"2. 重新测试定时推送功能")
    print(f"3. 确认配置正确后等待00:55自动执行")

if __name__ == "__main__":
    main()
