#!/usr/bin/env python3
"""
查看c_tgNotify表结构和数据
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_notify_table():
    """分析c_tgNotify表"""
    print("🔍 分析实时推送消息模板表 c_tgNotify")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取集合
        collection = mongo.get_collection("c_tgNotify")
        if collection is None:
            print("❌ 无法获取集合 c_tgNotify")
            return False
        
        # 1. 统计信息
        total_count = collection.count_documents({})
        print(f"📊 总记录数: {total_count}")
        
        if total_count == 0:
            print("ℹ️ 表中暂无数据")
            return True
        
        # 2. 查看表结构（通过第一条记录）
        print(f"\n📋 表结构分析:")
        first_doc = collection.find_one()
        if first_doc:
            print("字段列表:")
            for key, value in first_doc.items():
                value_type = type(value).__name__
                if isinstance(value, str) and len(value) > 50:
                    value_preview = value[:50] + "..."
                else:
                    value_preview = value
                print(f"  • {key}: {value_type} = {value_preview}")
        
        # 3. 查看所有记录
        print(f"\n📄 所有消息模板记录:")
        print("-" * 60)
        
        records = list(collection.find())
        for i, record in enumerate(records, 1):
            print(f"\n记录 {i}:")
            for key, value in record.items():
                if key == '_id':
                    print(f"  {key}: {value}")
                elif isinstance(value, datetime):
                    print(f"  {key}: {value.strftime('%Y-%m-%d %H:%M:%S')}")
                elif isinstance(value, dict):
                    print(f"  {key}: {json.dumps(value, ensure_ascii=False, indent=4)}")
                elif isinstance(value, list):
                    print(f"  {key}: {json.dumps(value, ensure_ascii=False, indent=4)}")
                else:
                    print(f"  {key}: {value}")
            print("-" * 40)
        
        # 4. 分析字段类型
        print(f"\n🔍 字段类型统计:")
        field_types = {}
        for record in records:
            for key, value in record.items():
                if key not in field_types:
                    field_types[key] = set()
                field_types[key].add(type(value).__name__)
        
        for field, types in field_types.items():
            print(f"  {field}: {', '.join(types)}")
        
        # 5. 查看状态分布
        print(f"\n📈 状态分布:")
        if 'status' in field_types:
            status_pipeline = [
                {"$group": {"_id": "$status", "count": {"$sum": 1}}}
            ]
            status_stats = list(collection.aggregate(status_pipeline))
            for stat in status_stats:
                print(f"  {stat['_id']}: {stat['count']} 个记录")
        elif 'enabled' in field_types:
            enabled_pipeline = [
                {"$group": {"_id": "$enabled", "count": {"$sum": 1}}}
            ]
            enabled_stats = list(collection.aggregate(enabled_pipeline))
            for stat in enabled_stats:
                print(f"  enabled={stat['_id']}: {stat['count']} 个记录")
        
        # 6. 查看消息类型分布
        print(f"\n📋 消息类型分布:")
        if 'messageType' in field_types:
            type_pipeline = [
                {"$group": {"_id": "$messageType", "count": {"$sum": 1}}}
            ]
            type_stats = list(collection.aggregate(type_pipeline))
            for stat in type_stats:
                print(f"  {stat['_id']}: {stat['count']} 个记录")
        elif 'type' in field_types:
            type_pipeline = [
                {"$group": {"_id": "$type", "count": {"$sum": 1}}}
            ]
            type_stats = list(collection.aggregate(type_pipeline))
            for stat in type_stats:
                print(f"  {stat['_id']}: {stat['count']} 个记录")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def suggest_notify_structure():
    """建议实时推送消息模板结构"""
    print(f"\n💡 建议的实时推送消息模板字段结构:")
    print("-" * 60)
    
    suggested_fields = {
        "_id": "ObjectId - MongoDB自动生成的ID",
        "template_id": "String - 模板唯一标识符",
        "template_name": "String - 模板名称",
        "template_type": "String - 模板类型 (text, photo, menu, document)",
        "category": "String - 消息分类 (notification, alert, promotion, etc.)",
        "content": {
            "text": "String - 消息文本内容",
            "image_url": "String - 图片URL",
            "file_url": "String - 文件URL",
            "buttons": "Array - 按钮配置"
        },
        "variables": "Array - 模板变量列表 (用于动态替换)",
        "target": {
            "chat_type": "String - 目标类型 (private, group, channel)",
            "default_chat_ids": "Array - 默认目标聊天ID"
        },
        "settings": {
            "priority": "String - 优先级 (high, normal, low)",
            "retry_count": "Number - 重试次数",
            "timeout": "Number - 超时时间(秒)"
        },
        "status": "String - 模板状态 (active, inactive, draft)",
        "created_at": "Date - 创建时间",
        "updated_at": "Date - 更新时间",
        "created_by": "String - 创建者",
        "usage_count": "Number - 使用次数"
    }
    
    for field, description in suggested_fields.items():
        if isinstance(description, dict):
            print(f"  {field}:")
            for sub_field, sub_desc in description.items():
                print(f"    {sub_field}: {sub_desc}")
        else:
            print(f"  {field}: {description}")


def compare_with_expected():
    """对比实际结构与预期结构"""
    print(f"\n🔍 实际结构 vs 预期结构对比:")
    print("-" * 60)
    
    expected_simple_fields = [
        "template_id",
        "template_name", 
        "message_text",
        "image_url",
        "button_config",
        "target_chats",
        "status",
        "created_at"
    ]
    
    print("📋 预期的简单字段:")
    for field in expected_simple_fields:
        print(f"  • {field}")
    
    print(f"\n💡 如果实际字段比预期多很多，可能的原因:")
    print("  1. 设计时考虑了更多的扩展功能")
    print("  2. 包含了业务相关的特殊字段")
    print("  3. 融合了多种消息类型的配置")
    print("  4. 添加了审核、统计等管理功能")


def main():
    """主函数"""
    print("🚀 c_tgNotify表分析开始")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析现有表结构
    success = analyze_notify_table()
    
    # 建议标准结构
    suggest_notify_structure()
    
    # 对比分析
    compare_with_expected()
    
    print(f"\n" + "=" * 60)
    print("📊 分析完成")
    print("=" * 60)
    
    if success:
        print("✅ 表结构分析成功")
        print("💡 接下来可以基于实际结构实现实时推送功能")
    else:
        print("❌ 表结构分析失败")
        print("💡 请检查MongoDB连接和表名是否正确")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
