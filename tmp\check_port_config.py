#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from config.settings import settings
    
    print("端口配置检查:")
    print(f"  api_host: {settings.api_host}")
    print(f"  api_port: {settings.api_port}")
    print(f"  配置文件: {settings.config_file}")
    
    # 检查配置文件内容
    if settings.config_file.exists():
        with open(settings.config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"\n配置文件中的API配置:")
        lines = content.split('\n')
        in_api_section = False
        for i, line in enumerate(lines, 1):
            if 'api:' in line:
                in_api_section = True
                print(f"  第{i}行: {line.strip()}")
            elif in_api_section and line.strip().startswith(('host:', 'port:')):
                print(f"  第{i}行: {line.strip()}")
            elif in_api_section and line.strip() and not line.startswith(' '):
                in_api_section = False
    
    if settings.api_port == 9005:
        print(f"\n✅ 端口配置正确: {settings.api_port}")
    else:
        print(f"\n❌ 端口配置错误: 期望9005，实际{settings.api_port}")
        
except Exception as e:
    print(f"❌ 检查失败: {e}")
    import traceback
    traceback.print_exc()
