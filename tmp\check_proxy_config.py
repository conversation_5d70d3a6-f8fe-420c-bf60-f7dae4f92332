#!/usr/bin/env python3
"""
检查代理配置状态
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def check_proxy_config():
    """检查代理配置"""
    print("🔍 检查代理配置状态")
    print("=" * 50)
    
    try:
        from config.settings import settings
        
        print("📋 当前代理配置:")
        print(f"   use_proxy: {settings.use_proxy}")
        print(f"   proxy_url: {settings.proxy_url}")
        
        # 检查环境变量
        print(f"\n🌍 环境变量检查:")
        proxy_env_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        for var in proxy_env_vars:
            value = os.environ.get(var)
            print(f"   {var}: {value if value else '未设置'}")
        
        # 检查配置文件
        print(f"\n📄 配置文件检查:")
        config_file = Path("config/config.yaml")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 查找proxy配置行
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if 'proxy:' in line or 'enabled:' in line and 'proxy' in lines[max(0, i-3):i]:
                    print(f"   第{i}行: {line.strip()}")
        
        # 总结
        print(f"\n📊 配置状态总结:")
        if settings.use_proxy:
            print("⚠️ 代理已启用")
            print(f"   代理地址: {settings.proxy_url}")
            print("💡 如需关闭代理，请修改 config/config.yaml 中的 proxy.enabled 为 false")
        else:
            print("✅ 代理已关闭")
            print("💡 适合测试环境和生产环境使用")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bot_client():
    """测试Bot客户端代理设置"""
    print(f"\n🤖 测试Bot客户端代理设置")
    print("=" * 50)
    
    try:
        from common.bot_client import TelegramBotClient

        # 创建Bot客户端实例
        bot_client = TelegramBotClient()
        
        print("✅ Bot客户端创建成功")
        
        # 检查环境变量是否被正确设置
        proxy_env_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy']
        proxy_set = any(os.environ.get(var) for var in proxy_env_vars)
        
        if proxy_set:
            print("⚠️ 检测到代理环境变量已设置")
            for var in proxy_env_vars:
                value = os.environ.get(var)
                if value:
                    print(f"   {var}: {value}")
        else:
            print("✅ 未检测到代理环境变量，直连模式")
        
        return True
        
    except Exception as e:
        print(f"❌ Bot客户端测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 代理配置检查工具")
    print("=" * 60)
    
    # 检查配置
    config_ok = check_proxy_config()
    
    # 测试Bot客户端
    client_ok = test_bot_client() if config_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 检查结果:")
    print(f"   配置检查: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"   客户端测试: {'✅ 正常' if client_ok else '❌ 异常'}")
    
    if config_ok and client_ok:
        print(f"\n🎉 代理配置检查完成！")
        print(f"💡 当前状态: 测试环境模式（无代理）")
        print(f"🚀 可以正常启动服务进行测试")
    else:
        print(f"\n⚠️ 检查发现问题，请修复后重试")
    
    return 0 if (config_ok and client_ok) else 1

if __name__ == "__main__":
    sys.exit(main())
