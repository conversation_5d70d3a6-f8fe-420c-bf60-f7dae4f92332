#!/usr/bin/env python3
"""
查询真实的types=300配置数据
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from database.mongodb_connection import MongoDBConnection


async def check_real_config():
    """查询真实的配置数据"""
    app_logger.info("🔍 查询真实的types=300配置数据")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        # 连接MongoDB
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 获取集合
        collection = mongo_conn.get_collection("c_tgNotify")
        if collection is None:
            app_logger.error("❌ 无法获取c_tgNotify集合")
            return False
        
        # 查询types=300的配置
        app_logger.info("📋 查询types=300的配置")
        configs = list(collection.find({"types": 300}))
        
        if not configs:
            app_logger.info("📊 没有找到types=300的配置")
            return True
        
        app_logger.info(f"📊 找到 {len(configs)} 条types=300配置:")
        
        for i, config in enumerate(configs):
            app_logger.info(f"\n📋 配置 {i+1}:")
            app_logger.info("-" * 60)
            
            # 显示所有字段和类型
            for key, value in config.items():
                value_type = type(value).__name__
                app_logger.info(f"   {key:<20}: {value} ({value_type})")
        
        # 分析字段类型
        if configs:
            app_logger.info(f"\n🔍 字段类型分析:")
            app_logger.info("-" * 60)
            
            sample_config = configs[0]
            
            # 检查关键字段
            key_fields = ['merchantId', 'groupId', 'templateId', 'language', 'types', 
                         'gameType', 'platformId', 'gameId', 'gameWinMul', 'status']
            
            for field in key_fields:
                if field in sample_config:
                    value = sample_config[field]
                    value_type = type(value).__name__
                    app_logger.info(f"   {field:<15}: {value_type:<10} = {value}")
                else:
                    app_logger.info(f"   {field:<15}: 不存在")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


async def check_template_config():
    """查询模板配置"""
    app_logger.info("\n🔍 查询相关的模板配置")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 获取集合
        collection = mongo_conn.get_collection("c_tgTemplate")
        if collection is None:
            app_logger.error("❌ 无法获取c_tgTemplate集合")
            return False
        
        # 查询所有模板
        templates = list(collection.find({}))
        
        app_logger.info(f"📊 找到 {len(templates)} 个模板:")
        
        for i, template in enumerate(templates[:5]):  # 只显示前5个
            app_logger.info(f"\n📋 模板 {i+1}:")
            app_logger.info(f"   templateId: {template.get('templateId')}")
            app_logger.info(f"   language: {template.get('language')} ({type(template.get('language')).__name__})")
            app_logger.info(f"   title: {template.get('title')}")
            app_logger.info(f"   type: {template.get('type')}")
            app_logger.info(f"   status: {template.get('status')} ({type(template.get('status')).__name__})")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 查询模板失败: {e}")
        return False
    
    finally:
        mongo_conn.disconnect()


async def main():
    """主函数"""
    app_logger.info("🔍 查询真实配置数据")
    app_logger.info("=" * 100)
    
    # 查询配置数据
    success1 = await check_real_config()
    
    # 查询模板数据
    success2 = await check_template_config()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 查询总结:")
    app_logger.info(f"   配置查询: {'✅ 成功' if success1 else '❌ 失败'}")
    app_logger.info(f"   模板查询: {'✅ 成功' if success2 else '❌ 失败'}")


if __name__ == "__main__":
    asyncio.run(main())
