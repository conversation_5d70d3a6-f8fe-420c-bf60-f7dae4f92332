#!/usr/bin/env python3
"""
查看定时任务表结构和数据
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_scheduled_tasks():
    """分析定时任务表"""
    print("🔍 分析定时任务表 c_tgScheduledPushTasks")
    print("=" * 60)
    
    try:
        with MongoDBConnection() as mongo:
            if not mongo.is_connected():
                print("❌ MongoDB连接失败")
                return False
            
            # 获取集合
            collection = mongo.get_collection("c_tgScheduledPushTasks")
            if collection is None:
                print("❌ 无法获取集合 c_tgScheduledPushTasks")
                return False
            
            # 1. 统计信息
            total_count = collection.count_documents({})
            print(f"📊 总记录数: {total_count}")
            
            if total_count == 0:
                print("ℹ️ 表中暂无数据")
                return True
            
            # 2. 查看表结构（通过第一条记录）
            print(f"\n📋 表结构分析:")
            first_doc = collection.find_one()
            if first_doc:
                print("字段列表:")
                for key, value in first_doc.items():
                    value_type = type(value).__name__
                    if isinstance(value, str) and len(value) > 50:
                        value_preview = value[:50] + "..."
                    else:
                        value_preview = value
                    print(f"  • {key}: {value_type} = {value_preview}")
            
            # 3. 查看所有记录
            print(f"\n📄 所有定时任务记录:")
            print("-" * 60)
            
            tasks = list(collection.find())
            for i, task in enumerate(tasks, 1):
                print(f"\n任务 {i}:")
                for key, value in task.items():
                    if key == '_id':
                        print(f"  {key}: {value}")
                    elif isinstance(value, datetime):
                        print(f"  {key}: {value.strftime('%Y-%m-%d %H:%M:%S')}")
                    elif isinstance(value, dict):
                        print(f"  {key}: {json.dumps(value, ensure_ascii=False, indent=4)}")
                    elif isinstance(value, list):
                        print(f"  {key}: {json.dumps(value, ensure_ascii=False, indent=4)}")
                    else:
                        print(f"  {key}: {value}")
                print("-" * 40)
            
            # 4. 分析字段类型
            print(f"\n🔍 字段类型统计:")
            field_types = {}
            for task in tasks:
                for key, value in task.items():
                    if key not in field_types:
                        field_types[key] = set()
                    field_types[key].add(type(value).__name__)
            
            for field, types in field_types.items():
                print(f"  {field}: {', '.join(types)}")
            
            # 5. 查看状态分布
            print(f"\n📈 任务状态分布:")
            if 'status' in field_types:
                status_pipeline = [
                    {"$group": {"_id": "$status", "count": {"$sum": 1}}}
                ]
                status_stats = list(collection.aggregate(status_pipeline))
                for stat in status_stats:
                    print(f"  {stat['_id']}: {stat['count']} 个任务")
            
            # 6. 查看任务类型分布
            print(f"\n📋 任务类型分布:")
            if 'task_type' in field_types:
                type_pipeline = [
                    {"$group": {"_id": "$task_type", "count": {"$sum": 1}}}
                ]
                type_stats = list(collection.aggregate(type_pipeline))
                for stat in type_stats:
                    print(f"  {stat['_id']}: {stat['count']} 个任务")
            elif 'type' in field_types:
                type_pipeline = [
                    {"$group": {"_id": "$type", "count": {"$sum": 1}}}
                ]
                type_stats = list(collection.aggregate(type_pipeline))
                for stat in type_stats:
                    print(f"  {stat['_id']}: {stat['count']} 个任务")
            
            return True
            
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def suggest_task_structure():
    """建议定时任务结构"""
    print(f"\n💡 建议的定时任务字段结构:")
    print("-" * 60)
    
    suggested_fields = {
        "_id": "ObjectId - MongoDB自动生成的ID",
        "task_id": "String - 任务唯一标识符",
        "task_name": "String - 任务名称",
        "task_type": "String - 任务类型 (push_message, send_menu, etc.)",
        "status": "String - 任务状态 (active, paused, completed, failed)",
        "schedule": {
            "type": "String - 调度类型 (cron, interval, once)",
            "expression": "String - cron表达式或间隔时间",
            "timezone": "String - 时区"
        },
        "target": {
            "chat_type": "String - 目标类型 (private, group, channel)",
            "chat_id": "Number - 目标聊天ID",
            "user_id": "Number - 目标用户ID"
        },
        "content": {
            "message_type": "String - 消息类型 (text, photo, menu)",
            "text": "String - 消息文本",
            "image": "String - 图片URL",
            "buttons": "Array - 按钮配置"
        },
        "execution": {
            "next_run": "Date - 下次执行时间",
            "last_run": "Date - 上次执行时间",
            "run_count": "Number - 执行次数",
            "max_runs": "Number - 最大执行次数"
        },
        "created_at": "Date - 创建时间",
        "updated_at": "Date - 更新时间",
        "created_by": "String - 创建者",
        "enabled": "Boolean - 是否启用"
    }
    
    for field, description in suggested_fields.items():
        if isinstance(description, dict):
            print(f"  {field}:")
            for sub_field, sub_desc in description.items():
                print(f"    {sub_field}: {sub_desc}")
        else:
            print(f"  {field}: {description}")


def main():
    """主函数"""
    print("🚀 定时任务表分析开始")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 分析现有表结构
    success = analyze_scheduled_tasks()
    
    # 建议标准结构
    suggest_task_structure()
    
    print(f"\n" + "=" * 60)
    print("📊 分析完成")
    print("=" * 60)
    
    if success:
        print("✅ 表结构分析成功")
        print("💡 接下来可以基于现有结构实现定时任务功能")
    else:
        print("❌ 表结构分析失败")
        print("💡 请检查MongoDB连接和表名是否正确")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
