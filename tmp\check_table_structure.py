#!/usr/bin/env python3
"""
检查ea_platform_wagered_rebate_rank_log表的实际结构
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def check_table_structure():
    """检查表结构"""
    print("🔍 检查ea_platform_wagered_rebate_rank_log表结构")
    print("=" * 60)
    
    try:
        import aiomysql
        
        # 连接参数
        config = {
            'host': '*************',
            'port': 3306,
            'user': 'wingame',
            'password': 'ws82H4HRFbzjmNtD',
            'db': 'wingame',
            'charset': 'utf8mb4'
        }
        
        # 创建连接池
        pool = await aiomysql.create_pool(**config)
        
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 1. 查询表结构
                print("1. 表结构:")
                await cursor.execute("DESCRIBE ea_platform_wagered_rebate_rank_log")
                columns = await cursor.fetchall()
                
                for col in columns:
                    field_name = col[0]
                    field_type = col[1]
                    is_null = col[2]
                    key = col[3]
                    default = col[4]
                    extra = col[5]
                    
                    print(f"   {field_name:25} {field_type:20} NULL:{is_null:5} KEY:{key:5} DEFAULT:{default} {extra}")
                
                # 2. 查询示例数据
                print(f"\n2. 示例数据 (前3条):")
                await cursor.execute("SELECT * FROM ea_platform_wagered_rebate_rank_log ORDER BY id DESC LIMIT 3")
                records = await cursor.fetchall()
                
                if records:
                    # 获取列名
                    await cursor.execute("SHOW COLUMNS FROM ea_platform_wagered_rebate_rank_log")
                    column_info = await cursor.fetchall()
                    column_names = [col[0] for col in column_info]
                    
                    for i, record in enumerate(records, 1):
                        print(f"\n   记录 {i}:")
                        for j, value in enumerate(record):
                            if j < len(column_names):
                                print(f"     {column_names[j]:20}: {value}")
                
                # 3. 查询商户分布
                print(f"\n3. 商户数据分布:")
                await cursor.execute("""
                    SELECT business_no, COUNT(*) as count 
                    FROM ea_platform_wagered_rebate_rank_log 
                    GROUP BY business_no 
                    ORDER BY count DESC 
                    LIMIT 10
                """)
                merchant_stats = await cursor.fetchall()
                
                for stat in merchant_stats:
                    business_no = stat[0]
                    count = stat[1]
                    print(f"   商户 {business_no:15}: {count:5} 条记录")
                
                # 4. 查询时间范围
                print(f"\n4. 数据时间范围:")
                await cursor.execute("""
                    SELECT 
                        MIN(created_at) as earliest,
                        MAX(created_at) as latest,
                        COUNT(*) as total
                    FROM ea_platform_wagered_rebate_rank_log
                """)
                time_range = await cursor.fetchone()
                
                if time_range:
                    print(f"   最早记录: {time_range[0]}")
                    print(f"   最新记录: {time_range[1]}")
                    print(f"   总记录数: {time_range[2]}")
        
        # 关闭连接池
        pool.close()
        await pool.wait_closed()
        
        print("\n✅ 表结构检查完成")
        return True
        
    except Exception as e:
        print(f"❌ 表结构检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await check_table_structure()
    
    if success:
        print(f"\n💡 下一步:")
        print(f"   1. 根据实际表结构修改查询SQL")
        print(f"   2. 更新投注返利排行榜处理器")
        print(f"   3. 重新测试功能")
    else:
        print(f"\n⚠️ 需要检查数据库连接和表是否存在")

if __name__ == "__main__":
    asyncio.run(main())
