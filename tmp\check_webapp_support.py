#!/usr/bin/env python3
"""
检查WebApp支持情况
"""

def check_telegram_version():
    """检查telegram库版本和WebApp支持"""
    print("🔍 检查Telegram库版本和WebApp支持")
    print("=" * 50)
    
    try:
        import telegram
        print(f"✅ python-telegram-bot版本: {telegram.__version__}")
        
        # 尝试导入WebApp
        try:
            from telegram import WebApp
            print("✅ WebApp可以从telegram直接导入")
            return WebApp
        except ImportError:
            try:
                from telegram.types import WebApp
                print("✅ WebApp可以从telegram.types导入")
                return WebApp
            except ImportError:
                try:
                    from telegram._webappinfo import WebAppInfo as WebApp
                    print("✅ WebApp可以从telegram._webappinfo导入（作为WebAppInfo）")
                    return WebApp
                except ImportError:
                    print("❌ WebApp不可用，将使用替代方案")
                    
                    # 创建替代类
                    class WebApp:
                        def __init__(self, url):
                            self.url = url
                        
                        def __str__(self):
                            return f"WebApp(url='{self.url}')"
                    
                    return WebApp
    
    except ImportError as e:
        print(f"❌ 无法导入telegram库: {e}")
        return None


def test_webapp_button_creation(WebApp):
    """测试WebApp按钮创建"""
    print("\n🧪 测试WebApp按钮创建")
    print("=" * 50)
    
    try:
        from telegram import InlineKeyboardButton
        
        # 测试创建WebApp按钮
        webapp = WebApp(url="https://t.me/telegram")
        print(f"✅ WebApp对象创建成功: {webapp}")
        
        button = InlineKeyboardButton(
            text="📱 测试WebApp",
            web_app=webapp
        )
        print(f"✅ WebApp按钮创建成功: {button.text}")
        
        if hasattr(button, 'web_app') and button.web_app:
            print(f"✅ 按钮包含WebApp: {button.web_app}")
            if hasattr(button.web_app, 'url'):
                print(f"✅ WebApp URL: {button.web_app.url}")
        
        return True
        
    except Exception as e:
        print(f"❌ WebApp按钮创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议")
    print("=" * 50)
    
    print("如果WebApp按钮仍然不显示，可能的原因和解决方案：")
    print()
    print("1. 🔄 **升级python-telegram-bot库**:")
    print("   pip install --upgrade python-telegram-bot")
    print()
    print("2. 📱 **Telegram客户端限制**:")
    print("   - WebApp按钮需要较新的Telegram客户端版本")
    print("   - 在不同设备上测试（手机、桌面、网页版）")
    print()
    print("3. 🔒 **群组权限问题**:")
    print("   - Bot需要在群组中有适当权限")
    print("   - 先在私聊中测试WebApp按钮")
    print()
    print("4. 🌐 **URL限制**:")
    print("   - WebApp URL必须是HTTPS")
    print("   - 某些域名可能被Telegram限制")
    print()
    print("5. 🎯 **替代方案**:")
    print("   - 使用普通URL按钮代替WebApp")
    print("   - 在按钮文字中说明会在Telegram内打开")


def main():
    """主函数"""
    print("🚀 WebApp支持检查开始")
    
    # 检查WebApp支持
    WebApp = check_telegram_version()
    
    if WebApp:
        # 测试WebApp按钮创建
        success = test_webapp_button_creation(WebApp)
        
        if success:
            print("\n🎉 WebApp功能测试成功！")
            print("代码层面没有问题，如果在Telegram中看不到按钮，")
            print("可能是客户端或权限问题。")
        else:
            print("\n⚠️ WebApp功能测试失败！")
    
    # 提供解决方案建议
    suggest_solutions()
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except Exception as e:
        print(f"❌ 检查过程异常: {e}")
        exit(1)
