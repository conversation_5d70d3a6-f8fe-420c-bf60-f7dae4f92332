#!/usr/bin/env python3
"""
诊断Bot配置问题
检查为什么找不到商户39bac42a的Bot配置
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager
from database.mongodb_connection import MongoDBConnection


async def debug_bot_config():
    """诊断Bot配置问题"""
    print("🔍 诊断Bot配置问题")
    print("=" * 60)
    
    # 1. 初始化配置管理器
    print("1️⃣ 初始化配置管理器...")
    success = await config_manager.initialize()
    if not success:
        print("❌ 配置管理器初始化失败")
        return False
    
    print("✅ 配置管理器初始化成功")
    
    # 2. 检查缓存状态
    print("\n2️⃣ 检查缓存状态...")
    stats = config_manager.get_cache_stats()
    robot_stats = stats.get('robot_configs', {})
    
    print(f"📊 Bot配置缓存统计:")
    print(f"   总数: {robot_stats.get('total', 0)}")
    print(f"   启用: {robot_stats.get('active', 0)}")
    print(f"   最后更新: {robot_stats.get('last_update', 'N/A')}")
    
    # 3. 检查缓存内容
    print("\n3️⃣ 检查缓存内容...")
    all_configs = config_manager.get_all_robot_configs()
    print(f"📋 缓存中的Bot配置: {len(all_configs)} 个")
    
    for i, config in enumerate(all_configs, 1):
        business_no = config.get("business_no")
        bot_name = config.get("bot_name", "")
        status = config.get("status", "")
        print(f"   {i}. business_no='{business_no}', bot_name='{bot_name}', status='{status}'")
    
    # 4. 测试特定商户查询
    print("\n4️⃣ 测试特定商户查询...")
    target_business_no = "39bac42a"
    
    print(f"🔍 查找商户: '{target_business_no}'")
    
    # 使用get_bot_info方法
    bot_info = config_manager.get_bot_info(target_business_no)
    if bot_info:
        print(f"✅ get_bot_info找到配置:")
        print(f"   bot_token: {bot_info.get('bot_token', '')[:20]}...")
        print(f"   bot_name: {bot_info.get('bot_name', '')}")
        print(f"   status: {bot_info.get('status', '')}")
    else:
        print(f"❌ get_bot_info未找到配置")
    
    # 使用get_bot_token方法
    bot_token = config_manager.get_bot_token(target_business_no)
    if bot_token:
        print(f"✅ get_bot_token找到Token: {bot_token[:20]}...")
    else:
        print(f"❌ get_bot_token未找到Token")
    
    # 5. 手动检查缓存
    print("\n5️⃣ 手动检查缓存...")
    with config_manager.cache_lock:
        cache_keys = list(config_manager.robot_config_cache.keys())
        print(f"📋 缓存中的business_no列表: {cache_keys}")
        
        if target_business_no in config_manager.robot_config_cache:
            cached_config = config_manager.robot_config_cache[target_business_no]
            print(f"✅ 在缓存中找到 '{target_business_no}':")
            print(f"   完整配置: {cached_config}")
        else:
            print(f"❌ 缓存中没有 '{target_business_no}'")
            
            # 检查是否有相似的key
            similar_keys = [key for key in cache_keys if target_business_no in str(key)]
            if similar_keys:
                print(f"🔍 找到相似的key: {similar_keys}")
    
    return True


async def debug_database_direct():
    """直接检查数据库"""
    print(f"\n🗄️ 直接检查数据库")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        collection = mongo.get_collection("c_tgRobotConfig")
        if collection is None:
            print("❌ 无法获取c_tgRobotConfig集合")
            return False
        
        # 查询所有记录
        all_records = list(collection.find({}))
        print(f"📊 c_tgRobotConfig表中的记录总数: {len(all_records)}")
        
        target_business_no = "39bac42a"
        
        # 查找特定商户
        target_record = collection.find_one({"business_no": target_business_no})
        
        if target_record:
            print(f"✅ 数据库中找到商户 '{target_business_no}':")
            for key, value in target_record.items():
                if key == '_id':
                    print(f"   {key}: {value}")
                else:
                    print(f"   {key}: {value}")
        else:
            print(f"❌ 数据库中没有商户 '{target_business_no}'")
            
            # 显示所有business_no
            business_nos = [record.get("business_no") for record in all_records]
            print(f"📋 数据库中的所有business_no: {business_nos}")
            
            # 检查字段名
            if all_records:
                first_record = all_records[0]
                print(f"📋 第一条记录的字段:")
                for key in first_record.keys():
                    if key != '_id':
                        print(f"   {key}: {first_record[key]}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def debug_cache_loading():
    """调试缓存加载过程"""
    print(f"\n🔄 调试缓存加载过程")
    print("=" * 60)
    
    try:
        # 手动重新加载Bot配置
        print("🔄 手动重新加载Bot配置...")
        success = await config_manager.load_robot_config()
        
        if success:
            print("✅ 重新加载成功")
            
            # 再次检查
            target_business_no = "39bac42a"
            bot_info = config_manager.get_bot_info(target_business_no)
            
            if bot_info:
                print(f"✅ 重新加载后找到配置: {bot_info}")
            else:
                print(f"❌ 重新加载后仍未找到配置")
                
                # 显示所有缓存的business_no
                all_configs = config_manager.get_all_robot_configs()
                business_nos = [config.get("business_no") for config in all_configs]
                print(f"📋 缓存中的所有business_no: {business_nos}")
        else:
            print("❌ 重新加载失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 缓存加载调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🚀 Bot配置问题诊断")
    print("=" * 80)
    
    # 1. 诊断Bot配置
    config_ok = await debug_bot_config()
    
    # 2. 直接检查数据库
    db_ok = await debug_database_direct()
    
    # 3. 调试缓存加载
    cache_ok = await debug_cache_loading()
    
    print(f"\n" + "=" * 80)
    print("📊 诊断结果汇总:")
    print(f"   🔧 配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
    print(f"   🗄️ 数据库检查: {'✅ 通过' if db_ok else '❌ 失败'}")
    print(f"   🔄 缓存加载: {'✅ 通过' if cache_ok else '❌ 失败'}")
    
    print(f"\n💡 可能的问题:")
    print(f"   1. 数据库中business_no字段值与期望不符")
    print(f"   2. Bot配置的status字段不是'active'")
    print(f"   3. 缓存加载时字段映射有问题")
    print(f"   4. 字符串类型或编码问题")
    
    print("=" * 80)
    
    return 0 if (config_ok and db_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 诊断被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 诊断过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
