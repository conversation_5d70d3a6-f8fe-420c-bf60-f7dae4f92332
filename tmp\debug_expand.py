#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🔍 调试展开逻辑")
    
    translator = TranslationManager()
    
    # 简单测试
    template = "Parabéns {test1} [repeat]pelo depósito – {Currency2}{Amount3} [/repeat] {gg8}"
    params = ["user_name_li", [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]], "thank_you_message"]
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    
    result = translator.render_template(template, params, "2")
    print(f"结果: {result}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
