#!/usr/bin/env python3
"""
调试语言映射问题
检查数据库中的language字段类型和翻译配置的匹配
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection
from common.translation_manager import translation_manager


def check_database_language_types():
    """检查数据库中的language字段类型"""
    print("🔍 检查数据库中的language字段类型")
    print("=" * 50)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 检查notify表
        notify_collection = mongo.get_collection("c_tgNotify")
        if notify_collection is not None:
            notify_records = list(notify_collection.find({}))
            print(f"📊 c_tgNotify表记录数: {len(notify_records)}")

            for i, record in enumerate(notify_records, 1):
                language_value = record.get("language")
                language_type = type(language_value).__name__
                print(f"   {i}. ID={record.get('id')}, language={language_value} (类型: {language_type})")

        # 检查robot配置表
        robot_collection = mongo.get_collection("c_tgRobotConfig")
        if robot_collection is not None:
            robot_records = list(robot_collection.find({}))
            print(f"\n📊 c_tgRobotConfig表记录数: {len(robot_records)}")

            for i, record in enumerate(robot_records, 1):
                business_no = record.get("business_no")
                print(f"   {i}. business_no={business_no}")
                # 显示关键字段和类型
                key_fields = ["business_no", "botToken", "botName", "botId"]
                for key in key_fields:
                    if key in record:
                        value = record[key]
                        value_type = type(value).__name__
                        print(f"      {key}: {value} ({value_type})")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_translation_config():
    """检查翻译配置"""
    print(f"\n🌐 检查翻译配置")
    print("=" * 50)
    
    try:
        # 加载翻译配置
        success = translation_manager.load_translations()
        print(f"📋 翻译配置加载: {'✅ 成功' if success else '❌ 失败'}")
        
        if success:
            print(f"📊 语言映射数量: {len(translation_manager.language_mapping)}")
            print(f"📊 默认语言: {translation_manager.default_language}")
            
            print(f"\n📋 语言映射详情:")
            for lang_id, lang_name in translation_manager.language_mapping.items():
                lang_id_type = type(lang_id).__name__
                print(f"   {lang_id} ({lang_id_type}): {lang_name}")
            
            # 测试特定语言ID
            test_ids = [1, 2, "1", "2"]
            print(f"\n🔍 测试语言ID转换:")
            
            for test_id in test_ids:
                test_id_type = type(test_id).__name__
                result = translation_manager.get_language_id(test_id)
                lang_name = translation_manager.language_mapping.get(result, f"Unknown({result})")
                print(f"   输入: {test_id} ({test_id_type}) -> 输出: {result} -> 名称: {lang_name}")
        
        return success
        
    except Exception as e:
        print(f"❌ 检查翻译配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_language_conversion():
    """测试语言转换逻辑"""
    print(f"\n🔧 测试语言转换逻辑")
    print("=" * 50)
    
    # 模拟数据库中的不同类型
    test_cases = [
        {"db_value": 1, "description": "数据库int类型1"},
        {"db_value": 2, "description": "数据库int类型2"},
        {"db_value": "1", "description": "数据库string类型'1'"},
        {"db_value": "2", "description": "数据库string类型'2'"},
    ]
    
    for case in test_cases:
        db_value = case["db_value"]
        description = case["description"]
        
        print(f"\n📝 {description}")
        print(f"   原始值: {db_value} (类型: {type(db_value).__name__})")
        
        # 当前的转换逻辑
        language_id_str = translation_manager.get_language_id(db_value)
        language_name = translation_manager.language_mapping.get(language_id_str, f"Unknown({language_id_str})")
        
        print(f"   转换后: {language_id_str}")
        print(f"   语言名: {language_name}")
        
        # 检查是否在映射中
        str_value = str(db_value)
        in_mapping = str_value in translation_manager.language_mapping
        print(f"   在映射中: {in_mapping}")
        
        if not in_mapping:
            print(f"   ⚠️ 语言ID {str_value} 不在映射中，使用默认语言 {translation_manager.default_language}")


def main():
    """主函数"""
    print("🚀 语言映射调试")
    print("=" * 60)
    
    # 1. 检查数据库类型
    db_ok = check_database_language_types()
    
    # 2. 检查翻译配置
    config_ok = check_translation_config()
    
    # 3. 测试转换逻辑
    if config_ok:
        test_language_conversion()
    
    print(f"\n" + "=" * 60)
    print("📊 调试结果:")
    print(f"   🗄️ 数据库检查: {'✅ 成功' if db_ok else '❌ 失败'}")
    print(f"   🌐 配置检查: {'✅ 成功' if config_ok else '❌ 失败'}")
    
    print(f"\n💡 可能的问题:")
    print(f"   1. 数据库中language字段是int，但翻译配置key是string")
    print(f"   2. 语言映射中缺少对应的语言ID")
    print(f"   3. get_language_id方法的兜底逻辑有问题")
    
    print("=" * 60)
    
    return 0 if (db_ok and config_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 调试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
