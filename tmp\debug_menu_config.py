#!/usr/bin/env python3
"""
调试菜单配置问题
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from pusher.menu.menu_config_manager import menu_config_manager
from pusher.menu.menu_builder import menu_builder


def debug_menu_config():
    """调试菜单配置"""
    print("🔍 调试菜单配置")
    print("=" * 60)
    
    bot_token = "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw"
    group_id = -1002316158105
    
    print(f"🤖 Bot Token: {bot_token[:10]}...")
    print(f"👥 群组ID: {group_id}")
    
    # 1. 检查菜单模板获取
    print(f"\n1. 检查菜单模板获取:")
    template_name = menu_config_manager.get_menu_template(bot_token, "group", group_id)
    print(f"   获取到的模板名称: {template_name}")
    
    # 2. 检查Bot配置
    print(f"\n2. 检查Bot配置:")
    bot_info = menu_config_manager.get_bot_info(bot_token)
    print(f"   Bot信息: {bot_info}")
    
    # 3. 检查群组配置
    print(f"\n3. 检查群组配置:")
    group_info = menu_config_manager.get_group_info(bot_token, group_id)
    print(f"   群组信息: {group_info}")
    
    # 4. 检查菜单加载
    print(f"\n4. 检查菜单加载:")
    menu_data = menu_builder.get_menu_for_context(bot_token, "group", group_id)
    if menu_data:
        print(f"   ✅ 菜单加载成功")
        print(f"   📋 标题: {menu_data.get('title')}")
        print(f"   🖼️ 图片: {menu_data.get('image')}")
        print(f"   🔘 按钮行数: {len(menu_data.get('buttons', []))}")
        
        buttons = menu_data.get('buttons', [])
        for i, row in enumerate(buttons):
            print(f"   第{i+1}行按钮:")
            for j, button in enumerate(row):
                print(f"     按钮{j+1}: {button}")
    else:
        print(f"   ❌ 菜单加载失败")
    
    # 5. 检查直接加载模板
    print(f"\n5. 检查直接加载模板:")
    direct_menu = menu_builder.load_menu_template("custom_test_group")
    if direct_menu:
        print(f"   ✅ 直接加载成功")
        print(f"   📋 标题: {direct_menu.get('title')}")
        print(f"   🔘 按钮数: {len(direct_menu.get('buttons', []))}")
    else:
        print(f"   ❌ 直接加载失败")
    
    return template_name, menu_data


def main():
    """主函数"""
    print("🚀 菜单配置调试开始")
    
    try:
        template_name, menu_data = debug_menu_config()
        
        print(f"\n" + "=" * 60)
        print("📊 调试结果汇总")
        print("=" * 60)
        
        if template_name == "custom_test_group" and menu_data:
            print("✅ 配置正确，应该显示4个按钮的定制菜单")
            print("   如果Bot中显示的不是这个菜单，可能需要:")
            print("   1. 重启Bot服务")
            print("   2. 检查Bot是否使用了正确的配置文件")
            print("   3. 检查菜单缓存是否需要清理")
        else:
            print("❌ 配置有问题")
            print(f"   期望模板: custom_test_group")
            print(f"   实际模板: {template_name}")
            print(f"   菜单数据: {'存在' if menu_data else '不存在'}")
        
    except Exception as e:
        print(f"❌ 调试过程异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
