#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🔍 调试特殊参数替换:")
    
    translator = TranslationManager()
    
    # 测试活动名替换
    template = "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
    params = ["张三", "18000", "1", "100"]
    language = "1"
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    print(f"语言: {language}")
    print("\n开始渲染...")
    
    result = translator.render_template(template, params, language)
    print(f"\n最终结果: {result}")
    
    # 单独测试特殊参数处理
    print(f"\n🧪 单独测试特殊参数处理:")
    processed = translator._process_special_params(template, params, language)
    print(f"处理后参数: {processed}")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
