#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🔍 调试变量替换:")
    
    translator = TranslationManager()
    
    # 测试变量替换
    template = "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
    params = ['张三', '每日签到活动', '金币', '100']
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    
    # 单独测试变量替换
    print(f"\n🧪 单独测试变量替换:")
    result = translator._replace_variables(template, params)
    print(f"替换结果: {result}")
    
    # 测试正则表达式匹配
    import re
    var_pattern = r'\{([a-zA-Z_]+)(\d+)\}'
    matches = re.findall(var_pattern, template)
    print(f"\n🔍 正则匹配结果: {matches}")
    
    # 手动测试每个匹配
    for i, (prefix, index) in enumerate(matches):
        param_index = int(index) - 1
        print(f"匹配 {i+1}: {prefix}{index} -> 参数索引 {param_index}")
        if 0 <= param_index < len(params):
            print(f"  参数值: {params[param_index]}")
        else:
            print(f"  参数不足")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
