#!/usr/bin/env python3
"""
调试WebApp按钮问题
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from pusher.menu.menu_builder import menu_builder
from telegram import InlineKeyboardButton, InlineKeyboardMarkup
try:
    from telegram._webappinfo import WebAppInfo as WebApp
except ImportError:
    class WebApp:
        def __init__(self, url):
            self.url = url


def test_webapp_button_creation():
    """测试WebApp按钮创建"""
    print("🔍 调试WebApp按钮创建")
    print("=" * 50)
    
    # 直接测试WebApp按钮创建
    try:
        print("1. 测试直接创建WebApp按钮...")
        webapp_button = InlineKeyboardButton(
            text="📱 直接WebApp",
            web_app=WebApp(url="https://t.me/telegram")
        )
        print(f"   ✅ 直接创建成功: {webapp_button.text}")
        print(f"   🔗 WebApp URL: {webapp_button.web_app.url}")
        
    except Exception as e:
        print(f"   ❌ 直接创建失败: {e}")
        return False
    
    # 测试通过menu_builder创建
    try:
        print("\n2. 测试通过menu_builder创建...")
        test_buttons = [
            [
                {"text": "🌐 URL按钮", "url": "https://www.google.com"},
                {"text": "📱 WebApp按钮", "web_app": {"url": "https://t.me/telegram"}}
            ]
        ]
        
        keyboard = menu_builder.create_inline_keyboard(test_buttons)
        print(f"   ✅ 键盘创建成功，行数: {len(keyboard.inline_keyboard)}")
        
        # 检查第一行按钮
        first_row = keyboard.inline_keyboard[0]
        print(f"   📊 第一行按钮数: {len(first_row)}")
        
        for i, button in enumerate(first_row):
            print(f"   按钮{i+1}: {button.text}")
            if button.url:
                print(f"     类型: URL, 链接: {button.url}")
            elif button.web_app:
                print(f"     类型: WebApp, 链接: {button.web_app.url}")
            elif button.callback_data:
                print(f"     类型: 回调, 数据: {button.callback_data}")
            else:
                print(f"     类型: 未知")
        
        return True
        
    except Exception as e:
        print(f"   ❌ menu_builder创建失败: {e}")
        return False


def test_menu_loading():
    """测试菜单加载"""
    print("\n🔍 调试菜单加载")
    print("=" * 50)
    
    try:
        print("1. 加载custom_test_group菜单...")
        menu_data = menu_builder.load_menu_template("custom_test_group")
        
        if not menu_data:
            print("   ❌ 菜单加载失败")
            return False
        
        print(f"   ✅ 菜单加载成功")
        print(f"   📋 标题: {menu_data.get('title')}")
        
        buttons = menu_data.get('buttons', [])
        print(f"   🔘 按钮行数: {len(buttons)}")
        
        for i, row in enumerate(buttons):
            print(f"\n   第{i+1}行按钮:")
            for j, button in enumerate(row):
                print(f"     按钮{j+1}: {button}")
        
        print("\n2. 测试创建键盘...")
        keyboard = menu_builder.create_inline_keyboard(buttons)
        print(f"   ✅ 键盘创建成功")
        
        # 详细检查每个按钮
        for i, row in enumerate(keyboard.inline_keyboard):
            print(f"\n   实际第{i+1}行按钮:")
            for j, button in enumerate(row):
                print(f"     按钮{j+1}: {button.text}")
                if hasattr(button, 'url') and button.url:
                    print(f"       URL: {button.url}")
                if hasattr(button, 'web_app') and button.web_app:
                    print(f"       WebApp: {button.web_app.url}")
                if hasattr(button, 'callback_data') and button.callback_data:
                    print(f"       回调: {button.callback_data}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 菜单测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_webapp_limitations():
    """测试WebApp限制"""
    print("\n🔍 检查WebApp限制")
    print("=" * 50)
    
    print("📝 WebApp按钮的已知限制:")
    print("1. WebApp按钮在某些Telegram客户端版本中可能不显示")
    print("2. WebApp按钮在群组中可能有限制（需要Bot是管理员）")
    print("3. WebApp按钮需要HTTPS链接")
    print("4. 某些URL可能不被Telegram支持作为WebApp")
    
    print("\n🔧 建议的测试方法:")
    print("1. 在私聊中测试WebApp按钮")
    print("2. 确保Bot在群组中是管理员")
    print("3. 使用不同的Telegram客户端测试")
    print("4. 检查Telegram客户端版本是否支持WebApp")


def main():
    """主函数"""
    print("🚀 WebApp按钮调试开始")
    
    # 测试1: WebApp按钮创建
    result1 = test_webapp_button_creation()
    
    # 测试2: 菜单加载
    result2 = test_menu_loading()
    
    # 测试3: WebApp限制说明
    test_webapp_limitations()
    
    # 结果汇总
    print("\n" + "=" * 50)
    print("📊 调试结果汇总")
    print("=" * 50)
    
    if result1 and result2:
        print("✅ 代码层面没有问题，WebApp按钮应该可以正常创建")
        print("\n💡 如果在Telegram中看不到WebApp按钮，可能的原因:")
        print("1. 🔒 群组权限问题 - Bot需要是管理员")
        print("2. 📱 客户端版本问题 - 需要较新的Telegram版本")
        print("3. 🌐 URL问题 - 某些URL可能不被支持")
        print("4. 🎯 聊天类型问题 - 在私聊中测试看是否显示")
        
        print("\n🔧 建议的解决方案:")
        print("1. 先在私聊中测试 /start，看WebApp按钮是否显示")
        print("2. 确保Bot在群组中有管理员权限")
        print("3. 尝试更换WebApp URL为其他HTTPS链接")
        print("4. 使用最新版本的Telegram客户端")
        
    else:
        print("❌ 代码层面有问题，需要修复")
    
    return 0


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ 调试异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
