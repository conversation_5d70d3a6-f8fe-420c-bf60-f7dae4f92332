#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("🎉 特殊参数替换功能测试")
    print("=" * 50)
    
    translator = TranslationManager()
    
    # 测试用例
    test_cases = [
        {
            "name": "活动名+币种替换 (中文)",
            "template": "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}",
            "params": ["张三", "18000", "1", "100"],
            "language": "1",
            "expected_contains": ["张三", "每日签到活动", "金币", "100"]
        },
        {
            "name": "活动名+币种替换 (英文)",
            "template": "Congratulations {玩家ID1} won {币种3}{金额4} in {活动名2}",
            "params": ["John", "19000", "2", "200"],
            "language": "2",
            "expected_contains": ["<PERSON>", "Recharge Bonus", "Diamond", "200"]
        },
        {
            "name": "游戏名替换",
            "template": "玩家 {玩家名1} 在 {游戏名2} 中获得大奖！",
            "params": ["王五", "1001"],
            "language": "1",
            "expected_contains": ["王五", "老虎机"]
        },
        {
            "name": "未知ID保持原值",
            "template": "恭喜 {玩家ID1} 在{活动名2} 获得奖励",
            "params": ["测试玩家", "99999"],
            "language": "1",
            "expected_contains": ["测试玩家", "99999"]
        }
    ]
    
    success_count = 0
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['name']}")
        print(f"   模板: {case['template']}")
        print(f"   参数: {case['params']}")
        print(f"   语言: {case['language']}")
        
        try:
            result = translator.render_template(
                case['template'], 
                case['params'], 
                case['language']
            )
            
            print(f"   结果: {result}")
            
            # 检查期望内容
            all_found = True
            for expected in case['expected_contains']:
                if expected in result:
                    print(f"   ✅ 包含: {expected}")
                else:
                    print(f"   ❌ 缺失: {expected}")
                    all_found = False
            
            if all_found:
                print(f"   🎉 测试通过")
                success_count += 1
            else:
                print(f"   ⚠️ 测试部分失败")
                
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
    
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过")
    
    if success_count == len(test_cases):
        print(f"🎉 所有测试通过！特殊参数替换功能正常工作")
        print(f"💡 功能特点:")
        print(f"   • 支持活动名、游戏名、币种等关键字自动替换")
        print(f"   • 根据当前语言选择对应翻译")
        print(f"   • 未找到映射时保持原值")
        print(f"   • 支持中文占位符")
    else:
        print(f"⚠️ 部分测试失败，需要进一步调试")
    
    print("=" * 50)
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
