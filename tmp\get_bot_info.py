#!/usr/bin/env python3
"""
获取Bot信息
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import settings
from telegram import Bot


async def get_bot_info():
    """获取Bot信息"""
    try:
        bot_token = settings.bot_token
        print(f"🤖 Bot Token: {bot_token[:10]}...")
        
        # 创建Bot实例
        if settings.use_proxy:
            from telegram.ext import Application
            app = Application.builder().token(bot_token).proxy_url(settings.proxy_url).build()
            bot = app.bot
            print(f"🌐 使用代理: {settings.proxy_url}")
        else:
            bot = Bot(token=bot_token)
            print("🌐 使用直连")
        
        # 获取Bot信息
        me = await bot.get_me()
        
        print(f"\n📋 Bot信息:")
        print(f"🤖 Bot名称: {me.first_name}")
        print(f"👤 用户名: @{me.username}")
        print(f"🆔 Bot ID: {me.id}")
        print(f"🔗 Bot链接: https://t.me/{me.username}")
        
        # 构建WebApp短链接
        webapp_short_link = f"https://t.me/{me.username}/webapp"
        print(f"\n📱 WebApp短链接:")
        print(f"🔗 短链接: {webapp_short_link}")
        print(f"💡 这个链接需要在BotFather中注册WebApp才能工作")
        
        return me.username
        
    except Exception as e:
        print(f"❌ 获取Bot信息失败: {e}")
        return None


async def main():
    """主函数"""
    print("🚀 获取Bot信息")
    print("=" * 50)
    
    username = await get_bot_info()
    
    if username:
        print(f"\n" + "=" * 50)
        print("📋 WebApp配置建议")
        print("=" * 50)
        
        print(f"1. 在BotFather中为 @{username} 配置WebApp")
        print(f"2. 使用短链接: https://t.me/{username}/webapp")
        print(f"3. 在菜单中使用普通URL按钮指向这个短链接")
        print(f"4. 这样可以在群组中以WebApp方式打开")
        
        print(f"\n🔧 菜单按钮配置:")
        print(f'{{\"text\": \"📱 Telegram WebApp\", \"url\": \"https://t.me/{username}/webapp\"}}')
        
    else:
        print("❌ 无法获取Bot信息")


if __name__ == "__main__":
    asyncio.run(main())
