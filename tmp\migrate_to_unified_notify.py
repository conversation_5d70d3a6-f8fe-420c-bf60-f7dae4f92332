#!/usr/bin/env python3
"""
统一通知表迁移方案
将c_tgScheduledPushTasks的功能合并到c_tgNotify表中
"""
import sys
from pathlib import Path
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection


def analyze_merge_feasibility():
    """分析合并可行性"""
    print("🔍 分析两张表的合并可行性")
    print("=" * 60)
    
    try:
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取两个集合
        tasks_collection = mongo.get_collection("c_tgScheduledPushTasks")
        notify_collection = mongo.get_collection("c_tgNotify")
        
        if tasks_collection is None or notify_collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 分析tasks表数据
        print("📋 c_tgScheduledPushTasks 表数据:")
        tasks_data = list(tasks_collection.find())
        for task in tasks_data:
            print(f"  任务ID: {task.get('notifyId')}")
            print(f"  消息: {task.get('messageText')}")
            print(f"  Bot Token: {task.get('botToken', '')[:20]}...")
            print(f"  频率: {task.get('sendFrequency')}")
            print(f"  时间: {task.get('sendTime')}")
            print(f"  目标: {task.get('channelId')}")
            print("-" * 30)
        
        # 分析notify表数据
        print("\n📋 c_tgNotify 表数据:")
        notify_data = list(notify_collection.find())
        for notify in notify_data:
            print(f"  通知ID: {notify.get('id')}")
            print(f"  名称: {notify.get('tgName')}")
            print(f"  类型: {notify.get('notifyType')} (1=实时, 2=定时)")
            print(f"  消息: {notify.get('text')}")
            print(f"  目标: {notify.get('notifyTarget')}")
            if notify.get('notifyType') == 2:  # 定时通知
                print(f"  时间: {notify.get('pushTimeString')}")
                print(f"  周期: {notify.get('cycle')}")
            print("-" * 30)
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def suggest_unified_schema():
    """建议统一的表结构"""
    print("\n💡 建议的统一 c_tgNotify 表结构:")
    print("=" * 60)
    
    unified_schema = {
        # 基础字段 (现有)
        "_id": "ObjectId - MongoDB ID",
        "id": "int - 通知ID",
        "business_no": "str - 业务编号",
        "tgName": "str - 通知名称",
        
        # 通知类型和内容 (现有)
        "notifyType": "int - 通知类型 (1=实时推送, 2=定时推送)",
        "text": "str - 消息文本",
        "msgBanner": "str - 消息图片URL",
        "notifyTarget": "list - 目标聊天ID数组",
        
        # 链接配置 (现有但需扩展)
        "jumpLink": "str - 外部链接",
        "tgLink": "str - TG内部链接",
        "urlLabel": "str - 链接按钮文本 (新增)",
        "urlType": "str - 链接类型 browser/webapp (新增)",
        
        # 定时配置 (现有)
        "pushTimeString": "str - 推送时间 HH:MM:SS",
        "pushTime": "int - 推送时间毫秒 (可选)",
        "cycle": "list - 星期周期 [1-7]",
        
        # 定时扩展 (新增)
        "sendFrequency": "str - 发送频率 daily/weekly/monthly (新增)",
        "monthDays": "list - 月日配置 [1-31] (新增)",
        
        # Bot配置 (新增)
        "botToken": "str - Bot令牌 (新增)",
        
        # 实时推送配置 (现有)
        "types": "list - 触发类型数组",
        
        # 业务字段 (现有)
        "currencyId": "int - 货币ID",
        "rewardType": "int - 奖励类型", 
        "turnoverMul": "int - 流水倍数",
        "amount": "float - 金额",
        
        # 状态和时间 (现有)
        "open": "bool - 是否启用",
        "createTime": "int64 - 创建时间",
        "updateTime": "int64 - 更新时间"
    }
    
    print("📋 完整字段列表:")
    for field, description in unified_schema.items():
        print(f"  {field}: {description}")
    
    print(f"\n🔧 需要添加的新字段:")
    new_fields = [
        "urlLabel", "urlType", "sendFrequency", 
        "monthDays", "botToken"
    ]
    for field in new_fields:
        print(f"  • {field}: {unified_schema[field]}")


def generate_migration_script():
    """生成迁移脚本"""
    print(f"\n🚀 生成数据迁移脚本:")
    print("=" * 60)
    
    migration_script = '''
# MongoDB 迁移脚本
# 为 c_tgNotify 表添加新字段

# 1. 为所有记录添加缺失的字段
db.c_tgNotify.updateMany(
    {},
    {
        $set: {
            "urlLabel": "",           // 链接按钮文本
            "urlType": "browser",     // 链接类型
            "sendFrequency": "weekly", // 发送频率 (定时通知默认weekly)
            "monthDays": [],          // 月日配置
            "botToken": ""            // Bot令牌 (需要手动配置)
        }
    }
)

# 2. 为定时通知(notifyType=2)设置正确的sendFrequency
db.c_tgNotify.updateMany(
    { "notifyType": 2 },
    {
        $set: {
            "sendFrequency": "weekly"
        }
    }
)

# 3. 为实时通知(notifyType=1)清空定时相关字段
db.c_tgNotify.updateMany(
    { "notifyType": 1 },
    {
        $set: {
            "pushTimeString": "",
            "pushTime": 0,
            "cycle": [],
            "sendFrequency": ""
        }
    }
)
'''
    
    print(migration_script)


def create_unified_scheduler():
    """创建统一的调度器配置"""
    print(f"\n⚙️ 统一调度器配置:")
    print("=" * 60)
    
    config = {
        "scheduler": {
            "table_name": "c_tgNotify",
            "check_interval": 60,  # 秒
            "query_conditions": {
                "scheduled_tasks": {
                    "notifyType": 2,  # 定时推送
                    "open": True      # 启用状态
                },
                "realtime_templates": {
                    "notifyType": 1,  # 实时推送
                    "open": True      # 启用状态
                }
            },
            "field_mapping": {
                "task_id": "id",
                "task_name": "tgName", 
                "message_text": "text",
                "image_url": "msgBanner",
                "target_chats": "notifyTarget",
                "send_time": "pushTimeString",
                "frequency": "sendFrequency",
                "week_days": "cycle",
                "month_days": "monthDays",
                "external_url": "jumpLink",
                "internal_url": "tgLink",
                "url_label": "urlLabel",
                "url_type": "urlType",
                "bot_token": "botToken",
                "enabled": "open"
            }
        }
    }
    
    print(json.dumps(config, ensure_ascii=False, indent=2))


def main():
    """主函数"""
    print("🚀 统一通知表迁移方案")
    print(f"⏰ 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 分析合并可行性
    success = analyze_merge_feasibility()
    
    # 2. 建议统一表结构
    suggest_unified_schema()
    
    # 3. 生成迁移脚本
    generate_migration_script()
    
    # 4. 创建统一调度器配置
    create_unified_scheduler()
    
    print(f"\n" + "=" * 60)
    print("📊 迁移方案分析完成")
    print("=" * 60)
    
    if success:
        print("✅ 合并方案可行")
        print("💡 建议步骤:")
        print("1. 执行MongoDB迁移脚本添加新字段")
        print("2. 手动配置botToken字段")
        print("3. 修改调度器使用c_tgNotify表")
        print("4. 测试定时和实时推送功能")
        print("5. 确认无误后删除c_tgScheduledPushTasks表")
    else:
        print("❌ 需要检查数据库连接")
    
    return 0 if success else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 分析被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 分析过程中发生异常: {e}")
        sys.exit(1)
