#!/usr/bin/env python3
"""
快速测试脚本
用于快速验证模板消息API的基本功能
"""
import requests
import json
import time


def test_api_basic():
    """基本API测试"""
    print("🚀 快速API测试")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # 1. 健康检查
    print("1️⃣ 健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ API服务正常")
        else:
            print(f"   ❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"   ❌ 连接失败: {e}")
        print("   💡 请先启动服务: python start_unified_server.py")
        return False
    
    # 2. 配置状态
    print("2️⃣ 配置状态...")
    try:
        response = requests.get(f"{base_url}/api/config-status", timeout=5)
        if response.status_code == 200:
            result = response.json()
            data = result.get("data", {})
            print(f"   ✅ 配置正常")
            print(f"      Bot配置: {data.get('robot_configs', {}).get('total', 0)} 个")
            print(f"      通知配置: {data.get('notify_configs', {}).get('total', 0)} 个")
        else:
            print(f"   ❌ 配置状态异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
    
    # 3. 模板消息预览
    print("3️⃣ 模板消息预览...")
    try:
        preview_data = {
            "business_no": "39bac42a",
            "type": "chat",
            "params": ["user_name_zhang", ["game_a"], ["100"], "thank_you_message"]
        }
        
        response = requests.post(
            f"{base_url}/api/realtime-push/template/preview",
            json=preview_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("   ✅ 预览成功")
                preview = result.get("data", {})
                print(f"      模板ID: {preview.get('template_id')}")
                print(f"      模板名称: {preview.get('template_name')}")
                print(f"      语言: {preview.get('language')}")
                print(f"      渲染结果: {preview.get('rendered_message', '')[:100]}...")
            else:
                print(f"   ❌ 预览失败: {result.get('message')}")
        else:
            print(f"   ❌ 预览请求失败: {response.status_code}")
            print(f"      错误: {response.text}")
    except Exception as e:
        print(f"   ❌ 预览测试失败: {e}")
    
    # 4. 数字type测试
    print("4️⃣ 数字type测试...")
    try:
        preview_data = {
            "business_no": "39bac42a",
            "type": "18000",
            "params": ["user_name_zhang"]
        }
        
        response = requests.post(
            f"{base_url}/api/realtime-push/template/preview",
            json=preview_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("   ✅ 数字type匹配成功")
                preview = result.get("data", {})
                print(f"      匹配到配置: {preview.get('template_name')}")
            else:
                print(f"   ❌ 数字type匹配失败: {result.get('message')}")
        else:
            print(f"   ❌ 数字type请求失败: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 数字type测试失败: {e}")
    
    # 5. 错误测试
    print("5️⃣ 错误处理测试...")
    try:
        error_data = {
            "business_no": "nonexistent",
            "type": "chat",
            "params": ["test"]
        }
        
        response = requests.post(
            f"{base_url}/api/realtime-push/template/preview",
            json=error_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if not result.get("success"):
                print("   ✅ 错误处理正常")
                print(f"      错误信息: {result.get('message')}")
            else:
                print("   ⚠️ 应该返回错误但成功了")
        else:
            print(f"   ✅ 正确返回错误状态: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 错误测试失败: {e}")
    
    print("\n✅ 快速测试完成")
    print("💡 如需详细测试，请使用: test_template_message_api.http")
    return True


if __name__ == "__main__":
    test_api_basic()
