#!/usr/bin/env python3
import sys
from pathlib import Path
from datetime import datetime

sys.path.insert(0, str(Path(__file__).parent))

from database.mongodb_connection import MongoDBConnection

# 连接MongoDB
mongo = MongoDBConnection()
mongo.connect()

# 计算新时间
current_time = datetime.now()
new_time = current_time.replace(second=0, microsecond=0)
new_time = new_time.replace(minute=new_time.minute + 2)
new_time_str = new_time.strftime("%H:%M:00")

print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
print(f"新执行时间: {new_time_str}")

# 更新任务时间
collection = mongo.get_collection("c_tgScheduledPushTasks")
result = collection.update_one(
    {"taskType": 500, "enabled": True},
    {"$set": {"scheduleTimes": [new_time_str]}}
)

print(f"更新结果: {result.modified_count} 条记录")
print(f"请等待 {new_time_str} 观察自动执行")

mongo.disconnect()
