#!/usr/bin/env python3
"""
设置周期性推送配置数据
在MongoDB中创建types=300的测试配置
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from database.mongodb_connection import MongoDBConnection


async def setup_periodic_push_configs():
    """设置周期性推送配置"""
    app_logger.info("🔧 设置周期性推送配置")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        # 连接MongoDB
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 测试配置数据
        test_configs = [
            {
                "merchantId": 1001,
                "groupId": -1002316158105,  # 测试群组ID
                "templateId": "periodic_win_template_1",
                "language": 1,  # 英语
                "types": 300,   # 周期性推送类型
                "gameType": 102,      # 游戏类型
                "platformId": 3,      # 平台ID
                "gameId": 300003,     # 游戏ID
                "gameWinMul": 5.0,    # 获奖倍数 >= 5倍
                "status": 1,          # 启用状态
                "createTime": 1720876800,  # 2024-07-13
                "updateTime": 1720876800,
                "description": "测试周期性推送 - 游戏获奖5倍以上推送"
            },
            {
                "merchantId": 1002,
                "groupId": -1002316158105,  # 测试群组ID
                "templateId": "periodic_win_template_2",
                "language": 2,  # 葡萄牙语
                "types": 300,   # 周期性推送类型
                "gameType": 102,      # 游戏类型
                "platformId": 3,      # 平台ID
                "gameId": None,       # 不限制游戏ID
                "gameWinMul": 10.0,   # 获奖倍数 >= 10倍
                "status": 1,          # 启用状态
                "createTime": 1720876800,
                "updateTime": 1720876800,
                "description": "测试周期性推送 - 游戏获奖10倍以上推送"
            },
            {
                "merchantId": 1003,
                "groupId": -1002316158105,  # 测试群组ID
                "templateId": "periodic_win_template_3",
                "language": 12, # 繁体中文
                "types": 300,   # 周期性推送类型
                "gameType": None,     # 不限制游戏类型
                "platformId": None,   # 不限制平台ID
                "gameId": None,       # 不限制游戏ID
                "gameWinMul": 20.0,   # 获奖倍数 >= 20倍
                "status": 1,          # 启用状态
                "createTime": 1720876800,
                "updateTime": 1720876800,
                "description": "测试周期性推送 - 任意游戏获奖20倍以上推送"
            }
        ]
        
        # 获取集合
        collection = mongo_conn.get_collection("c_tgNotify")
        if collection is None:
            app_logger.error("❌ 无法获取c_tgNotify集合")
            return False

        # 清除现有的types=300配置
        app_logger.info("🗑️ 清除现有的types=300配置")
        delete_result = collection.delete_many({"types": 300})
        app_logger.info(f"   删除了 {delete_result.deleted_count} 条现有配置")

        # 插入新配置
        app_logger.info("📝 插入新的周期性推送配置")
        for i, config in enumerate(test_configs):
            result = collection.insert_one(config)
            app_logger.info(f"   配置 {i+1}: merchantId={config['merchantId']}, "
                          f"gameWinMul={config['gameWinMul']}, "
                          f"插入ID={result.inserted_id}")

        # 验证插入结果
        app_logger.info("🔍 验证插入结果")
        configs = list(collection.find({"types": 300}))
        app_logger.info(f"📊 共找到 {len(configs)} 条types=300配置:")
        
        for config in configs:
            app_logger.info(f"   merchantId={config.get('merchantId')}, "
                          f"gameType={config.get('gameType')}, "
                          f"platformId={config.get('platformId')}, "
                          f"gameId={config.get('gameId')}, "
                          f"gameWinMul={config.get('gameWinMul')}")
        
        app_logger.info("✅ 周期性推送配置设置完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 设置配置失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        mongo_conn.disconnect()


async def setup_test_templates():
    """设置测试模板"""
    app_logger.info("📝 设置测试模板")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        # 测试模板数据
        test_templates = [
            {
                "templateId": "periodic_win_template_1",
                "language": 1,  # 英语
                "title": "🎉 Big Win Alert!",
                "content": "🎊 Congratulations! Player {arg1} just won {arg2}x their bet amount of {arg3} in {arg4}! Total winnings: {arg5}! 🎊",
                "type": "periodic_win",
                "status": 1,
                "createTime": 1720876800,
                "updateTime": 1720876800
            },
            {
                "templateId": "periodic_win_template_2", 
                "language": 2,  # 葡萄牙语
                "title": "🎉 Grande Vitória!",
                "content": "🎊 Parabéns! O jogador {arg1} acabou de ganhar {arg2}x o valor da aposta de {arg3} em {arg4}! Ganhos totais: {arg5}! 🎊",
                "type": "periodic_win",
                "status": 1,
                "createTime": 1720876800,
                "updateTime": 1720876800
            },
            {
                "templateId": "periodic_win_template_3",
                "language": 12, # 繁体中文
                "title": "🎉 大獎提醒！",
                "content": "🎊 恭喜！玩家 {arg1} 剛剛在 {arg4} 中贏得了 {arg2} 倍的投注金額 {arg3}！總獎金：{arg5}！🎊",
                "type": "periodic_win", 
                "status": 1,
                "createTime": 1720876800,
                "updateTime": 1720876800
            }
        ]
        
        # 获取集合
        collection = mongo_conn.get_collection("c_tgTemplate")
        if collection is None:
            app_logger.error("❌ 无法获取c_tgTemplate集合")
            return False

        # 清除现有模板
        app_logger.info("🗑️ 清除现有的周期性推送模板")
        for template in test_templates:
            delete_result = collection.delete_many({"templateId": template["templateId"]})
            app_logger.info(f"   删除模板 {template['templateId']}: {delete_result.deleted_count} 条")

        # 插入新模板
        app_logger.info("📝 插入新的周期性推送模板")
        for template in test_templates:
            result = collection.insert_one(template)
            app_logger.info(f"   模板 {template['templateId']}: 语言={template['language']}, "
                          f"插入ID={result.inserted_id}")
        
        app_logger.info("✅ 测试模板设置完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 设置模板失败: {e}")
        return False
    
    finally:
        mongo_conn.disconnect()


async def main():
    """主函数"""
    app_logger.info("🔧 周期性推送配置初始化")
    app_logger.info("=" * 100)
    
    # 设置配置
    success1 = await setup_periodic_push_configs()
    
    app_logger.info("\n" + "=" * 100)
    
    # 设置模板
    success2 = await setup_test_templates()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 初始化总结:")
    app_logger.info(f"   配置设置: {'✅ 成功' if success1 else '❌ 失败'}")
    app_logger.info(f"   模板设置: {'✅ 成功' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部成功' if success1 and success2 else '❌ 存在失败'}")
    
    if success1 and success2:
        app_logger.info("\n💡 下一步:")
        app_logger.info("   1. 运行 python tmp/test_periodic_push.py 测试服务")
        app_logger.info("   2. 检查游戏日志表中是否有满足条件的数据")
        app_logger.info("   3. 观察推送效果")


if __name__ == "__main__":
    asyncio.run(main())
