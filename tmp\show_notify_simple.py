#!/usr/bin/env python3
"""
简单展示notify表结构
"""
import asyncio
import sys
from pathlib import Path
import json

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager


async def show_notify_structure():
    """展示notify表结构"""
    print("📋 c_tgNotify表结构展示")
    print("=" * 60)
    
    # 初始化配置管理器
    success = await config_manager.initialize()
    if not success:
        print("❌ 配置管理器初始化失败")
        return
    
    # 获取所有通知配置
    all_configs = config_manager.get_all_notify_configs()
    
    print(f"📊 总记录数: {len(all_configs)}")
    
    if not all_configs:
        print("❌ 没有通知配置数据")
        return
    
    # 分析字段结构
    print(f"\n📋 字段结构:")
    print("-" * 40)
    
    first_record = all_configs[0]
    for key, value in first_record.items():
        if key not in ['_id', 'cache_updated_at']:
            value_type = type(value).__name__
            print(f"  {key:<20} {value_type:<10} = {value}")
    
    # 按类型分组显示
    realtime_configs = [c for c in all_configs if c.get("notifyType") == 1]
    scheduled_configs = [c for c in all_configs if c.get("notifyType") == 2]
    
    print(f"\n📊 按类型统计:")
    print(f"  🔴 实时通知 (notifyType=1): {len(realtime_configs)} 个")
    print(f"  🔵 定时通知 (notifyType=2): {len(scheduled_configs)} 个")
    
    # 显示实时通知示例
    if realtime_configs:
        print(f"\n🔴 实时通知示例:")
        print("-" * 40)
        for i, config in enumerate(realtime_configs[:3], 1):
            print(f"示例 {i}:")
            print(f"  ID: {config.get('id')}")
            print(f"  名称: {config.get('tgName')}")
            print(f"  消息: {config.get('text')}")
            print(f"  触发类型: {config.get('types')}")
            print(f"  目标: {config.get('notifyTarget')}")
            
            # 业务字段
            business_fields = {}
            for field in ["currencyId", "rewardType", "turnoverMul", "amount"]:
                if field in config and config[field] not in [0, "", None]:
                    business_fields[field] = config[field]
            if business_fields:
                print(f"  业务字段: {business_fields}")
            print()
    
    # 显示定时通知示例
    if scheduled_configs:
        print(f"🔵 定时通知示例:")
        print("-" * 40)
        config = scheduled_configs[0]
        print(f"  ID: {config.get('id')}")
        print(f"  名称: {config.get('tgName')}")
        print(f"  消息: {config.get('text')}")
        print(f"  时间: {config.get('pushTimeString')}")
        print(f"  周期: {config.get('cycle')}")
        print(f"  目标: {config.get('notifyTarget')}")


def suggest_api_design():
    """建议API设计"""
    print(f"\n💡 即时推送API设计建议")
    print("=" * 60)
    
    print("🔸 接口1: 完整消息推送 (直接指定所有参数)")
    print("POST /api/realtime-push/direct")
    print()
    api1 = {
        "bot_token": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
        "chat_ids": [-1002316158105, "pd001"],
        "message": {
            "text": "您有新的奖励！",
            "image_url": "https://example.com/image.png",
            "link": "https://example.com/details",
            "link_text": "查看详情",
            "link_type": "browser"
        }
    }
    print(json.dumps(api1, indent=2, ensure_ascii=False))
    
    print("\n🔸 接口2: 模板消息推送 (基于notify表)")
    print("POST /api/realtime-push/template")
    print()
    api2 = {
        "business_no": "39bac42a",
        "template_id": 2,  # notify表的id字段
        "target_chats": [-1002316158105],  # 可选，覆盖默认目标
        "variables": {  # 动态替换变量
            "amount": 100.0,
            "currency": "USDT",
            "user_name": "张三"
        }
    }
    print(json.dumps(api2, indent=2, ensure_ascii=False))
    
    print("\n🔄 模板推送流程:")
    print("1. 根据template_id从notify表获取模板")
    print("2. 根据business_no从robot_config获取bot_token")
    print("3. 使用variables替换模板中的{变量}")
    print("4. 发送消息到指定或默认目标")


async def main():
    """主函数"""
    print("🚀 c_tgNotify表结构分析")
    
    await show_notify_structure()
    suggest_api_design()
    
    print(f"\n" + "=" * 60)
    print("✅ 分析完成")


if __name__ == "__main__":
    asyncio.run(main())
