#!/usr/bin/env python3
"""
简化的c_currency表检查
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger
from database.mongodb_connection import MongoDBConnection


def main():
    """主函数"""
    app_logger.info("🔍 简化c_currency表检查")
    app_logger.info("=" * 80)
    
    mongo_conn = MongoDBConnection()
    
    try:
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return
        
        # 获取集合
        collection = mongo_conn.get_collection("c_currency")
        if collection is None:
            app_logger.error("❌ 无法获取c_currency集合")
            return
        
        # 查询前5条记录
        app_logger.info("📋 查询前5条记录:")
        records = list(collection.find({}).limit(5))
        
        if not records:
            app_logger.info("📊 表中没有数据")
            return
        
        app_logger.info(f"📊 找到 {len(records)} 条记录:")
        
        for i, record in enumerate(records):
            app_logger.info(f"\n📋 记录 {i+1}:")
            for key, value in record.items():
                app_logger.info(f"   {key}: {value} ({type(value).__name__})")
        
        # 测试一些currencyId
        app_logger.info(f"\n💰 测试currencyId查询:")
        test_ids = [1, 2, 3, 4, 5]
        
        for currency_id in test_ids:
            # 尝试不同字段查询
            record1 = collection.find_one({"currencyId": currency_id})
            record2 = collection.find_one({"id": currency_id})
            
            if record1:
                name = record1.get('currencyName', record1.get('name', 'Unknown'))
                app_logger.info(f"   currencyId={currency_id} -> {name} (字段: currencyId)")
            elif record2:
                name = record2.get('currencyName', record2.get('name', 'Unknown'))
                app_logger.info(f"   currencyId={currency_id} -> {name} (字段: id)")
            else:
                app_logger.info(f"   currencyId={currency_id} -> 未找到")
        
        # 查询总数
        total_count = collection.count_documents({})
        app_logger.info(f"\n📊 总记录数: {total_count}")
        
    except Exception as e:
        app_logger.error(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        mongo_conn.disconnect()


if __name__ == "__main__":
    main()
