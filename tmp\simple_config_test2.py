#!/usr/bin/env python3
"""
简化的配置测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import json
from config.logging_config import app_logger


def test_config_file():
    """测试配置文件"""
    app_logger.info("🧪 测试配置文件")
    app_logger.info("=" * 60)
    
    try:
        # 检查状态文件
        state_file = Path(__file__).parent.parent / "config" / "periodic_push_state.json"
        
        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            app_logger.info(f"📂 状态文件存在: {state_file}")
            app_logger.info(f"📊 配置结构:")
            
            for type_key, type_data in state_data.items():
                app_logger.info(f"\n   Type {type_key}:")
                
                config = type_data.get('config', {})
                app_logger.info(f"     配置: {config}")
                
                last_numbers = type_data.get('last_numbers', {})
                app_logger.info(f"     last_numbers: {last_numbers}")
                
                last_updated = type_data.get('last_updated')
                app_logger.info(f"     last_updated: {last_updated}")
            
            app_logger.info("✅ 配置文件格式正确")
            return True
        else:
            app_logger.error(f"❌ 状态文件不存在: {state_file}")
            return False
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    app_logger.info("🧪 简化配置测试")
    app_logger.info("=" * 80)
    
    success = test_config_file()
    
    app_logger.info("\n" + "=" * 80)
    app_logger.info(f"📊 测试结果: {'✅ 通过' if success else '❌ 失败'}")


if __name__ == "__main__":
    main()
