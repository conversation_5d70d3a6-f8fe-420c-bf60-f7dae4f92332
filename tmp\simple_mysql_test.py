#!/usr/bin/env python3
"""
简单的MySQL连接测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import aiomysql
from config.settings import settings

async def test_mysql_connection():
    """测试MySQL连接"""
    print("🔧 测试MySQL连接")
    print("=" * 50)
    
    try:
        # 获取MySQL配置
        print(f"📋 连接信息:")
        print(f"   主机: {settings.db_host}")
        print(f"   端口: {settings.db_port}")
        print(f"   用户: {settings.db_user}")
        print(f"   数据库: {settings.db_name}")

        # 创建连接
        connection = await aiomysql.connect(
            host=settings.db_host,
            port=settings.db_port,
            user=settings.db_user,
            password=settings.db_password,
            db=settings.db_name,
            charset='utf8mb4'
        )
        
        print("✅ MySQL连接成功")
        
        # 测试查询
        async with connection.cursor(aiomysql.DictCursor) as cursor:
            # 查询表列表
            await cursor.execute("SHOW TABLES LIKE 'ea_platform_agent_game_log_%'")
            tables = await cursor.fetchall()
            
            print(f"📊 找到 {len(tables)} 个游戏日志表:")
            for table in tables:
                table_name = list(table.values())[0]
                print(f"   - {table_name}")
            
            # 查询最新的表
            if tables:
                latest_table = list(tables[-1].values())[0]
                print(f"\n🔍 查询最新表: {latest_table}")
                
                # 查询表结构
                await cursor.execute(f"DESCRIBE `{latest_table}`")
                columns = await cursor.fetchall()
                
                print(f"📋 表结构 ({len(columns)} 个字段):")
                for col in columns[:10]:  # 只显示前10个字段
                    print(f"   - {col['Field']}: {col['Type']}")
                
                # 查询数据量
                await cursor.execute(f"SELECT COUNT(*) as total FROM `{latest_table}`")
                count_result = await cursor.fetchone()
                print(f"\n📊 数据量: {count_result['total']:,} 条记录")
                
                # 查询最新记录
                await cursor.execute(f"""
                    SELECT number, create_time, gameType, platformId, gameId, playerId, betAmount, winAmount
                    FROM `{latest_table}` 
                    ORDER BY create_time DESC 
                    LIMIT 3
                """)
                
                recent_records = await cursor.fetchall()
                print(f"\n🔍 最新3条记录:")
                for record in recent_records:
                    print(f"   Number: {record['number']}")
                    print(f"   时间: {record['create_time']}")
                    print(f"   游戏类型: {record['gameType']}")
                    print(f"   平台ID: {record['platformId']}")
                    print(f"   游戏ID: {record['gameId']}")
                    print(f"   玩家ID: {record['playerId']}")
                    print(f"   投注: {record['betAmount']}")
                    print(f"   赢取: {record['winAmount']}")
                    print("   " + "-" * 40)
        
        await connection.ensure_closed()
        return True
        
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    success = await test_mysql_connection()
    print("=" * 50)
    print(f"📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")

if __name__ == "__main__":
    asyncio.run(main())
