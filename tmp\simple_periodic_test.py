#!/usr/bin/env python3
"""
简化的周期性推送测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger

async def test_imports():
    """测试导入"""
    app_logger.info("🧪 测试导入")
    
    try:
        from services.periodic_push_service import PeriodicPushService
        app_logger.info("✅ PeriodicPushService 导入成功")
        
        from database.mysql_connection import MySQLConnection
        app_logger.info("✅ MySQLConnection 导入成功")
        
        from database.mongodb_connection import MongoDBConnection
        app_logger.info("✅ MongoDBConnection 导入成功")
        
        return True
    except Exception as e:
        app_logger.error(f"❌ 导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_basic_functionality():
    """测试基本功能"""
    app_logger.info("🧪 测试基本功能")
    
    try:
        from services.periodic_push_service import PeriodicPushService
        
        # 创建服务实例
        service = PeriodicPushService()
        app_logger.info("✅ 服务实例创建成功")
        
        # 测试初始化
        app_logger.info("🔧 测试初始化")
        success = await service.initialize()
        
        if success:
            app_logger.info("✅ 服务初始化成功")
            
            # 测试配置加载
            app_logger.info(f"📊 通知配置数量: {len(service.notify_configs)}")
            for merchant_id, config in service.notify_configs.items():
                app_logger.info(f"   商户 {merchant_id}: gameWinMul={config.get('gameWinMul')}")
            
            # 测试表名生成
            table_name = await service.get_current_table_name()
            app_logger.info(f"📋 当前表名: {table_name}")
            
            # 清理
            await service.cleanup()
            app_logger.info("✅ 服务清理完成")
            
        else:
            app_logger.error("❌ 服务初始化失败")
            return False
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    app_logger.info("🧪 简化周期性推送测试")
    app_logger.info("=" * 60)
    
    # 测试1: 导入测试
    success1 = await test_imports()
    
    app_logger.info("\n" + "=" * 60)
    
    # 测试2: 基本功能测试
    success2 = await test_basic_functionality()
    
    app_logger.info("\n" + "=" * 60)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   导入测试: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   基本功能测试: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if success1 and success2 else '❌ 存在失败'}")

if __name__ == "__main__":
    asyncio.run(main())
