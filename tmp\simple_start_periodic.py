#!/usr/bin/env python3
"""
简化的周期性推送服务启动脚本
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger

async def main():
    """主函数"""
    app_logger.info("🚀 启动周期性推送服务")
    
    try:
        # 导入服务
        app_logger.info("📦 导入服务模块")
        from services.periodic_push_service import periodic_push_service
        app_logger.info("✅ 服务模块导入成功")
        
        # 初始化服务
        app_logger.info("🔧 初始化服务")
        success = await periodic_push_service.initialize()
        
        if not success:
            app_logger.error("❌ 服务初始化失败")
            return 1
        
        app_logger.info("✅ 服务初始化成功")
        
        # 启动服务
        app_logger.info("🚀 启动周期性推送")
        await periodic_push_service.start()
        
        app_logger.info("✅ 周期性推送服务已启动")
        app_logger.info("💡 服务将每60秒检查一次游戏日志")
        app_logger.info("🔄 运行30秒后自动停止...")
        
        # 运行30秒
        await asyncio.sleep(30)
        
        # 停止服务
        app_logger.info("🛑 停止服务")
        await periodic_push_service.stop()
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 服务已停止")
        return 0
        
    except Exception as e:
        app_logger.error(f"❌ 服务运行失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 服务被用户中断")
        sys.exit(0)
    except Exception as e:
        print(f"❌ 服务启动失败: {e}")
        sys.exit(1)
