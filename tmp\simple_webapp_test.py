#!/usr/bin/env python3
"""
简单的WebApp测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def main():
    try:
        print("测试WebApp导入...")
        
        # 测试导入
        from telegram._webappinfo import WebAppInfo as WebApp
        print("✅ WebApp导入成功")
        
        # 测试创建
        webapp = WebApp(url="https://t.me/telegram")
        print(f"✅ WebApp创建成功: {webapp.url}")
        
        # 测试按钮
        from telegram import InlineKeyboardButton
        button = InlineKeyboardButton(text="📱 WebApp", web_app=webapp)
        print(f"✅ WebApp按钮创建成功: {button.text}")
        
        # 测试菜单构建器
        from pusher.menu.menu_builder import menu_builder
        test_buttons = [
            [
                {"text": "🌐 URL", "url": "https://www.google.com"},
                {"text": "📱 WebApp", "web_app": {"url": "https://t.me/telegram"}}
            ]
        ]
        
        keyboard = menu_builder.create_inline_keyboard(test_buttons)
        print(f"✅ 菜单构建器测试成功，按钮数: {len(keyboard.inline_keyboard[0])}")
        
        # 检查按钮类型
        for i, btn in enumerate(keyboard.inline_keyboard[0]):
            print(f"按钮{i+1}: {btn.text}")
            if hasattr(btn, 'web_app') and btn.web_app:
                print(f"  WebApp: {btn.web_app.url}")
            elif hasattr(btn, 'url') and btn.url:
                print(f"  URL: {btn.url}")
        
        print("🎉 所有测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n💡 WebApp按钮应该可以正常工作了！")
        print("重启Bot服务测试: python start_bot_interactive.py")
    else:
        print("\n⚠️ 还有问题需要解决")
