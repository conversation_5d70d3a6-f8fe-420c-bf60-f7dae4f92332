#!/usr/bin/env python3
"""
测试活动名称调试
"""
from config.logging_config import app_logger
import sys
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_direct_debug():
    """直接测试调试"""
    app_logger.info("🔧 直接测试活动名称调试")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板
        template = "Parabéns a {活动名1} por conquistar {3}{4} em {2}."
        language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"📋 测试模板: {template}")
        app_logger.info(f"📋 语言: {language_id}")
        
        # 测试用例：activity_id=1
        app_logger.info(f"\n🔍 测试activity_id=1的情况:")
        params = ["xxx", "ttttt", "100", "BRL"]
        activity_id = 1
        message_type = 19000
        
        app_logger.info(f"   参数: {params}")
        app_logger.info(f"   activity_id: {activity_id}")
        app_logger.info(f"   message_type: {message_type}")
        app_logger.info(f"   期望: 查询activity_id=1对应的活动名称")
        
        app_logger.info(f"\n📋 开始渲染，请查看详细日志:")
        app_logger.info("=" * 40)
        
        result = translation_manager.render_template(
            template, params, language_id, 
            activity_id=activity_id, message_type=message_type
        )
        
        app_logger.info("=" * 40)
        app_logger.info(f"📊 最终结果: {result}")
        
        # 分析结果
        if "xxx" not in result:
            app_logger.info(f"✅ 参数无效化成功：'xxx'被替换")
        else:
            app_logger.info(f"❌ 参数无效化失败：仍包含'xxx'")
        
        if "充值排行" in result:
            app_logger.info(f"✅ 期望结果：显示了'充值排行'")
        elif "First Deposit Bonus" in result:
            app_logger.info(f"⚠️ 意外结果：显示了'First Deposit Bonus'（可能是type映射）")
        else:
            app_logger.info(f"❓ 其他结果：显示了其他内容")
        
        return True
        
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_api_debug():
    """测试API调试"""
    app_logger.info(f"\n🔧 测试API调试")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据
    test_data = {
        "business_no": "39bac42a",
        "type": 18000,  # 使用已知存在的类型
        "params": ["xxx", "ttttt", "100", "BRL"],
        "activity_id": 1
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    app_logger.info(f"📋 期望: 查询activity_id=1，显示'充值排行'")
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                app_logger.info(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get("status") == "success":
                    app_logger.info(f"   ✅ API调用成功")
                    app_logger.info(f"   💬 请检查Telegram消息和服务器日志")
                    return True
                else:
                    app_logger.info(f"   ❌ API失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.info(f"   ⚠️ 响应格式异常: {response.text}")
                return False
        else:
            app_logger.info(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        app_logger.info(f"   ❌ 请求异常: {e}")
        return False

def main():
    """主函数"""
    app_logger.info("🔧 活动名称调试测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 调试目标:")
    app_logger.info("   1. 查看activity_id=1查询到的activityName是什么")
    app_logger.info("   2. 确认最终替换的活动名称")
    app_logger.info("   3. 确认替换来源：activity_id、type还是现有映射")
    app_logger.info("   4. 期望结果：显示'充值排行'而不是'First Deposit Bonus'")
    
    # 1. 测试直接渲染
    direct_ok = test_direct_debug()
    
    # 2. 测试API（如果有可用的通知配置）
    if direct_ok:
        app_logger.info(f"\n💡 如果有可用的通知配置，可以测试API:")
        # api_ok = test_api_debug()
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📋 调试说明:")
    app_logger.info(f"   请查看上面的详细日志，重点关注:")
    app_logger.info(f"   1. 🔍 开始查询activity_id: 1, language_id: 2")
    app_logger.info(f"   2. 📄 原始activityData: [数据库内容]")
    app_logger.info(f"   3. ✅/❌ 通过activity_id找到活动名称: [结果]")
    app_logger.info(f"   4. 🎯 最终替换结果: [来源说明]")
    
    app_logger.info(f"\n💡 如果显示'First Deposit Bonus'而不是'充值排行':")
    app_logger.info(f"   可能原因:")
    app_logger.info(f"   1. activity_id=1在数据库中不存在")
    app_logger.info(f"   2. language_id=2在activityData中不存在")
    app_logger.info(f"   3. 降级到了type=19000的映射")

if __name__ == "__main__":
    main()
