#!/usr/bin/env python3
"""
测试活动名称功能
"""
from config.logging_config import app_logger
import sys
import asyncio
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_activity_name_api():
    """测试活动名称API功能"""
    app_logger.info("🔧 测试活动名称API功能")
    app_logger.info("=" * 60)
    
    # API地址
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试数据 - 包含activity_id
    test_data = {
        "business_no": "39bac42a",
        "type": 100,  # 使用存在的类型
        "params": [
            "活动名测试消息",  # 包含"活动名"的参数
            "其他参数"
        ],
        "activity_id": 12  # 活动ID
    }
    
    app_logger.info(f"📡 API地址: {api_url}")
    app_logger.info(f"📋 测试数据:")
    app_logger.info(json.dumps(test_data, indent=2, ensure_ascii=False))
    
    try:
        response = requests.post(
            api_url,
            json=test_data,
            timeout=15
        )
        
        app_logger.info(f"\n📊 响应:")
        app_logger.info(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                app_logger.info(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
                
                if result.get("status") == "success":
                    app_logger.info(f"\n✅ 活动名称功能测试成功！")
                    app_logger.info(f"💬 消息已发送到Telegram")
                    app_logger.info(f"📱 请检查频道中的消息，活动名应该被正确替换")
                    return True
                else:
                    app_logger.info(f"\n❌ 推送失败: {result.get('message', '未知错误')}")
                    return False
            except json.JSONDecodeError:
                app_logger.info(f"   ⚠️ 响应格式异常: {response.text}")
                return False
        else:
            app_logger.info(f"   ❌ HTTP错误: {response.text}")
            return False
            
    except Exception as e:
        app_logger.info(f"   ❌ 请求失败: {e}")
        return False

async def test_activity_query_direct():
    """直接测试活动查询功能"""
    app_logger.info(f"\n🔍 直接测试活动查询功能")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试活动ID
        test_activity_id = 12
        test_language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"测试参数:")
        app_logger.info(f"   活动ID: {test_activity_id}")
        app_logger.info(f"   语言ID: {test_language_id}")
        
        # 查询活动名称
        activity_name = translation_manager.get_activity_name(test_activity_id, test_language_id)
        
        if activity_name:
            app_logger.info(f"\n✅ 查询成功:")
            app_logger.info(f"   活动名称: '{activity_name}'")
            
            # 测试参数替换
            test_params = [
                "活动名测试消息",
                "其他参数",
                ["活动名列表项", "普通项"]
            ]
            
            app_logger.info(f"\n🔄 测试参数替换:")
            app_logger.info(f"   原始参数: {test_params}")
            
            replaced_params = translation_manager._replace_activity_name_in_params(test_params, activity_name)
            app_logger.info(f"   替换后: {replaced_params}")
            
            return True
        else:
            app_logger.info(f"\n❌ 查询失败")
            return False
            
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_database_activity():
    """测试数据库中的活动数据"""
    app_logger.info(f"\n📋 测试数据库中的活动数据")
    app_logger.info("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            app_logger.info("❌ MongoDB连接失败")
            return False
        
        # 切换到wingame_config库
        db = mongo.client["wingame_config"]
        collection = db["c_activity"]
        
        # 查询活动记录
        app_logger.info(f"查询活动ID=12的记录...")
        activity_doc = collection.find_one({"id": 12})
        
        if activity_doc:
            app_logger.info(f"✅ 找到活动记录:")
            app_logger.info(f"   ID: {activity_doc.get('id')}")
            app_logger.info(f"   activityData: {activity_doc.get('activityData', 'N/A')}")
            
            # 尝试解析activityData
            activity_data_str = activity_doc.get("activityData")
            if activity_data_str:
                try:
                    activity_data = json.loads(activity_data_str)
                    app_logger.info(f"\n📄 解析后的activityData:")
                    app_logger.info(json.dumps(activity_data, indent=2, ensure_ascii=False))
                    
                    # 查找不同语言的活动名称
                    if isinstance(activity_data, list):
                        app_logger.info(f"\n🌐 各语言的活动名称:")
                        for item in activity_data:
                            if isinstance(item, dict):
                                language = item.get("language")
                                activity_name = item.get("activityName")
                                app_logger.info(f"   语言{language}: '{activity_name}'")
                    
                except json.JSONDecodeError as e:
                    app_logger.info(f"❌ JSON解析失败: {e}")
            
            mongo.disconnect()
            return True
        else:
            app_logger.info(f"❌ 未找到活动ID=12的记录")
            
            # 查看有哪些活动
            app_logger.info(f"\n📋 查看前5个活动记录:")
            activities = list(collection.find().limit(5))
            for activity in activities:
                app_logger.info(f"   ID: {activity.get('id')}, activityData存在: {bool(activity.get('activityData'))}")
            
            mongo.disconnect()
            return False
        
    except Exception as e:
        app_logger.info(f"❌ 数据库测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    app_logger.info("🔧 测试活动名称功能")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 功能说明:")
    app_logger.info("   1. API增加了activity_id可选参数")
    app_logger.info("   2. 当模板包含'活动名'时，从数据库查询真实活动名称")
    app_logger.info("   3. 在参数中替换'活动名'为真实名称")
    
    # 1. 测试数据库查询
    db_ok = await test_database_activity()
    
    # 2. 测试直接查询功能
    if db_ok:
        direct_ok = await test_activity_query_direct()
    else:
        direct_ok = False
    
    # 3. 测试API功能
    if direct_ok:
        api_ok = test_activity_name_api()
    else:
        api_ok = False
    
    app_logger.info(f"\n" + "=" * 80)
    app_logger.info(f"📊 测试结果:")
    app_logger.info(f"   数据库查询: {'✅ 成功' if db_ok else '❌ 失败'}")
    app_logger.info(f"   直接查询: {'✅ 成功' if direct_ok else '❌ 失败'}")
    app_logger.info(f"   API测试: {'✅ 成功' if api_ok else '❌ 失败'}")
    
    if db_ok and direct_ok and api_ok:
        app_logger.info(f"\n🎉 活动名称功能完全正常！")
        app_logger.info(f"💡 功能特点:")
        app_logger.info(f"   1. API支持activity_id可选参数")
        app_logger.info(f"   2. 自动从wingame_config.c_activity表查询活动名称")
        app_logger.info(f"   3. 根据语言ID获取对应的activityName")
        app_logger.info(f"   4. 在参数中智能替换'活动名'为真实名称")
        
        app_logger.info(f"\n🚀 使用方法:")
        app_logger.info(f"   POST /api/realtime-push/template")
        app_logger.info(f"   {{")
        app_logger.info(f"     \"business_no\": \"39bac42a\",")
        app_logger.info(f"     \"type\": 100,")
        app_logger.info(f"     \"params\": [\"活动名测试\"],")
        app_logger.info(f"     \"activity_id\": 12")
        app_logger.info(f"   }}")
    else:
        app_logger.info(f"\n⚠️ 部分功能有问题，请检查配置")

if __name__ == "__main__":
    asyncio.run(main())
