#!/usr/bin/env python3
"""
测试活动名映射功能
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_activity_mapping():
    """测试活动名映射"""
    print("🎯 测试活动名映射功能")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试用例 - 根据你提供的活动类型
        test_cases = [
            {
                "name": "充值成功（系统通知）",
                "template": "恭喜 {玩家名1} {活动名2} 获得奖励！",
                "params": ["张三", "100"],
                "language": "1",
                "expected_activity": "充值成功"
            },
            {
                "name": "提现成功（系统通知）- 英文",
                "template": "Congratulations {玩家名1} on {活动名2}!",
                "params": ["John", "200"],
                "language": "2",
                "expected_activity": "Withdrawal Success"
            },
            {
                "name": "游戏获奖",
                "template": "玩家 {玩家名1} 在{活动名2}中获得大奖！",
                "params": ["李四", "300"],
                "language": "1",
                "expected_activity": "游戏获奖"
            },
            {
                "name": "红包雨（活动参与提醒）",
                "template": "快来参与{活动名1}活动！",
                "params": ["400"],
                "language": "1",
                "expected_activity": "红包雨"
            },
            {
                "name": "投注返利（活动信息通知）",
                "template": "您的{活动名1}奖励已到账",
                "params": ["500"],
                "language": "1",
                "expected_activity": "投注返利"
            },
            {
                "name": "投注返利（领取奖励通知）",
                "template": "恭喜获得{活动名1}奖励",
                "params": ["18000"],
                "language": "1",
                "expected_activity": "投注返利"
            },
            {
                "name": "首充拉新（领取奖励通知）- 繁体",
                "template": "恭喜獲得{活动名1}獎勵",
                "params": ["19000"],
                "language": "3",
                "expected_activity": "首充拉新"
            }
        ]
        
        success_count = 0
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n📋 测试用例 {i}: {case['name']}")
            print(f"   模板: {case['template']}")
            print(f"   参数: {case['params']}")
            print(f"   语言: {case['language']}")
            
            try:
                result = translator.render_template(
                    case['template'], 
                    case['params'], 
                    case['language']
                )
                
                print(f"   结果: {result}")
                
                # 检查是否包含期望的活动名
                if case['expected_activity'] in result:
                    print(f"   ✅ 活动名映射成功: {case['expected_activity']}")
                    success_count += 1
                else:
                    print(f"   ❌ 活动名映射失败，期望: {case['expected_activity']}")
                    
            except Exception as e:
                print(f"   ❌ 测试异常: {e}")
        
        print(f"\n" + "=" * 60)
        print(f"📊 测试结果: {success_count}/{len(test_cases)} 通过")
        
        if success_count == len(test_cases):
            print(f"🎉 所有活动名映射测试通过！")
        else:
            print(f"⚠️ 部分测试失败，需要检查配置")
        
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_language_support():
    """测试多语言支持"""
    print(f"\n🌐 测试多语言支持")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试同一个活动在不同语言下的表现
        template = "参与{活动名1}活动获得奖励"
        activity_id = "400"  # 红包雨
        
        languages = [
            ("1", "中文", "红包雨"),
            ("2", "英文", "Red Envelope Rain"),
            ("3", "繁体中文", "紅包雨")
        ]
        
        print(f"📋 测试活动ID: {activity_id}")
        print(f"📋 测试模板: {template}")
        
        for lang_id, lang_name, expected in languages:
            print(f"\n🔍 测试语言: {lang_name} (ID: {lang_id})")
            
            try:
                result = translator.render_template(template, [activity_id], lang_id)
                print(f"   结果: {result}")
                
                if expected in result:
                    print(f"   ✅ {lang_name}映射正确: {expected}")
                else:
                    print(f"   ❌ {lang_name}映射失败，期望: {expected}")
                    
            except Exception as e:
                print(f"   ❌ {lang_name}测试异常: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 多语言测试失败: {e}")
        return False

def test_config_loading():
    """测试配置加载"""
    print(f"\n🔧 测试配置加载")
    print("=" * 60)
    
    try:
        from config.settings import settings
        
        # 检查活动名配置
        mappings = settings.special_params_mappings
        activity_mappings = mappings.get("activity_name", {})
        
        print(f"📋 活动名映射配置:")
        for activity_id, translations in activity_mappings.items():
            print(f"   {activity_id}: {translations}")
        
        # 检查是否包含所有必要的活动类型
        required_activities = ["100", "200", "300", "400", "500", "18000", "19000"]
        missing_activities = []
        
        for activity_id in required_activities:
            if activity_id not in activity_mappings:
                missing_activities.append(activity_id)
        
        if missing_activities:
            print(f"❌ 缺少活动映射: {missing_activities}")
            return False
        else:
            print(f"✅ 所有必要的活动类型都已配置")
            return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 活动名映射功能测试")
    print("=" * 80)
    
    # 1. 测试配置加载
    config_ok = test_config_loading()
    
    # 2. 测试活动名映射
    mapping_ok = test_activity_mapping() if config_ok else False
    
    # 3. 测试多语言支持
    language_ok = test_language_support() if config_ok else False
    
    print(f"\n" + "=" * 80)
    print("📊 总体测试结果:")
    print(f"   🔧 配置加载: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"   🎯 活动映射: {'✅ 成功' if mapping_ok else '❌ 失败'}")
    print(f"   🌐 多语言支持: {'✅ 成功' if language_ok else '❌ 失败'}")
    
    if config_ok and mapping_ok and language_ok:
        print(f"\n🎉 活动名映射功能完全正常！")
        print(f"💡 支持的活动类型:")
        print(f"   • 100 - 充值成功（系统通知）")
        print(f"   • 200 - 提现成功（系统通知）")
        print(f"   • 300 - 游戏获奖（游戏获奖）")
        print(f"   • 400 - 红包雨（活动参与提醒）")
        print(f"   • 500 - 投注返利（活动信息通知）")
        print(f"   • 18000 - 投注返利（领取奖励通知）")
        print(f"   • 19000 - 首充拉新（领取奖励通知）")
        print(f"💡 支持语言: 中文(1)、英文(2)、繁体中文(3)")
    else:
        print(f"\n⚠️ 部分功能异常，请检查配置")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
