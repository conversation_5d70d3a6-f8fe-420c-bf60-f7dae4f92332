#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🔍 测试Amount3的处理")
    
    translator = TranslationManager()
    
    # 测试Amount前缀映射
    amount_index = translator._get_sub_array_index("Amount")
    print(f"Amount前缀映射到索引: {amount_index}")
    
    # 模拟子参数上下文
    sub_params = ['user_name_li', ['10', 'USDT'], 'thank_you_message']
    
    print(f"子参数: {sub_params}")
    print(f"参数3存在吗: {3-1 < len(sub_params)} (索引2)")
    print(f"参数3的值: {sub_params[2] if 2 < len(sub_params) else '不存在'}")
    
    # 测试从嵌套数组获取
    print(f"\n测试从嵌套数组获取Amount:")
    for i, param in enumerate(sub_params):
        print(f"参数{i+1}: {param}, 是列表: {isinstance(param, list)}")
        if isinstance(param, list) and len(param) > 0:
            print(f"  索引0: {param[0] if 0 < len(param) else '不存在'}")
            print(f"  索引1: {param[1] if 1 < len(param) else '不存在'}")
    
    # 手动测试渲染
    repeat_content = "pelo depósito – {Amount3}"
    result = translator._render_repeat_content(repeat_content, sub_params)
    print(f"\n渲染结果: {result}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
