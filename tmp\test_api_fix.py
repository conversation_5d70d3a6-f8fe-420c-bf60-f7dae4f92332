#!/usr/bin/env python3
"""
测试API修复
"""
import requests
import json

def test_api():
    """测试API调用"""
    print("🔧 测试修复后的API")
    
    # 测试数据
    data = {
        "business_no": "39bac42a",
        "type": 18000,
        "params": ["张三", "18000", "1", "100"]
    }
    
    print(f"📋 测试数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        # 测试预览接口
        print(f"\n🔍 测试预览接口...")
        response = requests.post(
            "http://localhost:9005/api/realtime-push/template/preview",
            json=data,
            timeout=10
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('status') == 'success':
                print(f"✅ 预览接口测试成功")
                
                # 测试实际发送
                print(f"\n📤 测试实际发送...")
                response2 = requests.post(
                    "http://localhost:9005/api/realtime-push/template",
                    json=data,
                    timeout=10
                )
                
                print(f"状态码: {response2.status_code}")
                
                if response2.status_code == 200:
                    result2 = response2.json()
                    print(f"响应: {json.dumps(result2, indent=2, ensure_ascii=False)}")
                    
                    if result2.get('status') == 'success':
                        print(f"✅ 发送接口测试成功")
                    else:
                        print(f"❌ 发送失败: {result2.get('message')}")
                else:
                    print(f"❌ 发送HTTP错误: {response2.text}")
            else:
                print(f"❌ 预览失败: {result.get('message')}")
        else:
            print(f"❌ 预览HTTP错误: {response.text}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")

if __name__ == "__main__":
    test_api()
