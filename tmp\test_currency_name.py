#!/usr/bin/env python3
"""
测试货币名称查询功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mongo_currency_query():
    """测试MongoDB货币查询"""
    print("🔍 测试MongoDB货币查询")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 切换到wingame_config库
        db = mongo.client["wingame_config"]
        collection = db["c_baseCurrency"]
        
        # 检查集合是否存在
        if "c_baseCurrency" not in db.list_collection_names():
            print("❌ c_baseCurrency集合不存在")
            mongo.disconnect()
            return False
        
        print(f"✅ c_baseCurrency集合存在")
        
        # 统计文档数量
        count = collection.count_documents({})
        print(f"   文档总数: {count}")
        
        if count > 0:
            # 查看前几个文档的结构
            print(f"\n📄 前5个文档:")
            sample_docs = list(collection.find().limit(5))
            
            for i, doc in enumerate(sample_docs, 1):
                currency_id = doc.get("currencyId", "N/A")
                currency_name = doc.get("currencyName", "N/A")
                print(f"   {i}. currencyId={currency_id}, currencyName='{currency_name}'")
            
            # 测试查询特定的currencyId
            test_currency_ids = [1000, 1001, 1002, 999999]  # 最后一个是不存在的
            
            print(f"\n🔍 测试查询特定currencyId:")
            for currency_id in test_currency_ids:
                currency_doc = collection.find_one({"currencyId": currency_id})
                
                if currency_doc:
                    currency_name = currency_doc.get("currencyName", "未知")
                    print(f"   currencyId={currency_id} -> currencyName='{currency_name}' ✅")
                else:
                    print(f"   currencyId={currency_id} -> 未找到 ❌")
            
            # 测试批量查询
            print(f"\n📊 批量查询测试:")
            batch_docs = list(collection.find({"currencyId": {"$in": test_currency_ids}}))
            print(f"   批量查询结果: {len(batch_docs)} 条记录")
            
            for doc in batch_docs:
                currency_id = doc.get("currencyId")
                currency_name = doc.get("currencyName", "未知")
                print(f"     currencyId={currency_id} -> currencyName='{currency_name}'")
        
        else:
            print("❌ c_baseCurrency集合为空")
        
        mongo.disconnect()
        return count > 0
        
    except Exception as e:
        print(f"❌ MongoDB查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_handler_currency_query():
    """测试处理器的货币查询"""
    print(f"\n🎯 测试处理器的货币查询")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        
        # 测试单个查询
        test_currency_id = 1000
        currency_name = await rebate_rank_handler._query_currency_name(test_currency_id)
        print(f"单个查询: currencyId={test_currency_id} -> currencyName='{currency_name}'")
        
        # 测试批量查询
        test_currency_ids = [1000, 1001, 1002]
        currency_name_map = await rebate_rank_handler._batch_query_currency_names(test_currency_ids)
        
        print(f"\n批量查询结果:")
        for currency_id, currency_name in currency_name_map.items():
            print(f"  currencyId={currency_id} -> currencyName='{currency_name}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_handler_with_currency_names():
    """测试完整处理器（包含货币名称）"""
    print(f"\n🚀 测试完整处理器（包含货币名称）")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        print(f"期望: 使用playerName和currencyName替代ID")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and isinstance(params[0], list) and len(params[0]) > 0:
                print(f"\n参数数据 (使用playerName和currencyName):")
                print(f"  记录数: {len(params[0])}")
                
                # 显示前5条记录
                for i, record in enumerate(params[0][:5], 1):
                    if len(record) >= 4:
                        ranking = record[0]
                        player_name = record[1]  # 现在应该是playerName
                        currency_name = record[2]  # 现在应该是currencyName
                        wagered = record[3]
                        
                        print(f"  {i}. 排名={ranking}, 玩家='{player_name}', 货币='{currency_name}', 投注={wagered}")
                        
                        # 检查是否使用了名称而不是ID
                        player_is_name = not player_name.replace('WG', '').isdigit()
                        currency_is_name = not currency_name.isdigit()
                        
                        if player_is_name:
                            print(f"     ✅ 使用了playerName")
                        else:
                            print(f"     ⚠️ 可能仍是playerId")
                        
                        if currency_is_name:
                            print(f"     ✅ 使用了currencyName")
                        else:
                            print(f"     ⚠️ 可能仍是currencyId")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 完整处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def simulate_template_with_currency_names():
    """模拟模板渲染（使用货币名称）"""
    print(f"\n📝 模拟模板渲染（使用货币名称）")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模拟数据（假设查询到了playerName和currencyName）
        template = """Daily Ranking Revealed  
Today's Top Players:
[repeat]{1}::{2} – {3}~{4}
[/repeat]"""
        
        # 模拟参数（使用playerName和currencyName）
        rank_list = [
            ["1", "Alice", "USDT", "2,092.21"],      # 假设1000的currencyName是USDT
            ["2", "Bob", "BRL", "2,035.44"],         # 假设1001的currencyName是BRL
            ["3", "Charlie", "USDC", "1,821.96"]     # 假设1002的currencyName是USDC
        ]
        
        params = [rank_list]
        
        print(f"模板: {template}")
        print(f"参数 (使用playerName和currencyName): {rank_list}")
        
        # 渲染模板
        rendered = translation_manager.render_template(template, params, "1")
        
        print(f"\n渲染结果:")
        print(f"{rendered}")
        
        # 检查结果
        if "USDT" in rendered and "BRL" in rendered and "USDC" in rendered:
            print(f"\n✅ 成功使用currencyName渲染")
        else:
            print(f"\n❌ currencyName渲染失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟渲染失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试货币名称查询功能")
    print("=" * 80)
    
    # 1. 测试MongoDB查询
    mongo_ok = await test_mongo_currency_query()
    
    # 2. 测试处理器查询方法
    handler_ok = await test_handler_currency_query()
    
    # 3. 测试完整处理器
    full_ok = await test_full_handler_with_currency_names()
    
    # 4. 模拟模板渲染
    template_ok = await simulate_template_with_currency_names()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   MongoDB查询: {'✅ 成功' if mongo_ok else '❌ 失败'}")
    print(f"   处理器查询: {'✅ 成功' if handler_ok else '❌ 失败'}")
    print(f"   完整处理器: {'✅ 成功' if full_ok else '❌ 失败'}")
    print(f"   模板渲染: {'✅ 成功' if template_ok else '❌ 失败'}")
    
    if mongo_ok and handler_ok and full_ok and template_ok:
        print(f"\n🎉 货币名称查询功能完全正常！")
        print(f"💡 功能特点:")
        print(f"   1. 批量查询MongoDB wingame_config.c_baseCurrency表")
        print(f"   2. 通过currencyId获取currencyName")
        print(f"   3. 在模板中使用currencyName替代currencyId")
        print(f"   4. 查询失败时降级使用currencyId")
        print(f"   5. 同时支持playerName和currencyName")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息中的货币名称")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
