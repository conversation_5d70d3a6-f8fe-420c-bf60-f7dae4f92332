#!/usr/bin/env python3
"""
直接查询数据库
"""
from config.logging_config import app_logger
import sys
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_db_direct():
    """直接查询数据库"""
    app_logger.info("🔧 直接查询数据库")
    app_logger.info("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            app_logger.info("❌ MongoDB连接失败")
            return False
        
        # 切换到wingame_config库
        db = mongo.client["wingame_config"]
        collection = db["c_activity"]
        
        # 查询activity_id=1
        activity_id = 1
        app_logger.info(f"🔍 查询activity_id={activity_id}")
        
        activity_doc = collection.find_one({"id": activity_id})
        
        if not activity_doc:
            app_logger.info(f"❌ 未找到activity_id={activity_id}")
            return False
        
        app_logger.info(f"✅ 找到活动记录:")
        app_logger.info(f"   ID: {activity_doc.get('id')}")
        
        # 获取activityData
        activity_data_raw = activity_doc.get("activityData")
        app_logger.info(f"   activityData类型: {type(activity_data_raw)}")
        app_logger.info(f"   activityData内容: {activity_data_raw}")
        
        # 处理activityData
        if isinstance(activity_data_raw, str):
            try:
                activity_data = json.loads(activity_data_raw)
                app_logger.info(f"   JSON解析成功")
            except json.JSONDecodeError as e:
                app_logger.info(f"   JSON解析失败: {e}")
                return False
        elif isinstance(activity_data_raw, (list, dict)):
            activity_data = activity_data_raw
            app_logger.info(f"   直接使用对象")
        else:
            app_logger.info(f"   不支持的类型: {type(activity_data_raw)}")
            return False
        
        app_logger.info(f"\n📄 解析后的activityData:")
        app_logger.info(json.dumps(activity_data, indent=2, ensure_ascii=False))
        
        # 查找各语言的活动名称
        if isinstance(activity_data, list):
            app_logger.info(f"\n🌐 各语言的活动名称:")
            for i, item in enumerate(activity_data):
                if isinstance(item, dict):
                    language = item.get("language")
                    activity_name = item.get("activityName")
                    app_logger.info(f"   项目{i}: 语言={language}, 活动名='{activity_name}'")
                    
                    if str(language) == "2":
                        app_logger.info(f"   🎯 找到语言2的记录: '{activity_name}'")
                        if activity_name:
                            app_logger.info(f"   ✅ 语言2有活动名称: '{activity_name}'")
                        else:
                            app_logger.info(f"   ❌ 语言2的活动名称为空")
        else:
            app_logger.info(f"❌ activityData不是列表格式")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        app_logger.info(f"❌ 查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    app_logger.info("🔧 直接查询数据库测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 目标:")
    app_logger.info("   1. 直接查询activity_id=1的数据库记录")
    app_logger.info("   2. 查看activityData的实际内容")
    app_logger.info("   3. 确认language=2是否存在以及activityName是什么")
    
    success = test_db_direct()
    
    if success:
        app_logger.info(f"\n💡 根据查询结果:")
        app_logger.info(f"   如果language=2存在且有activityName，说明代码有问题")
        app_logger.info(f"   如果language=2不存在或activityName为空，说明数据库配置问题")
        app_logger.info(f"   期望的'充值排行'应该在language=2的activityName中")
    else:
        app_logger.info(f"\n⚠️ 查询失败，请检查数据库连接")

if __name__ == "__main__":
    main()
