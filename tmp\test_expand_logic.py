#!/usr/bin/env python3
import sys
import logging
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(levelname)s - %(name)s - %(message)s')

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 测试完全展开逻辑")
    
    translator = TranslationManager()
    
    # 你的完整用例
    template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
    
    params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    print(f"模板: {repr(template)}")
    print(f"参数: {params}")
    
    result = translator.render_template(template, params, "2")
    
    print(f"\n实际结果:")
    print(f"{result}")
    
    print(f"\n期望结果:")
    expected = """Parabéns Li
pelo depósito – 10USDT
pelo depósito – 50BRL
pelo depósito – 100USDC
thank_you_message"""
    print(f"{expected}")
    
    # 检查关键部分
    checks = [
        ("10USDT" in result, "10USDT"),
        ("50BRL" in result, "50BRL"),
        ("100USDC" in result, "100USDC"),
        ("thank_you_message" in result, "thank_you_message")
    ]
    
    print(f"\n验证:")
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"{status} {desc}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
