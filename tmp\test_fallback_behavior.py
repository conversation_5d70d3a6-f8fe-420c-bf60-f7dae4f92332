#!/usr/bin/env python3
"""
测试降级行为 - MongoDB查询失败时使用playerId
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_fallback_behavior():
    """测试降级行为"""
    print("🔧 测试降级行为")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        
        # 测试批量查询（预期会失败并降级）
        test_player_ids = [4743182, 2268779, 7731938]
        print(f"测试playerId: {test_player_ids}")
        
        player_name_map = await rebate_rank_handler._batch_query_player_names(test_player_ids)
        
        print(f"\n降级结果:")
        for player_id, player_name in player_name_map.items():
            print(f"  playerId={player_id} -> playerName='{player_name}'")
            
            # 检查是否正确降级
            if str(player_name) == str(player_id):
                print(f"    ✅ 正确降级使用playerId")
            else:
                print(f"    🎉 找到了真实的playerName")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def simulate_template_with_names():
    """模拟模板渲染（使用playerName）"""
    print(f"\n📝 模拟模板渲染（使用playerName）")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模拟数据（假设查询到了playerName）
        template = """Daily Ranking Revealed  
Today's Top Players:
[repeat]{1}::{2} – {3}~{4}
[/repeat]"""
        
        # 模拟参数（使用playerName）
        rank_list = [
            ["1", "Alice", "1000", "2,092.21"],      # 假设4743182的playerName是Alice
            ["2", "Bob", "1000", "2,035.44"],        # 假设2268779的playerName是Bob
            ["3", "Charlie", "1000", "1,821.96"]     # 假设7731938的playerName是Charlie
        ]
        
        params = [rank_list]
        
        print(f"模板: {template}")
        print(f"参数 (使用playerName): {rank_list}")
        
        # 渲染模板
        rendered = translation_manager.render_template(template, params, "1")
        
        print(f"\n渲染结果:")
        print(f"{rendered}")
        
        # 检查结果
        if "Alice" in rendered and "Bob" in rendered and "Charlie" in rendered:
            print(f"\n✅ 成功使用playerName渲染")
        else:
            print(f"\n❌ playerName渲染失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 模拟渲染失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试playerName功能的降级行为")
    print("=" * 80)
    
    # 1. 测试降级行为
    fallback_ok = await test_fallback_behavior()
    
    # 2. 模拟模板渲染
    template_ok = await simulate_template_with_names()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   降级行为: {'✅ 正常' if fallback_ok else '❌ 异常'}")
    print(f"   模板渲染: {'✅ 正常' if template_ok else '❌ 异常'}")
    
    if fallback_ok and template_ok:
        print(f"\n🎉 playerName功能基本正常！")
        print(f"💡 功能特点:")
        print(f"   1. MongoDB查询失败时正确降级使用playerId")
        print(f"   2. 模板渲染支持playerName")
        print(f"   3. 系统具有良好的容错性")
        
        print(f"\n📋 当前状态:")
        print(f"   - 如果MongoDB中有playerName数据，将显示真实姓名")
        print(f"   - 如果MongoDB查询失败，将显示playerId")
        print(f"   - 模板格式: 1::Alice – 1000~2,092.21")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 观察09:28:00的定时任务执行")
        print(f"   2. 检查Telegram消息中的玩家显示")
        print(f"   3. 确认MongoDB数据库配置")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
