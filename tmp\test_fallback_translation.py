#!/usr/bin/env python3
"""
测试翻译兜底方案
验证当目标语言不存在时，自动使用英文兜底
"""
import asyncio
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager


async def test_fallback_translation():
    """测试兜底翻译功能"""
    print("🛡️ 测试翻译兜底方案")
    print("=" * 60)
    
    # 加载翻译配置
    success = translation_manager.load_translations()
    if not success:
        print("❌ 翻译配置加载失败")
        return False
    
    print("✅ 翻译配置加载成功")
    
    # 显示当前配置的语言
    print(f"\n📋 当前配置的翻译语言:")
    test_word = "user_name_zhang"
    for category, items in translation_manager.translations.items():
        if test_word in items:
            configured_languages = list(items[test_word].keys())
            print(f"  {test_word}: {configured_languages}")
            break
    
    # 测试兜底方案
    print(f"\n🔄 兜底方案测试:")
    print("-" * 40)
    
    test_cases = [
        # (词汇, 目标语言ID, 期望结果类型)
        ("user_name_zhang", "1", "直接匹配"),      # 英文，直接匹配
        ("user_name_zhang", "2", "直接匹配"),      # 中文，直接匹配  
        ("user_name_zhang", "7", "直接匹配"),      # 日文，直接匹配
        ("user_name_zhang", "3", "英文兜底"),      # 西班牙文，未配置，使用英文兜底
        ("user_name_zhang", "5", "英文兜底"),      # 土耳其文，未配置，使用英文兜底
        ("user_name_zhang", "99", "英文兜底"),     # 不存在的语言ID，使用英文兜底
        ("unknown_word", "1", "原文返回"),         # 不存在的词汇，返回原文
        ("unknown_word", "2", "原文返回"),         # 不存在的词汇，返回原文
    ]
    
    for word, lang_id, expected_type in test_cases:
        result = translation_manager.translate_text(word, lang_id)
        lang_name = translation_manager.language_mapping.get(lang_id, f"Unknown({lang_id})")
        
        print(f"  {word} -> {lang_id}({lang_name}): '{result}' ({expected_type})")
    
    # 测试模板渲染的兜底
    print(f"\n📝 模板渲染兜底测试:")
    print("-" * 40)
    
    template = "您好 {arg1}，您获得了 {list2} 奖励！{test3}"
    params = ["user_name_zhang", ["game_a"], "thank_you_message"]
    
    print(f"模板: {template}")
    print(f"参数: {params}")
    
    # 测试不同语言的渲染
    test_languages = [
        ("1", "English", "直接匹配"),
        ("2", "中文", "直接匹配"),
        ("3", "Español", "英文兜底"),
        ("5", "Türkçe", "英文兜底"),
        ("99", "Unknown", "英文兜底")
    ]
    
    for lang_id, lang_name, expected_type in test_languages:
        result = translation_manager.render_template(template, params, lang_id)
        print(f"\n{lang_id} ({lang_name}) - {expected_type}:")
        print(f"  {result}")
    
    return True


async def test_partial_translation():
    """测试部分翻译的兜底"""
    print(f"\n🔍 测试部分翻译兜底")
    print("=" * 60)
    
    # 添加一个只有英文翻译的测试词汇
    translation_manager.add_translation("test", "english_only_word", {
        "1": "English Only"
    })
    
    # 添加一个缺少英文的测试词汇
    translation_manager.add_translation("test", "no_english_word", {
        "2": "只有中文",
        "7": "日本語のみ"
    })
    
    print("📋 测试词汇:")
    print("  english_only_word: 只有英文翻译")
    print("  no_english_word: 没有英文翻译")
    
    print(f"\n🔄 兜底测试结果:")
    
    # 测试只有英文的词汇
    test_cases = [
        ("english_only_word", "1", "English Only"),
        ("english_only_word", "2", "English Only"),  # 兜底到英文
        ("english_only_word", "7", "English Only"),  # 兜底到英文
    ]
    
    for word, lang_id, expected in test_cases:
        result = translation_manager.translate_text(word, lang_id)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {word} -> {lang_id}: '{result}' (期望: '{expected}')")
    
    # 测试没有英文的词汇
    test_cases_no_english = [
        ("no_english_word", "1", "no_english_word"),  # 没有英文，返回原文
        ("no_english_word", "2", "只有中文"),           # 直接匹配
        ("no_english_word", "7", "日本語のみ"),         # 直接匹配
        ("no_english_word", "3", "no_english_word"),  # 没有英文兜底，返回原文
    ]
    
    for word, lang_id, expected in test_cases_no_english:
        result = translation_manager.translate_text(word, lang_id)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {word} -> {lang_id}: '{result}' (期望: '{expected}')")


async def main():
    """主函数"""
    print("🚀 翻译兜底方案测试")
    print("=" * 80)
    
    # 1. 测试基本兜底功能
    success1 = await test_fallback_translation()
    
    # 2. 测试部分翻译兜底
    await test_partial_translation()
    
    print(f"\n" + "=" * 80)
    print("📊 测试总结")
    print("=" * 80)
    
    if success1:
        print("✅ 兜底方案测试通过")
        print("💡 兜底策略:")
        print("   1. 优先使用目标语言翻译")
        print("   2. 目标语言不存在时，使用英文(ID=1)兜底")
        print("   3. 英文也不存在时，返回原文")
        print()
        print("🎯 配置建议:")
        print("   • 至少为每个词汇配置英文翻译作为兜底")
        print("   • 根据实际需求配置2-3种主要语言")
        print("   • 其他语言会自动使用英文兜底")
    else:
        print("❌ 兜底方案测试失败")
    
    print("=" * 80)
    
    return 0 if success1 else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        sys.exit(1)
