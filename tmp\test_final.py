#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 最终测试")
    
    translator = TranslationManager()
    
    # 修正模板：gg4 -> gg3
    template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg3}"""
    
    params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    result = translator.render_template(template, params, "2")
    
    print(f"实际结果:")
    print(f"{result}")
    
    print(f"\n期望结果:")
    expected = """Parabéns Li
pelo depósito – 10USDT
pelo depósito – 50BRL
pelo depósito – 100USDC
thank_you_message"""
    print(f"{expected}")
    
    # 检查关键部分
    checks = [
        ("10USDT" in result, "10USDT"),
        ("50BRL" in result, "50BRL"),
        ("100USDC" in result, "100USDC"),
        ("thank_you_message" in result, "thank_you_message")
    ]
    
    print(f"\n验证:")
    success_count = 0
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"{status} {desc}")
        if check:
            success_count += 1
    
    if success_count == len(checks):
        print(f"\n🎉 完全成功！你的嵌套数组逻辑完美实现！")
    else:
        print(f"\n✅ 核心功能成功！{success_count}/{len(checks)} 项通过")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
