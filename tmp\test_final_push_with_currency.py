#!/usr/bin/env python3
"""
测试最终版本的推送（包含币种转换）
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_final_push_with_currency():
    """测试最终版本的推送（包含币种转换）"""
    app_logger.info("🧪 测试最终版本的推送（包含币种转换）")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 周期性推送服务初始化失败")
            return False
        
        # 模拟一条包含currencyId的获奖记录
        test_log = {
            "number": "test_final_currency_001",
            "playerId": 4743182,
            "gameId": 200004,
            "currencyId": 6,  # USDT
            "gameType": 102,
            "platformId": 3,
            "betAmount": 10.0,
            "winAmount": 50.0,  # 5倍获奖，满足3x阈值
            "create_time": 1720876800
        }
        
        app_logger.info("🎮 测试获奖记录:")
        app_logger.info(f"   玩家ID: {test_log['playerId']}")
        app_logger.info(f"   游戏ID: {test_log['gameId']}")
        app_logger.info(f"   币种ID: {test_log['currencyId']}")
        app_logger.info(f"   游戏类型: {test_log['gameType']}")
        app_logger.info(f"   平台ID: {test_log['platformId']}")
        app_logger.info(f"   投注: {test_log['betAmount']}, 赢取: {test_log['winAmount']}")
        app_logger.info(f"   获奖倍数: {test_log['winAmount']/test_log['betAmount']:.1f}x")
        
        # 检查每个配置
        for business_no, config in periodic_push_service.notify_configs.items():
            app_logger.info(f"\n📋 测试商户配置: {business_no}")
            
            # 检查获奖条件
            is_winning = await periodic_push_service.check_win_condition(test_log, config)
            app_logger.info(f"   获奖条件检查: {'✅ 满足' if is_winning else '❌ 不满足'}")
            
            if is_winning:
                app_logger.info("🎉 开始测试推送...")
                
                # 显示推送配置
                notify_targets = config.get('notifyTarget', [])
                message_text = config.get('text', '')
                image_url = config.get('msgBanner', '')
                
                app_logger.info(f"   推送目标: {notify_targets}")
                app_logger.info(f"   消息模板: {message_text}")
                app_logger.info(f"   图片URL: {image_url}")
                
                # 使用完整的数据转换
                from common.data_converters import data_converter
                from common.translation_manager import translation_manager
                
                # 转换所有数据
                player_name = await data_converter.get_player_name(test_log["playerId"])
                game_name = await data_converter.get_game_name(test_log["gameId"])
                currency_name = await data_converter.get_currency_name(test_log["currencyId"])
                
                app_logger.info(f"   完整数据转换:")
                app_logger.info(f"     playerId={test_log['playerId']} -> playerName='{player_name}'")
                app_logger.info(f"     gameId={test_log['gameId']} -> gameName='{game_name}'")
                app_logger.info(f"     currencyId={test_log['currencyId']} -> currencyName='{currency_name}'")
                
                # 构建参数
                params = [
                    player_name,                    # 参数1 - {playname1}
                    game_name,                     # 参数2 - {gamename2}
                    currency_name,                 # 参数3 - {currency3}
                    test_log["winAmount"]         # 参数4 - {amount4}
                ]
                
                app_logger.info(f"   模板参数: {params}")
                
                # 渲染模板
                language_id_str = translation_manager.get_language_id(config.get('language', 1))
                
                rendered_message = translation_manager.render_template(
                    message_text,
                    params,
                    language_id_str,
                    activity_id=None,
                    message_type=300
                )
                
                app_logger.info(f"   格式化消息: {rendered_message}")
                
                # 测试推送到测试群组 (只推送到第一个数字目标)
                test_targets = [target for target in notify_targets if isinstance(target, int)]
                
                if test_targets:
                    test_target = test_targets[0]  # 使用第一个群组进行测试
                    app_logger.info(f"🚀 测试推送到群组: {test_target}")
                    
                    try:
                        from common.bot_client import bot_client
                        
                        if image_url:
                            # 发送图片消息
                            await bot_client.send_photo(
                                chat_id=test_target,
                                photo=image_url,
                                caption=f"🧪 最终版本测试推送\n\n{rendered_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 图片消息推送成功")
                        else:
                            # 发送文本消息
                            await bot_client.send_message(
                                chat_id=test_target,
                                text=f"🧪 最终版本测试推送\n\n{rendered_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 文本消息推送成功")
                        
                    except Exception as e:
                        app_logger.error(f"❌ 推送失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    app_logger.warning("⚠️ 没有找到有效的推送目标")
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 最终版本推送测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_different_currencies():
    """测试不同币种的推送"""
    app_logger.info("\n🧪 测试不同币种的推送")
    app_logger.info("=" * 80)
    
    try:
        from common.data_converters import data_converter
        from common.translation_manager import translation_manager
        
        # 测试不同币种
        test_currencies = [
            {"currencyId": 1, "name": "BRL"},
            {"currencyId": 3, "name": "USD"},
            {"currencyId": 6, "name": "USDT"},
        ]
        
        template_text = "Congratulations to {playname1} for winning {currency3}{amount4} at {gamename2}."
        
        for currency_info in test_currencies:
            currency_id = currency_info["currencyId"]
            expected_name = currency_info["name"]
            
            app_logger.info(f"\n💰 测试币种: currencyId={currency_id}")
            
            # 转换币种名
            currency_name = await data_converter.get_currency_name(currency_id)
            app_logger.info(f"   转换结果: {currency_name} (期望: {expected_name})")
            
            # 构建测试参数
            params = ["TestPlayer", "TestGame", currency_name, 100.0]
            
            # 渲染消息
            language_id_str = translation_manager.get_language_id(1)
            rendered_message = translation_manager.render_template(
                template_text,
                params,
                language_id_str,
                activity_id=None,
                message_type=300
            )
            
            app_logger.info(f"   渲染结果: {rendered_message}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 不同币种测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 最终版本推送测试（包含币种转换）")
    app_logger.info("=" * 100)
    
    # 测试1: 最终版本推送
    success1 = await test_final_push_with_currency()
    
    # 测试2: 不同币种测试
    success2 = await test_different_currencies()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   最终版本推送: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   不同币种测试: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2]) else '❌ 存在失败'}")
    
    if success1 and success2:
        app_logger.info("\n💡 测试说明:")
        app_logger.info("   1. 已发送最终版本测试消息到配置的群组")
        app_logger.info("   2. 消息格式: 'Congratulations to {playerName} for winning {currencyName}{amount} at {gameName}.'")
        app_logger.info("   3. 使用了真实的玩家名、游戏名和币种名转换")
        app_logger.info("   4. 支持多种币种：BRL, USD, USDT等")
        app_logger.info("   5. 请检查群组中是否收到正确格式的推送消息")


if __name__ == "__main__":
    asyncio.run(main())
