#!/usr/bin/env python3
"""
测试模板修复
"""
from config.logging_config import app_logger
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_simple_placeholders():
    """测试简单数字占位符"""
    app_logger.info("🔧 测试简单数字占位符修复")
    app_logger.info("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 测试模板 - 使用您提供的模板
        template = "Parabéns a {1} por conquistar {3}{4} em {2}."
        params = ["João", "Poker", "100", "BRL"]
        language_id = "2"  # 葡萄牙语
        
        app_logger.info(f"📋 测试数据:")
        app_logger.info(f"   模板: {template}")
        app_logger.info(f"   参数: {params}")
        app_logger.info(f"   语言: {language_id}")
        
        # 渲染模板
        result = translation_manager.render_template(template, params, language_id)
        
        app_logger.info(f"\n📊 渲染结果:")
        app_logger.info(f"   结果: {result}")
        
        # 检查是否正确替换
        expected = "Parabéns a João por conquistar 100BRL em Poker."
        if result == expected:
            app_logger.info(f"   ✅ 完全正确！")
            return True
        elif "{" not in result and "}" not in result:
            app_logger.info(f"   ✅ 占位符已替换")
            return True
        else:
            app_logger.info(f"   ❌ 仍有未替换的占位符")
            return False
            
    except Exception as e:
        app_logger.info(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    app_logger.info("🔧 测试模板占位符修复")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 问题分析:")
    app_logger.info("   - 原始模板: Parabéns a {1} por conquistar {3}{4} em {2}.")
    app_logger.info("   - 问题: 纯数字占位符 {1}, {2}, {3}, {4} 没有被替换")
    app_logger.info("   - 修复: 添加对纯数字占位符的支持")
    
    # 测试直接模板渲染
    direct_ok = test_simple_placeholders()
    
    if direct_ok:
        app_logger.info(f"\n🎉 模板占位符问题已修复！")
        app_logger.info(f"💡 修复内容:")
        app_logger.info(f"   1. 支持纯数字占位符: {{1}}, {{2}}, {{3}}, {{4}}")
        app_logger.info(f"   2. 保持对带前缀占位符的支持")
        app_logger.info(f"   3. 正确的参数替换逻辑")
    else:
        app_logger.info(f"\n⚠️ 修复失败，请检查代码")

if __name__ == "__main__":
    main()
