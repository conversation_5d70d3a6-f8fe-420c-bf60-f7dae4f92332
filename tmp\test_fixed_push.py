#!/usr/bin/env python3
"""
测试修复后的推送功能
"""
import sys
import asyncio
import aiohttp
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_fixed_push():
    """测试修复后的推送功能"""
    print("🧪 测试修复后的推送功能")
    print("=" * 50)
    
    # 测试API端点
    api_url = "http://localhost:9005/api/realtime-push/template"
    
    # 测试红包雨活动 (type 400)
    test_data = {
        "business_no": "39bac42a",
        "type": 400,
        "params": [
            "测试用户",
            "400"  # 红包雨活动
        ]
    }
    
    print(f"📡 API地址: {api_url}")
    print(f"📋 测试数据: {test_data}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(api_url, json=test_data, timeout=aiohttp.ClientTimeout(total=15)) as response:
                status = response.status
                text = await response.text()
                
                print(f"\n📊 API响应:")
                print(f"   状态码: {status}")
                print(f"   响应: {text}")
                
                if status == 200:
                    try:
                        import json
                        response_data = json.loads(text)
                        if response_data.get("status") == "success":
                            print(f"🎉 推送成功！")
                            print(f"💬 消息已发送到Telegram频道")
                            return True
                        else:
                            print(f"⚠️ 推送失败: {response_data.get('message', '未知错误')}")
                            return False
                    except json.JSONDecodeError:
                        print(f"⚠️ 响应格式异常")
                        return False
                else:
                    print(f"❌ API调用失败")
                    return False
                    
    except aiohttp.ClientConnectorError:
        print(f"❌ 连接失败 - 服务可能未启动")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

async def main():
    """主函数"""
    print("🔍 测试修复后的定时推送功能")
    print("=" * 60)
    
    # 等待服务启动
    print("⏳ 等待服务启动...")
    await asyncio.sleep(3)
    
    # 测试推送功能
    success = await test_fixed_push()
    
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果:")
    print(f"   推送功能: {'✅ 正常' if success else '❌ 异常'}")
    
    if success:
        print(f"\n🎉 定时推送功能修复成功！")
        print(f"💡 下一步:")
        print(f"   1. 等待下一个定时任务 (01:17)")
        print(f"   2. 观察是否推送成功")
        print(f"   3. 检查Telegram频道: -1002316158105")
        print(f"   4. 如果成功，说明图片和按钮问题已解决")
    else:
        print(f"\n⚠️ 推送功能仍有问题")
        print(f"💡 可能的原因:")
        print(f"   1. 服务配置缓存未更新")
        print(f"   2. 数据库连接问题")
        print(f"   3. 通知配置仍有错误")
    
    print(f"\n📅 下次定时任务:")
    print(f"   时间: 01:17 (约22分钟后)")
    print(f"   任务: 红包雨活动推送")
    print(f"   频道: -1002316158105")

if __name__ == "__main__":
    asyncio.run(main())
