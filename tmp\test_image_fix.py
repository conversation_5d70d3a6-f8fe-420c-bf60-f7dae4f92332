#!/usr/bin/env python3
"""
测试图片发送修复
"""
import requests
import json

def test_template_message():
    """测试模板消息"""
    print("🖼️ 测试模板消息图片修复")
    print("=" * 50)
    
    try:
        # 测试预览功能
        preview_url = "http://localhost:8000/api/realtime-push/template/preview"
        preview_data = {
            "business_no": "39bac42a",
            "type": "18000",
            "params": ["user_name_zhang"]
        }
        
        print(f"📤 发送预览请求:")
        print(f"   URL: {preview_url}")
        print(f"   数据: {json.dumps(preview_data, indent=2, ensure_ascii=False)}")
        
        response = requests.post(preview_url, json=preview_data, timeout=10)
        
        print(f"\n📥 预览响应:")
        print(f"   状态: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 预览成功")
                preview = result.get("data", {})
                print(f"   模板名称: {preview.get('template_name')}")
                print(f"   语言: {preview.get('language')}")
                print(f"   渲染结果: {preview.get('rendered_message')}")
                print(f"   图片URL: {preview.get('image_url')}")
                print(f"   目标聊天: {preview.get('target_chats')}")
            else:
                print(f"❌ 预览失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 预览请求失败: {response.status_code}")
            print(f"   错误: {response.text}")
            return False
        
        # 测试实际发送（注释掉，避免真实发送）
        print(f"\n📤 测试实际发送 (仅日志，不真实发送):")
        send_url = "http://localhost:8000/api/realtime-push/template"
        send_data = {
            "business_no": "39bac42a",
            "type": "18000", 
            "params": ["user_name_zhang"]
        }
        
        print(f"   URL: {send_url}")
        print(f"   数据: {json.dumps(send_data, indent=2, ensure_ascii=False)}")
        print(f"   💡 实际发送已注释，请查看服务器日志")
        
        # 取消注释下面的代码来实际发送
        # response = requests.post(send_url, json=send_data, timeout=15)
        # print(f"   响应: {response.status_code}")
        # if response.status_code == 200:
        #     result = response.json()
        #     print(f"   结果: {result.get('message')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_health():
    """测试服务健康状态"""
    print(f"\n💚 测试服务健康状态")
    print("=" * 50)
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ API服务正常")
            return True
        else:
            print(f"❌ API服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False

def main():
    """主函数"""
    print("🚀 图片发送修复测试")
    print("=" * 60)
    
    # 1. 测试服务健康状态
    health_ok = test_health()
    
    # 2. 测试模板消息
    template_ok = test_template_message() if health_ok else False
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   💚 服务健康: {'✅ 正常' if health_ok else '❌ 异常'}")
    print(f"   🖼️ 模板消息: {'✅ 正常' if template_ok else '❌ 异常'}")
    
    if health_ok and template_ok:
        print(f"\n🎉 图片发送修复测试通过！")
        print(f"💡 修复要点:")
        print(f"   • 检查图片URL有效性")
        print(f"   • 无效URL时跳过图片发送")
        print(f"   • 发送纯文本消息作为兜底")
        print(f"   • 增加详细日志便于调试")
    else:
        print(f"\n⚠️ 测试失败，请检查服务状态")
    
    print("=" * 60)
    
    return 0 if (health_ok and template_ok) else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        exit(1)
