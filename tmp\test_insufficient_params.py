#!/usr/bin/env python3
"""
测试参数不足时的处理
验证多余的占位符用空字符串填充
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import translation_manager


def test_insufficient_params():
    """测试参数不足的情况"""
    print("🔧 测试参数不足时的处理")
    print("=" * 50)
    
    # 初始化翻译管理器
    success = translation_manager.load_translations()
    if not success:
        print("❌ 翻译管理器初始化失败")
        return False
    
    print("✅ 翻译管理器初始化成功")
    
    # 测试用例
    test_cases = [
        {
            "name": "参数完全匹配",
            "template": "用户 {arg1} 在 {arg2} 获得 {arg3}",
            "params": ["user_name_zhang", "game_a", "100"],
            "language": "2",
            "expected": "用户 张三 在 老虎机 获得 100"
        },
        {
            "name": "参数不足 - 缺少1个",
            "template": "用户 {arg1} 在 {arg2} 获得 {arg3}",
            "params": ["user_name_zhang", "game_a"],  # 缺少第3个参数
            "language": "2",
            "expected": "用户 张三 在 老虎机 获得 "  # 第3个占位符用空字符串
        },
        {
            "name": "参数不足 - 缺少2个",
            "template": "用户 {arg1} 在 {arg2} 获得 {arg3}",
            "params": ["user_name_zhang"],  # 只有1个参数
            "language": "2",
            "expected": "用户 张三 在  获得 "  # 第2、3个占位符用空字符串
        },
        {
            "name": "参数不足 - 全部缺少",
            "template": "用户 {arg1} 在 {arg2} 获得 {arg3}",
            "params": [],  # 没有参数
            "language": "2",
            "expected": "用户  在  获得 "  # 所有占位符用空字符串
        },
        {
            "name": "参数过多 - 多余参数忽略",
            "template": "用户 {arg1} 获得奖励",
            "params": ["user_name_zhang", "extra1", "extra2"],  # 多余参数
            "language": "2",
            "expected": "用户 张三 获得奖励"  # 多余参数被忽略
        },
        {
            "name": "复杂模板 - 参数不足",
            "template": "恭喜 {arg1}！您在 {list2} 游戏中获得了 {list3} 积分。{arg4}",
            "params": ["user_name_zhang", ["game_a"]],  # 缺少list3和arg4
            "language": "2",
            "expected": "恭喜 张三！您在 老虎机 游戏中获得了  积分。"
        },
        {
            "name": "循环模板 - 参数不足",
            "template": "[repeat2]{arg1} 在 {list2} 获得 {list3}，[/repeat]",
            "params": ["user_name_zhang", ["game_a", "game_b"]],  # 缺少list3
            "language": "2",
            "expected": "张三 在 老虎机 获得 ，张三 在 21点 获得 ，"
        }
    ]
    
    print(f"\n📋 测试用例: {len(test_cases)} 个")
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ {case['name']}")
        print(f"   模板: '{case['template']}'")
        print(f"   参数: {case['params']}")
        print(f"   语言: {case['language']}")
        
        result = translation_manager.render_template(
            case['template'], 
            case['params'], 
            case['language']
        )
        
        print(f"   结果: '{result}'")
        print(f"   期望: '{case['expected']}'")
        
        if result == case['expected']:
            print(f"   ✅ 通过")
        else:
            print(f"   ❌ 失败")
            all_passed = False
    
    return all_passed


def test_edge_cases():
    """测试边界情况"""
    print(f"\n🎯 测试边界情况")
    print("=" * 50)
    
    edge_cases = [
        {
            "name": "空模板",
            "template": "",
            "params": ["user_name_zhang"],
            "language": "2",
            "expected": ""
        },
        {
            "name": "无占位符模板",
            "template": "这是一条固定消息",
            "params": ["user_name_zhang", "extra"],
            "language": "2",
            "expected": "这是一条固定消息"
        },
        {
            "name": "只有占位符",
            "template": "{arg1}{arg2}{arg3}",
            "params": ["A"],  # 只有1个参数
            "language": "1",
            "expected": "A"  # 后面两个占位符用空字符串
        },
        {
            "name": "不连续的占位符",
            "template": "{arg1} 和 {arg3}",  # 跳过了arg2
            "params": ["user_name_zhang"],  # 只有1个参数
            "language": "2",
            "expected": "张三 和 "  # arg3用空字符串
        }
    ]
    
    all_passed = True
    
    for i, case in enumerate(edge_cases, 1):
        print(f"\n{i}️⃣ {case['name']}")
        print(f"   模板: '{case['template']}'")
        print(f"   参数: {case['params']}")
        
        result = translation_manager.render_template(
            case['template'], 
            case['params'], 
            case['language']
        )
        
        print(f"   结果: '{result}'")
        print(f"   期望: '{case['expected']}'")
        
        if result == case['expected']:
            print(f"   ✅ 通过")
        else:
            print(f"   ❌ 失败")
            all_passed = False
    
    return all_passed


def main():
    """主函数"""
    print("🚀 参数不足处理测试")
    print("=" * 60)
    
    # 1. 测试参数不足
    insufficient_ok = test_insufficient_params()
    
    # 2. 测试边界情况
    edge_ok = test_edge_cases()
    
    print(f"\n" + "=" * 60)
    print("📊 测试结果:")
    print(f"   🔧 参数不足: {'✅ 通过' if insufficient_ok else '❌ 失败'}")
    print(f"   🎯 边界情况: {'✅ 通过' if edge_ok else '❌ 失败'}")
    
    if insufficient_ok and edge_ok:
        print(f"\n🎉 参数不足处理测试通过！")
        print(f"💡 关键特性:")
        print(f"   • 参数不足时用空字符串填充占位符")
        print(f"   • 多余参数自动忽略")
        print(f"   • 支持复杂模板和循环")
        print(f"   • 边界情况处理正确")
    else:
        print(f"\n⚠️ 部分测试失败，需要检查")
    
    print("=" * 60)
    
    return 0 if (insufficient_ok and edge_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
