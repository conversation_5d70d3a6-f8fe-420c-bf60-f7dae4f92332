#!/usr/bin/env python3
"""
测试修复后的int类型参数
"""
import requests
import json

def test_int_type_api():
    """测试int类型的API调用"""
    print("🔧 测试int类型API调用")
    print("=" * 50)
    
    # 测试数据
    test_cases = [
        {
            "name": "测试type=100",
            "data": {
                "business_no": "39bac42a",
                "type": 100,
                "params": ["ttttt"]
            }
        },
        {
            "name": "测试type=18000",
            "data": {
                "business_no": "39bac42a", 
                "type": 18000,
                "params": ["user_name"]
            }
        },
        {
            "name": "测试type=19000",
            "data": {
                "business_no": "39bac42a",
                "type": 19000,
                "params": ["user_name", "100"]
            }
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}: {case['name']}")
        print(f"   数据: {json.dumps(case['data'], indent=2)}")
        
        try:
            # 测试预览接口
            response = requests.post(
                "http://localhost:9005/api/realtime-push/template/preview",
                json=case['data'],
                timeout=10
            )
            
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"   状态: {result.get('status')}")
                print(f"   消息: {result.get('message')}")
                
                data = result.get('data', {})
                if 'rendered_message' in data:
                    rendered = data['rendered_message']
                    print(f"   渲染结果: {rendered[:100]}..." if len(rendered) > 100 else f"   渲染结果: {rendered}")
                
                if result.get('status') == 'success':
                    print(f"   ✅ 测试通过")
                else:
                    print(f"   ❌ 测试失败: {result.get('message')}")
            else:
                print(f"   ❌ HTTP错误: {response.text}")
                
        except Exception as e:
            print(f"   ❌ 请求异常: {e}")

def test_database_check():
    """检查数据库中的type=100配置"""
    print(f"\n🗄️ 检查数据库配置")
    print("=" * 50)
    
    try:
        import sys
        from pathlib import Path
        sys.path.insert(0, str(Path(__file__).parent))
        
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        import asyncio
        async def check_config():
            await config_manager.initialize()
            
            # 获取所有notify配置
            all_configs = config_manager.get_all_notify_configs()
            print(f"📋 总配置数: {len(all_configs)}")
            
            # 查找包含100的配置
            configs_with_100 = []
            for config in all_configs:
                types = config.get("types", [])
                if 100 in types:
                    configs_with_100.append(config)
            
            print(f"📋 包含type=100的配置: {len(configs_with_100)} 个")
            
            for config in configs_with_100:
                print(f"   ID: {config.get('id')}")
                print(f"   名称: {config.get('tgName')}")
                print(f"   类型: {config.get('types')}")
                print(f"   通知类型: {config.get('notifyType')}")
                print()
            
            # 检查实时通知配置
            realtime_configs = config_manager.get_all_notify_configs(notify_type=1)
            print(f"📋 实时通知配置: {len(realtime_configs)} 个")
            
            realtime_with_100 = []
            for config in realtime_configs:
                types = config.get("types", [])
                if 100 in types:
                    realtime_with_100.append(config)
            
            print(f"📋 实时通知中包含type=100的配置: {len(realtime_with_100)} 个")
            
            return len(realtime_with_100) > 0
        
        result = asyncio.run(check_config())
        return result
        
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 int类型参数修复测试")
    print("=" * 60)
    
    # 1. 检查数据库配置
    db_ok = test_database_check()
    
    # 2. 测试API调用
    if db_ok:
        test_int_type_api()
    else:
        print("⚠️ 数据库中没有type=100的配置，请先添加配置")
    
    print(f"\n" + "=" * 60)
    print("📊 修复说明:")
    print("   • API参数type现在只接受int类型")
    print("   • 移除了复杂的字符串转换逻辑")
    print("   • 直接进行精确匹配，避免了作用域问题")
    print("   • 提高了性能和可靠性")
    print("=" * 60)

if __name__ == "__main__":
    main()
