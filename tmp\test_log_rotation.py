#!/usr/bin/env python3
"""
测试日志轮转功能
"""
import time
from datetime import datetime
from config.logging_config import app_logger

def test_log_rotation():
    """测试日志轮转功能"""
    app_logger.info("🔧 测试日志轮转功能")
    app_logger.info("=" * 60)
    
    # 记录当前时间
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    app_logger.info(f"📅 当前时间: {current_time}")
    
    # 测试不同级别的日志
    app_logger.info("📱 这是一条INFO级别的日志")
    app_logger.warning("⚠️ 这是一条WARNING级别的日志")
    app_logger.error("❌ 这是一条ERROR级别的日志")
    
    # 模拟应用程序启动
    app_logger.info("🚀 应用程序启动")
    app_logger.info("🔧 初始化配置")
    app_logger.info("🗄️ 连接数据库")
    app_logger.info("📡 启动API服务")
    
    # 模拟一些业务操作
    for i in range(5):
        app_logger.info(f"📋 处理业务操作 #{i+1}")
        time.sleep(0.1)  # 短暂延迟
    
    # 模拟API请求
    app_logger.info("📥 收到API请求: POST /api/realtime-push/template")
    app_logger.info("📤 API响应: 200 OK")
    
    # 模拟数据库操作
    app_logger.info("🗄️ 查询数据库: SELECT * FROM c_activity WHERE id=1")
    app_logger.info("✅ 数据库查询成功")
    
    # 模拟推送操作
    app_logger.info("📤 发送Telegram消息")
    app_logger.info("✅ 消息发送成功")
    
    app_logger.info("=" * 60)
    app_logger.info("✅ 日志轮转测试完成")

def main():
    """主函数"""
    app_logger.info("🔧 日志轮转系统测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 测试说明:")
    app_logger.info("   1. 当前日志写入: logs/log.log")
    app_logger.info("   2. 每天午夜自动轮转")
    app_logger.info("   3. 归档格式: logs/YYYY-MM-DD.log")
    app_logger.info("   4. 保留365天的日志备份")
    
    test_log_rotation()
    
    app_logger.info("=" * 80)
    app_logger.info("🎉 日志轮转系统正常工作！")
    app_logger.info("💡 功能特点:")
    app_logger.info("   1. 当前日志: logs/log.log")
    app_logger.info("   2. 自动轮转: 每天午夜")
    app_logger.info("   3. 归档命名: logs/2025-07-12.log")
    app_logger.info("   4. 追加模式: 重启时追加到log.log")
    app_logger.info("   5. 统一记录: INFO/WARNING/ERROR都在同一文件")
    
    app_logger.info("📁 日志文件说明:")
    app_logger.info("   - logs/log.log: 当前日志文件")
    app_logger.info("   - logs/2025-07-11.log: 昨天的归档日志")
    app_logger.info("   - logs/2025-07-10.log: 前天的归档日志")
    app_logger.info("   - ...")

if __name__ == "__main__":
    main()
