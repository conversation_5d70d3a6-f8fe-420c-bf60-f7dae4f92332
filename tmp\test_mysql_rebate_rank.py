#!/usr/bin/env python3
"""
测试MySQL连接和投注返利排行榜功能
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_connection():
    """测试MySQL连接"""
    print("🔗 测试MySQL连接")
    print("=" * 50)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL连接
        print("1. 初始化MySQL连接")
        success = await init_mysql()
        
        if not success:
            print("❌ MySQL连接失败")
            return False
        
        print("✅ MySQL连接成功")
        
        # 测试基本查询
        print("\n2. 测试基本查询")
        mysql_conn = get_mysql_connection()
        
        # 查询数据库版本
        result = await mysql_conn.execute_single_query("SELECT VERSION() as version")
        if result:
            print(f"   MySQL版本: {result.get('version')}")
        
        # 查询表是否存在
        table_check = await mysql_conn.execute_single_query(
            "SELECT COUNT(*) as count FROM information_schema.tables WHERE table_schema = 'wingame' AND table_name = 'ea_platform_wagered_rebate_rank_log'"
        )
        
        if table_check and table_check.get('count', 0) > 0:
            print("✅ ea_platform_wagered_rebate_rank_log 表存在")
        else:
            print("❌ ea_platform_wagered_rebate_rank_log 表不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_rank_query():
    """测试投注返利排行榜查询"""
    print(f"\n📊 测试投注返利排行榜查询")
    print("=" * 50)
    
    try:
        from database.mysql_connection import get_mysql_connection
        
        mysql_conn = get_mysql_connection()
        
        # 1. 查询表结构
        print("1. 查询表结构")
        columns = await mysql_conn.execute_query(
            "DESCRIBE ea_platform_wagered_rebate_rank_log"
        )
        
        print(f"   表字段 ({len(columns)} 个):")
        for col in columns:
            field_name = col.get('Field', '')
            field_type = col.get('Type', '')
            print(f"     {field_name}: {field_type}")
        
        # 2. 查询数据总数
        print(f"\n2. 查询数据总数")
        count_result = await mysql_conn.execute_single_query(
            "SELECT COUNT(*) as total FROM ea_platform_wagered_rebate_rank_log"
        )
        
        total_count = count_result.get('total', 0) if count_result else 0
        print(f"   总记录数: {total_count}")
        
        if total_count == 0:
            print("⚠️ 表中没有数据，无法进行进一步测试")
            return True
        
        # 3. 查询最近的数据
        print(f"\n3. 查询最近的数据")
        recent_data = await mysql_conn.execute_query(
            "SELECT * FROM ea_platform_wagered_rebate_rank_log ORDER BY created_at DESC LIMIT 5"
        )
        
        print(f"   最近 {len(recent_data)} 条记录:")
        for i, record in enumerate(recent_data, 1):
            business_no = record.get('business_no', 'N/A')
            player_name = record.get('player_name', 'N/A')
            rank_position = record.get('rank_position', 0)
            created_at = record.get('created_at', 'N/A')
            
            print(f"     {i}. 商户: {business_no}, 玩家: {player_name}, 排名: {rank_position}, 时间: {created_at}")
        
        # 4. 查询特定商户的数据
        print(f"\n4. 查询特定商户的数据")
        merchant_data = await mysql_conn.execute_query(
            "SELECT DISTINCT business_no, COUNT(*) as count FROM ea_platform_wagered_rebate_rank_log GROUP BY business_no LIMIT 10"
        )
        
        print(f"   商户数据分布:")
        for record in merchant_data:
            business_no = record.get('business_no', 'N/A')
            count = record.get('count', 0)
            print(f"     商户 {business_no}: {count} 条记录")
        
        return True
        
    except Exception as e:
        print(f"❌ 投注返利排行榜查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_rank_handler():
    """测试投注返利排行榜处理器"""
    print(f"\n🎯 测试投注返利排行榜处理器")
    print("=" * 50)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB获取测试任务
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 查找taskType=500的任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        test_task = collection.find_one({"taskType": 500, "enabled": True})
        
        if not test_task:
            print("⚠️ 没有找到taskType=500的启用任务")
            # 创建一个测试任务
            test_task = {
                "_id": "test_task_500",
                "taskType": 500,
                "business_no": "39bac42a",  # 使用已知的商户
                "notifyId": 1,  # 使用测试通知ID
                "enabled": True
            }
            print(f"   使用模拟任务进行测试")
        else:
            print(f"✅ 找到taskType=500的任务: {test_task.get('_id')}")
        
        # 测试处理器
        print(f"\n   执行处理器测试...")
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n   处理结果:")
        print(f"     成功: {result.get('success', False)}")
        print(f"     消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            print(f"     数据:")
            print(f"       模板文本长度: {len(data.get('template_text', ''))}")
            print(f"       参数数量: {len(data.get('params', []))}")
            print(f"       目标聊天数: {len(data.get('target_chats', []))}")
            print(f"       排行榜记录数: {data.get('rank_count', 0)}")
            
            # 显示参数详情
            params = data.get('params', [])
            if params:
                print(f"       参数详情:")
                for i, param in enumerate(params, 1):
                    if isinstance(param, list):
                        print(f"         参数{i}: 列表 ({len(param)} 项)")
                    else:
                        param_str = str(param)[:50]
                        print(f"         参数{i}: {param_str}...")
        
        mongo.disconnect()
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 投注返利排行榜处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scheduler_integration():
    """测试调度器集成"""
    print(f"\n🔧 测试调度器集成")
    print("=" * 50)
    
    try:
        from scheduler.task_scheduler import TaskScheduler
        
        # 创建调度器
        scheduler = TaskScheduler()
        
        print("1. 初始化调度器（包含MySQL）")
        success = await scheduler.start()
        
        if not success:
            print("❌ 调度器初始化失败")
            return False
        
        print("✅ 调度器初始化成功（MongoDB + MySQL）")
        
        # 停止调度器
        await scheduler.stop()
        print("✅ 调度器正常关闭")
        
        return True
        
    except Exception as e:
        print(f"❌ 调度器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔗 MySQL连接和投注返利排行榜功能测试")
    print("=" * 80)
    
    # 1. 测试MySQL连接
    mysql_ok = await test_mysql_connection()
    
    # 2. 测试排行榜查询
    query_ok = False
    if mysql_ok:
        query_ok = await test_rebate_rank_query()
    
    # 3. 测试处理器
    handler_ok = False
    if mysql_ok and query_ok:
        handler_ok = await test_rebate_rank_handler()
    
    # 4. 测试调度器集成
    scheduler_ok = False
    if mysql_ok:
        scheduler_ok = await test_scheduler_integration()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   MySQL连接: {'✅ 成功' if mysql_ok else '❌ 失败'}")
    print(f"   排行榜查询: {'✅ 成功' if query_ok else '❌ 失败'}")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    print(f"   调度器集成: {'✅ 成功' if scheduler_ok else '❌ 失败'}")
    
    if mysql_ok and query_ok and handler_ok and scheduler_ok:
        print(f"\n🎉 MySQL和投注返利排行榜功能测试成功！")
        print(f"💡 功能特点:")
        print(f"   1. 双数据库支持 (MongoDB + MySQL)")
        print(f"   2. taskType=500专门处理器")
        print(f"   3. 查询ea_platform_wagered_rebate_rank_log表")
        print(f"   4. 组合排行榜消息")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 等待taskType=500任务执行")
        print(f"   3. 观察排行榜消息推送")
    else:
        print(f"\n⚠️ 测试中发现问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
