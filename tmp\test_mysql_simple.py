#!/usr/bin/env python3
"""
简单测试MySQL连接
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_basic():
    """基本MySQL连接测试"""
    print("🔗 基本MySQL连接测试")
    print("=" * 50)
    
    try:
        import aiomysql
        
        # 连接参数
        config = {
            'host': '*************',
            'port': 3306,
            'user': 'wingame',
            'password': 'ws82H4HRFbzjmNtD',
            'db': 'wingame',
            'charset': 'utf8mb4'
        }
        
        print(f"连接到: {config['host']}:{config['port']}")
        print(f"数据库: {config['db']}")
        
        # 创建连接池
        pool = await aiomysql.create_pool(**config)
        
        # 测试查询
        async with pool.acquire() as conn:
            async with conn.cursor() as cursor:
                # 查询版本
                await cursor.execute("SELECT VERSION()")
                version = await cursor.fetchone()
                print(f"✅ MySQL版本: {version[0]}")
                
                # 查询表是否存在
                await cursor.execute("""
                    SELECT COUNT(*) 
                    FROM information_schema.tables 
                    WHERE table_schema = 'wingame' 
                    AND table_name = 'ea_platform_wagered_rebate_rank_log'
                """)
                table_exists = await cursor.fetchone()
                
                if table_exists[0] > 0:
                    print("✅ ea_platform_wagered_rebate_rank_log 表存在")
                    
                    # 查询表数据
                    await cursor.execute("SELECT COUNT(*) FROM ea_platform_wagered_rebate_rank_log")
                    count = await cursor.fetchone()
                    print(f"📊 表中记录数: {count[0]}")
                    
                    if count[0] > 0:
                        # 查询最近的记录
                        await cursor.execute("""
                            SELECT business_no, player_name, rank_position, created_at 
                            FROM ea_platform_wagered_rebate_rank_log 
                            ORDER BY created_at DESC 
                            LIMIT 3
                        """)
                        records = await cursor.fetchall()
                        
                        print(f"📋 最近的记录:")
                        for i, record in enumerate(records, 1):
                            print(f"   {i}. 商户: {record[0]}, 玩家: {record[1]}, 排名: {record[2]}, 时间: {record[3]}")
                else:
                    print("❌ ea_platform_wagered_rebate_rank_log 表不存在")
        
        # 关闭连接池
        pool.close()
        await pool.wait_closed()
        
        print("✅ MySQL连接测试成功")
        return True
        
    except Exception as e:
        print(f"❌ MySQL连接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config_loading():
    """测试配置加载"""
    print(f"\n⚙️ 测试配置加载")
    print("=" * 50)
    
    try:
        from config.settings import settings
        
        print(f"MySQL配置:")
        print(f"   主机: {settings.db_host}")
        print(f"   端口: {settings.db_port}")
        print(f"   用户: {settings.db_user}")
        print(f"   数据库: {settings.db_name}")
        print(f"   密码: {'*' * len(settings.db_password)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔗 MySQL连接简单测试")
    print("=" * 60)
    
    # 1. 测试配置加载
    config_ok = await test_config_loading()
    
    # 2. 测试MySQL连接
    mysql_ok = False
    if config_ok:
        mysql_ok = await test_mysql_basic()
    
    print(f"\n" + "=" * 60)
    print(f"📊 测试结果:")
    print(f"   配置加载: {'✅ 成功' if config_ok else '❌ 失败'}")
    print(f"   MySQL连接: {'✅ 成功' if mysql_ok else '❌ 失败'}")
    
    if config_ok and mysql_ok:
        print(f"\n🎉 MySQL连接正常！")
        print(f"💡 下一步:")
        print(f"   1. 测试完整的投注返利排行榜功能")
        print(f"   2. 重启定时任务服务")
        print(f"   3. 等待taskType=500任务执行")
    else:
        print(f"\n⚠️ MySQL连接有问题，需要检查配置")

if __name__ == "__main__":
    asyncio.run(main())
