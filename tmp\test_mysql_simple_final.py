#!/usr/bin/env python3
"""
最终测试MySQL和投注返利排行榜功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_data():
    """测试MySQL数据查询"""
    print("🔍 测试MySQL数据查询")
    print("=" * 50)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL
        await init_mysql()
        mysql_conn = get_mysql_connection()
        
        # 查询有数据的商户和日期
        print("1. 查询数据分布")
        query = """
            SELECT business_no, date, COUNT(*) as count 
            FROM ea_platform_wagered_rebate_rank_log 
            WHERE robot = 0
            GROUP BY business_no, date 
            ORDER BY date DESC, count DESC 
            LIMIT 10
        """
        
        results = await mysql_conn.execute_query(query)
        
        print(f"   数据分布 (前10条):")
        for record in results:
            business_no = record.get("business_no", "")
            date = record.get("date", "")
            count = record.get("count", 0)
            print(f"     商户: {business_no}, 日期: {date}, 记录数: {count}")
        
        # 选择有数据的商户和日期进行测试
        if results:
            test_business_no = results[0].get("business_no", "")
            test_date = results[0].get("date", "")
            
            print(f"\n2. 测试查询排行榜数据")
            print(f"   测试商户: {test_business_no}")
            print(f"   测试日期: {test_date}")
            
            # 查询排行榜数据
            rank_query = """
                SELECT 
                    id, playerId, ranking, wagered, reward, prize, currencyId
                FROM ea_platform_wagered_rebate_rank_log 
                WHERE business_no = %s AND date = %s AND robot = 0
                ORDER BY ranking ASC
                LIMIT 5
            """
            
            rank_results = await mysql_conn.execute_query(rank_query, (test_business_no, test_date))
            
            print(f"   排行榜数据 ({len(rank_results)} 条):")
            for i, record in enumerate(rank_results, 1):
                ranking = record.get("ranking", 0)
                player_id = record.get("playerId", 0)
                wagered = record.get("wagered", 0)
                reward = record.get("reward", 0)
                
                print(f"     {i}. 排名: {ranking}, 玩家: {player_id}, 投注: {wagered}, 奖励: {reward}")
            
            return test_business_no, test_date, rank_results
        else:
            print("❌ 没有找到数据")
            return None, None, []
        
    except Exception as e:
        print(f"❌ MySQL数据查询失败: {e}")
        import traceback
        traceback.print_exc()
        return None, None, []

async def test_template_rendering():
    """测试模板渲染"""
    print(f"\n📝 测试模板渲染")
    print("=" * 50)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模拟排行榜模板
        template_text = """
🏆 {arg1}

[repeat]
{arg1}. {arg2} - 投注: {arg3}, 奖励: {arg4}
[/repeat]

📊 {arg3}
        """.strip()
        
        # 模拟参数
        params = [
            "投注返利排行榜 - 2025-07-10",
            [
                ["1", "玩家1234", "1000.00", "50.00"],
                ["2", "玩家5678", "800.00", "40.00"],
                ["3", "玩家9012", "600.00", "30.00"]
            ],
            "共 3 位玩家，总投注 2400.00，总奖励 120.00"
        ]
        
        print(f"模板文本:")
        print(f"  {template_text}")
        
        print(f"\n参数:")
        for i, param in enumerate(params, 1):
            if isinstance(param, list):
                print(f"  参数{i}: 列表 ({len(param)} 项)")
                for j, item in enumerate(param[:2], 1):
                    print(f"    {j}. {item}")
                if len(param) > 2:
                    print(f"    ... 还有 {len(param) - 2} 项")
            else:
                print(f"  参数{i}: {param}")
        
        # 渲染模板
        language_id = "2"  # 葡萄牙语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n渲染结果:")
        print(f"  语言: {language_id}")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler_with_real_data():
    """使用真实数据测试投注返利处理器"""
    print(f"\n🎯 使用真实数据测试投注返利处理器")
    print("=" * 50)
    
    try:
        # 先获取真实数据
        business_no, test_date, rank_data = await test_mysql_data()
        
        if not business_no or not rank_data:
            print("⚠️ 没有真实数据，跳过处理器测试")
            return False
        
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "test_task_500_real",
            "taskType": 500,
            "business_no": business_no,
            "notifyId": 1,  # 使用已存在的通知配置
            "enabled": True
        }
        
        print(f"测试任务:")
        print(f"  商户: {test_task['business_no']}")
        print(f"  类型: {test_task['taskType']}")
        print(f"  通知ID: {test_task['notifyId']}")
        print(f"  数据日期: {test_date}")
        print(f"  排行榜记录: {len(rank_data)} 条")
        
        # 执行处理器
        print(f"\n执行处理器...")
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            print(f"\n数据详情:")
            print(f"  模板文本长度: {len(data.get('template_text', ''))}")
            print(f"  参数数量: {len(data.get('params', []))}")
            print(f"  目标聊天数: {len(data.get('target_chats', []))}")
            print(f"  排行榜记录数: {data.get('rank_count', 0)}")
            
            # 显示参数详情
            params = data.get('params', [])
            if len(params) >= 3:
                print(f"\n参数详情:")
                print(f"  参数1 (标题): {params[0]}")
                
                if isinstance(params[1], list):
                    print(f"  参数2 (排行榜): {len(params[1])} 条记录")
                    for i, rank_item in enumerate(params[1][:3], 1):
                        if isinstance(rank_item, list) and len(rank_item) >= 4:
                            print(f"    {i}. 排名:{rank_item[0]}, 玩家:{rank_item[1]}, 投注:{rank_item[2]}, 奖励:{rank_item[3]}")
                
                print(f"  参数3 (总结): {params[2]}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔗 MySQL和投注返利排行榜功能最终测试")
    print("=" * 80)
    
    # 1. 测试MySQL数据
    business_no, test_date, rank_data = await test_mysql_data()
    mysql_ok = business_no is not None and len(rank_data) > 0
    
    # 2. 测试模板渲染
    template_ok = await test_template_rendering()
    
    # 3. 使用真实数据测试处理器
    handler_ok = False
    if mysql_ok:
        handler_ok = await test_rebate_handler_with_real_data()
    
    print(f"\n" + "=" * 80)
    print(f"📊 最终测试结果:")
    print(f"   MySQL数据: {'✅ 成功' if mysql_ok else '❌ 失败'}")
    print(f"   模板渲染: {'✅ 成功' if template_ok else '❌ 失败'}")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if mysql_ok and template_ok and handler_ok:
        print(f"\n🎉 MySQL和投注返利排行榜功能完全正常！")
        print(f"💡 功能特点:")
        print(f"   1. 成功连接MySQL数据库")
        print(f"   2. 查询ea_platform_wagered_rebate_rank_log表")
        print(f"   3. 处理真实排行榜数据")
        print(f"   4. 生成格式化消息模板")
        print(f"   5. 支持多语言和嵌套数组")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 等待taskType=500任务执行")
        print(f"   3. 观察排行榜消息推送到Telegram")
        
        if business_no:
            print(f"\n📋 测试数据信息:")
            print(f"   测试商户: {business_no}")
            print(f"   测试日期: {test_date}")
            print(f"   排行榜记录: {len(rank_data)} 条")
    else:
        print(f"\n⚠️ 测试中发现问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
