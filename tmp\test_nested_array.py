#!/usr/bin/env python3
"""
测试嵌套数组的repeat循环
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_your_use_case():
    """测试你的具体用例"""
    print("🎯 测试你的嵌套数组用例")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 你的用例
        template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
        
        params = [
            "user_name_li",
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
            "thank_you_message"
        ]
        
        language = "2"  # Português
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 语言: {language}")
        print(f"\n📋 说明:")
        print(f"   参数2是嵌套数组: [['10', 'USDT'], ['50', 'BRL'], ['100', 'USDC']]")
        print(f"   {Currency2} 应该取子数组的索引1: USDT, BRL, USDC")
        print(f"   {Amount3} 应该取子数组的索引0: 10, 50, 100")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        # 验证结果
        expected_parts = [
            "Parabéns user_name_li",
            "pelo depósito – USDT10",
            "pelo depósito – BRL50", 
            "pelo depósito – USDC100",
            "thank_you_message"
        ]
        
        print(f"\n🔍 验证结果:")
        result_lines = [line.strip() for line in result.strip().split('\n') if line.strip()]
        
        success = True
        for i, expected in enumerate(expected_parts):
            if i < len(result_lines):
                actual = result_lines[i]
                if expected in actual or actual in expected:
                    print(f"   ✅ 第{i+1}行: {actual}")
                else:
                    print(f"   ❌ 第{i+1}行: {actual} (期望包含: {expected})")
                    success = False
            else:
                print(f"   ❌ 缺少第{i+1}行")
                success = False
        
        if success:
            print(f"\n🎉 嵌套数组用例测试成功！")
        else:
            print(f"\n⚠️ 嵌套数组用例测试部分失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_corrected_template():
    """测试修正后的模板（Amount在前，Currency在后）"""
    print(f"\n🔧 测试修正后的模板")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 修正后的模板：Amount在前，Currency在后
        template = """Parabéns {test1}
[repeat]pelo depósito – {Amount2} {Currency3}
[/repeat]
{gg4}"""
        
        params = [
            "user_name_li",
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],  # Amount, Currency
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],  # 重复一份给Currency3
            "thank_you_message"
        ]
        
        language = "2"
        
        print(f"📋 修正后的模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 说明:")
        print(f"   {Amount2} 取参数2子数组的索引0: 10, 50, 100")
        print(f"   {Currency3} 取参数3子数组的索引1: USDT, BRL, USDC")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_single_param_nested():
    """测试单参数嵌套数组的最佳实践"""
    print(f"\n💡 测试单参数嵌套数组的最佳实践")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 最佳实践：使用单个嵌套数组参数
        template = """Parabéns {test1}
[repeat]pelo depósito – {Amount2} {Currency2}
[/repeat]
{gg4}"""
        
        params = [
            "user_name_li",
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],  # [Amount, Currency]
            "thank_you_message"
        ]
        
        language = "2"
        
        print(f"📋 最佳实践模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 说明:")
        print(f"   使用同一个参数2，但不同的占位符名称")
        print(f"   {Amount2} 取子数组索引0: 10, 50, 100")
        print(f"   {Currency2} 取子数组索引1: USDT, BRL, USDC")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        # 验证结果格式
        expected_format = [
            "pelo depósito – 10 USDT",
            "pelo depósito – 50 BRL",
            "pelo depósito – 100 USDC"
        ]
        
        print(f"\n🔍 验证格式:")
        for expected in expected_format:
            if expected in result:
                print(f"   ✅ 包含: {expected}")
            else:
                print(f"   ❌ 缺少: {expected}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 嵌套数组repeat循环测试")
    print("=" * 80)
    
    # 1. 测试你的原始用例
    original_ok = test_your_use_case()
    
    # 2. 测试修正后的模板
    corrected_ok = test_corrected_template()
    
    # 3. 测试最佳实践
    best_practice_ok = test_single_param_nested()
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"   🎯 原始用例: {'✅ 成功' if original_ok else '❌ 失败'}")
    print(f"   🔧 修正模板: {'✅ 成功' if corrected_ok else '❌ 失败'}")
    print(f"   💡 最佳实践: {'✅ 成功' if best_practice_ok else '❌ 失败'}")
    
    print(f"\n💡 嵌套数组使用建议:")
    print(f"   📋 模板格式:")
    print(f"      Parabéns {{test1}}")
    print(f"      [repeat]pelo depósito – {{Amount2}} {{Currency2}}")
    print(f"      [/repeat]")
    print(f"      {{gg3}}")
    
    print(f"\n   📋 参数格式:")
    print(f"      [")
    print(f"        'user_name_li',")
    print(f"        [['10', 'USDT'], ['50', 'BRL'], ['100', 'USDC']],")
    print(f"        'thank_you_message'")
    print(f"      ]")
    
    print(f"\n   📋 占位符映射:")
    print(f"      • Amount -> 子数组索引0 (金额)")
    print(f"      • Currency -> 子数组索引1 (货币)")
    print(f"      • 可以扩展更多映射规则")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
