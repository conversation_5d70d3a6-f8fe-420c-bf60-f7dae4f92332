#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 测试嵌套数组逻辑")
    
    translator = TranslationManager()
    
    # 测试用例1: {Currency2}{Amount3}
    print("用例1: {Currency2}{Amount3}")
    template1 = "Parabéns {test1}\n[repeat]pelo depósito – {Currency2}{Amount3}\n[/repeat]\n{gg4}"
    params1 = ["user_name_li", [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]], "thank_you_message"]
    
    result1 = translator.render_template(template1, params1, "2")
    print(f"结果1:\n{result1}")
    
    # 检查是否包含期望的内容
    checks1 = ["10USDT", "50BRL", "100USDC"]
    for check in checks1:
        status = "✅" if check in result1 else "❌"
        print(f"{status} 包含 {check}")
    
    print("\n" + "="*50)
    
    # 测试用例2: {Currency3}{Amount2}
    print("用例2: {Currency3}{Amount2}")
    template2 = "Parabéns {test1} {gg4}\n[repeat]pelo depósito – {Currency3}{Amount2}\n[/repeat]"
    params2 = ["user_name_li", [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]], "thank_you_message"]
    
    result2 = translator.render_template(template2, params2, "2")
    print(f"结果2:\n{result2}")
    
    # 检查是否包含期望的内容
    checks2 = ["USDT10", "BRL50", "USDC100"]
    for check in checks2:
        status = "✅" if check in result2 else "❌"
        print(f"{status} 包含 {check}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
