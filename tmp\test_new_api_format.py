#!/usr/bin/env python3
"""
测试新的API返回格式
"""
import requests
import json

def test_template_preview():
    """测试模板预览接口"""
    print("🔍 测试模板预览接口")
    print("=" * 50)
    
    url = "http://localhost:8000/api/realtime-push/template/preview"
    data = {
        "business_no": "39bac42a",
        "type": "18000",
        "params": ["test_user", "100", "bonus"]
    }
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"📤 请求: {url}")
        print(f"📋 数据: {json.dumps(data, indent=2)}")
        print(f"📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应格式:")
            print(f"   status: {result.get('status')}")
            print(f"   message: {result.get('message')}")
            print(f"   data: {type(result.get('data', {}))}")
            
            data_content = result.get('data', {})
            if data_content:
                print(f"\n📋 data内容:")
                for key, value in data_content.items():
                    if key == 'rendered_message':
                        print(f"   {key}: {value[:100]}..." if len(str(value)) > 100 else f"   {key}: {value}")
                    else:
                        print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_template_send():
    """测试模板发送接口"""
    print(f"\n📤 测试模板发送接口")
    print("=" * 50)
    
    url = "http://localhost:8000/api/realtime-push/template"
    data = {
        "business_no": "39bac42a",
        "type": "18000",
        "params": ["test_user", "200", "reward"]
    }
    
    try:
        response = requests.post(url, json=data, timeout=30)
        print(f"📤 请求: {url}")
        print(f"📋 数据: {json.dumps(data, indent=2)}")
        print(f"📥 状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 响应格式:")
            print(f"   status: {result.get('status')}")
            print(f"   message: {result.get('message')}")
            print(f"   data: {type(result.get('data', {}))}")
            
            data_content = result.get('data', {})
            if data_content:
                print(f"\n📋 data内容:")
                for key, value in data_content.items():
                    if key == 'rendered_message':
                        print(f"   {key}: {value[:100]}..." if len(str(value)) > 100 else f"   {key}: {value}")
                    elif key == 'results':
                        print(f"   {key}: {len(value)} 个结果")
                        for i, result_item in enumerate(value[:3], 1):  # 只显示前3个
                            print(f"     {i}. chat_id: {result_item.get('chat_id')}, success: {result_item.get('success')}")
                    else:
                        print(f"   {key}: {value}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_api_format_compliance():
    """测试API格式合规性"""
    print(f"\n✅ 测试API格式合规性")
    print("=" * 50)
    
    # 期望的格式
    expected_format = {
        "status": "success|failed",
        "message": "中文解释",
        "data": "json对象"
    }
    
    print("📋 期望的返回格式:")
    print(json.dumps(expected_format, indent=2, ensure_ascii=False))
    
    # 测试预览接口
    print(f"\n🔍 测试预览接口格式合规性...")
    preview_ok = test_template_preview()
    
    # 测试发送接口
    print(f"\n📤 测试发送接口格式合规性...")
    send_ok = test_template_send()
    
    print(f"\n📊 格式合规性测试结果:")
    print(f"   预览接口: {'✅ 合规' if preview_ok else '❌ 不合规'}")
    print(f"   发送接口: {'✅ 合规' if send_ok else '❌ 不合规'}")
    
    if preview_ok and send_ok:
        print(f"\n🎉 所有接口都符合新的返回格式！")
        print(f"💡 新格式特点:")
        print(f"   • status: 'success' 或 'failed'")
        print(f"   • message: 中文说明信息")
        print(f"   • data: 包含详细信息的JSON对象")
        print(f"   • data.rendered_message: 拼接好的消息内容")
        print(f"   • data.results: 发送结果详情")
    else:
        print(f"\n⚠️ 部分接口格式需要调整")
    
    return preview_ok and send_ok

def main():
    """主函数"""
    print("🚀 新API返回格式测试")
    print("=" * 60)
    
    try:
        # 检查服务器状态
        health_response = requests.get("http://localhost:8000/health", timeout=5)
        if health_response.status_code != 200:
            print("❌ 服务器未运行，请先启动统一服务器")
            return 1
        
        print("✅ 服务器运行正常")
        
        # 测试API格式
        format_ok = test_api_format_compliance()
        
        print(f"\n" + "=" * 60)
        print(f"📊 测试总结:")
        print(f"   API格式: {'✅ 符合要求' if format_ok else '❌ 需要调整'}")
        
        if format_ok:
            print(f"\n🎯 下一步建议:")
            print(f"   • 可以开始使用新的返回格式")
            print(f"   • data.rendered_message 包含完整的拼接消息")
            print(f"   • 后续可以扩展data中的其他字段")
        
        return 0 if format_ok else 1
        
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器，请确保统一服务器正在运行")
        return 1
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        return 1

if __name__ == "__main__":
    import sys
    sys.exit(main())
