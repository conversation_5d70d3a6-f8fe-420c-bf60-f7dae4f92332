#!/usr/bin/env python3
"""
测试新的架构（框架与业务分离）
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_handler_factory():
    """测试处理器工厂"""
    app_logger.info("🧪 测试处理器工厂")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_handlers import handler_factory
        
        # 获取支持的类型
        supported_types = handler_factory.get_supported_types()
        app_logger.info(f"📋 支持的类型: {supported_types}")
        
        # 测试获取处理器
        for message_type in supported_types:
            handler = handler_factory.get_handler(message_type)
            if handler:
                app_logger.info(f"✅ Type {message_type} 处理器: {handler.__class__.__name__}")
                
                # 获取默认配置（仅用于文档）
                default_config = handler.get_default_config()
                app_logger.info(f"   默认配置: {default_config}")
            else:
                app_logger.error(f"❌ Type {message_type} 处理器获取失败")
                return False
        
        # 测试不存在的类型
        invalid_handler = handler_factory.get_handler(999)
        if invalid_handler is None:
            app_logger.info("✅ 不存在的类型正确返回None")
        else:
            app_logger.error("❌ 不存在的类型应该返回None")
            return False
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_type_300_handler():
    """测试Type 300处理器"""
    app_logger.info("\n🧪 测试Type 300处理器")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_handlers import handler_factory
        
        # 获取Type 300处理器
        handler = handler_factory.get_handler(300)
        if handler is None:
            app_logger.error("❌ 无法获取Type 300处理器")
            return False
        
        app_logger.info(f"✅ 获取到处理器: {handler.__class__.__name__}")
        
        # 测试配置验证
        valid_config = {
            "check_interval": 60,
            "game_end_wait": 300,
            "query_time_range": {
                "min_ago": 240,
                "max_ago": 3600
            },
            "max_records_per_query": 1000
        }
        
        is_valid = handler.validate_config(valid_config)
        app_logger.info(f"✅ 有效配置验证: {'通过' if is_valid else '失败'}")
        
        # 测试无效配置
        invalid_config = {
            "check_interval": 60
            # 缺少其他必需字段
        }
        
        is_invalid = handler.validate_config(invalid_config)
        app_logger.info(f"✅ 无效配置验证: {'正确拒绝' if not is_invalid else '错误通过'}")
        
        # 测试获奖条件检查
        test_log = {
            "number": "test_001",
            "gameType": 102,
            "platformId": 3,
            "gameId": 200004,
            "betAmount": 10.0,
            "winAmount": 50.0,
            "playerId": 12345,
            "currencyId": 6
        }
        
        test_config = {
            "gameType": 102,
            "platformId": [2, 3],
            "gameId": [200004],
            "gameWinMul": 3,
            "language": 1,
            "text": "Congratulations to {playname1} for winning {currency3}{amount4} at {gamename2}.",
            "notifyTarget": [-1002708340396]
        }
        
        # 测试获奖条件
        is_winning = await handler.check_win_condition(test_log, test_config)
        app_logger.info(f"✅ 获奖条件检查: {'满足' if is_winning else '不满足'}")
        
        if is_winning:
            app_logger.info("🎉 模拟处理获奖日志...")
            # 注意：这里不实际发送消息，只测试处理逻辑
            # success = await handler.process_winning_log(test_log, test_config)
            # app_logger.info(f"✅ 获奖日志处理: {'成功' if success else '失败'}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_periodic_service_integration():
    """测试周期性服务集成"""
    app_logger.info("\n🧪 测试周期性服务集成")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        app_logger.info("✅ 服务初始化成功")
        
        # 检查type配置
        app_logger.info(f"📊 加载的type配置: {list(periodic_push_service.type_configs.keys())}")
        
        # 检查通知配置
        app_logger.info(f"📋 通知配置数量: {len(periodic_push_service.notify_configs)}")
        
        # 测试配置获取
        for type_key in periodic_push_service.type_configs.keys():
            message_type = int(type_key)
            type_config = await periodic_push_service.get_type_config(message_type)
            
            if type_config:
                app_logger.info(f"✅ Type {message_type} 配置获取成功")
                app_logger.info(f"   配置: {type_config}")
            else:
                app_logger.warning(f"⚠️ Type {message_type} 配置为空")
        
        # 清理
        await periodic_push_service.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 新架构测试（框架与业务分离）")
    app_logger.info("=" * 100)
    
    # 测试1: 处理器工厂
    success1 = await test_handler_factory()
    
    # 测试2: Type 300处理器
    success2 = await test_type_300_handler()
    
    # 测试3: 周期性服务集成
    success3 = await test_periodic_service_integration()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   处理器工厂: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   Type 300处理器: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   服务集成: {'✅ 通过' if success3 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2, success3]) else '❌ 存在失败'}")
    
    if all([success1, success2, success3]):
        app_logger.info("\n💡 新架构优势:")
        app_logger.info("   1. 框架与业务逻辑完全分离")
        app_logger.info("   2. 每个type有独立的处理器文件")
        app_logger.info("   3. 新增type只需创建新的处理器文件")
        app_logger.info("   4. 配置验证和错误处理统一管理")
        app_logger.info("   5. 不对未实现的type进行兜底处理")


if __name__ == "__main__":
    asyncio.run(main())
