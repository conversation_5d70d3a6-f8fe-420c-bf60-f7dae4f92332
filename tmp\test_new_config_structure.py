#!/usr/bin/env python3
"""
测试新的配置结构（按type分组）
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from config.logging_config import app_logger


async def test_new_config_structure():
    """测试新的配置结构"""
    app_logger.info("🧪 测试新的配置结构（按type分组）")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 1. 初始化服务
        app_logger.info("📋 初始化服务...")
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 2. 显示type配置
        app_logger.info(f"📊 type配置数量: {len(periodic_push_service.type_configs)}")
        
        for type_key, type_data in periodic_push_service.type_configs.items():
            app_logger.info(f"\n📋 Type {type_key}:")
            
            # 显示配置
            config = type_data.get('config', {})
            app_logger.info(f"   配置:")
            app_logger.info(f"     check_interval: {config.get('check_interval')}")
            app_logger.info(f"     game_end_wait: {config.get('game_end_wait')}")
            app_logger.info(f"     query_time_range: {config.get('query_time_range')}")
            app_logger.info(f"     max_records_per_query: {config.get('max_records_per_query')}")
            
            # 显示last_numbers
            last_numbers = type_data.get('last_numbers', {})
            app_logger.info(f"   last_numbers: {last_numbers}")
            
            # 显示更新时间
            last_updated = type_data.get('last_updated')
            app_logger.info(f"   last_updated: {last_updated}")
        
        # 3. 测试配置获取方法
        app_logger.info(f"\n🔧 测试配置获取方法:")
        
        type_300_config = await periodic_push_service.get_type_config(300)
        app_logger.info(f"   type 300 配置: {type_300_config}")
        
        last_number = await periodic_push_service.get_last_number(300, "ea_platform_agent_game_log_2025-07")
        app_logger.info(f"   type 300 last_number: {last_number}")
        
        # 4. 测试保存功能
        app_logger.info(f"\n💾 测试保存功能:")
        
        await periodic_push_service.save_last_number(300, "ea_platform_agent_game_log_2025-07", "99999")
        app_logger.info("   保存测试数据: type=300, table=ea_platform_agent_game_log_2025-07, number=99999")
        
        # 验证保存结果
        saved_number = await periodic_push_service.get_last_number(300, "ea_platform_agent_game_log_2025-07")
        app_logger.info(f"   验证保存结果: {saved_number}")
        
        if saved_number == "99999":
            app_logger.info("   ✅ 保存功能正常")
        else:
            app_logger.error("   ❌ 保存功能异常")
            return False
        
        # 5. 检查状态文件
        state_file = Path(__file__).parent.parent / "config" / "periodic_push_state.json"
        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            app_logger.info(f"\n📂 状态文件结构:")
            for type_key, type_data in state_data.items():
                app_logger.info(f"   Type {type_key}:")
                app_logger.info(f"     config: {type_data.get('config', {})}")
                app_logger.info(f"     last_numbers: {type_data.get('last_numbers', {})}")
                app_logger.info(f"     last_updated: {type_data.get('last_updated')}")
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 新配置结构测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_type_check_process():
    """测试type检查流程"""
    app_logger.info("\n🧪 测试type检查流程")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 获取配置
        if not periodic_push_service.notify_configs:
            app_logger.error("❌ 没有找到通知配置")
            return False
        
        config = list(periodic_push_service.notify_configs.values())[0]
        app_logger.info(f"📋 使用配置: {config.get('business_no')}")
        
        # 测试type检查流程
        message_type = 300
        app_logger.info(f"🔄 测试type {message_type} 检查流程")
        
        # 获取type配置
        type_config = await periodic_push_service.get_type_config(message_type)
        app_logger.info(f"   type配置: {type_config}")
        
        # 获取last_number
        table_name = "ea_platform_agent_game_log_2025-07"
        last_number = await periodic_push_service.get_last_number(message_type, table_name)
        app_logger.info(f"   last_number: {last_number}")
        
        # 模拟查询（不实际查询数据库）
        app_logger.info(f"   模拟查询参数:")
        app_logger.info(f"     table_name: {table_name}")
        app_logger.info(f"     last_number: {last_number}")
        app_logger.info(f"     type_config: {type_config}")
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ type检查流程测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 新配置结构测试")
    app_logger.info("=" * 100)
    
    # 测试1: 新配置结构
    success1 = await test_new_config_structure()
    
    # 测试2: type检查流程
    success2 = await test_type_check_process()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   新配置结构: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   type检查流程: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2]) else '❌ 存在失败'}")
    
    if success1 and success2:
        app_logger.info("\n💡 新配置结构优势:")
        app_logger.info("   1. 按type分组管理，支持不同类型的独立配置")
        app_logger.info("   2. last_numbers按type分组，避免冲突")
        app_logger.info("   3. 配置参数与type强绑定，便于扩展")
        app_logger.info("   4. 支持多表的last_number管理")


if __name__ == "__main__":
    asyncio.run(main())
