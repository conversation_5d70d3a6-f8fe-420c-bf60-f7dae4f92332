#!/usr/bin/env python3
"""
测试新的模板格式和数据转换器
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_data_converter():
    """测试数据转换器"""
    app_logger.info("🧪 测试数据转换器")
    app_logger.info("=" * 80)
    
    try:
        from common.data_converters import data_converter
        
        # 测试playerId转换
        app_logger.info("📋 测试playerId转换:")
        test_player_ids = [4743182, 2268779, 7731938, 999999999]  # 最后一个不存在
        
        for player_id in test_player_ids:
            player_name = await data_converter.get_player_name(player_id)
            app_logger.info(f"   playerId={player_id} -> playerName='{player_name}'")
        
        # 测试gameId转换
        app_logger.info("\n🎮 测试gameId转换:")
        test_game_ids = [200004, 300003, 100001, 999999]  # 最后一个不存在
        
        for game_id in test_game_ids:
            game_name = await data_converter.get_game_name(game_id)
            app_logger.info(f"   gameId={game_id} -> gameName='{game_name}'")
        
        # 测试批量转换
        app_logger.info("\n📊 测试批量转换:")
        player_name_map = await data_converter.batch_get_player_names(test_player_ids)
        app_logger.info(f"   批量玩家名: {player_name_map}")
        
        game_name_map = await data_converter.batch_get_game_names(test_game_ids)
        app_logger.info(f"   批量游戏名: {game_name_map}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 数据转换器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_new_template_format():
    """测试新的模板格式"""
    app_logger.info("\n🧪 测试新的模板格式")
    app_logger.info("=" * 80)
    
    try:
        from common.translation_manager import translation_manager
        from common.data_converters import data_converter
        
        # 模拟获奖记录
        test_log = {
            "playerId": 4743182,
            "gameId": 200004,
            "betAmount": 10.0,
            "winAmount": 50.0
        }
        
        app_logger.info(f"📋 模拟获奖记录: {test_log}")
        
        # 转换数据
        player_name = await data_converter.get_player_name(test_log["playerId"])
        game_name = await data_converter.get_game_name(test_log["gameId"])
        
        app_logger.info(f"🔄 转换结果:")
        app_logger.info(f"   playerId={test_log['playerId']} -> playerName='{player_name}'")
        app_logger.info(f"   gameId={test_log['gameId']} -> gameName='{game_name}'")
        
        # 构建参数
        params = [
            player_name,                    # 参数1 - {playname1}
            game_name,                     # 参数2 - {gamename2}
            "USDT",                       # 参数3 - {currency3}
            test_log["winAmount"]         # 参数4 - {amount4}
        ]
        
        app_logger.info(f"📋 模板参数: {params}")
        
        # 测试模板渲染
        template_text = "Congratulations to {playname1} for winning {currency3}{amount4} at {gamename2}."
        language_id_str = translation_manager.get_language_id(1)
        
        app_logger.info(f"📄 模板: {template_text}")
        
        rendered_message = translation_manager.render_template(
            template_text,
            params,
            language_id_str,
            activity_id=None,
            message_type=300
        )
        
        app_logger.info(f"✅ 渲染结果: {rendered_message}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 模板格式测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_periodic_push_with_new_format():
    """测试周期性推送服务使用新格式"""
    app_logger.info("\n🧪 测试周期性推送服务使用新格式")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 周期性推送服务初始化失败")
            return False
        
        # 模拟获奖记录
        test_log = {
            "number": "test_new_format_001",
            "playerId": 4743182,
            "gameId": 200004,
            "gameType": 102,
            "platformId": 3,
            "betAmount": 10.0,
            "winAmount": 50.0,
            "create_time": 1720876800
        }
        
        app_logger.info(f"📋 模拟获奖记录: {test_log}")
        
        # 获取配置
        if not periodic_push_service.notify_configs:
            app_logger.error("❌ 没有找到通知配置")
            return False
        
        config = list(periodic_push_service.notify_configs.values())[0]
        app_logger.info(f"📋 使用配置: {config.get('business_no')}")
        
        # 检查获奖条件
        is_winning = await periodic_push_service.check_win_condition(test_log, config)
        app_logger.info(f"🎯 获奖条件检查: {'✅ 满足' if is_winning else '❌ 不满足'}")
        
        if is_winning:
            # 测试处理获奖日志（不实际发送）
            app_logger.info("🎉 模拟处理获奖日志...")
            
            # 这里我们只测试数据转换和模板渲染，不实际发送消息
            from common.data_converters import data_converter
            from common.translation_manager import translation_manager
            
            # 转换数据
            player_name = await data_converter.get_player_name(test_log["playerId"])
            game_name = await data_converter.get_game_name(test_log["gameId"])
            
            # 构建参数
            params = [
                player_name,
                game_name,
                "USDT",
                test_log["winAmount"]
            ]
            
            # 渲染模板
            template_text = config.get('text', '')
            language_id_str = translation_manager.get_language_id(config.get('language', 1))
            
            rendered_message = translation_manager.render_template(
                template_text,
                params,
                language_id_str,
                activity_id=None,
                message_type=300
            )
            
            app_logger.info(f"📄 最终渲染结果: {rendered_message}")
        
        # 清理
        await periodic_push_service.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 周期性推送测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 新模板格式和数据转换器测试")
    app_logger.info("=" * 100)
    
    # 测试1: 数据转换器
    success1 = await test_data_converter()
    
    # 测试2: 新模板格式
    success2 = await test_new_template_format()
    
    # 测试3: 周期性推送服务
    success3 = await test_periodic_push_with_new_format()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   数据转换器: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   新模板格式: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   周期性推送: {'✅ 通过' if success3 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2, success3]) else '❌ 存在失败'}")


if __name__ == "__main__":
    asyncio.run(main())
