#!/usr/bin/env python3
"""
测试纯数字占位符的repeat渲染
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_numeric_placeholders():
    """测试纯数字占位符"""
    print("📝 测试纯数字占位符的repeat渲染")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 使用数据库中的实际模板
        template_text = """Daily Ranking Revealed
Today's Top Players:
[repeat]{1}{2} – {3}{4}
[/repeat]"""
        
        # 使用已验证的嵌套数组格式
        rank_list = [
            ["1", "4743182", "1000", "2,092.21"],  # [rank, playid, currency, wagered]
            ["2", "2268779", "1000", "2,035.44"],
            ["3", "7731938", "1000", "1,821.96"]
        ]
        
        params = [rank_list]
        
        print(f"模板:")
        print(f"  {template_text}")
        
        print(f"\n参数:")
        print(f"  嵌套数组: {len(rank_list)} 条记录")
        for i, item in enumerate(rank_list, 1):
            print(f"    {i}. {item}")
        
        print(f"\n占位符映射:")
        print(f"  {{1}} -> 子数组[0] (排名)")
        print(f"  {{2}} -> 子数组[1] (玩家ID)")
        print(f"  {{3}} -> 子数组[2] (货币)")
        print(f"  {{4}} -> 子数组[3] (投注额)")
        
        # 渲染模板
        language_id = "1"  # 英语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n渲染结果:")
        print(f"  语言: {language_id}")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        # 检查结果
        expected_patterns = ["14743182", "22268779", "37731938"]
        success = all(pattern in rendered_text for pattern in expected_patterns)
        
        if success and "{" not in rendered_text:
            print(f"  ✅ 渲染成功，所有占位符正确替换")
            return True
        else:
            print(f"  ❌ 渲染结果不符合预期")
            if "{" in rendered_text:
                print(f"     仍有未替换的占位符")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_your_demo():
    """测试您的demo代码逻辑"""
    print(f"\n🎯 测试您的demo代码逻辑")
    print("=" * 60)
    
    try:
        import re

        template = "Parabéns {0} \n[repeat]pelo depósito – {1}{2} tt {3}[/repeat]\n{4} {5}"

        params = [
            "user_name_li",  # 用户名
            [["20", "USDT", "aa", "t1"], ["50", "BRL", "bb", "t2"], ["1000", "USDC", "cc", "t3"]],  # 存款信息
            "thank_you_message"  # 感谢信息
        ]

        def func1(tt, pp):
            # 分割tt字符串
            start_repeat = tt.find("[repeat]")
            end_repeat = tt.find("[/repeat]")

            before_repeat = tt[:start_repeat] if start_repeat != -1 else tt
            repeat_content = tt[start_repeat + len("[repeat]"):end_repeat] if start_repeat != -1 and end_repeat != -1 else ""
            after_repeat = tt[end_repeat + len("[/repeat]"):] if end_repeat != -1 else ""

            def get_placeholders(text):
                return re.findall(r'\{(\d+)\}', text)

            # 获取占位符
            placeholders_repeat = get_placeholders(repeat_content)
            placeholders_after = get_placeholders(after_repeat)

            # 动态替换占位符
            for i, placeholder in enumerate(placeholders_repeat):
                repeat_content = repeat_content.replace(f"{{{placeholder}}}", f"{{{i}}}")

            for i, placeholder in enumerate(placeholders_after):
                after_repeat = after_repeat.replace(f"{{{placeholder}}}", f"{{{i}}}")

            # 切割参数
            before_list = []
            list_part = []
            after_list = []

            list_started = False
            for item in pp:
                if isinstance(item, list):
                    list_started = True
                    list_part = item
                elif not list_started:
                    before_list.append(item)
                else:
                    after_list.append(item)

            # 格式化
            before_combined = before_repeat.format(*before_list, *[""] * (before_repeat.count("{") - len(before_list)))
            after_combined = after_repeat.format(*after_list, *[""] * (after_repeat.count("{") - len(after_list)))

            final_text = before_combined

            for sub_item in list_part:
                final_text += repeat_content.format(*sub_item) + "\n"

            final_text += after_combined

            return final_text

        print(f"您的demo模板: {template}")
        print(f"您的demo参数: {params}")
        
        result = func1(template, params)
        print(f"\n您的demo结果:")
        print(f"  {result}")
        
        # 现在用我们的系统测试相同逻辑
        from common.translation_manager import translation_manager
        
        # 转换为我们系统的格式
        our_template = "Parabéns {test1} \n[repeat]pelo depósito – {Currency2}{Amount3} tt {Extra4}[/repeat]\n{gg5} {hh6}"
        our_params = [
            "user_name_li",
            [["20", "USDT", "aa", "t1"], ["50", "BRL", "bb", "t2"], ["1000", "USDC", "cc", "t3"]],
            "thank_you_message"
        ]
        
        our_result = translation_manager.render_template(our_template, our_params, "2")
        print(f"\n我们系统的结果:")
        print(f"  {our_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ demo测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 测试纯数字占位符修复")
    print("=" * 80)
    
    # 1. 测试纯数字占位符
    numeric_ok = test_numeric_placeholders()
    
    # 2. 测试demo逻辑
    demo_ok = test_your_demo()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   纯数字占位符: {'✅ 修复成功' if numeric_ok else '❌ 仍有问题'}")
    print(f"   demo逻辑对比: {'✅ 正常' if demo_ok else '❌ 异常'}")
    
    if numeric_ok:
        print(f"\n🎉 纯数字占位符修复成功！")
        print(f"💡 修复内容:")
        print(f"   1. 支持纯数字占位符 {{1}}, {{2}}, {{3}}, {{4}}")
        print(f"   2. 正确重新编号为 {{0}}, {{1}}, {{2}}, {{3}}")
        print(f"   3. 与您的demo逻辑一致")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息格式")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    main()
