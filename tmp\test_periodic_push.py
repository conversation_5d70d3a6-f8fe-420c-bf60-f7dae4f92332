#!/usr/bin/env python3
"""
测试周期性推送服务
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from services.periodic_push_service import periodic_push_service


async def test_periodic_push_service():
    """测试周期性推送服务"""
    app_logger.info("🧪 测试周期性推送服务")
    app_logger.info("=" * 80)
    
    try:
        # 初始化服务
        app_logger.info("🔧 初始化服务")
        success = await periodic_push_service.initialize()
        
        if not success:
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 测试配置加载
        app_logger.info("📋 测试配置加载")
        app_logger.info(f"   通知配置数量: {len(periodic_push_service.notify_configs)}")
        app_logger.info(f"   最后处理number: {periodic_push_service.last_numbers}")
        
        # 测试单次检查
        app_logger.info("🔍 测试单次周期性检查")
        await periodic_push_service.process_periodic_check()
        
        # 测试表名生成
        table_name = await periodic_push_service.get_current_table_name()
        app_logger.info(f"📊 当前表名: {table_name}")
        
        # 测试查询新日志
        app_logger.info("🔍 测试查询新游戏日志")
        new_logs = await periodic_push_service.query_new_game_logs(table_name)
        app_logger.info(f"📊 查询到 {len(new_logs)} 条新记录")
        
        if new_logs:
            # 显示前3条记录
            app_logger.info("📋 前3条记录:")
            for i, log in enumerate(new_logs[:3]):
                app_logger.info(f"   {i+1}. number={log.get('number')}, "
                              f"gameType={log.get('gameType')}, "
                              f"platformId={log.get('platformId')}, "
                              f"gameId={log.get('gameId')}, "
                              f"betAmount={log.get('betAmount')}, "
                              f"winAmount={log.get('winAmount')}")
        
        # 测试获奖条件检查
        if new_logs and periodic_push_service.notify_configs:
            app_logger.info("🎯 测试获奖条件检查")
            test_log = new_logs[0]
            
            for merchant_id, config in periodic_push_service.notify_configs.items():
                is_winning = await periodic_push_service.check_win_condition(test_log, config)
                app_logger.info(f"   商户 {merchant_id}: {'✅ 满足条件' if is_winning else '❌ 不满足条件'}")
        
        app_logger.info("✅ 周期性推送服务测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        await periodic_push_service.cleanup()


async def test_service_lifecycle():
    """测试服务生命周期"""
    app_logger.info("🔄 测试服务生命周期")
    app_logger.info("=" * 80)
    
    try:
        # 初始化
        await periodic_push_service.initialize()
        
        # 启动服务
        app_logger.info("🚀 启动服务")
        await periodic_push_service.start()
        
        # 运行10秒
        app_logger.info("⏱️ 运行10秒...")
        await asyncio.sleep(10)
        
        # 停止服务
        app_logger.info("🛑 停止服务")
        await periodic_push_service.stop()
        
        app_logger.info("✅ 服务生命周期测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 服务生命周期测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await periodic_push_service.cleanup()


async def main():
    """主函数"""
    app_logger.info("🧪 周期性推送服务测试")
    app_logger.info("=" * 100)
    
    # 测试1: 基本功能测试
    app_logger.info("📋 测试1: 基本功能测试")
    success1 = await test_periodic_push_service()
    
    app_logger.info("\n" + "=" * 100)
    
    # 测试2: 服务生命周期测试
    app_logger.info("📋 测试2: 服务生命周期测试")
    success2 = await test_service_lifecycle()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   基本功能测试: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   生命周期测试: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if success1 and success2 else '❌ 存在失败'}")


if __name__ == "__main__":
    asyncio.run(main())
