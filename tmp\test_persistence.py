#!/usr/bin/env python3
"""
测试last_numbers持久化功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from config.logging_config import app_logger


async def test_persistence():
    """测试持久化功能"""
    app_logger.info("🧪 测试last_numbers持久化功能")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 1. 初始化服务
        app_logger.info("📋 初始化服务...")
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 2. 显示初始状态
        app_logger.info(f"📊 初始last_numbers: {periodic_push_service.last_numbers}")
        
        # 3. 模拟保存一些数据
        test_data = {
            "ea_platform_agent_game_log_2025-07": "12345",
            "ea_platform_agent_game_log_2025-06": "67890"
        }
        
        app_logger.info("💾 保存测试数据...")
        for table_name, last_number in test_data.items():
            await periodic_push_service.save_last_number(table_name, last_number)
            app_logger.info(f"   {table_name} -> {last_number}")
        
        # 4. 显示保存后的状态
        app_logger.info(f"📊 保存后last_numbers: {periodic_push_service.last_numbers}")
        
        # 5. 检查状态文件
        state_file = Path(__file__).parent.parent / "config" / "periodic_push_state.json"
        if state_file.exists():
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            app_logger.info(f"📂 状态文件内容:")
            app_logger.info(f"   last_numbers: {state_data.get('last_numbers')}")
            app_logger.info(f"   last_updated: {state_data.get('last_updated')}")
        else:
            app_logger.error("❌ 状态文件不存在")
            return False
        
        # 6. 清理并重新初始化，测试加载
        app_logger.info("🔄 测试重新加载...")
        await periodic_push_service.cleanup()
        
        # 重新初始化
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 重新初始化失败")
            return False
        
        # 7. 检查是否正确加载
        app_logger.info(f"📊 重新加载后last_numbers: {periodic_push_service.last_numbers}")
        
        # 验证数据是否一致
        for table_name, expected_number in test_data.items():
            actual_number = periodic_push_service.last_numbers.get(table_name)
            if actual_number == expected_number:
                app_logger.info(f"✅ {table_name}: {actual_number} (正确)")
            else:
                app_logger.error(f"❌ {table_name}: 期望 {expected_number}, 实际 {actual_number}")
                return False
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 持久化功能测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_win_multiplier_check():
    """测试获奖倍数检查功能"""
    app_logger.info("\n🧪 测试获奖倍数检查功能")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 获取配置
        if not periodic_push_service.notify_configs:
            app_logger.error("❌ 没有找到通知配置")
            return False
        
        config = list(periodic_push_service.notify_configs.values())[0]
        game_win_mul = config.get('gameWinMul', 0)
        
        app_logger.info(f"📋 使用配置: {config.get('business_no')}")
        app_logger.info(f"🎯 配置的获奖倍数阈值: {game_win_mul}x")
        
        # 测试不同倍数的记录
        test_cases = [
            {"betAmount": 10.0, "winAmount": 20.0, "expected": 20.0/10.0 >= game_win_mul},  # 2x
            {"betAmount": 10.0, "winAmount": 30.0, "expected": 30.0/10.0 >= game_win_mul},  # 3x
            {"betAmount": 10.0, "winAmount": 40.0, "expected": 40.0/10.0 >= game_win_mul},  # 4x
            {"betAmount": 10.0, "winAmount": 50.0, "expected": 50.0/10.0 >= game_win_mul},  # 5x
            {"betAmount": 5.0, "winAmount": 20.0, "expected": 20.0/5.0 >= game_win_mul},    # 4x
        ]
        
        app_logger.info(f"\n🧪 测试不同获奖倍数:")
        
        for i, case in enumerate(test_cases):
            bet_amount = case["betAmount"]
            win_amount = case["winAmount"]
            expected = case["expected"]
            multiplier = win_amount / bet_amount
            
            # 构建测试日志
            test_log = {
                "number": f"test_{i+1}",
                "gameType": config.get('gameType'),
                "platformId": config.get('platformId', [None])[0] if config.get('platformId') else None,
                "gameId": config.get('gameId', [None])[0] if config.get('gameId') else None,
                "betAmount": bet_amount,
                "winAmount": win_amount,
                "playerId": 12345,
                "currencyId": 6
            }
            
            # 检查获奖条件
            is_winning = await periodic_push_service.check_win_condition(test_log, config)
            
            status = "✅ 满足" if is_winning else "❌ 不满足"
            expected_status = "✅ 满足" if expected else "❌ 不满足"
            
            app_logger.info(f"   投注 {bet_amount}, 赢取 {win_amount} ({multiplier:.1f}x): {status} (期望: {expected_status})")
            
            if is_winning == expected:
                app_logger.debug(f"     ✅ 检查结果正确")
            else:
                app_logger.error(f"     ❌ 检查结果错误")
                return False
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 获奖倍数检查功能测试通过")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 持久化和获奖倍数检查测试")
    app_logger.info("=" * 100)
    
    # 测试1: 持久化功能
    success1 = await test_persistence()
    
    # 测试2: 获奖倍数检查
    success2 = await test_win_multiplier_check()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   持久化功能: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   获奖倍数检查: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2]) else '❌ 存在失败'}")


if __name__ == "__main__":
    asyncio.run(main())
