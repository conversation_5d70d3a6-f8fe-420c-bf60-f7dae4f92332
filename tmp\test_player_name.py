#!/usr/bin/env python3
"""
测试玩家名称查询功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mongo_player_query():
    """测试MongoDB玩家查询"""
    print("🔍 测试MongoDB玩家查询")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 切换到wingame_game库
        db = mongo.client["wingame_game"]
        collection = db["player"]
        
        # 测试查询几个playerId
        test_player_ids = [4743182, 2268779, 7731938, 999999999]  # 最后一个是不存在的
        
        print(f"测试playerId: {test_player_ids}")
        
        for player_id in test_player_ids:
            player_doc = collection.find_one({"playerId": player_id})
            
            if player_doc:
                player_name = player_doc.get("playerName", "未知")
                print(f"  playerId={player_id} -> playerName='{player_name}' ✅")
            else:
                print(f"  playerId={player_id} -> 未找到 ❌")
        
        # 测试批量查询
        print(f"\n批量查询测试:")
        batch_docs = list(collection.find({"playerId": {"$in": test_player_ids}}))
        print(f"  批量查询结果: {len(batch_docs)} 条记录")
        
        for doc in batch_docs:
            player_id = doc.get("playerId")
            player_name = doc.get("playerName", "未知")
            print(f"    playerId={player_id} -> playerName='{player_name}'")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ MongoDB查询失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_handler_player_name():
    """测试处理器的玩家名称查询"""
    print(f"\n🎯 测试处理器的玩家名称查询")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        
        # 测试单个查询
        test_player_id = 4743182
        player_name = await rebate_rank_handler._query_player_name(test_player_id)
        print(f"单个查询: playerId={test_player_id} -> playerName='{player_name}'")
        
        # 测试批量查询
        test_player_ids = [4743182, 2268779, 7731938]
        player_name_map = await rebate_rank_handler._batch_query_player_names(test_player_ids)
        
        print(f"\n批量查询结果:")
        for player_id, player_name in player_name_map.items():
            print(f"  playerId={player_id} -> playerName='{player_name}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_handler_with_names():
    """测试完整处理器（包含玩家名称）"""
    print(f"\n🚀 测试完整处理器（包含玩家名称）")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        print(f"期望: 使用playerName替代playerId")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and isinstance(params[0], list) and len(params[0]) > 0:
                print(f"\n参数数据 (使用playerName):")
                print(f"  记录数: {len(params[0])}")
                
                # 显示前5条记录
                for i, record in enumerate(params[0][:5], 1):
                    if len(record) >= 4:
                        ranking = record[0]
                        player_name = record[1]  # 现在应该是playerName
                        currency = record[2]
                        wagered = record[3]
                        
                        print(f"  {i}. 排名={ranking}, 玩家名='{player_name}', 货币={currency}, 投注={wagered}")
                        
                        # 检查是否是playerName而不是playerId
                        if not player_name.isdigit():
                            print(f"     ✅ 使用了playerName")
                        else:
                            print(f"     ⚠️ 可能仍是playerId或玩家名就是数字")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 完整处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试玩家名称查询功能")
    print("=" * 80)
    
    # 1. 测试MongoDB查询
    mongo_ok = await test_mongo_player_query()
    
    # 2. 测试处理器查询方法
    handler_ok = await test_handler_player_name()
    
    # 3. 测试完整处理器
    full_ok = await test_full_handler_with_names()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   MongoDB查询: {'✅ 成功' if mongo_ok else '❌ 失败'}")
    print(f"   处理器查询: {'✅ 成功' if handler_ok else '❌ 失败'}")
    print(f"   完整处理器: {'✅ 成功' if full_ok else '❌ 失败'}")
    
    if mongo_ok and handler_ok and full_ok:
        print(f"\n🎉 玩家名称查询功能完全正常！")
        print(f"💡 功能特点:")
        print(f"   1. 批量查询MongoDB wingame_game.player表")
        print(f"   2. 通过playerId获取playerName")
        print(f"   3. 在模板中使用playerName替代playerId")
        print(f"   4. 查询失败时降级使用playerId")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息中的玩家名称")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
