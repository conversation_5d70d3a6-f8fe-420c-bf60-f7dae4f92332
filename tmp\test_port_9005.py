#!/usr/bin/env python3
"""
测试9005端口是否正常工作
"""
import requests
import time

def test_port_9005():
    """测试9005端口"""
    print("🔍 测试9005端口")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:9005/health", timeout=5)
        if response.status_code == 200:
            print("✅ 9005端口正常工作")
            print(f"   响应: {response.json()}")
            return True
        else:
            print(f"❌ 9005端口响应异常: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到9005端口，服务器可能未启动")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_port_8000():
    """测试8000端口是否还在使用"""
    print("\n🔍 检查8000端口")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=3)
        if response.status_code == 200:
            print("⚠️ 8000端口仍在工作，可能有其他服务")
            return True
        else:
            print("✅ 8000端口未响应")
            return False
    except requests.exceptions.ConnectionError:
        print("✅ 8000端口未使用")
        return False
    except Exception as e:
        print(f"❌ 8000端口测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 端口测试")
    print("=" * 40)
    
    # 测试9005端口
    port_9005_ok = test_port_9005()
    
    # 测试8000端口
    port_8000_used = test_port_8000()
    
    print(f"\n📊 测试结果:")
    print(f"   9005端口: {'✅ 正常' if port_9005_ok else '❌ 异常'}")
    print(f"   8000端口: {'⚠️ 仍在使用' if port_8000_used else '✅ 未使用'}")
    
    if port_9005_ok:
        print(f"\n🎉 服务器已在9005端口正常运行！")
        print(f"💡 可以使用以下URL测试:")
        print(f"   • 健康检查: http://localhost:9005/health")
        print(f"   • API文档: http://localhost:9005/docs")
        print(f"   • 配置状态: http://localhost:9005/api/config-status")
    else:
        print(f"\n⚠️ 9005端口未正常工作")
        print(f"💡 请检查:")
        print(f"   • 服务器是否已启动")
        print(f"   • 配置文件是否正确")
        print(f"   • 端口是否被占用")
