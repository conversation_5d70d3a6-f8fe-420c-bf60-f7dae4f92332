#!/usr/bin/env python3
"""
测试真实的推送功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from services.periodic_push_service import periodic_push_service


async def test_real_push():
    """测试真实推送功能"""
    app_logger.info("🧪 测试真实推送功能")
    app_logger.info("=" * 80)
    
    try:
        # 初始化服务
        success = await periodic_push_service.initialize()
        if not success:
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 模拟一条满足条件的获奖记录
        # 根据真实配置：gameType=102, platformId=[2,3], gameId=[200004], gameWinMul=3
        test_log = {
            "number": "test_real_push_001",
            "gameType": 102,
            "platformId": 3,      # 在允许列表中
            "gameId": 200004,     # 在允许列表中
            "playerId": 12345,
            "playerName": "TestWinner",
            "betAmount": 10.0,
            "winAmount": 50.0,  # 5倍获奖，满足3x阈值
            "create_time": 1720876800
        }
        
        app_logger.info("🎮 测试获奖记录:")
        app_logger.info(f"   玩家: {test_log['playerName']} (ID: {test_log['playerId']})")
        app_logger.info(f"   游戏: gameType={test_log['gameType']}, platformId={test_log['platformId']}")
        app_logger.info(f"   投注: {test_log['betAmount']}, 赢取: {test_log['winAmount']}")
        app_logger.info(f"   获奖倍数: {test_log['winAmount']/test_log['betAmount']:.1f}x")
        
        # 检查每个配置
        for business_no, config in periodic_push_service.notify_configs.items():
            app_logger.info(f"\n📋 测试商户配置: {business_no}")
            
            # 检查获奖条件
            is_winning = await periodic_push_service.check_win_condition(test_log, config)
            app_logger.info(f"   获奖条件检查: {'✅ 满足' if is_winning else '❌ 不满足'}")
            
            if is_winning:
                app_logger.info("🎉 开始测试推送...")
                
                # 显示推送配置
                notify_targets = config.get('notifyTarget', [])
                message_text = config.get('text', '')
                image_url = config.get('msgBanner', '')
                
                app_logger.info(f"   推送目标: {notify_targets}")
                app_logger.info(f"   消息模板: {message_text}")
                app_logger.info(f"   图片URL: {image_url}")
                
                # 格式化消息
                player_name = test_log.get('playerName', f"WG{test_log.get('playerId')}")
                win_amount = test_log.get('winAmount')
                
                try:
                    # 先尝试使用 {0}, {1}, {2}, {3} 格式
                    formatted_message = message_text.format(
                        player_name,     # {0} - 玩家名
                        "Game",         # {1} - 游戏名
                        win_amount,     # {2} - 获奖金额
                        "USDT"          # {3} - 币种
                    )
                except (IndexError, KeyError):
                    # 如果失败，使用字符串替换方法
                    formatted_message = message_text.replace('{1}', player_name) \
                                                    .replace('{2}', 'Game') \
                                                    .replace('{3}', str(win_amount)) \
                                                    .replace('{4}', 'USDT')

                app_logger.info(f"   格式化消息: {formatted_message}")
                
                # 测试推送到测试群组 (只推送到第一个数字目标)
                test_targets = [target for target in notify_targets if isinstance(target, int)]
                
                if test_targets:
                    test_target = test_targets[0]  # 使用第一个群组进行测试
                    app_logger.info(f"🚀 测试推送到群组: {test_target}")
                    
                    try:
                        from common.bot_client import bot_client
                        
                        if image_url:
                            # 发送图片消息
                            await bot_client.send_photo(
                                chat_id=test_target,
                                photo=image_url,
                                caption=f"🧪 测试推送\n\n{formatted_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 图片消息推送成功")
                        else:
                            # 发送文本消息
                            await bot_client.send_message(
                                chat_id=test_target,
                                text=f"🧪 测试推送\n\n{formatted_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 文本消息推送成功")
                        
                    except Exception as e:
                        app_logger.error(f"❌ 推送失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    app_logger.warning("⚠️ 没有找到有效的推送目标")
        
        app_logger.info("✅ 真实推送测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await periodic_push_service.cleanup()


async def main():
    """主函数"""
    app_logger.info("🧪 真实推送功能测试")
    app_logger.info("=" * 100)
    
    success = await test_real_push()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   真实推送测试: {'✅ 通过' if success else '❌ 失败'}")
    
    if success:
        app_logger.info("\n💡 测试说明:")
        app_logger.info("   1. 已发送测试消息到配置的群组")
        app_logger.info("   2. 请检查群组中是否收到推送消息")
        app_logger.info("   3. 消息格式和图片显示是否正确")


if __name__ == "__main__":
    asyncio.run(main())
