#!/usr/bin/env python3
"""
测试新格式的真实推送
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_real_push_new_format():
    """测试新格式的真实推送"""
    app_logger.info("🧪 测试新格式的真实推送")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 周期性推送服务初始化失败")
            return False
        
        # 模拟一条满足条件的获奖记录
        test_log = {
            "number": "test_new_format_real_001",
            "playerId": 4743182,
            "gameId": 200004,
            "gameType": 102,
            "platformId": 3,
            "betAmount": 10.0,
            "winAmount": 50.0,  # 5倍获奖，满足3x阈值
            "create_time": 1720876800
        }
        
        app_logger.info("🎮 测试获奖记录:")
        app_logger.info(f"   玩家ID: {test_log['playerId']}")
        app_logger.info(f"   游戏ID: {test_log['gameId']}")
        app_logger.info(f"   游戏类型: {test_log['gameType']}")
        app_logger.info(f"   平台ID: {test_log['platformId']}")
        app_logger.info(f"   投注: {test_log['betAmount']}, 赢取: {test_log['winAmount']}")
        app_logger.info(f"   获奖倍数: {test_log['winAmount']/test_log['betAmount']:.1f}x")
        
        # 检查每个配置
        for business_no, config in periodic_push_service.notify_configs.items():
            app_logger.info(f"\n📋 测试商户配置: {business_no}")
            
            # 检查获奖条件
            is_winning = await periodic_push_service.check_win_condition(test_log, config)
            app_logger.info(f"   获奖条件检查: {'✅ 满足' if is_winning else '❌ 不满足'}")
            
            if is_winning:
                app_logger.info("🎉 开始测试推送...")
                
                # 显示推送配置
                notify_targets = config.get('notifyTarget', [])
                message_text = config.get('text', '')
                image_url = config.get('msgBanner', '')
                
                app_logger.info(f"   推送目标: {notify_targets}")
                app_logger.info(f"   消息模板: {message_text}")
                app_logger.info(f"   图片URL: {image_url}")
                
                # 使用新的数据转换和模板渲染
                from common.data_converters import data_converter
                from common.translation_manager import translation_manager
                
                # 转换数据
                player_name = await data_converter.get_player_name(test_log["playerId"])
                game_name = await data_converter.get_game_name(test_log["gameId"])
                
                app_logger.info(f"   数据转换:")
                app_logger.info(f"     playerId={test_log['playerId']} -> playerName='{player_name}'")
                app_logger.info(f"     gameId={test_log['gameId']} -> gameName='{game_name}'")
                
                # 构建参数
                params = [
                    player_name,                    # 参数1 - {playname1}
                    game_name,                     # 参数2 - {gamename2}
                    "USDT",                       # 参数3 - {currency3}
                    test_log["winAmount"]         # 参数4 - {amount4}
                ]
                
                app_logger.info(f"   模板参数: {params}")
                
                # 渲染模板
                language_id_str = translation_manager.get_language_id(config.get('language', 1))
                
                rendered_message = translation_manager.render_template(
                    message_text,
                    params,
                    language_id_str,
                    activity_id=None,
                    message_type=300
                )
                
                app_logger.info(f"   格式化消息: {rendered_message}")
                
                # 测试推送到测试群组 (只推送到第一个数字目标)
                test_targets = [target for target in notify_targets if isinstance(target, int)]
                
                if test_targets:
                    test_target = test_targets[0]  # 使用第一个群组进行测试
                    app_logger.info(f"🚀 测试推送到群组: {test_target}")
                    
                    try:
                        from common.bot_client import bot_client
                        
                        if image_url:
                            # 发送图片消息
                            await bot_client.send_photo(
                                chat_id=test_target,
                                photo=image_url,
                                caption=f"🧪 新格式测试推送\n\n{rendered_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 图片消息推送成功")
                        else:
                            # 发送文本消息
                            await bot_client.send_message(
                                chat_id=test_target,
                                text=f"🧪 新格式测试推送\n\n{rendered_message}",
                                parse_mode='HTML'
                            )
                            app_logger.info("✅ 文本消息推送成功")
                        
                    except Exception as e:
                        app_logger.error(f"❌ 推送失败: {e}")
                        import traceback
                        traceback.print_exc()
                else:
                    app_logger.warning("⚠️ 没有找到有效的推送目标")
        
        # 清理
        await periodic_push_service.cleanup()
        
        app_logger.info("✅ 新格式真实推送测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 新格式真实推送测试")
    app_logger.info("=" * 100)
    
    success = await test_real_push_new_format()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   新格式真实推送: {'✅ 通过' if success else '❌ 失败'}")
    
    if success:
        app_logger.info("\n💡 测试说明:")
        app_logger.info("   1. 已发送新格式测试消息到配置的群组")
        app_logger.info("   2. 消息格式: 'Congratulations to {playerName} for winning {currency}{amount} at {gameName}.'")
        app_logger.info("   3. 使用了真实的玩家名和游戏名转换")
        app_logger.info("   4. 请检查群组中是否收到正确格式的推送消息")


if __name__ == "__main__":
    asyncio.run(main())
