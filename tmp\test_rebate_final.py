#!/usr/bin/env python3
"""
最终测试投注返利排行榜功能
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_mysql_query_simple():
    """测试简化的MySQL查询"""
    print("🔍 测试简化的MySQL查询")
    print("=" * 50)
    
    try:
        from database.mysql_connection import init_mysql, get_mysql_connection
        
        # 初始化MySQL
        await init_mysql()
        mysql_conn = get_mysql_connection()
        
        # 使用您指定的查询
        business_no = "39bac42a"
        today = "2025-07-10"
        
        query = """
            SELECT playerId, ranking, currencyId, wagered 
            FROM ea_platform_wagered_rebate_rank_log 
            WHERE business_no = %s AND date = %s 
            ORDER BY ranking
        """
        
        print(f"查询SQL: {query}")
        print(f"参数: business_no={business_no}, date={today}")
        
        results = await mysql_conn.execute_query(query, (business_no, today))
        
        print(f"\n查询结果: {len(results)} 条记录")
        for i, record in enumerate(results, 1):
            ranking = record.get("ranking", 0)
            player_id = record.get("playerId", 0)
            currency_id = record.get("currencyId", 0)
            wagered = record.get("wagered", 0)
            
            print(f"  {i}. 排名: {ranking}, 玩家: {player_id}, 货币: {currency_id}, 投注: {wagered}")
        
        return results
        
    except Exception as e:
        print(f"❌ MySQL查询失败: {e}")
        import traceback
        traceback.print_exc()
        return []

async def test_template_with_real_data():
    """使用真实数据测试模板"""
    print(f"\n📝 使用真实数据测试模板")
    print("=" * 50)
    
    try:
        # 获取真实数据
        real_data = await test_mysql_query_simple()
        
        if not real_data:
            print("⚠️ 没有真实数据，使用模拟数据")
            real_data = [
                {"ranking": 1, "playerId": 4743182, "currencyId": 1000, "wagered": 2092.21},
                {"ranking": 2, "playerId": 2268779, "currencyId": 1000, "wagered": 2035.44},
                {"ranking": 3, "playerId": 7731938, "currencyId": 1000, "wagered": 1821.96}
            ]
        
        # 准备参数 (按照新的格式)
        rank_list = []
        for record in real_data:
            ranking = record.get("ranking", 0)
            player_id = record.get("playerId", 0)
            currency_id = record.get("currencyId", 0)
            wagered = record.get("wagered", 0)
            
            rank_list.append([
                str(ranking),                                    # {rank1}
                str(player_id),                                  # {playid2}
                str(currency_id),                               # {currency3}
                f"{float(wagered):,.2f}" if wagered else "0.00" # {wagered4}
            ])
        
        # 模板
        template_text = """Daily Ranking Revealed
Today's Top Players:
[repeat]{rank1}{playid2} – {currency3}{wagered4}
[/repeat]"""
        
        # 参数
        params = [rank_list]
        
        print(f"模板:")
        print(f"  {template_text}")
        
        print(f"\n参数 (嵌套数组):")
        print(f"  记录数: {len(rank_list)}")
        for i, item in enumerate(rank_list[:3], 1):
            print(f"    {i}. {item}")
        if len(rank_list) > 3:
            print(f"    ... 还有 {len(rank_list) - 3} 条")
        
        # 渲染模板
        from common.translation_manager import translation_manager
        
        language_id = "2"  # 葡萄牙语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n渲染结果:")
        print(f"  语言: {language_id}")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler_final():
    """最终测试投注返利处理器"""
    print(f"\n🎯 最终测试投注返利处理器")
    print("=" * 50)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建真实的测试任务 (使用实际的notifyId=10)
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,  # 使用实际的notifyId
            "enabled": True
        }
        
        print(f"测试任务:")
        print(f"  ID: {test_task['_id']}")
        print(f"  商户: {test_task['business_no']}")
        print(f"  类型: {test_task['taskType']}")
        print(f"  通知ID: {test_task['notifyId']}")
        
        # 执行处理器
        print(f"\n执行处理器...")
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            print(f"\n数据详情:")
            print(f"  模板文本长度: {len(data.get('template_text', ''))}")
            print(f"  参数数量: {len(data.get('params', []))}")
            print(f"  目标聊天数: {len(data.get('target_chats', []))}")
            print(f"  排行榜记录数: {data.get('rank_count', 0)}")
            
            # 显示参数详情
            params = data.get('params', [])
            if params and isinstance(params[0], list):
                print(f"\n参数详情:")
                print(f"  嵌套数组: {len(params[0])} 条记录")
                for i, rank_item in enumerate(params[0][:3], 1):
                    if isinstance(rank_item, list) and len(rank_item) >= 4:
                        print(f"    {i}. 排名:{rank_item[0]}, 玩家:{rank_item[1]}, 货币:{rank_item[2]}, 投注:{rank_item[3]}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔗 投注返利排行榜功能最终测试")
    print("=" * 80)
    
    # 1. 测试MySQL查询
    mysql_data = await test_mysql_query_simple()
    mysql_ok = len(mysql_data) > 0
    
    # 2. 测试模板渲染
    template_ok = await test_template_with_real_data()
    
    # 3. 测试完整处理器
    handler_ok = await test_rebate_handler_final()
    
    print(f"\n" + "=" * 80)
    print(f"📊 最终测试结果:")
    print(f"   MySQL查询: {'✅ 成功' if mysql_ok else '❌ 失败'} ({len(mysql_data)} 条记录)")
    print(f"   模板渲染: {'✅ 成功' if template_ok else '❌ 失败'}")
    print(f"   处理器测试: {'✅ 成功' if handler_ok else '❌ 失败'}")
    
    if mysql_ok and template_ok and handler_ok:
        print(f"\n🎉 投注返利排行榜功能完全正常！")
        print(f"💡 功能特点:")
        print(f"   1. 查询当天的排行榜数据 (4个字段)")
        print(f"   2. 匹配模板格式: {{rank1}}{{playid2}} – {{currency3}}{{wagered4}}")
        print(f"   3. 支持嵌套数组参数")
        print(f"   4. 集成到定时任务系统")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 等待17:00定时执行taskType=500任务")
        print(f"   3. 观察排行榜消息推送到Telegram")
        
        print(f"\n📋 任务信息:")
        print(f"   商户: 39bac42a")
        print(f"   执行时间: 每天17:00")
        print(f"   通知ID: 10")
        print(f"   数据: 2025-07-10的排行榜 ({len(mysql_data)} 条记录)")
    else:
        print(f"\n⚠️ 测试中发现问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
