#!/usr/bin/env python3
"""
测试参考代码格式
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_reference_code():
    """测试参考代码"""
    print("🎯 测试参考代码")
    print("=" * 60)
    
    import re

    # 固定的 template 和 params
    template = """Daily Ranking Revealed  
Today's Top Players:
[repeat]{AAA1} {BBB2} – {CCC3}{DDD4}
[/repeat]"""

    params = [
        [["1", "A11111", "USDT", "10"], ["2", "B22222", "BRL", "20"], ["3", "C33333", "USDC", "50"]],  # 存款信息（包含多个存款项）
    ]

    def func1(tt, pp):
        # 解析 template 中的 [repeat] 部分，找出占位符
        start_repeat = tt.find("[repeat]")  # 查找[repeat]的位置
        end_repeat = tt.find("[/repeat]")  # 查找[/repeat]的位置

        # 提取 [repeat] 和 [/repeat] 之间的内容
        repeat_content = tt[start_repeat + len("[repeat]"):end_repeat] if start_repeat != -1 and end_repeat != -1 else ""

        # 获取 repeat_content 中的所有占位符，使用正则表达式提取
        placeholders_repeat = re.findall(r'\{(\w+)\}', repeat_content)

        # 提取 [repeat] 之前和之后的内容
        before_repeat = tt[:start_repeat] if start_repeat != -1 else tt
        after_repeat = tt[end_repeat + len("[/repeat]"):] if end_repeat != -1 else ""

        # 动态拆分 pp 以提取存款信息
        before_list = []
        list_part = []
        after_list = []

        list_started = False  # 用来标记列表开始的地方
        for item in pp:
            if isinstance(item, list):
                list_started = True
                list_part = item  # 找到第一个list后，记录此列表
            elif not list_started:
                before_list.append(item)  # 列表前的部分
            else:
                after_list.append(item)  # 列表后的部分

        # 确保格式化时，如果没有足够的占位符值，用空字符串填充
        before_combined = before_repeat.format(*before_list, *[""] * (before_repeat.count("{") - len(before_list)))
        after_combined = after_repeat.format(*after_list, *[""] * (after_repeat.count("{") - len(after_list)))

        # 初始化最终拼接的文本
        final_text = ""

        # 处理before_combined部分，使用位置参数格式化
        final_text += before_combined

        # 处理list_part部分，使用位置参数格式化
        for sub_item in list_part:
            # 动态生成 sub_item_dict 字典，使用占位符名称生成字典项
            sub_item_dict = {}
            for idx, placeholder in enumerate(placeholders_repeat):
                sub_item_dict[placeholder] = sub_item[idx]

            # 使用 format 格式化 repeat_content
            formatted_repeat_content = repeat_content.format(**sub_item_dict)

            final_text += formatted_repeat_content + "\n"  # 拼接格式化后的内容

        # 处理after_combined部分，使用位置参数格式化
        final_text += after_combined

        return final_text

    print(f"参考代码模板: {template}")
    print(f"参考代码参数: {params}")
    
    result = func1(template, params)
    print(f"\n参考代码结果:")
    print(f"{result}")
    
    return result

def test_our_system():
    """测试我们的系统"""
    print(f"\n🔧 测试我们的系统")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 使用数据库中的实际模板
        template_text = """Daily Ranking Revealed
Today's Top Players:
[repeat]{1}{2} – {3}{4}
[/repeat]"""
        
        # 使用参考代码的数据格式
        rank_list = [
            ["1", "4743182", "1000", "2,092.21"],  # [rank, playid, currency, wagered]
            ["2", "2268779", "1000", "2,035.44"],
            ["3", "7731938", "1000", "1,821.96"]
        ]
        
        params = [rank_list]
        
        print(f"我们的模板: {template_text}")
        print(f"我们的参数: {params}")
        
        # 渲染模板
        language_id = "1"  # 英语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n我们的结果:")
        print(f"{rendered_text}")
        
        return rendered_text
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return ""

def test_exact_format():
    """测试完全相同的格式"""
    print(f"\n🎯 测试完全相同的格式")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 使用与参考代码完全相同的格式
        template_text = """Daily Ranking Revealed  
Today's Top Players:
[repeat]{AAA1} {BBB2} – {CCC3}{DDD4}
[/repeat]"""
        
        # 使用与参考代码完全相同的参数
        rank_list = [
            ["1", "A11111", "USDT", "10"], 
            ["2", "B22222", "BRL", "20"], 
            ["3", "C33333", "USDC", "50"]
        ]
        
        params = [rank_list]
        
        print(f"完全相同的模板: {template_text}")
        print(f"完全相同的参数: {params}")
        
        # 渲染模板
        language_id = "1"  # 英语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n我们系统的结果:")
        print(f"{rendered_text}")
        
        return rendered_text
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return ""

def main():
    """主函数"""
    print("🔧 测试参考代码格式")
    print("=" * 80)
    
    # 1. 测试参考代码
    ref_result = test_reference_code()
    
    # 2. 测试我们的系统
    our_result = test_our_system()
    
    # 3. 测试完全相同的格式
    exact_result = test_exact_format()
    
    print(f"\n" + "=" * 80)
    print(f"📊 结果对比:")
    
    print(f"\n参考代码结果:")
    print(f"{ref_result}")
    
    print(f"\n我们系统结果:")
    print(f"{our_result}")
    
    print(f"\n完全相同格式结果:")
    print(f"{exact_result}")
    
    # 检查是否一致
    ref_lines = ref_result.strip().split('\n')
    exact_lines = exact_result.strip().split('\n')
    
    if len(ref_lines) == len(exact_lines):
        match = True
        for i, (ref_line, exact_line) in enumerate(zip(ref_lines, exact_lines)):
            if ref_line.strip() != exact_line.strip():
                match = False
                print(f"\n❌ 第{i+1}行不匹配:")
                print(f"   参考: '{ref_line.strip()}'")
                print(f"   我们: '{exact_line.strip()}'")
        
        if match:
            print(f"\n✅ 结果完全一致！")
        else:
            print(f"\n❌ 结果不一致")
    else:
        print(f"\n❌ 行数不一致: 参考{len(ref_lines)}行 vs 我们{len(exact_lines)}行")
    
    if "{" not in our_result and our_result.strip():
        print(f"\n🎉 我们的系统已正确处理纯数字占位符！")
        print(f"💡 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息格式")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    main()
