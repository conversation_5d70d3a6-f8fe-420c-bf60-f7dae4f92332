#!/usr/bin/env python3
"""
测试repeat循环的两种模式
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_auto_repeat():
    """测试无数字的repeat模式（循环次数由参数决定）"""
    print("🔄 测试无数字的repeat模式")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 你的测试用例
        template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
        
        params = [
            "tttttt",
            ["10", "USDT"],
            ["20", "BRL"], 
            ["100", "USDC"],
            "thank_you_message"
        ]
        
        language = "2"  # Português
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 语言: {language}")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        # 验证结果
        expected_lines = [
            "Parabéns tttttt",
            "pelo depósito – 10USDT",
            "pelo depósito – 20BRL", 
            "pelo depósito – 100USDC",
            "thank_you_message"
        ]
        
        print(f"\n🔍 验证结果:")
        result_lines = result.strip().split('\n')
        
        success = True
        for i, expected in enumerate(expected_lines):
            if i < len(result_lines):
                actual = result_lines[i].strip()
                if expected in actual or actual in expected:
                    print(f"   ✅ 第{i+1}行: {actual}")
                else:
                    print(f"   ❌ 第{i+1}行: {actual} (期望包含: {expected})")
                    success = False
            else:
                print(f"   ❌ 缺少第{i+1}行")
                success = False
        
        if success:
            print(f"\n🎉 无数字repeat模式测试成功！")
        else:
            print(f"\n⚠️ 无数字repeat模式测试部分失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fixed_repeat():
    """测试有数字的repeat模式（最大循环次数）"""
    print(f"\n🔢 测试有数字的repeat模式")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试最大循环次数限制
        template = """Rewards:
[repeat2]- {item1}: {amount2}
[/repeat]
Total items"""
        
        params = [
            ["sword", "shield", "potion", "gem"],  # 4个物品
            ["100", "200", "50", "300"]           # 4个数量
        ]
        
        language = "1"  # English
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 语言: {language}")
        print(f"📋 说明: 数组有4个元素，但repeat2限制最多2次循环")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        # 验证只循环了2次（不是4次）
        result_lines = result.strip().split('\n')
        repeat_lines = [line for line in result_lines if line.strip().startswith('-')]
        
        print(f"\n🔍 验证结果:")
        print(f"   数组长度: 4")
        print(f"   最大循环次数: 2")
        print(f"   实际循环次数: {len(repeat_lines)}")
        
        if len(repeat_lines) == 2:
            print(f"   ✅ 循环次数限制正确")
            return True
        else:
            print(f"   ❌ 循环次数错误，期望2次")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mixed_repeat():
    """测试混合使用两种repeat模式"""
    print(f"\n🔀 测试混合使用两种repeat模式")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        template = """Player {name1}:
[repeat]Deposit: {currency2} {amount3}
[/repeat]
Top rewards (max 2):
[repeat2]- {reward4}: {value5}
[/repeat]
Thank you!"""
        
        params = [
            "John",
            ["USD", "EUR", "BTC"],
            ["100", "200", "0.5"],
            ["Gold", "Silver", "Bronze", "Copper"],
            ["1000", "500", "250", "100"]
        ]
        
        language = "1"
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 说明:")
        print(f"   第一个repeat: 根据currency/amount数组循环3次")
        print(f"   第二个repeat2: 最多循环2次（虽然reward数组有4个）")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 渲染结果:")
        print(f"{result}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 repeat循环模式测试")
    print("=" * 80)
    
    # 1. 测试无数字repeat（你的用例）
    auto_ok = test_auto_repeat()
    
    # 2. 测试有数字repeat
    fixed_ok = test_fixed_repeat()
    
    # 3. 测试混合使用
    mixed_ok = test_mixed_repeat()
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"   🔄 无数字repeat: {'✅ 成功' if auto_ok else '❌ 失败'}")
    print(f"   🔢 有数字repeat: {'✅ 成功' if fixed_ok else '❌ 失败'}")
    print(f"   🔀 混合使用: {'✅ 成功' if mixed_ok else '❌ 失败'}")
    
    if auto_ok and fixed_ok and mixed_ok:
        print(f"\n🎉 所有repeat模式都正常工作！")
        print(f"💡 支持的模式:")
        print(f"   • [repeat]...[/repeat] - 循环次数由数组参数决定")
        print(f"   • [repeat数字]...[/repeat] - 最大循环次数限制")
        print(f"   • 可以在同一模板中混合使用")
        
        print(f"\n📋 你的用例:")
        print(f"   模板: Parabéns {{test1}} [repeat]pelo depósito – {{Currency2}}{{Amount3}} [/repeat] {{gg4}}")
        print(f"   参数: ['tttttt', ['10', 'USDT'], ['20', 'BRL'], ['100', 'USDC'], 'thank_you_message']")
        print(f"   结果: 会循环3次（根据Currency和Amount数组的长度）")
    else:
        print(f"\n⚠️ 部分功能异常，需要进一步调试")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
