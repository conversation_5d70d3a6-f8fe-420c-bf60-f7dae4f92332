#!/usr/bin/env python3
"""
测试Robot名称修复
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger

async def test_single_player_query():
    """测试单个玩家查询"""
    app_logger.info("🔧 测试单个玩家查询")
    app_logger.info("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import RebateRankHandler
        
        handler = RebateRankHandler()
        
        # 测试已知的robot用户
        test_player_ids = [4743182, 2268779, 7731938]
        
        for player_id in test_player_ids:
            player_name = await handler._query_player_name(player_id)
            app_logger.info(f"单个查询: playerId={player_id} -> playerName='{player_name}'")
            
            # 检查是否正确使用WG前缀
            if player_name.startswith('WG'):
                app_logger.info(f"  ✅ 正确使用Robot名称格式")
            else:
                app_logger.info(f"  👤 找到真实玩家名称")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 单个查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_batch_player_query():
    """测试批量玩家查询"""
    app_logger.info("\n🔧 测试批量玩家查询")
    app_logger.info("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import RebateRankHandler
        
        handler = RebateRankHandler()
        
        # 测试已知的robot用户
        test_player_ids = [4743182, 2268779, 7731938, 5940330, 8918979]
        
        app_logger.info(f"测试playerId列表: {test_player_ids}")
        
        player_name_map = await handler._batch_query_player_names(test_player_ids)
        
        app_logger.info(f"\n批量查询结果:")
        robot_count = 0
        real_player_count = 0
        
        for player_id, player_name in player_name_map.items():
            if player_name.startswith('WG') and player_name[2:].isdigit():
                robot_count += 1
                app_logger.info(f"  🤖 playerId={player_id} -> {player_name} (Robot)")
            else:
                real_player_count += 1
                app_logger.info(f"  👤 playerId={player_id} -> {player_name} (真实玩家)")
        
        app_logger.info(f"\n📊 统计结果:")
        app_logger.info(f"  总数: {len(player_name_map)}")
        app_logger.info(f"  真实玩家: {real_player_count}")
        app_logger.info(f"  Robot用户: {robot_count}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 批量查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_full_ranking_handler():
    """测试完整的排行榜处理器"""
    app_logger.info("\n🚀 测试完整的排行榜处理器")
    app_logger.info("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import RebateRankHandler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        handler = RebateRankHandler()
        
        # 创建测试任务
        test_task = {
            "_id": "test_robot_fix",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        app_logger.info(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        app_logger.info(f"期望: Robot用户显示为WG+playerId格式")
        
        # 执行处理器
        result = await handler.handle_task(test_task)
        
        app_logger.info(f"\n处理结果:")
        app_logger.info(f"  成功: {result.get('success', False)}")
        app_logger.info(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success'):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and len(params) > 0 and isinstance(params[0], list):
                rank_list = params[0]
                app_logger.info(f"\n📋 排行榜数据预览:")
                
                robot_count = 0
                real_player_count = 0
                
                for i, rank_item in enumerate(rank_list[:5], 1):  # 显示前5条
                    if len(rank_item) >= 2:
                        rank_str = rank_item[0]
                        player_name = rank_item[1]
                        
                        if player_name.startswith('WG') and player_name[2:].isdigit():
                            robot_count += 1
                            app_logger.info(f"  {i}. 排名{rank_str}: {player_name} 🤖 (Robot)")
                        else:
                            real_player_count += 1
                            app_logger.info(f"  {i}. 排名{rank_str}: {player_name} 👤 (真实玩家)")
                
                # 统计所有数据
                total_robot = sum(1 for row in rank_list if len(row) >= 2 and row[1].startswith('WG') and row[1][2:].isdigit())
                total_real = len(rank_list) - total_robot
                
                app_logger.info(f"\n📊 完整统计:")
                app_logger.info(f"  总排行榜记录: {len(rank_list)}")
                app_logger.info(f"  真实玩家: {total_real}")
                app_logger.info(f"  🤖 Robot用户: {total_robot}")
                
                if total_robot > 0:
                    app_logger.info(f"  ✅ Robot名称格式修复成功！")
                else:
                    app_logger.warning(f"  ⚠️ 没有发现Robot用户，可能都是真实玩家")
        
        return result.get('success', False)
        
    except Exception as e:
        app_logger.error(f"❌ 完整处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    app_logger.info("🔧 Robot名称修复测试")
    app_logger.info("=" * 80)
    
    app_logger.info("📋 修复内容:")
    app_logger.info("   1. 未找到玩家名的用户使用 'WG' + playerId 格式")
    app_logger.info("   2. 所有降级逻辑统一使用Robot名称格式")
    app_logger.info("   3. 添加详细日志区分Robot用户和真实玩家")
    
    # 执行测试
    test1_ok = await test_single_player_query()
    test2_ok = await test_batch_player_query()
    test3_ok = await test_full_ranking_handler()
    
    app_logger.info("=" * 80)
    app_logger.info(f"📊 测试结果:")
    app_logger.info(f"   单个查询: {'✅ 成功' if test1_ok else '❌ 失败'}")
    app_logger.info(f"   批量查询: {'✅ 成功' if test2_ok else '❌ 失败'}")
    app_logger.info(f"   完整处理: {'✅ 成功' if test3_ok else '❌ 失败'}")
    
    if test1_ok and test2_ok and test3_ok:
        app_logger.info(f"\n🎉 Robot名称修复完全成功！")
        app_logger.info(f"💡 现在Robot用户会显示为: *********, ********* 等格式")
        app_logger.info(f"📱 推送消息中不再显示纯数字playerId")
    else:
        app_logger.warning(f"\n⚠️ 部分测试失败，请检查配置和数据库连接")

if __name__ == "__main__":
    asyncio.run(main())
