#!/usr/bin/env python3
"""
直接测试定时任务功能
"""
import sys
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def test_scheduler_direct():
    """直接测试调度器"""
    print("🔍 直接测试定时任务功能")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 1. 连接数据库
        print("1. 连接数据库")
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        print("✅ MongoDB连接成功")
        
        # 2. 获取定时任务集合
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        if collection is None:
            print("❌ 无法获取集合")
            return False
        
        # 3. 查询启用的任务
        print("\n2. 查询启用的任务")
        enabled_tasks = list(collection.find({"enabled": True}).sort("_id", 1))
        print(f"   启用的任务数: {len(enabled_tasks)}")
        
        # 4. 分析任务
        print("\n3. 分析任务")
        current_time = datetime.now()
        current_date = current_time.date()
        day_of_week = current_date.weekday() + 1  # 1=周一, 7=周日
        day_of_month = current_date.day
        
        print(f"   当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   星期几: {day_of_week} (1=周一, 7=周日)")
        print(f"   月份日期: {day_of_month}")
        
        # 5. 查找匹配的任务
        print("\n4. 查找匹配的任务")
        matched_tasks = []
        
        for task in enabled_tasks:
            send_frequency = task.get("sendFrequency")
            schedule_days = task.get("scheduleDays", [])
            
            is_match = False
            if send_frequency == "weekly" and day_of_week in schedule_days:
                is_match = True
                print(f"   ✅ 周任务匹配: {task.get('_id')} (周{day_of_week})")
            elif send_frequency == "monthly" and day_of_month in schedule_days:
                is_match = True
                print(f"   ✅ 月任务匹配: {task.get('_id')} (第{day_of_month}天)")
            
            if is_match:
                matched_tasks.append(task)
        
        print(f"   匹配的任务总数: {len(matched_tasks)}")
        
        # 6. 检查当前时间是否有任务应该执行
        print("\n5. 检查当前时间是否有任务应该执行")
        current_time_str = current_time.strftime("%H:%M")
        print(f"   当前时间: {current_time_str}")
        
        should_execute_tasks = []
        for task in matched_tasks:
            schedule_times = task.get("scheduleTimes", [])
            task_id = task.get("_id")
            
            for schedule_time in schedule_times:
                # 处理时间格式
                if len(schedule_time.split(':')) == 3:
                    schedule_time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    schedule_time_str = schedule_time
                
                if current_time_str == schedule_time_str:
                    should_execute_tasks.append((task, schedule_time))
                    print(f"   🔥 应该执行: 任务 {task_id} 在 {schedule_time}")
        
        if not should_execute_tasks:
            print(f"   ℹ️ 当前时间没有任务需要执行")
        
        # 7. 显示接下来的执行时间
        print("\n6. 显示接下来的执行时间")
        all_times = set()
        for task in matched_tasks:
            schedule_times = task.get("scheduleTimes", [])
            for schedule_time in schedule_times:
                if len(schedule_time.split(':')) == 3:
                    time_str = ':'.join(schedule_time.split(':')[:2])
                else:
                    time_str = schedule_time
                all_times.add(time_str)
        
        sorted_times = sorted(list(all_times))
        print(f"   今天的所有执行时间点:")
        for i, time_str in enumerate(sorted_times):
            status = "🔥 当前" if time_str == current_time_str else "⏰ 待执行"
            print(f"     {status} {time_str}")
            if i >= 9:  # 只显示前10个
                break
        
        # 8. UTC时间信息
        print(f"\n7. UTC时间信息")
        utc_now = datetime.now(timezone.utc)
        local_now = datetime.now()
        
        print(f"   UTC时间: {utc_now.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        print(f"   本地时间: {local_now.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"   时差: {(local_now - utc_now.replace(tzinfo=None)).total_seconds() / 3600:.1f} 小时")
        
        # 9. 建议
        print(f"\n8. 建议")
        if should_execute_tasks:
            print(f"   🎯 当前有 {len(should_execute_tasks)} 个任务应该执行")
            print(f"   💡 可以手动触发这些任务进行测试")
        else:
            print(f"   ⏰ 当前没有任务需要执行")
            if sorted_times:
                # 找到下一个执行时间
                next_time = None
                for time_str in sorted_times:
                    if time_str > current_time_str:
                        next_time = time_str
                        break
                
                if next_time:
                    print(f"   📅 下一个执行时间: {next_time}")
                else:
                    print(f"   📅 今天剩余时间没有任务，明天会重新检查")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    success = test_scheduler_direct()
    
    if success:
        print(f"\n🎉 定时任务功能检查完成")
    else:
        print(f"\n⚠️ 定时任务功能检查失败")

if __name__ == "__main__":
    main()
