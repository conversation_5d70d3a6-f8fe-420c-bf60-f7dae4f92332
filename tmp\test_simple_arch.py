#!/usr/bin/env python3
"""
简化架构测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.logging_config import app_logger


def test_imports():
    """测试导入"""
    app_logger.info("🧪 测试导入")
    app_logger.info("=" * 60)
    
    try:
        # 测试Type 300处理器导入
        from services.type_300_handler import type_300_handler
        app_logger.info(f"✅ Type 300处理器导入成功: {type_300_handler.message_type}")
        
        # 测试周期性服务导入
        from services.periodic_push_service import periodic_push_service
        app_logger.info(f"✅ 周期性服务导入成功")
        
        # 测试处理器获取
        handler = periodic_push_service._get_handler(300)
        if handler:
            app_logger.info(f"✅ 处理器获取成功: {handler.__class__.__name__}")
        else:
            app_logger.error("❌ 处理器获取失败")
            return False
        
        # 测试配置验证
        valid_config = {
            "check_interval": 60,
            "game_end_wait": 300,
            "query_time_range": {
                "min_ago": 240,
                "max_ago": 3600
            },
            "max_records_per_query": 1000
        }
        
        is_valid = periodic_push_service._validate_type_config(valid_config)
        app_logger.info(f"✅ 配置验证: {'通过' if is_valid else '失败'}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    app_logger.info("🧪 简化架构测试")
    app_logger.info("=" * 80)
    
    success = test_imports()
    
    app_logger.info("\n" + "=" * 80)
    app_logger.info(f"📊 测试结果: {'✅ 通过' if success else '❌ 失败'}")
    
    if success:
        app_logger.info("\n💡 简化架构完成:")
        app_logger.info("   📂 services/periodic_push_service.py - 周期性推送框架")
        app_logger.info("   📂 services/type_300_handler.py - Type 300获奖推送业务")
        app_logger.info("   🔧 结构简单，按需扩展")


if __name__ == "__main__":
    main()
