#!/usr/bin/env python3
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

try:
    from common.translation_manager import TranslationManager
    
    print("🎯 测试完全展开逻辑")
    
    translator = TranslationManager()
    
    # 测试参数展开
    print("1. 测试参数展开")
    
    original_params = [
        "user_name_li",
        [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
        "thank_you_message"
    ]
    
    expanded = translator._fully_expand_params(original_params)
    print(f"原始参数: {original_params}")
    print(f"展开参数: {expanded}")
    
    expected = ["user_name_li", "10", "USDT", "50", "BRL", "100", "USDC", "thank_you_message"]
    print(f"期望展开: {expected}")
    print(f"展开正确: {expanded == expected}")
    
    # 测试模板渲染
    print(f"\n2. 测试模板渲染")
    
    template = """Parabéns {{test1}}
[repeat]pelo depósito – {{Currency2}}{{Amount3}}
[/repeat]
{{gg8}}"""
    
    print(f"模板: {template}")
    print(f"说明:")
    print(f"  test1 -> 参数1: user_name_li")
    print(f"  Currency2 -> 参数2: 10")
    print(f"  Amount3 -> 参数3: USDT")
    print(f"  gg8 -> 参数8: thank_you_message")
    
    result = translator.render_template(template, original_params, "2")
    
    print(f"\n实际结果:")
    print(f"{result}")
    
    # 检查关键部分
    checks = [
        ("user_name_li" in result, "包含用户名"),
        ("10USDT" in result, "包含10USDT"),
        ("thank_you_message" in result, "包含结尾消息")
    ]
    
    print(f"\n验证:")
    for check, desc in checks:
        status = "✅" if check else "❌"
        print(f"{status} {desc}")
    
    # 测试单次循环
    print(f"\n3. 测试单次循环")
    
    single_params = [
        "user_name_li",
        [["10", "USDT"]],
        "thank_you_message"
    ]
    
    single_expanded = translator._fully_expand_params(single_params)
    print(f"单次参数: {single_params}")
    print(f"展开后: {single_expanded}")
    
    expected_single = ["user_name_li", "10", "USDT", "thank_you_message"]
    print(f"期望: {expected_single}")
    print(f"正确: {single_expanded == expected_single}")
    
except Exception as e:
    print(f"❌ 错误: {e}")
    import traceback
    traceback.print_exc()
