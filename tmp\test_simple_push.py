#!/usr/bin/env python3
"""
简化的推送测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_simple_push():
    """简化的推送测试"""
    app_logger.info("🧪 简化推送测试")
    app_logger.info("=" * 80)
    
    try:
        # 获取真实配置
        from database.mongodb_connection import MongoDBConnection
        
        mongo_conn = MongoDBConnection()
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        collection = mongo_conn.get_collection("c_tgNotify")
        configs = list(collection.find({"types": {"$in": [300]}, "open": True}))
        
        if not configs:
            app_logger.info("📊 没有找到配置")
            return False
        
        config = configs[0]
        app_logger.info(f"📋 使用配置: {config.get('business_no')}")
        
        # 渲染消息
        from common.translation_manager import translation_manager
        
        params = ["TestWinner", "Game", 50.0, "USDT"]
        template_text = config.get('text', '')
        language_id_str = translation_manager.get_language_id(1)
        
        rendered_message = translation_manager.render_template(
            template_text, params, language_id_str, activity_id=None, message_type=300
        )
        
        app_logger.info(f"📄 渲染消息: {rendered_message}")
        
        # 获取推送目标
        notify_targets = config.get('notifyTarget', [])
        image_url = config.get('msgBanner')
        
        app_logger.info(f"📋 推送目标: {notify_targets}")
        app_logger.info(f"🖼️ 图片URL: {image_url}")
        
        # 尝试直接使用bot_client推送
        test_targets = [target for target in notify_targets if isinstance(target, int)]
        
        if test_targets:
            test_target = test_targets[0]
            app_logger.info(f"🚀 测试推送到: {test_target}")
            
            try:
                from common.bot_client import bot_client
                
                if image_url:
                    await bot_client.send_photo(
                        chat_id=test_target,
                        photo=image_url,
                        caption=f"🧪 简化测试\n\n{rendered_message}",
                        parse_mode='HTML'
                    )
                    app_logger.info("✅ 图片推送成功")
                else:
                    await bot_client.send_message(
                        chat_id=test_target,
                        text=f"🧪 简化测试\n\n{rendered_message}",
                        parse_mode='HTML'
                    )
                    app_logger.info("✅ 文本推送成功")
                
            except Exception as e:
                app_logger.error(f"❌ 推送失败: {e}")
                import traceback
                traceback.print_exc()
        
        mongo_conn.disconnect()
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 简化推送测试")
    app_logger.info("=" * 100)
    
    success = await test_simple_push()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info(f"📊 测试结果: {'✅ 成功' if success else '❌ 失败'}")


if __name__ == "__main__":
    asyncio.run(main())
