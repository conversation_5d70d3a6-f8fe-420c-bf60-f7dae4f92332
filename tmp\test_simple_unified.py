#!/usr/bin/env python3
"""
简化的统一服务器测试
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger

async def test_periodic_integration():
    """测试周期性推送集成"""
    app_logger.info("🧪 测试周期性推送集成")
    app_logger.info("=" * 80)
    
    try:
        # 测试周期性推送服务
        from services.periodic_push_service import periodic_push_service
        app_logger.info("✅ 周期性推送服务导入成功")
        
        # 初始化服务
        success = await periodic_push_service.initialize()
        if success:
            app_logger.info("✅ 周期性推送服务初始化成功")
            
            # 显示配置信息
            app_logger.info(f"📊 配置数量: {len(periodic_push_service.notify_configs)}")
            
            # 测试配置刷新
            refresh_success = await periodic_push_service.refresh_configs()
            app_logger.info(f"🔄 配置刷新: {'✅ 成功' if refresh_success else '❌ 失败'}")
            
            # 清理
            await periodic_push_service.cleanup()
            app_logger.info("✅ 服务清理完成")
            
        else:
            app_logger.error("❌ 周期性推送服务初始化失败")
            return False
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_config_manager_integration():
    """测试配置管理器集成"""
    app_logger.info("\n🧪 测试配置管理器集成")
    app_logger.info("=" * 80)
    
    try:
        # 测试配置管理器
        from scheduler.config_manager import config_manager
        app_logger.info("✅ 配置管理器导入成功")
        
        # 初始化配置管理器
        success = await config_manager.initialize()
        if success:
            app_logger.info("✅ 配置管理器初始化成功")
            
            # 获取缓存统计
            stats = config_manager.get_cache_stats()
            app_logger.info(f"📊 缓存统计: {stats}")
            
            # 检查notify缓存
            if hasattr(config_manager, 'notify_cache'):
                notify_count = len(config_manager.notify_cache)
                app_logger.info(f"📋 通知配置缓存: {notify_count} 条")
                
                # 查找types=300的配置
                type_300_count = 0
                for config in config_manager.notify_cache.values():
                    types = config.get('types', [])
                    if isinstance(types, list) and 300 in types:
                        type_300_count += 1
                
                app_logger.info(f"🎯 types=300配置: {type_300_count} 条")
            
            # 清理
            config_manager.mongo.disconnect()
            app_logger.info("✅ 配置管理器清理完成")
            
        else:
            app_logger.error("❌ 配置管理器初始化失败")
            return False
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    app_logger.info("🧪 统一服务器集成测试")
    app_logger.info("=" * 100)
    
    # 测试1: 周期性推送集成
    success1 = await test_periodic_integration()
    
    # 测试2: 配置管理器集成
    success2 = await test_config_manager_integration()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   周期性推送集成: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   配置管理器集成: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if success1 and success2 else '❌ 存在失败'}")
    
    if success1 and success2:
        app_logger.info("\n💡 集成测试通过，可以启动统一服务器")
    else:
        app_logger.info("\n⚠️ 集成测试失败，需要修复问题")

if __name__ == "__main__":
    asyncio.run(main())
