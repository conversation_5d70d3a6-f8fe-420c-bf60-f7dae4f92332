#!/usr/bin/env python3
"""
测试简化的架构（直接的业务文件）
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_type_300_handler():
    """测试Type 300处理器"""
    app_logger.info("🧪 测试Type 300处理器")
    app_logger.info("=" * 80)
    
    try:
        from services.type_300_handler import type_300_handler
        
        app_logger.info(f"✅ 成功导入处理器: {type_300_handler.__class__.__name__}")
        app_logger.info(f"   消息类型: {type_300_handler.message_type}")
        
        # 测试获奖条件检查
        test_log = {
            "number": "test_001",
            "gameType": 102,
            "platformId": 3,
            "gameId": 200004,
            "betAmount": 10.0,
            "winAmount": 50.0,
            "playerId": 12345,
            "currencyId": 6
        }
        
        test_config = {
            "gameType": 102,
            "platformId": [2, 3],
            "gameId": [200004],
            "gameWinMul": 3,
            "language": 1,
            "text": "Congratulations to {playname1} for winning {currency3}{amount4} at {gamename2}.",
            "notifyTarget": [-1002708340396]
        }
        
        # 测试获奖条件
        is_winning = await type_300_handler.check_win_condition(test_log, test_config)
        app_logger.info(f"✅ 获奖条件检查: {'满足' if is_winning else '不满足'}")
        
        # 测试不满足条件的情况
        test_log_fail = test_log.copy()
        test_log_fail["winAmount"] = 20.0  # 2倍，不满足3倍条件
        
        is_not_winning = await type_300_handler.check_win_condition(test_log_fail, test_config)
        app_logger.info(f"✅ 不满足条件检查: {'满足' if is_not_winning else '不满足'} (应该是不满足)")
        
        if is_winning and not is_not_winning:
            app_logger.info("✅ Type 300 处理器逻辑正确")
            return True
        else:
            app_logger.error("❌ Type 300 处理器逻辑错误")
            return False
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_periodic_service():
    """测试周期性服务"""
    app_logger.info("\n🧪 测试周期性服务")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        app_logger.info("✅ 服务初始化成功")
        
        # 测试处理器获取
        handler_300 = periodic_push_service._get_handler(300)
        if handler_300:
            app_logger.info(f"✅ 获取Type 300处理器成功: {handler_300.__class__.__name__}")
        else:
            app_logger.error("❌ 获取Type 300处理器失败")
            return False
        
        # 测试不存在的处理器
        handler_999 = periodic_push_service._get_handler(999)
        if handler_999 is None:
            app_logger.info("✅ 不存在的处理器正确返回None")
        else:
            app_logger.error("❌ 不存在的处理器应该返回None")
            return False
        
        # 测试配置验证
        valid_config = {
            "check_interval": 60,
            "game_end_wait": 300,
            "query_time_range": {
                "min_ago": 240,
                "max_ago": 3600
            },
            "max_records_per_query": 1000
        }
        
        is_valid = periodic_push_service._validate_type_config(valid_config)
        app_logger.info(f"✅ 有效配置验证: {'通过' if is_valid else '失败'}")
        
        # 测试无效配置
        invalid_config = {"check_interval": 60}  # 缺少其他字段
        
        is_invalid = periodic_push_service._validate_type_config(invalid_config)
        app_logger.info(f"✅ 无效配置验证: {'正确拒绝' if not is_invalid else '错误通过'}")
        
        # 检查加载的配置
        app_logger.info(f"📊 加载的type配置: {list(periodic_push_service.type_configs.keys())}")
        app_logger.info(f"📋 通知配置数量: {len(periodic_push_service.notify_configs)}")
        
        # 清理
        await periodic_push_service.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_integration():
    """测试集成功能"""
    app_logger.info("\n🧪 测试集成功能")
    app_logger.info("=" * 80)
    
    try:
        from services.periodic_push_service import periodic_push_service
        
        # 初始化服务
        if not await periodic_push_service.initialize():
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 模拟一次type检查（不实际查询数据库）
        if periodic_push_service.notify_configs:
            config = list(periodic_push_service.notify_configs.values())[0]
            app_logger.info(f"📋 使用配置: {config.get('business_no')}")
            
            # 检查types字段
            types = config.get('types', [])
            app_logger.info(f"📊 配置的types: {types}")
            
            for message_type in types:
                if message_type == 300:
                    # 获取type配置
                    type_config = await periodic_push_service.get_type_config(message_type)
                    if type_config:
                        app_logger.info(f"✅ Type {message_type} 配置获取成功")
                        app_logger.info(f"   配置: {type_config}")
                    else:
                        app_logger.warning(f"⚠️ Type {message_type} 配置为空")
        
        # 清理
        await periodic_push_service.cleanup()
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 简化架构测试")
    app_logger.info("=" * 100)
    
    # 测试1: Type 300处理器
    success1 = await test_type_300_handler()
    
    # 测试2: 周期性服务
    success2 = await test_periodic_service()
    
    # 测试3: 集成功能
    success3 = await test_integration()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   Type 300处理器: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   周期性服务: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   集成功能: {'✅ 通过' if success3 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if all([success1, success2, success3]) else '❌ 存在失败'}")
    
    if all([success1, success2, success3]):
        app_logger.info("\n💡 简化架构优势:")
        app_logger.info("   1. 结构简单，只有2个文件")
        app_logger.info("   2. 业务逻辑独立在各自的文件中")
        app_logger.info("   3. 新增type只需创建新的处理器文件")
        app_logger.info("   4. 不同type可以有完全不同的业务逻辑")
        app_logger.info("   5. 避免过度设计，按需扩展")


if __name__ == "__main__":
    asyncio.run(main())
