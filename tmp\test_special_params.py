#!/usr/bin/env python3
"""
测试特殊参数替换功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from common.translation_manager import TranslationManager

def test_activity_name_replacement():
    """测试活动名替换功能"""
    print("🎯 测试活动名替换功能")
    print("=" * 50)
    
    # 创建翻译管理器实例
    translator = TranslationManager()
    
    # 测试用例
    test_cases = [
        {
            "template": "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}",
            "params": ["张三", "18000", "1", "100"],
            "language": "1",  # 中文
            "expected_activity": "每日签到活动",
            "expected_currency": "金币"
        },
        {
            "template": "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}",
            "params": ["<PERSON>", "19000", "2", "200"],
            "language": "2",  # 英文
            "expected_activity": "Recharge Bonus",
            "expected_currency": "Diamond"
        },
        {
            "template": "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}",
            "params": ["李四", "20000", "1", "500"],
            "language": "3",  # 繁体中文
            "expected_activity": "VIP專屬活動",
            "expected_currency": "金幣"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}:")
        print(f"   模板: {case['template']}")
        print(f"   参数: {case['params']}")
        print(f"   语言: {case['language']}")
        
        try:
            # 渲染模板
            result = translator.render_template(
                case['template'], 
                case['params'], 
                case['language']
            )
            
            print(f"   结果: {result}")
            
            # 验证是否包含期望的替换
            if case['expected_activity'] in result:
                print(f"   ✅ 活动名替换成功: {case['expected_activity']}")
            else:
                print(f"   ❌ 活动名替换失败，期望: {case['expected_activity']}")
            
            if case['expected_currency'] in result:
                print(f"   ✅ 币种替换成功: {case['expected_currency']}")
            else:
                print(f"   ❌ 币种替换失败，期望: {case['expected_currency']}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_game_name_replacement():
    """测试游戏名替换功能"""
    print(f"\n🎮 测试游戏名替换功能")
    print("=" * 50)
    
    translator = TranslationManager()
    
    test_cases = [
        {
            "template": "玩家 {玩家名1} 在 {游戏名2} 中获得大奖！",
            "params": ["王五", "1001"],
            "language": "1",
            "expected": "老虎机"
        },
        {
            "template": "Player {玩家名1} won big in {游戏名2}!",
            "params": ["Alice", "1002"],
            "language": "2",
            "expected": "Baccarat"
        }
    ]
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n📋 测试用例 {i}:")
        print(f"   模板: {case['template']}")
        print(f"   参数: {case['params']}")
        
        try:
            result = translator.render_template(
                case['template'], 
                case['params'], 
                case['language']
            )
            
            print(f"   结果: {result}")
            
            if case['expected'] in result:
                print(f"   ✅ 游戏名替换成功: {case['expected']}")
            else:
                print(f"   ❌ 游戏名替换失败，期望: {case['expected']}")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_unknown_mapping():
    """测试未知映射的处理"""
    print(f"\n❓ 测试未知映射处理")
    print("=" * 50)
    
    translator = TranslationManager()
    
    # 测试不存在的活动ID
    template = "恭喜 {玩家ID1} 在{活动名2} 获得奖励"
    params = ["测试玩家", "99999"]  # 不存在的活动ID
    language = "1"
    
    print(f"📋 测试未知活动ID:")
    print(f"   模板: {template}")
    print(f"   参数: {params}")
    
    try:
        result = translator.render_template(template, params, language)
        print(f"   结果: {result}")
        
        if "99999" in result:
            print(f"   ✅ 未知ID保持原值: 99999")
        else:
            print(f"   ❌ 未知ID处理异常")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def test_mixed_params():
    """测试混合参数（有些需要替换，有些不需要）"""
    print(f"\n🔀 测试混合参数")
    print("=" * 50)
    
    translator = TranslationManager()
    
    template = "玩家 {玩家名1} 在 {活动名2} 中使用 {币种3} 购买了 {道具名4}"
    params = ["赵六", "18000", "1", "超级武器"]  # 只有活动名和币种需要替换
    language = "1"
    
    print(f"📋 测试混合参数:")
    print(f"   模板: {template}")
    print(f"   参数: {params}")
    
    try:
        result = translator.render_template(template, params, language)
        print(f"   结果: {result}")
        
        checks = [
            ("赵六" in result, "玩家名保持不变"),
            ("每日签到活动" in result, "活动名正确替换"),
            ("金币" in result, "币种正确替换"),
            ("超级武器" in result, "道具名保持不变")
        ]
        
        for check, description in checks:
            status = "✅" if check else "❌"
            print(f"   {status} {description}")
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🚀 特殊参数替换功能测试")
    print("=" * 60)
    
    # 运行各种测试
    test_activity_name_replacement()
    test_game_name_replacement()
    test_unknown_mapping()
    test_mixed_params()
    
    print(f"\n" + "=" * 60)
    print("📊 测试完成")
    print("💡 功能说明:")
    print("   • 支持活动名、游戏名、币种、等级等关键字替换")
    print("   • 根据当前语言自动选择对应翻译")
    print("   • 未找到映射时保持原值")
    print("   • 支持混合参数（部分替换，部分保持）")
    print("=" * 60)

if __name__ == "__main__":
    main()
