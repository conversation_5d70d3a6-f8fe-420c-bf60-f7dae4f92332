#!/usr/bin/env python3
"""
手动测试taskType=500任务
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timezone

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_task_500_manual():
    """手动测试taskType=500任务"""
    print("🎯 手动测试taskType=500任务")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 获取taskType=500的任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        task_500 = collection.find_one({"taskType": 500, "enabled": True})
        
        if not task_500:
            print("❌ 没有找到启用的taskType=500任务")
            return False
        
        print(f"找到任务:")
        print(f"  ID: {task_500.get('_id')}")
        print(f"  商户: {task_500.get('business_no')}")
        print(f"  taskType: {task_500.get('taskType')}")
        print(f"  notifyId: {task_500.get('notifyId')}")
        print(f"  调度时间: {task_500.get('scheduleTimes')}")
        
        # 手动执行处理器
        print(f"\n🚀 手动执行处理器...")
        result = await rebate_rank_handler.handle_task(task_500)
        
        print(f"\n📊 处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            print(f"\n数据详情:")
            print(f"  模板文本: {data.get('template_text', '')}")
            print(f"  参数数量: {len(data.get('params', []))}")
            print(f"  目标聊天数: {len(data.get('target_chats', []))}")
            print(f"  排行榜记录数: {data.get('rank_count', 0)}")
            
            # 显示参数
            params = data.get('params', [])
            if params and isinstance(params[0], list):
                print(f"\n排行榜数据 (前3条):")
                for i, rank_item in enumerate(params[0][:3], 1):
                    if isinstance(rank_item, list) and len(rank_item) >= 4:
                        print(f"  {i}. 排名:{rank_item[0]}, 玩家:{rank_item[1]}, 货币:{rank_item[2]}, 投注:{rank_item[3]}")
            
            # 测试发送消息
            print(f"\n📤 测试发送消息...")
            from scheduler.task_scheduler import TaskScheduler
            
            scheduler = TaskScheduler()
            await scheduler.start()
            
            # 使用调度器的发送方法
            await scheduler._send_composed_message(task_500, data)
            
            await scheduler.stop()
            
        mongo.disconnect()
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def update_task_time():
    """更新任务时间到当前时间+2分钟"""
    print(f"\n⏰ 更新任务时间")
    print("=" * 60)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 计算新的执行时间 (当前时间+2分钟)
        current_time = datetime.now()
        new_time = current_time.replace(second=0, microsecond=0)
        new_time = new_time.replace(minute=new_time.minute + 2)
        new_time_str = new_time.strftime("%H:%M:00")
        
        print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
        print(f"新执行时间: {new_time_str}")
        
        # 更新任务时间
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        result = collection.update_one(
            {"taskType": 500, "enabled": True},
            {"$set": {"scheduleTimes": [new_time_str]}}
        )
        
        if result.modified_count > 0:
            print(f"✅ 任务时间更新成功")
            print(f"💡 请等待 {new_time_str} 观察自动执行")
        else:
            print(f"❌ 任务时间更新失败")
        
        mongo.disconnect()
        return result.modified_count > 0
        
    except Exception as e:
        print(f"❌ 更新时间失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔍 taskType=500任务测试")
    print("=" * 80)
    
    print("选择测试方式:")
    print("1. 手动执行任务")
    print("2. 更新任务时间到2分钟后")
    print("3. 两个都执行")
    
    try:
        choice = input("\n请选择 (1-3): ").strip()
        
        if choice == "1":
            success = await test_task_500_manual()
        elif choice == "2":
            success = await update_task_time()
        elif choice == "3":
            print("🔄 执行完整测试...")
            success1 = await test_task_500_manual()
            success2 = await update_task_time()
            success = success1 and success2
        else:
            print("❌ 无效选择")
            return
        
        print(f"\n" + "=" * 80)
        if success:
            print(f"🎉 测试成功！")
            if choice in ["2", "3"]:
                print(f"💡 请观察定时任务服务日志，等待自动执行")
        else:
            print(f"⚠️ 测试失败")
            
    except KeyboardInterrupt:
        print(f"\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 测试异常: {e}")

if __name__ == "__main__":
    asyncio.run(main())
