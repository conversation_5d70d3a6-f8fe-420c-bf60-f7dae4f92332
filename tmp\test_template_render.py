#!/usr/bin/env python3
"""
测试模板渲染功能
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger


async def test_template_render():
    """测试模板渲染"""
    app_logger.info("🧪 测试模板渲染功能")
    app_logger.info("=" * 80)
    
    try:
        # 导入模板管理器
        from common.translation_manager import translation_manager
        
        # 测试模板和参数
        template_text = "Congratulations to {1} for winning {3}{4} at {2}."
        params = [
            "TestWinner",    # 参数1 - 玩家名
            "Game",          # 参数2 - 游戏名
            50.0,            # 参数3 - 获奖金额
            "USDT",          # 参数4 - 币种
            5.0,             # 参数5 - 获奖倍数
            10.0             # 参数6 - 投注金额
        ]
        
        app_logger.info(f"📄 原始模板: {template_text}")
        app_logger.info(f"📋 参数列表: {params}")
        
        # 渲染模板
        language_id_str = translation_manager.get_language_id(1)  # 英语
        app_logger.info(f"🌐 语言ID: {language_id_str}")
        
        rendered_message = translation_manager.render_template(
            template_text, 
            params, 
            language_id_str, 
            activity_id=None, 
            message_type=300
        )
        
        app_logger.info(f"✅ 渲染结果: {rendered_message}")
        
        # 测试复杂模板
        complex_template = "🎉 Player {player1} won {amount3}{currency4} (x{multiplier5}) in {game2}!"
        
        app_logger.info(f"\n📄 复杂模板: {complex_template}")
        
        complex_rendered = translation_manager.render_template(
            complex_template, 
            params, 
            language_id_str, 
            activity_id=None, 
            message_type=300
        )
        
        app_logger.info(f"✅ 复杂渲染结果: {complex_rendered}")
        
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 模板渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_real_config_render():
    """测试真实配置的模板渲染"""
    app_logger.info("\n🔍 测试真实配置的模板渲染")
    app_logger.info("=" * 80)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        from common.translation_manager import translation_manager
        
        # 连接MongoDB获取真实配置
        mongo_conn = MongoDBConnection()
        if not mongo_conn.connect():
            app_logger.error("❌ MongoDB连接失败")
            return False
        
        collection = mongo_conn.get_collection("c_tgNotify")
        configs = list(collection.find({"types": {"$in": [300]}, "open": True}))
        
        if not configs:
            app_logger.info("📊 没有找到真实配置")
            return True
        
        config = configs[0]  # 使用第一个配置
        
        app_logger.info(f"📋 真实配置:")
        app_logger.info(f"   business_no: {config.get('business_no')}")
        app_logger.info(f"   language: {config.get('language')}")
        app_logger.info(f"   text: {config.get('text')}")
        
        # 构建测试参数
        params = [
            "TestWinner",    # 参数1 - 玩家名
            "Game",          # 参数2 - 游戏名
            50.0,            # 参数3 - 获奖金额
            "USDT",          # 参数4 - 币种
            5.0,             # 参数5 - 获奖倍数
            10.0,            # 参数6 - 投注金额
            102,             # 参数7 - 游戏类型
            3,               # 参数8 - 平台ID
            200004,          # 参数9 - 游戏ID
            12345            # 参数10 - 玩家ID
        ]
        
        # 渲染真实模板
        template_text = config.get('text', '')
        language_id = config.get('language', 1)
        language_id_str = translation_manager.get_language_id(language_id)
        
        app_logger.info(f"🌐 语言设置: {language_id} -> {language_id_str}")
        app_logger.info(f"📋 参数列表: {params}")
        
        rendered_message = translation_manager.render_template(
            template_text, 
            params, 
            language_id_str, 
            activity_id=None, 
            message_type=300
        )
        
        app_logger.info(f"✅ 真实配置渲染结果: {rendered_message}")
        
        mongo_conn.disconnect()
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 真实配置渲染测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    app_logger.info("🧪 模板渲染测试")
    app_logger.info("=" * 100)
    
    # 测试1: 基本模板渲染
    success1 = await test_template_render()
    
    # 测试2: 真实配置渲染
    success2 = await test_real_config_render()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   基本模板渲染: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   真实配置渲染: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if success1 and success2 else '❌ 存在失败'}")


if __name__ == "__main__":
    asyncio.run(main())
