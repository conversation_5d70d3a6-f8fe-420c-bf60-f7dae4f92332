#!/usr/bin/env python3
"""
测试三表缓存功能
验证c_tbRobotConfig、c_tgNotify、c_tgScheduledPushTasks三张表的缓存管理
"""
import asyncio
import requests
import json
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from scheduler.config_manager import config_manager
from common.utils import setup_logging

import logging
logger = logging.getLogger(__name__)


async def test_three_table_cache():
    """测试三表缓存功能"""
    print("🧪 测试三表缓存功能")
    print("=" * 70)
    
    # 1. 初始化配置管理器
    print("1️⃣ 初始化配置管理器...")
    success = await config_manager.initialize()
    if not success:
        print("❌ 配置管理器初始化失败")
        return False
    
    # 2. 查看三表缓存状态
    print("\n2️⃣ 三表缓存状态:")
    stats = config_manager.get_cache_stats()
    
    print("📊 缓存统计:")
    print(f"   🤖 Bot配置 (c_tbRobotConfig):")
    print(f"      总数: {stats['robot_configs']['total']}")
    print(f"      启用: {stats['robot_configs']['active']}")
    print(f"      更新: {stats['robot_configs']['last_update']}")
    
    print(f"   📢 通知配置 (c_tgNotify):")
    print(f"      总数: {stats['notify_configs']['total']}")
    print(f"      实时: {stats['notify_configs']['realtime']}")
    print(f"      定时: {stats['notify_configs']['scheduled']}")
    print(f"      更新: {stats['notify_configs']['last_update']}")
    
    print(f"   ⏰ 定时任务 (c_tgScheduledPushTasks):")
    print(f"      总数: {stats['scheduled_tasks']['total']}")
    print(f"      启用: {stats['scheduled_tasks']['enabled']}")
    print(f"      更新: {stats['scheduled_tasks']['last_update']}")
    
    # 3. 测试数据获取
    print("\n3️⃣ 测试数据获取:")
    
    # 测试Bot配置获取
    robot_configs = config_manager.get_all_robot_configs()
    print(f"   🤖 获取到 {len(robot_configs)} 个Bot配置")
    if robot_configs:
        first_config = robot_configs[0]
        business_no = first_config.get("business_no")
        print(f"      示例: 商户 {business_no}")
        
        # 测试单个获取
        bot_info = config_manager.get_bot_info(business_no)
        if bot_info:
            print(f"      Token: {bot_info['bot_token'][:20]}...")
            print(f"      名称: {bot_info['bot_name']}")
    
    # 测试通知配置获取
    notify_configs = config_manager.get_all_notify_configs()
    print(f"   📢 获取到 {len(notify_configs)} 个通知配置")
    if notify_configs:
        first_notify = notify_configs[0]
        notify_id = first_notify.get("id")
        print(f"      示例: 通知 {notify_id}")
        print(f"      类型: {first_notify.get('notifyType')} (1=实时, 2=定时)")
        print(f"      名称: {first_notify.get('tgName')}")
    
    # 测试定时任务获取
    scheduled_tasks = config_manager.get_enabled_scheduled_tasks()
    print(f"   ⏰ 获取到 {len(scheduled_tasks)} 个启用的定时任务")
    if scheduled_tasks:
        first_task = scheduled_tasks[0]
        task_id = first_task.get("notifyId")
        print(f"      示例: 任务 {task_id}")
        print(f"      消息: {first_task.get('messageText')}")
        print(f"      时间: {first_task.get('sendTime')}")
    
    return True


def test_api_updates():
    """测试API更新功能"""
    print("\n🔄 测试API更新功能")
    print("=" * 70)
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code != 200:
            print(f"❌ API服务异常: {response.status_code}")
            return False
        
        print("✅ API服务运行正常")
        
        # 测试各种更新类型
        update_tests = [
            ("all", None, "更新所有三表"),
            ("robot_config", None, "更新所有Bot配置"),
            ("notify_config", None, "更新所有通知配置"),
            ("scheduled_tasks", None, "更新所有定时任务"),
            ("robot_config", ["39bac42a"], "更新指定商户"),
            ("notify_config", ["1"], "更新指定通知"),
            ("scheduled_tasks", ["1"], "更新指定任务")
        ]
        
        for update_type, target_ids, description in update_tests:
            print(f"\n📡 测试: {description}")
            
            payload = {"update_type": update_type}
            if target_ids:
                payload["target_ids"] = target_ids
            
            response = requests.post(
                "http://localhost:8000/api/config-update",
                json=payload,
                timeout=10
            )
            
            if response.status_code == 200:
                result = response.json()
                print(f"   ✅ 成功: {result['message']}")
            else:
                print(f"   ❌ 失败: {response.status_code} - {response.text}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ API连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False


def test_cache_performance():
    """测试缓存性能"""
    print("\n⚡ 测试缓存性能")
    print("=" * 70)
    
    try:
        import time
        
        # 测试多次获取的性能
        start_time = time.time()
        
        for i in range(1000):
            # 模拟频繁的缓存访问
            config_manager.get_all_robot_configs()
            config_manager.get_all_notify_configs()
            config_manager.get_enabled_scheduled_tasks()
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"📊 性能测试结果:")
        print(f"   🔢 操作次数: 1000次 × 3种查询 = 3000次")
        print(f"   ⏱️ 总耗时: {duration:.3f}秒")
        print(f"   🚀 平均耗时: {duration/3000*1000:.3f}毫秒/次")
        print(f"   📈 QPS: {3000/duration:.0f} 查询/秒")
        
        if duration < 1.0:
            print("   ✅ 缓存性能优秀")
        else:
            print("   ⚠️ 缓存性能需要优化")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        return False


async def test_cache_sync():
    """测试缓存同步"""
    print("\n🔄 测试缓存同步")
    print("=" * 70)
    
    try:
        # 获取初始状态
        initial_stats = config_manager.get_cache_stats()
        print("📊 初始缓存状态:")
        print(f"   Bot配置: {initial_stats['robot_configs']['total']}")
        print(f"   通知配置: {initial_stats['notify_configs']['total']}")
        print(f"   定时任务: {initial_stats['scheduled_tasks']['total']}")
        
        # 模拟API更新
        print("\n🔄 模拟API更新...")
        await config_manager.refresh_all_cache()
        
        # 获取更新后状态
        updated_stats = config_manager.get_cache_stats()
        print("📊 更新后缓存状态:")
        print(f"   Bot配置: {updated_stats['robot_configs']['total']}")
        print(f"   通知配置: {updated_stats['notify_configs']['total']}")
        print(f"   定时任务: {updated_stats['scheduled_tasks']['total']}")
        
        # 检查时间戳更新
        robot_time_changed = (initial_stats['robot_configs']['last_update'] != 
                            updated_stats['robot_configs']['last_update'])
        notify_time_changed = (initial_stats['notify_configs']['last_update'] != 
                             updated_stats['notify_configs']['last_update'])
        tasks_time_changed = (initial_stats['scheduled_tasks']['last_update'] != 
                            updated_stats['scheduled_tasks']['last_update'])
        
        print("\n⏰ 时间戳更新检查:")
        print(f"   Bot配置: {'✅ 已更新' if robot_time_changed else '❌ 未更新'}")
        print(f"   通知配置: {'✅ 已更新' if notify_time_changed else '❌ 未更新'}")
        print(f"   定时任务: {'✅ 已更新' if tasks_time_changed else '❌ 未更新'}")
        
        return robot_time_changed and notify_time_changed and tasks_time_changed
        
    except Exception as e:
        print(f"❌ 缓存同步测试失败: {e}")
        return False


async def main():
    """主函数"""
    setup_logging()
    
    print("🚀 三表缓存功能测试")
    print("=" * 80)
    
    # 1. 测试三表缓存
    try:
        cache_ok = await test_three_table_cache()
        if not cache_ok:
            print("\n❌ 三表缓存测试失败")
            return 1
    except Exception as e:
        print(f"\n❌ 三表缓存测试异常: {e}")
        return 1
    
    # 2. 测试API更新
    api_ok = test_api_updates()
    if not api_ok:
        print("\n⚠️ API更新测试失败，但缓存功能正常")
    
    # 3. 测试缓存性能
    perf_ok = test_cache_performance()
    
    # 4. 测试缓存同步
    try:
        sync_ok = await test_cache_sync()
        if not sync_ok:
            print("\n⚠️ 缓存同步测试失败")
    except Exception as e:
        print(f"\n❌ 缓存同步测试异常: {e}")
        sync_ok = False
    
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    print(f"   🗄️ 三表缓存: {'✅ 通过' if cache_ok else '❌ 失败'}")
    print(f"   🔄 API更新: {'✅ 通过' if api_ok else '❌ 失败'}")
    print(f"   ⚡ 缓存性能: {'✅ 通过' if perf_ok else '❌ 失败'}")
    print(f"   🔄 缓存同步: {'✅ 通过' if sync_ok else '❌ 失败'}")
    
    if cache_ok and perf_ok and sync_ok:
        print("\n🎉 三表缓存功能完全正常！")
        print("💡 优势:")
        print("   • 三张核心表数据全部缓存在内存")
        print("   • API更新立即生效，无延迟")
        print("   • 高性能查询，支持高并发")
        print("   • 统一管理，维护简单")
    else:
        print("\n⚠️ 部分功能需要检查")
    
    print("=" * 80)
    
    return 0 if (cache_ok and perf_ok and sync_ok) else 1


if __name__ == "__main__":
    try:
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
