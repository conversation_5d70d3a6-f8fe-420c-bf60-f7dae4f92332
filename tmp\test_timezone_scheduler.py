#!/usr/bin/env python3
"""
测试多时区定时任务调度器
"""
import sys
import asyncio
from pathlib import Path
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_timezone_manager():
    """测试时区管理器"""
    print("🌍 测试时区管理器")
    print("=" * 50)
    
    try:
        from scheduler.timezone_manager import TimeZoneManager
        from database.mongodb_connection import MongoDBConnection
        
        # 创建时区管理器
        tz_manager = TimeZoneManager()
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 加载商户时区
        print("1. 加载商户时区配置")
        success = await tz_manager.load_merchant_timezones(mongo)
        if not success:
            print("❌ 加载时区配置失败")
            return False
        
        # 测试时区转换
        print(f"\n2. 测试时区转换")
        test_merchants = ["39bac42a", "TYT_100", "Testxm001_100"]
        
        current_utc = datetime.now(timezone.utc)
        print(f"   当前UTC时间: {current_utc.strftime('%Y-%m-%d %H:%M:%S %Z')}")
        
        for business_no in test_merchants:
            merchant_tz = tz_manager.get_merchant_timezone(business_no)
            local_time = tz_manager.get_merchant_local_time(business_no, current_utc)
            
            print(f"\n   商户 {business_no}:")
            print(f"     时区: {merchant_tz}")
            print(f"     本地时间: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
            # 检查日期是否不同
            if local_time.date() != current_utc.date():
                print(f"     ⚠️ 与UTC日期不同")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_task_matching():
    """测试任务匹配逻辑"""
    print(f"\n🎯 测试任务匹配逻辑")
    print("=" * 50)
    
    try:
        from scheduler.timezone_manager import TimeZoneManager
        from database.mongodb_connection import MongoDBConnection
        
        # 创建时区管理器
        tz_manager = TimeZoneManager()
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 加载时区配置
        await tz_manager.load_merchant_timezones(mongo)
        
        # 获取定时任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks = list(collection.find({"enabled": True}))
        
        print(f"1. 获取定时任务: {len(tasks)} 个")
        
        # 测试当前时间的任务匹配
        print(f"\n2. 测试当前时间的任务匹配")
        current_utc = datetime.now(timezone.utc)
        
        matching_tasks = []
        for task in tasks:
            if tz_manager.should_execute_task(task, current_utc):
                matching_tasks.append(task)
        
        print(f"   匹配的任务: {len(matching_tasks)}")
        
        for task in matching_tasks:
            business_no = task.get("business_no", "")
            local_time = tz_manager.get_merchant_local_time(business_no, current_utc)
            
            print(f"\n   任务 {task.get('_id')}:")
            print(f"     商户: {business_no}")
            print(f"     本地时间: {local_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            print(f"     频率: {task.get('sendFrequency')}")
            print(f"     调度日期: {task.get('scheduleDays')}")
            print(f"     调度时间: {task.get('scheduleTimes')}")
        
        # 测试优先级过滤
        print(f"\n3. 测试优先级过滤")
        filtered_tasks = tz_manager.filter_tasks_by_priority(matching_tasks, current_utc)
        
        print(f"   过滤后的任务: {len(filtered_tasks)}")
        
        if len(filtered_tasks) != len(matching_tasks):
            print(f"   ⚠️ 优先级过滤生效，过滤掉 {len(matching_tasks) - len(filtered_tasks)} 个任务")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_scheduler_integration():
    """测试调度器集成"""
    print(f"\n🔧 测试调度器集成")
    print("=" * 50)
    
    try:
        from scheduler.task_scheduler import TaskScheduler
        
        # 创建调度器
        scheduler = TaskScheduler()
        
        print("1. 初始化调度器")
        success = await scheduler.start()
        if not success:
            print("❌ 调度器初始化失败")
            return False
        
        print("✅ 调度器初始化成功")
        
        # 测试任务检查
        print(f"\n2. 测试任务检查")
        await scheduler._load_tasks()
        print(f"   加载任务数: {len(scheduler.tasks)}")
        
        # 模拟任务检查
        await scheduler._check_and_execute_tasks()
        
        # 停止调度器
        await scheduler.stop()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def simulate_different_times():
    """模拟不同时间的任务执行"""
    print(f"\n⏰ 模拟不同时间的任务执行")
    print("=" * 50)
    
    try:
        from scheduler.timezone_manager import TimeZoneManager
        from database.mongodb_connection import MongoDBConnection
        
        # 创建时区管理器
        tz_manager = TimeZoneManager()
        
        # 连接数据库
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 加载配置
        await tz_manager.load_merchant_timezones(mongo)
        
        # 获取任务
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        tasks = list(collection.find({"enabled": True}))
        
        # 模拟不同时间
        test_times = [
            datetime.now(timezone.utc),
            datetime.now(timezone.utc) + timedelta(hours=1),
            datetime.now(timezone.utc) + timedelta(hours=8),  # 北京时间
            datetime.now(timezone.utc) - timedelta(hours=3),  # 巴西时间
        ]
        
        for i, test_time in enumerate(test_times, 1):
            print(f"\n{i}. 模拟时间: {test_time.strftime('%Y-%m-%d %H:%M:%S %Z')}")
            
            matching_tasks = tz_manager.get_tasks_for_execution(tasks, test_time)
            print(f"   匹配任务数: {len(matching_tasks)}")
            
            for task in matching_tasks:
                business_no = task.get("business_no", "")
                local_time = tz_manager.get_merchant_local_time(business_no, test_time)
                print(f"     任务 {task.get('_id')}: 商户 {business_no} 本地时间 {local_time.strftime('%H:%M')}")
        
        mongo.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ 模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🌍 多时区定时任务调度器测试")
    print("=" * 80)
    
    # 1. 测试时区管理器
    tz_ok = await test_timezone_manager()
    
    # 2. 测试任务匹配
    match_ok = False
    if tz_ok:
        match_ok = await test_task_matching()
    
    # 3. 测试调度器集成
    scheduler_ok = False
    if match_ok:
        scheduler_ok = await test_scheduler_integration()
    
    # 4. 模拟不同时间
    sim_ok = False
    if tz_ok:
        sim_ok = await simulate_different_times()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   时区管理器: {'✅ 成功' if tz_ok else '❌ 失败'}")
    print(f"   任务匹配: {'✅ 成功' if match_ok else '❌ 失败'}")
    print(f"   调度器集成: {'✅ 成功' if scheduler_ok else '❌ 失败'}")
    print(f"   时间模拟: {'✅ 成功' if sim_ok else '❌ 失败'}")
    
    if tz_ok and match_ok and scheduler_ok:
        print(f"\n🎉 多时区定时任务调度器测试成功！")
        print(f"💡 功能特点:")
        print(f"   1. 支持多时区商户")
        print(f"   2. 实时时区转换")
        print(f"   3. 月任务优先于周任务")
        print(f"   4. 处理跨日期边界")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 观察多时区任务执行")
        print(f"   3. 验证优先级过滤")
    else:
        print(f"\n⚠️ 测试中发现问题，需要修复")

if __name__ == "__main__":
    asyncio.run(main())
