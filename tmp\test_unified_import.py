#!/usr/bin/env python3
"""
测试统一服务器导入
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

print("🧪 测试统一服务器导入")
print("=" * 60)

try:
    print("📦 测试基础导入...")
    from config.settings import settings
    print(f"✅ settings: {settings.api_port}")
    
    from api.main import create_app
    print("✅ api.main")
    
    from scheduler.task_scheduler import TaskScheduler
    print("✅ scheduler.task_scheduler")
    
    from scheduler.config_manager import config_manager
    print("✅ scheduler.config_manager")
    
    from services.periodic_push_service import periodic_push_service
    print("✅ services.periodic_push_service")
    
    from common.utils import setup_logging
    print("✅ common.utils")
    
    print("\n📦 测试FastAPI导入...")
    from fastapi import FastAPI
    from contextlib import asynccontextmanager
    import uvicorn
    print("✅ FastAPI相关模块")
    
    print("\n✅ 所有导入测试通过")
    
except Exception as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
