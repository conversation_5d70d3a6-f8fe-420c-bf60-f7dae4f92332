#!/usr/bin/env python3
"""
对比测试：统一服务器 vs 分离服务器
"""
import requests
import json
import time
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))


def test_unified_server():
    """测试统一服务器"""
    print("🔗 测试统一服务器架构")
    print("=" * 50)
    
    try:
        # 1. 健康检查
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ 健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
        
        # 2. 获取统一状态
        response = requests.get("http://localhost:8000/api/unified-status", timeout=5)
        if response.status_code == 200:
            result = response.json()
            print("✅ 统一状态获取成功")
            
            data = result.get("data", {})
            services = data.get("services", {})
            config_stats = data.get("config_manager", {})
            scheduler_stats = data.get("scheduler", {})
            
            print(f"   📊 服务类型: {data.get('server_type')}")
            print(f"   🔧 FastAPI: {services.get('fastapi')}")
            print(f"   ⏰ 调度器: {services.get('scheduler')}")
            print(f"   💾 内存共享: {data.get('memory_shared')}")
            print(f"   🔢 进程数: {data.get('process_count')}")
            print(f"   📋 Token配置: {config_stats.get('total_configs', 0)}")
            print(f"   ⚡ 定时任务: {scheduler_stats.get('task_count', 0)}")
            
        else:
            print(f"❌ 统一状态获取失败: {response.status_code}")
        
        # 3. 测试配置更新的实时性
        print("\n🔄 测试配置更新实时性...")
        
        start_time = time.time()
        
        # 更新Bot配置
        response = requests.post(
            "http://localhost:8000/api/config-update",
            json={"update_type": "bot_config"},
            timeout=10
        )
        
        if response.status_code == 200:
            update_time = time.time() - start_time
            print(f"✅ Bot配置更新成功 (耗时: {update_time:.3f}s)")
        else:
            print(f"❌ Bot配置更新失败: {response.status_code}")
        
        # 更新定时任务
        start_time = time.time()
        
        response = requests.post(
            "http://localhost:8000/api/config-update",
            json={"update_type": "scheduled_tasks"},
            timeout=10
        )
        
        if response.status_code == 200:
            update_time = time.time() - start_time
            print(f"✅ 定时任务更新成功 (耗时: {update_time:.3f}s)")
        else:
            print(f"❌ 定时任务更新失败: {response.status_code}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"❌ 连接失败: {e}")
        print("💡 请确保统一服务器已启动: python start_unified_server.py")
        return False


def compare_architectures():
    """对比两种架构"""
    print("\n📊 架构对比分析")
    print("=" * 80)
    
    comparison = [
        ("特性", "分离架构", "统一架构"),
        ("进程数量", "2个 (API + 调度器)", "1个"),
        ("内存使用", "重复资源", "共享资源"),
        ("配置同步", "跨进程通信", "内存直接共享"),
        ("部署复杂度", "需要管理多个进程", "单一进程启动"),
        ("调试难度", "多进程日志分散", "统一日志"),
        ("资源消耗", "较高", "较低"),
        ("配置更新延迟", "可能有延迟", "立即生效"),
        ("故障隔离", "进程间相对独立", "单点故障"),
        ("扩展性", "可独立扩展", "整体扩展"),
        ("适用场景", "大型分布式系统", "中小型集成系统")
    ]
    
    # 打印对比表格
    print(f"{'特性':<15} {'分离架构':<25} {'统一架构':<25}")
    print("-" * 80)
    
    for feature, separate, unified in comparison:
        print(f"{feature:<15} {separate:<25} {unified:<25}")
    
    print("\n💡 推荐:")
    print("   • 对于当前的TG Bot项目，统一架构更合适")
    print("   • 资源消耗更低，配置同步更及时")
    print("   • 部署和维护更简单")


def show_migration_guide():
    """显示迁移指南"""
    print("\n🔄 迁移指南")
    print("=" * 50)
    
    print("从分离架构迁移到统一架构:")
    print()
    print("1️⃣ 停止现有服务:")
    print("   • 停止API服务")
    print("   • 停止调度器服务")
    print()
    print("2️⃣ 启动统一服务:")
    print("   python start_unified_server.py")
    print()
    print("3️⃣ 验证功能:")
    print("   • 访问 http://localhost:8000/docs")
    print("   • 检查 http://localhost:8000/api/unified-status")
    print("   • 测试配置更新API")
    print()
    print("4️⃣ 优势验证:")
    print("   • 配置更新立即生效")
    print("   • 单一进程管理")
    print("   • 统一日志输出")


def main():
    """主函数"""
    print("🚀 统一服务器 vs 分离服务器对比测试")
    print("=" * 80)
    
    # 测试统一服务器
    unified_ok = test_unified_server()
    
    # 架构对比
    compare_architectures()
    
    # 迁移指南
    show_migration_guide()
    
    print("\n" + "=" * 80)
    if unified_ok:
        print("✅ 统一服务器测试通过")
        print("💡 建议使用统一架构，优势明显")
    else:
        print("❌ 统一服务器测试失败")
        print("💡 请检查服务启动状态")
    print("=" * 80)
    
    return 0 if unified_ok else 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n🛑 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
