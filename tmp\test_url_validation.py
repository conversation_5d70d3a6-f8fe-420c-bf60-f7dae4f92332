#!/usr/bin/env python3
"""
测试URL验证功能
"""
import sys
from pathlib import Path

sys.path.insert(0, str(Path(__file__).parent))

from scheduler.task_scheduler import TaskScheduler

def test_url_validation():
    """测试URL验证"""
    print("🔗 测试URL验证功能")
    print("=" * 50)
    
    scheduler = TaskScheduler()
    
    # 测试用例
    test_cases = [
        # 有效URL
        ("https://example.com", True, "完整HTTPS URL"),
        ("http://example.com", True, "完整HTTP URL"),
        ("https://example.com/path?param=value", True, "带参数的URL"),
        ("t.me/bot/app", True, "Telegram链接"),
        ("t.me/channel", True, "Telegram频道"),
        
        # 无效URL
        ("a", False, "单字符"),
        ("", False, "空字符串"),
        ("abc", False, "短字符串"),
        ("example.com", False, "缺少协议"),
        ("ftp://example.com", False, "非HTTP协议"),
        (None, False, "None值"),
        ("   ", False, "空白字符"),
    ]
    
    print("📋 测试用例:")
    all_passed = True
    
    for url, expected, description in test_cases:
        result = scheduler._is_valid_url(url)
        status = "✅" if result == expected else "❌"
        
        print(f"  {status} {description}")
        print(f"     输入: '{url}'")
        print(f"     期望: {expected}, 实际: {result}")
        
        if result != expected:
            all_passed = False
        print()
    
    print(f"📊 测试结果: {'✅ 全部通过' if all_passed else '❌ 部分失败'}")
    return all_passed

def test_current_task_urls():
    """检查当前任务中的URL"""
    print(f"\n🔍 检查当前任务中的URL")
    print("=" * 50)
    
    try:
        import asyncio
        from scheduler.config_manager import config_manager
        
        async def check_urls():
            # 初始化配置管理器
            await config_manager.initialize()
            
            # 获取所有任务
            tasks = config_manager.get_all_scheduled_tasks()
            print(f"📋 检查 {len(tasks)} 个任务的URL")
            
            scheduler = TaskScheduler()
            problem_tasks = []
            
            for task in tasks:
                task_id = task.get('_id')
                external_url = task.get('externalUrl', '')
                internal_url = task.get('internalUrl', '')
                
                external_valid = scheduler._is_valid_url(external_url) if external_url else True
                internal_valid = scheduler._is_valid_url(internal_url) if internal_url else True
                
                if not external_valid or not internal_valid:
                    problem_tasks.append({
                        'id': task_id,
                        'notifyId': task.get('notifyId'),
                        'external_url': external_url,
                        'internal_url': internal_url,
                        'external_valid': external_valid,
                        'internal_valid': internal_valid
                    })
                
                print(f"  任务 {task_id}:")
                print(f"    notifyId: {task.get('notifyId')}")
                print(f"    externalUrl: '{external_url}' {'✅' if external_valid else '❌'}")
                print(f"    internalUrl: '{internal_url}' {'✅' if internal_valid else '❌'}")
                print()
            
            if problem_tasks:
                print(f"⚠️ 发现 {len(problem_tasks)} 个任务有URL问题:")
                for task in problem_tasks:
                    print(f"  - 任务ID: {task['id']}, notifyId: {task['notifyId']}")
                    if not task['external_valid']:
                        print(f"    无效的externalUrl: '{task['external_url']}'")
                    if not task['internal_valid']:
                        print(f"    无效的internalUrl: '{task['internal_url']}'")
            else:
                print("✅ 所有任务的URL都有效")
            
            return len(problem_tasks) == 0
        
        return asyncio.run(check_urls())
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 URL验证测试")
    print("=" * 60)
    
    # 1. 测试URL验证逻辑
    validation_ok = test_url_validation()
    
    # 2. 检查当前任务的URL
    current_ok = test_current_task_urls()
    
    print(f"\n" + "=" * 60)
    print("📊 测试总结:")
    print(f"   🔗 URL验证逻辑: {'✅ 正常' if validation_ok else '❌ 异常'}")
    print(f"   📋 当前任务URL: {'✅ 正常' if current_ok else '❌ 有问题'}")
    
    if validation_ok and current_ok:
        print(f"\n🎉 URL验证功能正常！")
        print(f"💡 修复内容:")
        print(f"   • 添加了URL有效性验证")
        print(f"   • 无效URL会被忽略，不会创建按钮")
        print(f"   • 支持HTTP/HTTPS和Telegram链接格式")
        print(f"   • 详细的警告日志便于调试")
    else:
        print(f"\n⚠️ 发现问题，建议:")
        if not current_ok:
            print(f"   • 检查数据库中的URL字段值")
            print(f"   • 更新无效的URL为正确格式")
            print(f"   • 或者将无效URL设为空字符串")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
