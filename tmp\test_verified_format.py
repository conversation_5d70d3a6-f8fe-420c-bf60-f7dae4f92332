#!/usr/bin/env python3
"""
测试使用已验证的嵌套数组格式
"""
import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

async def test_verified_nested_array():
    """测试已验证的嵌套数组格式"""
    print("📝 测试已验证的嵌套数组格式")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        # 模板 (与taskType=500使用的相同)
        template_text = """Daily Ranking Revealed
Today's Top Players:
[repeat]{rank1}{playid2} – {currency3}{wagered4}
[/repeat]"""
        
        # 使用已验证的嵌套数组格式 (不需要索引0占位符)
        rank_list = [
            ["1", "4743182", "1000", "2,092.21"],  # [rank, playid, currency, wagered]
            ["2", "2268779", "1000", "2,035.44"],
            ["3", "7731938", "1000", "1,821.96"]
        ]
        
        params = [rank_list]
        
        print(f"模板:")
        print(f"  {template_text}")
        
        print(f"\n参数 (已验证格式):")
        print(f"  嵌套数组: {len(rank_list)} 条记录")
        for i, item in enumerate(rank_list, 1):
            print(f"    {i}. {item}")
        
        print(f"\n参数映射:")
        print(f"  {{rank1}} -> 子数组[0] (排名)")
        print(f"  {{playid2}} -> 子数组[1] (玩家ID)")
        print(f"  {{currency3}} -> 子数组[2] (货币)")
        print(f"  {{wagered4}} -> 子数组[3] (投注额)")
        
        # 渲染模板
        language_id = "1"  # 英语
        rendered_text = translation_manager.render_template(template_text, params, language_id)
        
        print(f"\n渲染结果:")
        print(f"  语言: {language_id}")
        print(f"  长度: {len(rendered_text)} 字符")
        print(f"  内容:")
        print(f"    {rendered_text}")
        
        # 检查结果
        expected_patterns = ["14743182", "22268779", "37731938"]
        success = all(pattern in rendered_text for pattern in expected_patterns)
        
        if success:
            print(f"  ✅ 渲染成功，所有数据正确显示")
            return True
        else:
            print(f"  ❌ 渲染结果不符合预期")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def compare_formats():
    """比较不同格式的效果"""
    print(f"\n🔍 比较不同参数格式")
    print("=" * 60)
    
    try:
        from common.translation_manager import translation_manager
        
        template = "[repeat]{rank1}{playid2} – {currency3}{wagered4}[/repeat]"
        
        # 格式1: 已验证的嵌套数组格式
        format1_params = [
            [
                ["1", "4743182", "1000", "2,092.21"],
                ["2", "2268779", "1000", "2,035.44"]
            ]
        ]
        
        # 格式2: 带索引0占位符的格式
        format2_params = [
            [
                ["", "1", "4743182", "1000", "2,092.21"],
                ["", "2", "2268779", "1000", "2,035.44"]
            ]
        ]
        
        print(f"模板: {template}")
        
        print(f"\n格式1 (已验证格式):")
        print(f"  参数: {format1_params}")
        result1 = translation_manager.render_template(template, format1_params, "1")
        print(f"  结果: {result1}")
        
        print(f"\n格式2 (带占位符格式):")
        print(f"  参数: {format2_params}")
        result2 = translation_manager.render_template(template, format2_params, "1")
        print(f"  结果: {result2}")
        
        # 比较结果
        if result1 == result2:
            print(f"\n✅ 两种格式产生相同结果")
            return True
        else:
            print(f"\n❌ 两种格式产生不同结果")
            print(f"   格式1长度: {len(result1)}")
            print(f"   格式2长度: {len(result2)}")
            return False
        
    except Exception as e:
        print(f"❌ 比较失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_rebate_handler_with_verified_format():
    """测试使用已验证格式的处理器"""
    print(f"\n🎯 测试使用已验证格式的处理器")
    print("=" * 60)
    
    try:
        from scheduler.task_handlers.rebate_rank_handler import rebate_rank_handler
        from scheduler.config_manager import config_manager
        
        # 初始化配置管理器
        await config_manager.initialize()
        
        # 创建测试任务
        test_task = {
            "_id": "686f2e0fc63122421402b6e4",
            "taskType": 500,
            "business_no": "39bac42a",
            "notifyId": 10,
            "enabled": True
        }
        
        print(f"测试任务: taskType=500, 商户=39bac42a, notifyId=10")
        
        # 执行处理器
        result = await rebate_rank_handler.handle_task(test_task)
        
        print(f"\n处理结果:")
        print(f"  成功: {result.get('success', False)}")
        print(f"  消息: {result.get('message', 'N/A')}")
        
        if result.get('success', False):
            data = result.get('data', {})
            params = data.get('params', [])
            
            if params and isinstance(params[0], list):
                print(f"\n参数格式检查:")
                print(f"  嵌套数组: {len(params[0])} 条记录")
                
                # 检查前3条记录的格式
                for i, rank_item in enumerate(params[0][:3], 1):
                    if isinstance(rank_item, list):
                        print(f"    {i}. 长度={len(rank_item)}, 内容={rank_item}")
                        if len(rank_item) == 4:
                            print(f"       ✅ 使用已验证格式: 排名={rank_item[0]}, 玩家={rank_item[1]}, 货币={rank_item[2]}, 投注={rank_item[3]}")
                        else:
                            print(f"       ❌ 格式不正确，长度应为4")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主函数"""
    print("🔧 测试已验证的嵌套数组格式")
    print("=" * 80)
    
    # 1. 测试已验证格式
    format_ok = await test_verified_nested_array()
    
    # 2. 比较不同格式
    compare_ok = await compare_formats()
    
    # 3. 测试处理器
    handler_ok = await test_rebate_handler_with_verified_format()
    
    print(f"\n" + "=" * 80)
    print(f"📊 测试结果:")
    print(f"   已验证格式: {'✅ 正常' if format_ok else '❌ 异常'}")
    print(f"   格式比较: {'✅ 一致' if compare_ok else '❌ 不一致'}")
    print(f"   处理器测试: {'✅ 正常' if handler_ok else '❌ 异常'}")
    
    if format_ok and handler_ok:
        print(f"\n🎉 已验证格式工作正常！")
        print(f"💡 优势:")
        print(f"   1. 使用即时消息中已验证通过的格式")
        print(f"   2. 不需要索引0占位符")
        print(f"   3. 参数映射更直观: [rank, playid, currency, wagered]")
        print(f"   4. 与现有系统完全兼容")
        
        print(f"\n🚀 下一步:")
        print(f"   1. 重启定时任务服务")
        print(f"   2. 更新任务时间进行测试")
        print(f"   3. 观察Telegram消息格式")
    else:
        print(f"\n⚠️ 仍有问题需要修复")

if __name__ == "__main__":
    asyncio.run(main())
