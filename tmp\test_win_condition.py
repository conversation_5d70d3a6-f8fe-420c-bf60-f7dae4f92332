#!/usr/bin/env python3
"""
测试获奖条件检测
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
from config.logging_config import app_logger
from services.periodic_push_service import periodic_push_service


async def test_win_conditions():
    """测试获奖条件检测"""
    app_logger.info("🧪 测试获奖条件检测")
    app_logger.info("=" * 80)
    
    try:
        # 初始化服务
        success = await periodic_push_service.initialize()
        if not success:
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 测试数据 - 模拟游戏日志记录
        test_logs = [
            {
                "number": "test_001",
                "gameType": 102,
                "platformId": 3,
                "gameId": 300003,
                "playerId": 12345,
                "playerName": "TestPlayer1",
                "betAmount": 10.0,
                "winAmount": 50.0,  # 5倍获奖
                "create_time": 1720876800
            },
            {
                "number": "test_002",
                "gameType": 102,
                "platformId": 3,
                "gameId": 300003,
                "playerId": 12346,
                "playerName": "TestPlayer2",
                "betAmount": 20.0,
                "winAmount": 200.0,  # 10倍获奖
                "create_time": 1720876801
            },
            {
                "number": "test_003",
                "gameType": 102,
                "platformId": 3,
                "gameId": 300003,
                "playerId": 12347,
                "playerName": "TestPlayer3",
                "betAmount": 5.0,
                "winAmount": 100.0,  # 20倍获奖
                "create_time": 1720876802
            },
            {
                "number": "test_004",
                "gameType": 101,  # 不同游戏类型
                "platformId": 3,
                "gameId": 300003,
                "playerId": 12348,
                "playerName": "TestPlayer4",
                "betAmount": 10.0,
                "winAmount": 100.0,  # 10倍获奖
                "create_time": 1720876803
            },
            {
                "number": "test_005",
                "gameType": 102,
                "platformId": 3,
                "gameId": 300003,
                "playerId": 12349,
                "playerName": "TestPlayer5",
                "betAmount": 10.0,
                "winAmount": 30.0,  # 3倍获奖 (不满足条件)
                "create_time": 1720876804
            }
        ]
        
        app_logger.info(f"📊 测试 {len(test_logs)} 条模拟游戏日志")
        app_logger.info(f"📋 当前配置的商户数量: {len(periodic_push_service.notify_configs)}")
        
        # 测试每条日志对每个商户配置的匹配情况
        for i, log in enumerate(test_logs):
            app_logger.info(f"\n🎮 测试日志 {i+1}: {log['playerName']}")
            app_logger.info(f"   游戏信息: gameType={log['gameType']}, platformId={log['platformId']}, gameId={log['gameId']}")
            app_logger.info(f"   投注信息: bet={log['betAmount']}, win={log['winAmount']}, 倍数={log['winAmount']/log['betAmount']:.1f}x")
            
            winning_configs = []
            
            for merchant_id, config in periodic_push_service.notify_configs.items():
                is_winning = await periodic_push_service.check_win_condition(log, config)
                
                app_logger.info(f"   商户 {merchant_id} (阈值{config.get('gameWinMul')}x): {'✅ 满足' if is_winning else '❌ 不满足'}")
                
                if is_winning:
                    winning_configs.append((merchant_id, config))
            
            # 如果有满足条件的配置，模拟处理
            if winning_configs:
                app_logger.info(f"   🎉 该记录满足 {len(winning_configs)} 个商户的获奖条件")
                for merchant_id, config in winning_configs:
                    app_logger.info(f"   📤 模拟推送到商户 {merchant_id} (群组: {config.get('groupId')})")
            else:
                app_logger.info(f"   📊 该记录不满足任何获奖条件")
        
        # 统计测试结果
        app_logger.info(f"\n📊 测试总结:")
        total_matches = 0
        for log in test_logs:
            for merchant_id, config in periodic_push_service.notify_configs.items():
                if await periodic_push_service.check_win_condition(log, config):
                    total_matches += 1
        
        app_logger.info(f"   总测试记录: {len(test_logs)}")
        app_logger.info(f"   总匹配次数: {total_matches}")
        app_logger.info(f"   平均匹配率: {total_matches/(len(test_logs)*len(periodic_push_service.notify_configs))*100:.1f}%")
        
        app_logger.info("✅ 获奖条件检测测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await periodic_push_service.cleanup()


async def test_actual_data():
    """测试实际数据"""
    app_logger.info("🔍 测试实际游戏日志数据")
    app_logger.info("=" * 80)
    
    try:
        # 初始化服务
        success = await periodic_push_service.initialize()
        if not success:
            app_logger.error("❌ 服务初始化失败")
            return False
        
        # 获取当前表名
        table_name = await periodic_push_service.get_current_table_name()
        app_logger.info(f"📋 查询表: {table_name}")
        
        # 查询最近的游戏日志
        query = f"""
        SELECT number, create_time, gameType, platformId, gameId, 
               playerId, playerName, betAmount, winAmount, profit
        FROM `{table_name}`
        WHERE betAmount > 0 AND winAmount > 0
        ORDER BY create_time DESC 
        LIMIT 10
        """
        
        results = await periodic_push_service.mysql_conn.execute_query(query)
        
        if not results:
            app_logger.info("📊 没有找到有效的游戏日志数据")
            return True
        
        app_logger.info(f"📊 找到 {len(results)} 条实际游戏日志")
        
        # 分析每条记录
        for i, log in enumerate(results):
            bet_amount = float(log.get('betAmount', 0))
            win_amount = float(log.get('winAmount', 0))
            win_multiplier = win_amount / bet_amount if bet_amount > 0 else 0
            
            app_logger.info(f"\n🎮 记录 {i+1}: {log.get('number')}")
            app_logger.info(f"   玩家: {log.get('playerName', 'N/A')} (ID: {log.get('playerId')})")
            app_logger.info(f"   游戏: gameType={log.get('gameType')}, platformId={log.get('platformId')}, gameId={log.get('gameId')}")
            app_logger.info(f"   投注: {bet_amount}, 赢取: {win_amount}, 倍数: {win_multiplier:.2f}x")
            
            # 检查是否满足获奖条件
            winning_count = 0
            for merchant_id, config in periodic_push_service.notify_configs.items():
                if await periodic_push_service.check_win_condition(log, config):
                    winning_count += 1
                    app_logger.info(f"   ✅ 满足商户 {merchant_id} 的获奖条件 (阈值: {config.get('gameWinMul')}x)")
            
            if winning_count == 0:
                app_logger.info(f"   📊 不满足任何获奖条件")
        
        app_logger.info("✅ 实际数据测试完成")
        return True
        
    except Exception as e:
        app_logger.error(f"❌ 实际数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        await periodic_push_service.cleanup()


async def main():
    """主函数"""
    app_logger.info("🧪 获奖条件检测测试")
    app_logger.info("=" * 100)
    
    # 测试1: 模拟数据测试
    app_logger.info("📋 测试1: 模拟数据测试")
    success1 = await test_win_conditions()
    
    app_logger.info("\n" + "=" * 100)
    
    # 测试2: 实际数据测试
    app_logger.info("📋 测试2: 实际数据测试")
    success2 = await test_actual_data()
    
    app_logger.info("\n" + "=" * 100)
    app_logger.info("📊 测试总结:")
    app_logger.info(f"   模拟数据测试: {'✅ 通过' if success1 else '❌ 失败'}")
    app_logger.info(f"   实际数据测试: {'✅ 通过' if success2 else '❌ 失败'}")
    app_logger.info(f"   总体结果: {'✅ 全部通过' if success1 and success2 else '❌ 存在失败'}")


if __name__ == "__main__":
    asyncio.run(main())
