#!/usr/bin/env python3
"""
测试你的具体逻辑需求
"""
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent))

def test_case_1():
    """测试用例1：{Currency2}{Amount3}"""
    print("🎯 测试用例1：{Currency2}{Amount3}")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        template = """Parabéns {test1}
[repeat]pelo depósito – {Currency2}{Amount3}
[/repeat]
{gg4}"""
        
        params = [
            "user_name_li",
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
            "thank_you_message"
        ]
        
        language = "2"
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 语言: {language}")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 实际结果:")
        print(f"{result}")
        
        expected = """Parabéns user_name_li
pelo depósito – 10USDT
pelo depósito – 50BRL
pelo depósito – 100USDC
thank_you_message"""
        
        print(f"\n📋 期望结果:")
        print(f"{expected}")
        
        # 验证关键部分
        success_checks = [
            ("10USDT" in result, "10USDT"),
            ("50BRL" in result, "50BRL"),
            ("100USDC" in result, "100USDC"),
            ("user_name_li" in result, "user_name_li"),
            ("thank_you_message" in result, "thank_you_message")
        ]
        
        print(f"\n🔍 验证结果:")
        all_success = True
        for check, desc in success_checks:
            status = "✅" if check else "❌"
            print(f"   {status} {desc}")
            if not check:
                all_success = False
        
        return all_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_case_2():
    """测试用例2：{Currency3}{Amount2}"""
    print(f"\n🎯 测试用例2：{Currency3}{Amount2}")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        template = """Parabéns {test1} {gg4}
[repeat]pelo depósito – {Currency3}{Amount2}
[/repeat]"""
        
        params = [
            "user_name_li",
            [["10", "USDT"], ["50", "BRL"], ["100", "USDC"]],
            "thank_you_message"
        ]
        
        language = "2"
        
        print(f"📋 模板:")
        print(f"{template}")
        print(f"\n📋 参数: {params}")
        print(f"📋 语言: {language}")
        
        result = translator.render_template(template, params, language)
        
        print(f"\n📤 实际结果:")
        print(f"{result}")
        
        expected = """Parabéns user_name_li thank_you_message
pelo depósito – USDT10
pelo depósito – BRL50
pelo depósito – USDC100"""
        
        print(f"\n📋 期望结果:")
        print(f"{expected}")
        
        # 验证关键部分
        success_checks = [
            ("USDT10" in result, "USDT10"),
            ("BRL50" in result, "BRL50"),
            ("USDC100" in result, "USDC100"),
            ("user_name_li" in result, "user_name_li"),
            ("thank_you_message" in result, "thank_you_message")
        ]
        
        print(f"\n🔍 验证结果:")
        all_success = True
        for check, desc in success_checks:
            status = "✅" if check else "❌"
            print(f"   {status} {desc}")
            if not check:
                all_success = False
        
        return all_success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_mapping_logic():
    """测试映射逻辑"""
    print(f"\n🔧 测试映射逻辑")
    print("=" * 60)
    
    try:
        from common.translation_manager import TranslationManager
        
        translator = TranslationManager()
        
        # 测试占位符映射
        test_mappings = [
            ("Currency", 1, "应该映射到索引1（货币）"),
            ("Amount", 0, "应该映射到索引0（金额）"),
            ("currency", 1, "小写也应该映射到索引1"),
            ("amount", 0, "小写也应该映射到索引0"),
            ("unknown", 0, "未知前缀应该默认索引0")
        ]
        
        print(f"📋 占位符映射测试:")
        for prefix, expected_index, description in test_mappings:
            actual_index = translator._get_sub_array_index(prefix)
            status = "✅" if actual_index == expected_index else "❌"
            print(f"   {status} {prefix} -> {actual_index} ({description})")
        
        return True
        
    except Exception as e:
        print(f"❌ 映射逻辑测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试你的具体逻辑需求")
    print("=" * 80)
    
    # 1. 测试映射逻辑
    mapping_ok = test_mapping_logic()
    
    # 2. 测试用例1
    case1_ok = test_case_1()
    
    # 3. 测试用例2
    case2_ok = test_case_2()
    
    print(f"\n" + "=" * 80)
    print("📊 测试结果总结:")
    print(f"   🔧 映射逻辑: {'✅ 成功' if mapping_ok else '❌ 失败'}")
    print(f"   🎯 用例1 (Currency2+Amount3): {'✅ 成功' if case1_ok else '❌ 失败'}")
    print(f"   🎯 用例2 (Currency3+Amount2): {'✅ 成功' if case2_ok else '❌ 失败'}")
    
    if mapping_ok and case1_ok and case2_ok:
        print(f"\n🎉 所有测试通过！你的逻辑需求已完全实现！")
        print(f"💡 功能特点:")
        print(f"   • 支持灵活的参数索引组合")
        print(f"   • Currency自动取子数组索引1（货币）")
        print(f"   • Amount自动取子数组索引0（金额）")
        print(f"   • 方便多语言顺序调整")
        print(f"   • 简化调用者的参数传递")
    else:
        print(f"\n⚠️ 部分测试失败，需要进一步调试")
    
    print("=" * 80)

if __name__ == "__main__":
    main()
