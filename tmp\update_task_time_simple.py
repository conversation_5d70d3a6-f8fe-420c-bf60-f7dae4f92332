#!/usr/bin/env python3
"""
简单更新任务时间
"""
import sys
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

def update_task_time():
    """更新任务时间到当前时间+2分钟"""
    print("⏰ 更新taskType=500任务时间")
    print("=" * 50)
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        # 连接MongoDB
        mongo = MongoDBConnection()
        if not mongo.connect():
            print("❌ MongoDB连接失败")
            return False
        
        # 计算新的执行时间 (当前时间+2分钟)
        current_time = datetime.now()
        new_time = current_time.replace(second=0, microsecond=0)
        new_time = new_time.replace(minute=new_time.minute + 2)
        new_time_str = new_time.strftime("%H:%M:00")
        
        print(f"当前时间: {current_time.strftime('%H:%M:%S')}")
        print(f"新执行时间: {new_time_str}")
        
        # 更新任务时间
        collection = mongo.get_collection("c_tgScheduledPushTasks")
        result = collection.update_one(
            {"taskType": 500, "enabled": True},
            {"$set": {"scheduleTimes": [new_time_str]}}
        )
        
        if result.modified_count > 0:
            print(f"✅ 任务时间更新成功")
            print(f"💡 请等待 {new_time_str} 观察自动执行")
            print(f"🔍 观察定时任务服务日志")
        else:
            print(f"❌ 任务时间更新失败")
        
        mongo.disconnect()
        return result.modified_count > 0
        
    except Exception as e:
        print(f"❌ 更新时间失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    update_task_time()
