#!/usr/bin/env python3
"""
统一服务器 - 集成FastAPI和定时任务调度器
单一进程同时提供API服务和定时任务功能
"""
import asyncio
import uvicorn
import logging
from contextlib import asynccontextmanager
from fastapi import FastAPI

from api.main import create_app
from scheduler.task_scheduler import TaskScheduler
from scheduler.config_manager import config_manager
from common.utils import setup_logging

logger = logging.getLogger(__name__)

# 全局调度器实例
scheduler_instance = None


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    global scheduler_instance
    
    # 启动阶段
    logger.info("🚀 启动统一服务器")
    
    try:
        # 1. 初始化配置管理器
        logger.info("📋 初始化配置管理器...")
        if not await config_manager.initialize():
            logger.error("❌ 配置管理器初始化失败")
            raise Exception("配置管理器初始化失败")
        
        # 2. 创建并启动调度器
        logger.info("⏰ 启动定时任务调度器...")
        scheduler_instance = TaskScheduler()
        
        # 在后台启动调度器
        asyncio.create_task(scheduler_instance.start())
        
        logger.info("✅ 统一服务器启动完成")
        logger.info("=" * 60)
        logger.info("🌐 FastAPI服务: http://localhost:8000")
        logger.info("📖 API文档: http://localhost:8000/docs")
        logger.info("💚 健康检查: http://localhost:8000/health")
        logger.info("⏰ 定时任务调度器: 运行中")
        logger.info("=" * 60)
        
        yield
        
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {e}")
        raise
    
    # 关闭阶段
    logger.info("🛑 关闭统一服务器")
    
    try:
        # 停止调度器
        if scheduler_instance:
            await scheduler_instance.stop()
            logger.info("✅ 定时任务调度器已停止")
        
        # 关闭配置管理器
        config_manager.mongo.disconnect()
        logger.info("✅ 配置管理器已关闭")
        
    except Exception as e:
        logger.error(f"❌ 服务关闭异常: {e}")


def create_unified_app() -> FastAPI:
    """创建统一的FastAPI应用"""
    
    # 创建基础应用
    app = create_app()
    
    # 设置生命周期管理
    app.router.lifespan_context = lifespan
    
    # 添加调度器状态接口
    @app.get("/api/unified-status")
    async def get_unified_status():
        """获取统一服务器状态"""
        try:
            # 获取配置管理器状态
            config_stats = config_manager.get_cache_stats()
            
            # 获取调度器状态
            scheduler_status = {
                "running": scheduler_instance.running if scheduler_instance else False,
                "task_count": len(scheduler_instance.tasks) if scheduler_instance else 0
            }
            
            return {
                "success": True,
                "message": "统一服务器状态获取成功",
                "data": {
                    "server_type": "unified",
                    "services": {
                        "fastapi": "running",
                        "scheduler": "running" if scheduler_status["running"] else "stopped"
                    },
                    "config_manager": config_stats,
                    "scheduler": scheduler_status,
                    "memory_shared": True,
                    "process_count": 1
                }
            }
            
        except Exception as e:
            logger.error(f"❌ 获取统一服务器状态失败: {e}")
            return {
                "success": False,
                "message": f"获取状态失败: {str(e)}"
            }
    
    return app


async def main():
    """主函数"""
    # 设置日志
    setup_logging()
    
    logger.info("🚀 启动统一服务器 (FastAPI + 定时任务调度器)")
    
    try:
        # 创建应用
        app = create_unified_app()
        
        # 配置服务器
        from config.settings import settings
        config = uvicorn.Config(
            app,
            host=settings.api_host,
            port=settings.api_port,
            log_level="info",
            access_log=True
        )
        
        # 启动服务器
        server = uvicorn.Server(config)
        await server.serve()
        
    except KeyboardInterrupt:
        logger.info("🛑 收到中断信号，正在关闭服务器...")
    except Exception as e:
        logger.error(f"❌ 服务器运行异常: {e}")
        raise


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 服务器被用户中断")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        exit(1)
