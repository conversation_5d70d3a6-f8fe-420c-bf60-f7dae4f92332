#!/usr/bin/env python3
"""
部署验证脚本
检查所有依赖和配置是否正确
"""
import sys
import importlib
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本")
    version = sys.version_info
    print(f"   当前版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_dependencies():
    """检查关键依赖"""
    print(f"\n📦 检查关键依赖")
    
    required_packages = [
        ('telegram', 'python-telegram-bot'),
        ('fastapi', 'fastapi'),
        ('uvicorn', 'uvicorn'),
        ('pydantic', 'pydantic'),
        ('pymongo', 'pymongo'),
        ('motor', 'motor'),
        ('aiohttp', 'aiohttp'),
        ('requests', 'requests'),
        ('yaml', 'PyYAML'),
        ('apscheduler', 'apscheduler')
    ]
    
    success_count = 0
    for module_name, package_name in required_packages:
        try:
            importlib.import_module(module_name)
            print(f"✅ {package_name}")
            success_count += 1
        except ImportError:
            print(f"❌ {package_name} - 未安装")
    
    print(f"\n📊 依赖检查结果: {success_count}/{len(required_packages)} 成功")
    return success_count == len(required_packages)

def check_project_structure():
    """检查项目结构"""
    print(f"\n📁 检查项目结构")
    
    required_files = [
        'config/settings.py',
        'config/config.yaml',
        'api/main.py',
        'scheduler/config_manager.py',
        'scheduler/task_scheduler.py',
        'database/mongodb_connection.py',
        'pusher/template_message_handler.py',
        'common/translation_manager.py',
        'start_unified_server.py'
    ]
    
    success_count = 0
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
            success_count += 1
        else:
            print(f"❌ {file_path} - 文件不存在")
    
    print(f"\n📊 文件检查结果: {success_count}/{len(required_files)} 成功")
    return success_count == len(required_files)

def check_configuration():
    """检查配置"""
    print(f"\n⚙️ 检查配置")
    
    try:
        from config.settings import settings
        print("✅ 配置文件加载成功")
        
        # 检查关键配置
        checks = [
            ('MongoDB主机', settings.mongodb_host),
            ('MongoDB端口', settings.mongodb_port),
            ('MongoDB数据库', settings.mongodb_database),
            ('API端口', settings.api_port),
        ]
        
        for name, value in checks:
            if value:
                print(f"✅ {name}: {value}")
            else:
                print(f"❌ {name}: 未配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False

def check_database_connection():
    """检查数据库连接"""
    print(f"\n🗄️ 检查数据库连接")
    
    try:
        from database.mongodb_connection import MongoDBConnection
        
        mongo = MongoDBConnection()
        if mongo.connect():
            print("✅ MongoDB连接成功")
            
            # 检查关键集合
            collections = ['c_tgRobotConfig', 'c_tgNotify', 'c_tgScheduledPushTasks']
            for collection_name in collections:
                collection = mongo.get_collection(collection_name)
                if collection is not None:
                    count = collection.count_documents({})
                    print(f"✅ {collection_name}: {count} 条记录")
                else:
                    print(f"❌ {collection_name}: 集合不存在")
            
            mongo.disconnect()
            return True
        else:
            print("❌ MongoDB连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def check_api_creation():
    """检查API应用创建"""
    print(f"\n🌐 检查API应用")
    
    try:
        from api.main import create_app
        app = create_app()
        print("✅ FastAPI应用创建成功")
        print(f"✅ API文档: /docs")
        return True
        
    except Exception as e:
        print(f"❌ API应用创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Telegram Bot服务部署验证")
    print("=" * 60)
    
    checks = [
        ("Python版本", check_python_version),
        ("依赖包", check_dependencies),
        ("项目结构", check_project_structure),
        ("配置文件", check_configuration),
        ("数据库连接", check_database_connection),
        ("API应用", check_api_creation),
    ]
    
    results = []
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}检查异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"\n" + "=" * 60)
    print("📊 验证结果总结:")
    
    success_count = 0
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n🎯 总体结果: {success_count}/{len(results)} 项检查通过")
    
    if success_count == len(results):
        print(f"\n🎉 所有检查通过！可以开始部署测试环境")
        print(f"💡 下一步:")
        print(f"   1. 启动服务: python start_unified_server.py")
        print(f"   2. 访问API文档: http://localhost:8000/docs")
        print(f"   3. 测试API接口: curl http://localhost:8000/health")
        return 0
    else:
        print(f"\n⚠️ 部分检查失败，请修复后重新验证")
        print(f"💡 建议:")
        print(f"   1. 检查requirements.txt并安装缺失依赖")
        print(f"   2. 确认配置文件正确")
        print(f"   3. 验证数据库连接")
        return 1

if __name__ == "__main__":
    sys.exit(main())
