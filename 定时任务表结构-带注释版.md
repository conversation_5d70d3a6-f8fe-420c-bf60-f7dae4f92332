# 定时任务表结构设计 - 带注释版

## 📊 表结构设计说明

### 设计目标
1. **支持多种调度频率**: 每天、每周、每月、指定日期
2. **支持多时间点**: 一天内可以有多个执行时间
3. **支持同类型多周期**: 同一个通知可以有不同的调度策略
4. **冲突优先级**: 每月 > 每周 > 每天，ID小优先级高

## 🔧 完整表结构

```json
{
  "_id": "ObjectId",               // MongoDB自动生成的唯一ID，用作优先级排序
  
  // === 关联字段 ===
  "notifyId": "int",               // 通知配置ID，关联c_tgNotify表
  "business_no": "str",            // 商户号，关联c_tgRobotConfig表
  
  // === Bot配置字段 ===
  "botToken": "str",               // Bot的API Token (可选，也可从robot表获取)
  "channelId": "list",             // 目标频道/群组ID列表 [-1002316158105, -1001234567890]
  
  // === 消息内容字段 ===
  "bannerUrl": "str",              // 横幅图片URL，可以是完整URL或相对路径
  "messageText": "str",            // 消息文本内容，支持模板变量
  "externalUrl": "str",            // 外部链接URL (浏览器打开)
  "internalUrl": "str",            // 内部链接URL (Telegram内打开)
  "urlLabel": "str",               // 链接按钮显示文本
  "urlType": "str",                // 链接类型: "browser"|"webapp"|"inline"|""
  "language": "int",               // 语言ID，对应language_mapping配置
  
  // === 调度配置字段 (核心) ===
  "sendFrequency": "str",          // 发送频率: "daily"|"weekly"|"monthly"|"once"
  "scheduleDays": "list",          // 调度天数配置 (根据sendFrequency含义不同)
  "scheduleTimes": "list",         // 调度时间点 ["09:00", "15:00", "21:00"]
  "taskType": "int",               // 任务类型ID，用于区分同一notifyId的不同调度策略
  
  // === 状态字段 ===
  "enabled": "bool",               // 是否启用该任务
  "createTime": "int64",           // 创建时间戳 (毫秒)
  "updateTime": "int64"            // 更新时间戳 (毫秒)
}
```

## 📋 调度配置详解

### sendFrequency 字段说明
```javascript
// 支持的调度频率类型
"sendFrequency": "daily"     // 每天执行
"sendFrequency": "weekly"    // 每周执行
"sendFrequency": "monthly"   // 每月执行
"sendFrequency": "once"      // 指定日期执行一次
```

### scheduleDays 字段说明 (根据sendFrequency含义不同)
```javascript
// 当 sendFrequency = "daily" 时
"scheduleDays": []           // 空数组，表示每天都执行

// 当 sendFrequency = "weekly" 时
"scheduleDays": [1, 3, 5]    // 周一、周三、周五 (1=周一, 7=周日)

// 当 sendFrequency = "monthly" 时  
"scheduleDays": [1, 15, 30]  // 每月1号、15号、30号

// 当 sendFrequency = "once" 时
"scheduleDays": [20250715]   // 指定日期 YYYYMMDD 格式 (2025年7月15日)
```

### scheduleTimes 字段说明
```javascript
// 时间格式: HH:MM (24小时制)
"scheduleTimes": ["09:00"]                    // 每天1次
"scheduleTimes": ["09:00", "15:00"]           // 每天2次  
"scheduleTimes": ["09:00", "15:00", "21:00"] // 每天3次
"scheduleTimes": ["08:30", "12:00", "18:30", "22:00"] // 每天4次
```

### taskType 字段说明
```javascript
// 建议的taskType分配规则 (可根据业务调整)
100: "每天任务"
200: "每周任务" 
300: "每月任务"
400: "指定日期任务"
500: "活动提醒"
501: "充值促销"
502: "VIP服务"
// 数字越小优先级越高 (相同频率时)
```

## 📅 实际应用示例

### 示例1: 每天任务 - 签到提醒
```json
{
  "_id": "ObjectId_1",
  "notifyId": 1,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/daily_checkin.png",
  "messageText": "每日签到提醒：记得来签到领取奖励哦！",
  "externalUrl": "https://example.com/checkin",
  "internalUrl": "t.me/bot/checkin",
  "urlLabel": "立即签到",
  "urlType": "webapp",
  "language": 2,
  
  // 每天任务配置
  "sendFrequency": "daily",        // 每天执行
  "scheduleDays": [],              // 空数组表示每天
  "scheduleTimes": ["09:00", "21:00"], // 每天早上9点和晚上9点
  "taskType": 100,                 // 每天任务类型
  
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210
}
```

### 示例2: 每周任务 - 周末活动
```json
{
  "_id": "ObjectId_2", 
  "notifyId": 2,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/weekend_activity.gif",
  "messageText": "周末活动火热进行中！双倍奖励等你来拿！",
  "externalUrl": "https://example.com/weekend-events",
  "internalUrl": "",
  "urlLabel": "参与活动",
  "urlType": "browser",
  "language": 2,
  
  // 每周任务配置
  "sendFrequency": "weekly",       // 每周执行
  "scheduleDays": [6, 7],          // 周六、周日
  "scheduleTimes": ["10:00", "20:00"], // 每天上午10点和晚上8点
  "taskType": 200,                 // 每周任务类型
  
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210
}
```

### 示例3: 每月任务 - 投注返利
```json
{
  "_id": "ObjectId_3",
  "notifyId": 3,
  "business_no": "39bac42a", 
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/monthly_reward.png",
  "messageText": "每月投注返利已到账，请查收！",
  "externalUrl": "https://example.com/monthly-rewards",
  "internalUrl": "t.me/bot/monthly_rewards",
  "urlLabel": "查看详情",
  "urlType": "webapp",
  "language": 2,
  
  // 每月任务配置
  "sendFrequency": "monthly",      // 每月执行
  "scheduleDays": [1, 15, 30],     // 每月1号、15号、30号
  "scheduleTimes": ["09:00", "15:00", "21:00"], // 每天3个时间点
  "taskType": 300,                 // 每月任务类型
  
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210
}
```

### 示例4: 指定日期任务 - 特殊活动
```json
{
  "_id": "ObjectId_4",
  "notifyId": 4,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw", 
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/special_event.png",
  "messageText": "🎉 特殊活动开始啦！限时优惠不要错过！",
  "externalUrl": "https://example.com/special-event",
  "internalUrl": "t.me/bot/special_event",
  "urlLabel": "立即参与",
  "urlType": "webapp",
  "language": 2,
  
  // 指定日期任务配置
  "sendFrequency": "once",         // 指定日期执行一次
  "scheduleDays": [20250715, 20250716], // 2025年7月15日和16日
  "scheduleTimes": ["12:00"],      // 中午12点
  "taskType": 400,                 // 指定日期任务类型
  
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210
}
```

### 示例5: 同一notifyId的多种调度策略
```json
// 同一个投注返利通知的每周版本
{
  "_id": "ObjectId_5",
  "notifyId": 3,                   // 与示例3相同的notifyId
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/weekly_reward.png",
  "messageText": "每周投注返利提醒，继续加油！",
  "externalUrl": "https://example.com/weekly-rewards",
  "internalUrl": "t.me/bot/weekly_rewards", 
  "urlLabel": "立即查看",
  "urlType": "webapp",
  "language": 2,
  
  // 每周任务配置 (同一notifyId的不同策略)
  "sendFrequency": "weekly",       // 每周执行
  "scheduleDays": [1, 3, 5],       // 周一、周三、周五
  "scheduleTimes": ["10:00", "16:00"], // 每天2个时间点
  "taskType": 201,                 // 每周任务类型 (不同于示例2)
  
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210
}
```

## 🎯 冲突解决机制

### 优先级规则 (按顺序执行)
```javascript
// 1. 频率优先级 (sendFrequency)
"monthly" > "weekly" > "daily" > "once"

// 2. ID优先级 (相同频率时)
_id小 > _id大

// 3. 类型优先级 (相同频率和ID时)  
taskType小 > taskType大
```

### 冲突场景示例: 2025年7月15日 (周三)
```json
// 假设有以下4个任务在同一天匹配

// 任务1: 每天任务
{
  "_id": "ObjectId_1",
  "sendFrequency": "daily",
  "scheduleDays": [],              // 每天
  "scheduleTimes": ["08:00"],
  "taskType": 100
}

// 任务2: 每周任务  
{
  "_id": "ObjectId_2",
  "sendFrequency": "weekly", 
  "scheduleDays": [3],             // 周三
  "scheduleTimes": ["10:00"],
  "taskType": 200
}

// 任务3: 每月任务
{
  "_id": "ObjectId_3", 
  "sendFrequency": "monthly",
  "scheduleDays": [15],            // 15号
  "scheduleTimes": ["12:00"],
  "taskType": 300
}

// 任务4: 指定日期任务
{
  "_id": "ObjectId_4",
  "sendFrequency": "once",
  "scheduleDays": [20250715],      // 2025年7月15日
  "scheduleTimes": ["14:00"],
  "taskType": 400
}

// 冲突解决结果: 只执行每月任务 (任务3)
// 原因: monthly优先级最高，其他任务被忽略
// 最终执行: 2025年7月15日 12:00
```

## 🔍 查询示例

### 获取指定日期的任务
```javascript
// 获取2025年7月15日的所有可能任务
function getTasksForDate(date) {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay(); // 周日=7
  const dateNumber = year * 10000 + month * 100 + day; // 20250715
  
  // 查询所有可能匹配的任务
  return db.c_tgScheduledPushTasks.find({
    $or: [
      // 每天任务
      { "sendFrequency": "daily", "scheduleDays": [] },
      
      // 每周任务
      { "sendFrequency": "weekly", "scheduleDays": dayOfWeek },
      
      // 每月任务  
      { "sendFrequency": "monthly", "scheduleDays": day },
      
      // 指定日期任务
      { "sendFrequency": "once", "scheduleDays": dateNumber }
    ],
    "enabled": true
  }).sort({ "_id": 1 });
}
```

### 按优先级获取最终执行任务
```javascript
// 按优先级顺序查询
const frequencyPriority = ["monthly", "weekly", "daily", "once"];

for (const frequency of frequencyPriority) {
  const tasks = getTasksByFrequency(frequency, date);
  if (tasks.length > 0) {
    return tasks; // 返回最高优先级的任务
  }
}
```

## 💡 核心优势

### ✅ 灵活性强
- 支持4种调度频率: 每天、每周、每月、指定日期
- 支持一天内多个时间点执行
- 支持同一通知的多种调度策略

### ✅ 优先级清晰
- 明确的频率优先级规则
- ID和taskType提供细粒度控制
- 冲突解决逻辑简单明了

### ✅ 易于配置
- 字段含义直观易懂
- 配置灵活，覆盖各种业务场景
- 便于运营人员理解和操作

### ✅ 扩展性好
- taskType支持业务类型扩展
- 新增调度频率只需扩展sendFrequency
- 便于添加新的调度规则
