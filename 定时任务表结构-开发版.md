# 定时任务表结构设计 - 开发版

## 🎯 设计原则

### 核心需求
1. **多周期支持**: 同一商户同一类型可以有每月和每周设置
2. **多时间点**: 一天中可以有多个执行时间点
3. **优先级规则**: 每月优先于每周，ID小优先于ID大
4. **代码友好**: 扁平结构，查询高效

### 设计理念
- ✅ **分离记录**: 每月和每周分别存储
- ✅ **扁平结构**: 无嵌套，直接字段访问
- ✅ **简化字段**: 去除冗余，保留核心
- ✅ **统一命名**: 清晰的字段命名规范

## 📊 优化后的表结构

### 核心字段设计
```json
{
  "_id": "ObjectId",               // MongoDB自动生成，用作优先级
  "business_no": "str",            // 商户号
  "notify_id": "int",              // 通知配置ID (关联c_tgNotify表)
  "task_group": "str",             // 任务组标识 (用于关联同一业务的不同周期)
  "task_type": "str",              // 任务类型: "monthly" | "weekly"
  "schedule_days": "list",         // 调度天数 (月份日期或星期几)
  "schedule_times": "list",        // 调度时间点 ["09:00", "15:00", "21:00"]
  "target_chats": "list",          // 目标聊天ID列表
  "message_config": {              // 消息配置
    "text": "str",                 // 消息文本
    "banner_url": "str",           // 图片URL
    "external_url": "str",         // 外部链接
    "internal_url": "str",         // 内部链接
    "url_label": "str",            // 链接标签
    "url_type": "str",             // 链接类型
    "language": "int"              // 语言ID
  },
  "status": "str",                 // 状态: "active" | "inactive" | "deleted"
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime"         // 更新时间
}
```

## 📋 实际数据示例

### 场景: 投注返利通知的每月和每周任务

#### 每月任务记录
```json
{
  "_id": "ObjectId_1",
  "business_no": "39bac42a",
  "notify_id": 3,
  "task_group": "reward_notification",
  "task_type": "monthly",
  "schedule_days": [1, 15, 30],
  "schedule_times": ["09:00", "15:00", "21:00"],
  "target_chats": [-1002316158105],
  "message_config": {
    "text": "每月投注返利已到账，请查收！",
    "banner_url": "https://cdn.example.com/monthly_reward.png",
    "external_url": "https://example.com/monthly-rewards",
    "internal_url": "t.me/bot/monthly_rewards",
    "url_label": "查看详情",
    "url_type": "webapp",
    "language": 2
  },
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}
```

#### 每周任务记录
```json
{
  "_id": "ObjectId_2",
  "business_no": "39bac42a",
  "notify_id": 3,
  "task_group": "reward_notification",
  "task_type": "weekly",
  "schedule_days": [1, 3, 5],
  "schedule_times": ["10:00", "16:00"],
  "target_chats": [-1002316158105],
  "message_config": {
    "text": "每周投注返利提醒，继续加油！",
    "banner_url": "https://cdn.example.com/weekly_reward.png",
    "external_url": "https://example.com/weekly-rewards",
    "internal_url": "t.me/bot/weekly_rewards",
    "url_label": "立即查看",
    "url_type": "webapp",
    "language": 2
  },
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}
```

### 不同业务类型示例

#### 活动提醒任务
```json
{
  "_id": "ObjectId_3",
  "business_no": "39bac42a",
  "notify_id": 5,
  "task_group": "activity_reminder",
  "task_type": "weekly",
  "schedule_days": [6, 7],  // 周六、周日
  "schedule_times": ["20:00"],
  "target_chats": [-1002316158105],
  "message_config": {
    "text": "周末活动火热进行中！",
    "banner_url": "https://cdn.example.com/weekend_activity.gif",
    "external_url": "https://example.com/weekend-events",
    "internal_url": "",
    "url_label": "参与活动",
    "url_type": "browser",
    "language": 2
  },
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}
```

## 🔧 字段详细说明

### schedule_days 字段规则
```javascript
// 每周任务: 1=周一, 2=周二, ..., 7=周日
"schedule_days": [1, 3, 5]  // 周一、周三、周五

// 每月任务: 1-31表示月份中的日期
"schedule_days": [1, 15, 30]  // 每月1号、15号、30号
```

### schedule_times 字段规则
```javascript
// 时间格式: HH:MM (24小时制)
"schedule_times": ["09:00", "15:00", "21:00"]  // 每天执行3次

// 支持任意时间点
"schedule_times": ["08:30", "12:00", "18:30", "22:00"]  // 每天4次
```

### task_group 字段规则
```javascript
// 命名规范: {业务类型}_{可选标识}
"task_group": "reward_notification"     // 奖励通知
"task_group": "activity_reminder"       // 活动提醒
"task_group": "deposit_promotion"       // 充值促销
"task_group": "vip_service"            // VIP服务
```

### message_config 嵌套结构
```json
{
  "text": "消息文本内容",
  "banner_url": "图片URL (可选)",
  "external_url": "外部链接 (可选)",
  "internal_url": "内部链接 (可选)",
  "url_label": "链接显示文本 (可选)",
  "url_type": "链接类型: browser|webapp|inline",
  "language": "语言ID"
}
```

## 📅 冲突解决逻辑

### 优先级规则
1. **类型优先级**: monthly > weekly
2. **ID优先级**: _id小 > _id大 (MongoDB自然排序)

### 冲突场景示例
```json
// 2025年7月15日 (周三) 的冲突解决

// 每月任务 (优先级高)
{
  "_id": "ObjectId_1",  // ID较小
  "task_type": "monthly",
  "schedule_days": [15],
  "schedule_times": ["09:00", "15:00"]
}

// 每周任务 (优先级低)
{
  "_id": "ObjectId_2",  // ID较大
  "task_type": "weekly", 
  "schedule_days": [3],  // 周三
  "schedule_times": ["10:00", "16:00"]
}

// 结果: 执行每月任务的 09:00, 15:00，忽略每周任务
```

## 🔍 查询示例

### 基础查询
```javascript
// 查询某日期的每月任务
db.c_tgScheduledPushTasks.find({
  "task_type": "monthly",
  "schedule_days": 15,
  "status": "active"
}).sort({"_id": 1})

// 查询某日期的每周任务
db.c_tgScheduledPushTasks.find({
  "task_type": "weekly",
  "schedule_days": 3,  // 周三
  "status": "active"
}).sort({"_id": 1})

// 查询某商户的所有任务
db.c_tgScheduledPushTasks.find({
  "business_no": "39bac42a",
  "status": "active"
}).sort({"_id": 1})
```

### 复合查询
```javascript
// 查询某任务组的所有记录
db.c_tgScheduledPushTasks.find({
  "task_group": "reward_notification",
  "status": "active"
}).sort({"_id": 1})

// 查询包含特定时间点的任务
db.c_tgScheduledPushTasks.find({
  "schedule_times": "09:00",
  "status": "active"
})

// 查询某语言的任务
db.c_tgScheduledPushTasks.find({
  "message_config.language": 2,
  "status": "active"
})
```

## 🚀 代码实现示例

### 任务生成逻辑
```python
def generate_daily_tasks(date):
    """生成指定日期的任务"""
    day_of_month = date.day
    day_of_week = date.weekday() + 1
    
    # 查找每月任务
    monthly_tasks = collection.find({
        "task_type": "monthly",
        "schedule_days": day_of_month,
        "status": "active"
    }).sort("_id", 1)
    
    # 如果有每月任务，直接返回
    monthly_instances = list(monthly_tasks)
    if monthly_instances:
        return generate_task_instances(monthly_instances, date)
    
    # 否则查找每周任务
    weekly_tasks = collection.find({
        "task_type": "weekly",
        "schedule_days": day_of_week,
        "status": "active"
    }).sort("_id", 1)
    
    return generate_task_instances(list(weekly_tasks), date)

def generate_task_instances(task_configs, date):
    """从配置生成具体任务实例"""
    instances = []
    for config in task_configs:
        for time in config['schedule_times']:
            instances.append({
                'config_id': str(config['_id']),
                'business_no': config['business_no'],
                'notify_id': config['notify_id'],
                'execute_time': f"{date} {time}",
                'task_type': config['task_type'],
                'target_chats': config['target_chats'],
                'message_config': config['message_config']
            })
    return instances
```

### 配置管理
```python
class ScheduledTaskManager:
    def __init__(self):
        self.task_cache = {}
    
    def load_tasks(self):
        """加载所有活跃任务"""
        tasks = collection.find({"status": "active"}).sort("_id", 1)
        
        for task in tasks:
            business_no = task['business_no']
            if business_no not in self.task_cache:
                self.task_cache[business_no] = []
            self.task_cache[business_no].append(task)
    
    def get_tasks_for_date(self, business_no, date):
        """获取指定商户和日期的任务"""
        business_tasks = self.task_cache.get(business_no, [])
        
        day_of_month = date.day
        day_of_week = date.weekday() + 1
        
        monthly_matches = []
        weekly_matches = []
        
        for task in business_tasks:
            if task['task_type'] == 'monthly' and day_of_month in task['schedule_days']:
                monthly_matches.append(task)
            elif task['task_type'] == 'weekly' and day_of_week in task['schedule_days']:
                weekly_matches.append(task)
        
        # 每月优先
        return monthly_matches if monthly_matches else weekly_matches
```

## 💡 核心优势

### ✅ 简洁高效
- 扁平结构，无冗余字段
- 直接字段访问，查询高效
- 清晰的命名规范

### ✅ 功能完整
- 支持多周期、多时间点
- 灵活的消息配置
- 明确的优先级规则

### ✅ 易于维护
- 分离记录，逻辑清晰
- 统一的数据结构
- 简单的CRUD操作

### ✅ 扩展性强
- 易于添加新的任务类型
- 支持复杂的调度规则
- 便于集成其他系统
