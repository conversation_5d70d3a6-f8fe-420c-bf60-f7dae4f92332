# 定时任务表结构设计 - 最终版

## 📊 最终表结构

### 完整字段定义
```json
{
  "_id": "ObjectId",               // MongoDB自动生成，用作优先级
  "notifyId": "int",               // 通知ID (关联c_tgNotify表)
  "business_no": "str",            // 商户号
  "botToken": "str",               // Bot Token
  "channelId": "list",             // 目标频道ID列表
  "bannerUrl": "str",              // 横幅图片URL
  "messageText": "str",            // 消息文本
  "externalUrl": "str",            // 外部链接
  "internalUrl": "str",            // 内部链接
  "urlLabel": "str",               // 链接标签
  "urlType": "str",                // 链接类型
  "sendFrequency": "str",          // 发送频率: "monthly" | "weekly"
  "scheduleDays": "list",          // 调度天数 (月份日期或星期几)
  "scheduleTimes": "list",         // 调度时间点 ["09:00", "15:00", "21:00"]
  "enabled": "bool",               // 是否启用
  "createTime": "int64",           // 创建时间 (时间戳)
  "updateTime": "int64",           // 更新时间 (时间戳)
  "language": "int",               // 语言ID
  "taskType": "int"                // 任务类型ID (用于区分同一notifyId的不同周期)
}
```

## 📋 实际数据示例

### 场景: 投注返利通知的每月和每周任务

#### 每月任务记录
```json
{
  "_id": "ObjectId_1",
  "notifyId": 3,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/monthly_reward.png",
  "messageText": "每月投注返利已到账，请查收！",
  "externalUrl": "https://example.com/monthly-rewards",
  "internalUrl": "t.me/bot/monthly_rewards",
  "urlLabel": "查看详情",
  "urlType": "webapp",
  "sendFrequency": "monthly",
  "scheduleDays": [1, 15, 30],           // 每月1号、15号、30号
  "scheduleTimes": ["09:00", "15:00", "21:00"], // 每天3个时间点
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210,
  "language": 2,
  "taskType": 400                        // 每月任务类型
}
```

#### 每周任务记录
```json
{
  "_id": "ObjectId_2",
  "notifyId": 3,                         // 同一个notifyId
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/weekly_reward.png",
  "messageText": "每周投注返利提醒，继续加油！",
  "externalUrl": "https://example.com/weekly-rewards",
  "internalUrl": "t.me/bot/weekly_rewards",
  "urlLabel": "立即查看",
  "urlType": "webapp",
  "sendFrequency": "weekly",
  "scheduleDays": [1, 3, 5],             // 周一、周三、周五
  "scheduleTimes": ["10:00", "16:00"],   // 每天2个时间点
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210,
  "language": 2,
  "taskType": 401                        // 每周任务类型
}
```

### 不同业务类型示例

#### 活动提醒任务
```json
{
  "_id": "ObjectId_3",
  "notifyId": 5,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "https://cdn.example.com/weekend_activity.gif",
  "messageText": "周末活动火热进行中！",
  "externalUrl": "https://example.com/weekend-events",
  "internalUrl": "",
  "urlLabel": "参与活动",
  "urlType": "browser",
  "sendFrequency": "weekly",
  "scheduleDays": [6, 7],                // 周六、周日
  "scheduleTimes": ["20:00"],            // 每天1个时间点
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210,
  "language": 2,
  "taskType": 500                        // 活动提醒类型
}
```

## 🔧 字段详细说明

### scheduleDays 字段规则
```javascript
// 每周任务: 1=周一, 2=周二, ..., 7=周日
"scheduleDays": [1, 3, 5]  // 周一、周三、周五

// 每月任务: 1-31表示月份中的日期
"scheduleDays": [1, 15, 30]  // 每月1号、15号、30号
```

### scheduleTimes 字段规则
```javascript
// 时间格式: HH:MM (24小时制)
"scheduleTimes": ["09:00", "15:00", "21:00"]  // 每天执行3次

// 支持任意时间点
"scheduleTimes": ["08:30", "12:00", "18:30", "22:00"]  // 每天4次
```

### sendFrequency 字段值
```javascript
"sendFrequency": "monthly"  // 每月任务
"sendFrequency": "weekly"   // 每周任务
```

### taskType 字段规则
```javascript
// 建议的taskType分配规则
400: "每月任务"
401: "每周任务"
500: "活动提醒"
501: "充值促销"
502: "VIP服务"
// 可以根据业务需要扩展...
```

### urlType 字段值
```javascript
"urlType": "browser"   // 浏览器打开
"urlType": "webapp"    // Telegram WebApp
"urlType": "inline"    // 内联链接
"urlType": ""          // 无链接
```

## 📅 冲突解决逻辑

### 优先级规则
1. **频率优先级**: monthly > weekly (sendFrequency字段)
2. **ID优先级**: _id小 > _id大 (MongoDB自然排序)
3. **类型优先级**: taskType小 > taskType大 (相同频率时)

### 冲突场景示例
```json
// 2025年7月15日 (周三) 的冲突解决

// 每月任务 (优先级最高)
{
  "_id": "ObjectId_1",
  "sendFrequency": "monthly",
  "scheduleDays": [15],
  "scheduleTimes": ["09:00", "15:00"],
  "taskType": 400
}

// 每周任务 (优先级较低)
{
  "_id": "ObjectId_2",
  "sendFrequency": "weekly", 
  "scheduleDays": [3],  // 周三
  "scheduleTimes": ["10:00", "16:00"],
  "taskType": 401
}

// 结果: 执行每月任务的 09:00, 15:00，忽略每周任务
```

## 🔍 查询示例

### 基础查询
```javascript
// 查询某日期的每月任务
db.c_tgScheduledPushTasks.find({
  "sendFrequency": "monthly",
  "scheduleDays": 15,
  "enabled": true
}).sort({"_id": 1})

// 查询某日期的每周任务
db.c_tgScheduledPushTasks.find({
  "sendFrequency": "weekly",
  "scheduleDays": 3,  // 周三
  "enabled": true
}).sort({"_id": 1})

// 查询某商户的所有任务
db.c_tgScheduledPushTasks.find({
  "business_no": "39bac42a",
  "enabled": true
}).sort({"_id": 1})
```

### 复合查询
```javascript
// 查询某notifyId的所有记录
db.c_tgScheduledPushTasks.find({
  "notifyId": 3,
  "enabled": true
}).sort({"_id": 1})

// 查询包含特定时间点的任务
db.c_tgScheduledPushTasks.find({
  "scheduleTimes": "09:00",
  "enabled": true
})

// 查询某语言的任务
db.c_tgScheduledPushTasks.find({
  "language": 2,
  "enabled": true
})

// 查询某任务类型
db.c_tgScheduledPushTasks.find({
  "taskType": 400,
  "enabled": true
})
```

## 🚀 代码实现示例

### 任务生成逻辑
```python
def generate_daily_tasks(date):
    """生成指定日期的任务"""
    day_of_month = date.day
    day_of_week = date.weekday() + 1
    
    # 查找每月任务
    monthly_tasks = collection.find({
        "sendFrequency": "monthly",
        "scheduleDays": day_of_month,
        "enabled": True
    }).sort("_id", 1)
    
    # 如果有每月任务，直接返回
    monthly_instances = list(monthly_tasks)
    if monthly_instances:
        return generate_task_instances(monthly_instances, date)
    
    # 否则查找每周任务
    weekly_tasks = collection.find({
        "sendFrequency": "weekly",
        "scheduleDays": day_of_week,
        "enabled": True
    }).sort("_id", 1)
    
    return generate_task_instances(list(weekly_tasks), date)

def generate_task_instances(task_configs, date):
    """从配置生成具体任务实例"""
    instances = []
    for config in task_configs:
        for time in config['scheduleTimes']:
            instances.append({
                'config_id': str(config['_id']),
                'business_no': config['business_no'],
                'notify_id': config['notifyId'],
                'execute_time': f"{date} {time}",
                'send_frequency': config['sendFrequency'],
                'task_type': config['taskType'],
                'target_chats': config['channelId'],
                'message_text': config['messageText'],
                'banner_url': config['bannerUrl'],
                'external_url': config['externalUrl'],
                'internal_url': config['internalUrl'],
                'url_label': config['urlLabel'],
                'url_type': config['urlType'],
                'language': config['language'],
                'bot_token': config['botToken']
            })
    return instances
```

### 配置管理
```python
class ScheduledTaskManager:
    def __init__(self):
        self.task_cache = {}
    
    def load_tasks(self):
        """加载所有活跃任务"""
        tasks = collection.find({"enabled": True}).sort("_id", 1)
        
        for task in tasks:
            business_no = task['business_no']
            if business_no not in self.task_cache:
                self.task_cache[business_no] = []
            self.task_cache[business_no].append(task)
    
    def get_tasks_for_date(self, business_no, date):
        """获取指定商户和日期的任务"""
        business_tasks = self.task_cache.get(business_no, [])
        
        day_of_month = date.day
        day_of_week = date.weekday() + 1
        
        monthly_matches = []
        weekly_matches = []
        
        for task in business_tasks:
            if (task['sendFrequency'] == 'monthly' and 
                day_of_month in task['scheduleDays']):
                monthly_matches.append(task)
            elif (task['sendFrequency'] == 'weekly' and 
                  day_of_week in task['scheduleDays']):
                weekly_matches.append(task)
        
        # 每月优先
        return monthly_matches if monthly_matches else weekly_matches
```

## 💡 核心优势

### ✅ 保持兼容
- 字段名称基本保持不变
- 数据类型与现有结构一致
- 最小化迁移成本

### ✅ 功能增强
- 支持多时间点执行 (scheduleTimes数组)
- 灵活的每月/每周配置 (sendFrequency + scheduleDays)
- 清晰的任务分类 (taskType)
- 明确的优先级规则

### ✅ 易于维护
- 分离记录，逻辑清晰
- 统一的数据结构
- 简单的查询和操作

### ✅ 扩展性强
- taskType支持业务扩展
- scheduleTimes支持复杂时间安排
- 便于添加新的调度类型
