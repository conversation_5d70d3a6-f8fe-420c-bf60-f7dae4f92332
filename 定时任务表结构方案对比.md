# 定时任务表结构方案对比分析

## 📋 需求背景

### 核心需求
1. **多周期支持**: 1个商户的相同类型可以有每月和每周多种设置
2. **多时间点**: 一天中可以有多个执行时间点
3. **优先级规则**: 当每月和每周在某一天重叠时，采用每月定时，忽略每周定时
4. **简化优先级**: 相同类型冲突时，以数据库ID为优先级（ID小优先级高）

## 🔧 方案A: 嵌套配置结构

### 表结构设计
```json
{
  "id": 1,
  "business_no": "39bac42a",
  "notify_id": 1,
  "task_group": "reward_notification",
  "task_name": "投注返利通知",
  "schedule_type": "mixed",
  "schedule_config": {
    "monthly": {
      "enabled": true,
      "days": [1, 15, 30],
      "times": ["09:00", "15:00", "21:00"]
    },
    "weekly": {
      "enabled": true,
      "days": [1, 3, 5],
      "times": ["10:00", "16:00"]
    }
  },
  "conflict_resolution": "monthly_first",
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}
```

### 代码实现示例
```python
def generate_daily_tasks_nested(date):
    """方案A: 嵌套结构的任务生成"""
    configs = get_active_configs_sorted_by_id()
    monthly_tasks = []
    weekly_tasks = []
    
    for config in configs:
        schedule_config = config.get('schedule_config', {})
        
        # 检查每月配置
        monthly_config = schedule_config.get('monthly', {})
        if monthly_config.get('enabled') and is_monthly_match(monthly_config, date):
            for time in monthly_config.get('times', []):
                monthly_tasks.append({
                    'config_id': config['id'],
                    'business_no': config['business_no'],
                    'notify_id': config['notify_id'],
                    'schedule_time': time,
                    'type': 'monthly'
                })
        
        # 检查每周配置
        weekly_config = schedule_config.get('weekly', {})
        if weekly_config.get('enabled') and is_weekly_match(weekly_config, date):
            for time in weekly_config.get('times', []):
                weekly_tasks.append({
                    'config_id': config['id'],
                    'business_no': config['business_no'],
                    'notify_id': config['notify_id'],
                    'schedule_time': time,
                    'type': 'weekly'
                })
    
    # 冲突解决：每月优先
    return monthly_tasks if monthly_tasks else weekly_tasks

def is_monthly_match(monthly_config, date):
    """检查每月匹配"""
    return date.day in monthly_config.get('days', [])

def is_weekly_match(weekly_config, date):
    """检查每周匹配"""
    return (date.weekday() + 1) in weekly_config.get('days', [])
```

### MongoDB查询示例
```python
# 查询包含每月15号配置的任务
db.c_tgScheduledPushTasks.find({
    "schedule_config.monthly.enabled": True,
    "schedule_config.monthly.days": 15,
    "status": "active"
})

# 查询包含周三配置的任务
db.c_tgScheduledPushTasks.find({
    "schedule_config.weekly.enabled": True,
    "schedule_config.weekly.days": 3,
    "status": "active"
})
```

## 🔧 方案B: 分离记录结构

### 表结构设计
```json
// 记录1 - 每月任务
{
  "id": 1,
  "business_no": "39bac42a",
  "notify_id": 1,
  "task_group": "reward_notification",
  "task_name": "投注返利通知-每月",
  "schedule_type": "monthly",
  "schedule_days": [1, 15, 30],
  "schedule_times": ["09:00", "15:00", "21:00"],
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}

// 记录2 - 每周任务
{
  "id": 2,
  "business_no": "39bac42a",
  "notify_id": 1,
  "task_group": "reward_notification",
  "task_name": "投注返利通知-每周",
  "schedule_type": "weekly",
  "schedule_days": [1, 3, 5],
  "schedule_times": ["10:00", "16:00"],
  "status": "active",
  "created_at": "2025-07-08T10:00:00Z",
  "updated_at": "2025-07-08T10:00:00Z"
}
```

### 代码实现示例
```python
def generate_daily_tasks_separated(date):
    """方案B: 分离结构的任务生成"""
    day_of_month = date.day
    day_of_week = date.weekday() + 1
    
    # 查找每月匹配的任务
    monthly_tasks = collection.find({
        "schedule_type": "monthly",
        "schedule_days": day_of_month,
        "status": "active"
    }).sort("id", 1)
    
    monthly_instances = []
    for task in monthly_tasks:
        for time in task['schedule_times']:
            monthly_instances.append({
                'config_id': task['id'],
                'business_no': task['business_no'],
                'notify_id': task['notify_id'],
                'schedule_time': time,
                'type': 'monthly'
            })
    
    # 如果有每月任务，直接返回（每月优先）
    if monthly_instances:
        return monthly_instances
    
    # 否则查找每周任务
    weekly_tasks = collection.find({
        "schedule_type": "weekly",
        "schedule_days": day_of_week,
        "status": "active"
    }).sort("id", 1)
    
    weekly_instances = []
    for task in weekly_tasks:
        for time in task['schedule_times']:
            weekly_instances.append({
                'config_id': task['id'],
                'business_no': task['business_no'],
                'notify_id': task['notify_id'],
                'schedule_time': time,
                'type': 'weekly'
            })
    
    return weekly_instances

# 缓存管理
class ScheduleManager:
    def __init__(self):
        self.monthly_tasks = {}  # {business_no: [tasks]}
        self.weekly_tasks = {}   # {business_no: [tasks]}
    
    def load_tasks(self):
        # 分别加载，结构清晰
        monthly = collection.find({"schedule_type": "monthly", "status": "active"})
        for task in monthly:
            business_no = task['business_no']
            if business_no not in self.monthly_tasks:
                self.monthly_tasks[business_no] = []
            self.monthly_tasks[business_no].append(task)
        
        weekly = collection.find({"schedule_type": "weekly", "status": "active"})
        for task in weekly:
            business_no = task['business_no']
            if business_no not in self.weekly_tasks:
                self.weekly_tasks[business_no] = []
            self.weekly_tasks[business_no].append(task)
```

### MongoDB查询示例
```python
# 查询每月15号的任务
db.c_tgScheduledPushTasks.find({
    "schedule_type": "monthly",
    "schedule_days": 15,
    "status": "active"
}).sort({"id": 1})

# 查询周三的任务
db.c_tgScheduledPushTasks.find({
    "schedule_type": "weekly",
    "schedule_days": 3,
    "status": "active"
}).sort({"id": 1})

# 查询某商户的所有定时任务
db.c_tgScheduledPushTasks.find({
    "business_no": "39bac42a",
    "status": "active"
}).sort({"id": 1})
```

## 📅 实际场景对比

### 场景: 2025年7月15日（周三）

#### 配置数据
```json
// 方案A: 一条记录
{
  "id": 1,
  "business_no": "39bac42a",
  "schedule_config": {
    "monthly": {"days": [15], "times": ["09:00", "15:00"]},
    "weekly": {"days": [3], "times": ["10:00", "16:00"]}
  }
}

// 方案B: 两条记录
{
  "id": 1,
  "schedule_type": "monthly",
  "schedule_days": [15],
  "schedule_times": ["09:00", "15:00"]
}
{
  "id": 2,
  "schedule_type": "weekly", 
  "schedule_days": [3],
  "schedule_times": ["10:00", "16:00"]
}
```

#### 执行结果
- **冲突检测**: 7月15日既是15号（每月）又是周三（每周）
- **优先级规则**: 每月优先
- **最终执行**: 09:00, 15:00（忽略每周的10:00, 16:00）
- **两种方案结果一致** ✅

## 📊 详细对比分析

| 对比维度 | 方案A (嵌套结构) | 方案B (分离记录) |
|----------|------------------|------------------|
| **数据存储** | 1条记录包含所有配置 | 每种类型1条记录 |
| **查询复杂度** | ❌ 需要嵌套字段查询 | ✅ 简单字段查询 |
| **索引效率** | ❌ 嵌套字段索引复杂 | ✅ 直接字段索引 |
| **代码可读性** | ❌ 深度嵌套访问 | ✅ 扁平结构访问 |
| **缓存友好** | ❌ 需要解析嵌套结构 | ✅ 直接使用 |
| **扩展性** | ❌ 修改嵌套结构 | ✅ 增加新记录 |
| **调试难度** | ❌ 嵌套结构难追踪 | ✅ 记录清晰可见 |
| **配置管理** | ❌ 复杂的嵌套操作 | ✅ 简单的CRUD操作 |
| **数据一致性** | ✅ 单记录事务 | ❌ 多记录需要事务 |
| **存储空间** | ✅ 相对节省 | ❌ 稍多冗余 |

## 💡 推荐结论

### 🎯 推荐方案B (分离记录结构)

#### 核心理由:
1. **代码友好**: 扁平结构，无嵌套访问
2. **查询高效**: 直接字段查询，可建立高效索引
3. **维护简单**: 清晰的记录结构，易于调试
4. **兼容现有**: 与当前代码架构匹配
5. **扩展容易**: 新增周期类型只需增加记录

#### 实施建议:
1. **表结构**: 使用分离记录设计
2. **优先级**: 利用ID天然排序（ID小优先级高）
3. **冲突解决**: 每月 > 每周的简单规则
4. **缓存策略**: 按类型分别缓存，提高查询效率
5. **API设计**: 保持现有接口不变，内部逻辑调整

#### 迁移路径:
1. **新表结构**: 创建新的分离记录
2. **数据迁移**: 将现有数据拆分为多条记录
3. **代码调整**: 修改调度器和配置管理器
4. **测试验证**: 确保冲突解决逻辑正确
5. **逐步上线**: 灰度发布，确保稳定性
