# 定时任务表结构调整方案

## 📊 当前表结构分析

### 现有字段 (c_tgScheduledPushTasks)
```json
{
  "_id": "ObjectId",
  "notifyId": 3,               // 通知ID
  "business_no": "39bac42a",   // 商户号
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105], // 目标频道ID列表
  "bannerUrl": "1",            // 横幅图片URL
  "messageText": "定时通知文本", // 消息文本
  "externalUrl": "a",          // 外部链接
  "internalUrl": "b",          // 内部链接
  "urlLabel": "",              // 链接标签
  "urlType": "",               // 链接类型
  "sendTime": "05:00:00",      // 发送时间 (单个)
  "sendFrequency": "weekly",   // 发送频率
  "weekDays": [1,2,3,4,5,6,7], // 周几
  "monthDays": [],             // 月几
  "enabled": true,             // 是否启用
  "createTime": 1751897960210, // 创建时间
  "updateTime": 1751897960210, // 更新时间
  "language": 2                // 语言ID (可选)
}
```

### 现有数据示例
```json
// 记录1: 每周任务
{
  "notifyId": 3,
  "business_no": "39bac42a",
  "messageText": "定时通知文本",
  "sendTime": "05:00:00",
  "sendFrequency": "weekly",
  "weekDays": [1,2,3,4,5,6,7],
  "monthDays": [],
  "enabled": true
}

// 记录2: 每周任务 (空配置)
{
  "notifyId": 4,
  "messageText": "活动参与提醒-红包雨-通知文本",
  "sendFrequency": "weekly",
  "weekDays": [],
  "monthDays": [],
  "enabled": true
}
```

## 🔧 调整后的表结构

### 新表结构设计
```json
{
  "_id": "ObjectId",
  
  // === 保留的现有字段 ===
  "notifyId": "int",           // 通知ID
  "business_no": "str",        // 商户号
  "botToken": "str",           // Bot Token (可能后续从robot表获取)
  "channelId": "list",         // 目标频道ID列表
  "bannerUrl": "str",          // 横幅图片URL
  "messageText": "str",        // 消息文本
  "externalUrl": "str",        // 外部链接
  "internalUrl": "str",        // 内部链接
  "urlLabel": "str",           // 链接标签
  "urlType": "str",            // 链接类型
  "language": "int",           // 语言ID
  "enabled": "bool",           // 是否启用
  "createTime": "Int64",       // 创建时间
  "updateTime": "Int64",       // 更新时间
  
  // === 新增字段 ===
  "task_type": "str",          // 任务类型: "monthly" | "weekly"
  "task_group": "str",         // 任务组标识 (用于关联同一业务的不同周期)
  "schedule_days": "list",     // 统一的天数字段
  "schedule_times": "list",    // 支持多个时间点 ["09:00", "15:00", "21:00"]
  
  // === 兼容字段 (保留用于向后兼容) ===
  "sendTime": "str",           // 主要时间点 (从schedule_times[0]获取)
  "sendFrequency": "str",      // 频率 (从task_type映射)
  "weekDays": "list",          // 周几 (当task_type="weekly"时同步)
  "monthDays": "list"          // 月几 (当task_type="monthly"时同步)
}
```

## 📋 实际数据示例

### 场景: 同一notifyId的每月和每周任务

#### 每月任务记录
```json
{
  "_id": "ObjectId_1",
  "notifyId": 3,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "1",
  "messageText": "投注返利通知-每月",
  "externalUrl": "https://example.com/monthly",
  "internalUrl": "t.me/bot/monthly",
  "urlLabel": "查看详情",
  "urlType": "webapp",
  "language": 2,
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210,
  
  // 新增字段
  "task_type": "monthly",
  "task_group": "reward_notification_3",
  "schedule_days": [1, 15, 30],              // 每月1号、15号、30号
  "schedule_times": ["09:00", "15:00", "21:00"], // 每天3个时间点
  
  // 兼容字段
  "sendTime": "09:00",
  "sendFrequency": "monthly",
  "weekDays": [],
  "monthDays": [1, 15, 30]
}
```

#### 每周任务记录
```json
{
  "_id": "ObjectId_2",
  "notifyId": 3,
  "business_no": "39bac42a",
  "botToken": "7311411735:AAFwr_O4IjnwM0dpdbWNk7ENHji3zjFX_Uw",
  "channelId": [-1002316158105],
  "bannerUrl": "1",
  "messageText": "投注返利通知-每周",
  "externalUrl": "https://example.com/weekly",
  "internalUrl": "t.me/bot/weekly",
  "urlLabel": "查看详情",
  "urlType": "webapp",
  "language": 2,
  "enabled": true,
  "createTime": 1751897960210,
  "updateTime": 1751897960210,
  
  // 新增字段
  "task_type": "weekly",
  "task_group": "reward_notification_3",     // 同一任务组
  "schedule_days": [1, 3, 5],               // 周一、周三、周五
  "schedule_times": ["10:00", "16:00"],     // 每天2个时间点
  
  // 兼容字段
  "sendTime": "10:00",
  "sendFrequency": "weekly",
  "weekDays": [1, 3, 5],
  "monthDays": []
}
```

## 🔄 字段映射关系

### 新旧字段对应
| 原字段 | 新字段 | 映射规则 |
|--------|--------|----------|
| `sendFrequency` | `task_type` | "weekly" → "weekly", "monthly" → "monthly" |
| `weekDays` | `schedule_days` | 当task_type="weekly"时使用 |
| `monthDays` | `schedule_days` | 当task_type="monthly"时使用 |
| `sendTime` | `schedule_times[0]` | 单个时间转为数组的第一个元素 |
| - | `task_group` | 新增：基于notifyId生成，格式: "notification_{notifyId}" |

### schedule_days字段说明
```javascript
// 每周任务: 1=周一, 2=周二, ..., 7=周日
"schedule_days": [1, 3, 5]  // 周一、周三、周五

// 每月任务: 1-31表示月份中的日期
"schedule_days": [1, 15, 30]  // 每月1号、15号、30号
```

### schedule_times字段说明
```javascript
// 支持一天中的多个时间点
"schedule_times": ["09:00", "15:00", "21:00"]  // 每天执行3次

// 时间格式: HH:MM (24小时制)
"schedule_times": ["08:30", "20:30"]  // 早上8:30和晚上8:30
```

## 📅 冲突解决示例

### 场景: 2025年7月15日 (周三)

#### 配置数据
```json
// 每月任务 (ID较小，优先级高)
{
  "_id": "ObjectId_1",
  "task_type": "monthly",
  "schedule_days": [15],
  "schedule_times": ["09:00", "15:00"]
}

// 每周任务 (ID较大，优先级低)
{
  "_id": "ObjectId_2", 
  "task_type": "weekly",
  "schedule_days": [3],  // 周三
  "schedule_times": ["10:00", "16:00"]
}
```

#### 冲突检测
- **每月匹配**: ✅ 7月15日是15号
- **每周匹配**: ✅ 7月15日是周三(3)
- **冲突**: 同一天有两种类型的任务

#### 解决结果
- **优先级规则**: 每月 > 每周
- **最终执行**: 09:00, 15:00 (来自每月任务)
- **忽略**: 10:00, 16:00 (每周任务被忽略)

## 🔧 数据迁移策略

### 迁移逻辑
```python
def migrate_task_record(old_record):
    """迁移单条记录"""
    notify_id = old_record.get('notifyId')
    send_frequency = old_record.get('sendFrequency', 'weekly')
    week_days = old_record.get('weekDays', [])
    month_days = old_record.get('monthDays', [])
    send_time = old_record.get('sendTime', '09:00')
    
    # 生成任务组标识
    task_group = f"notification_{notify_id}"
    
    migrated_records = []
    
    # 处理每周任务
    if send_frequency == 'weekly' and week_days:
        weekly_record = old_record.copy()
        weekly_record.update({
            'task_type': 'weekly',
            'task_group': task_group,
            'schedule_days': week_days,
            'schedule_times': [send_time.split(':')[0] + ':' + send_time.split(':')[1]]
        })
        migrated_records.append(weekly_record)
    
    # 处理每月任务
    if month_days:
        monthly_record = old_record.copy()
        monthly_record.update({
            'task_type': 'monthly',
            'task_group': task_group,
            'schedule_days': month_days,
            'schedule_times': [send_time.split(':')[0] + ':' + send_time.split(':')[1]]
        })
        migrated_records.append(monthly_record)
    
    return migrated_records
```

### 迁移示例
```json
// 原记录
{
  "notifyId": 3,
  "sendTime": "05:00:00",
  "sendFrequency": "weekly", 
  "weekDays": [1,2,3,4,5,6,7],
  "monthDays": []
}

// 迁移后
{
  "notifyId": 3,
  "task_type": "weekly",
  "task_group": "notification_3",
  "schedule_days": [1,2,3,4,5,6,7],
  "schedule_times": ["05:00"],
  // 保留原字段用于兼容
  "sendTime": "05:00:00",
  "sendFrequency": "weekly",
  "weekDays": [1,2,3,4,5,6,7],
  "monthDays": []
}
```

## 💡 实施建议

### 分阶段实施
1. **阶段1**: 添加新字段，保持向后兼容
2. **阶段2**: 运行数据迁移脚本
3. **阶段3**: 更新代码逻辑使用新字段
4. **阶段4**: 验证和清理

### 核心优势
- ✅ **完全兼容**: 保留所有现有字段
- ✅ **渐进迁移**: 可以逐步切换逻辑
- ✅ **多时间点**: 支持一天多次执行
- ✅ **ID优先级**: 利用MongoDB自然排序
- ✅ **灵活扩展**: 易于添加新的调度类型

### 查询示例
```javascript
// 查询某日期的每月任务
db.c_tgScheduledPushTasks.find({
  "task_type": "monthly",
  "schedule_days": 15,
  "enabled": true
}).sort({"_id": 1})

// 查询某商户的所有任务
db.c_tgScheduledPushTasks.find({
  "business_no": "39bac42a",
  "enabled": true
}).sort({"_id": 1})

// 查询同一任务组的所有记录
db.c_tgScheduledPushTasks.find({
  "task_group": "notification_3",
  "enabled": true
}).sort({"_id": 1})
```
