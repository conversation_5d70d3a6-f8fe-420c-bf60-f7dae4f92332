# 特殊参数配置说明

## 📋 功能概述

特殊参数替换功能允许在模板中使用特定关键字，系统会自动将数字参数转换为对应语言的文本。

### 🎯 使用场景
```
模板: "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
参数: ["张三", "18000", "1", "100"]
结果: "恭喜 张三 在每日签到活动 获得 金币100"
```

## 🔧 配置文件位置

配置文件：`config/config.yaml`

## 📝 配置结构

```yaml
# 特殊参数映射配置
special_params:
  # 关键字映射 (模板中的关键字 -> 映射类型)
  keywords:
    活动名: "activity_name"
    游戏名: "game_name"
    币种: "currency_type"
    等级: "level_name"
    道具名: "item_name"
    称号: "title_name"
  
  # 映射表配置
  mappings:
    # 活动名映射
    activity_name:
      "18000":
        "1": "每日签到活动"
        "2": "Daily Check-in"
        "3": "每日簽到活動"
      "19000":
        "1": "充值返利活动"
        "2": "Recharge Bonus"
        "3": "充值返利活動"
    
    # 币种映射
    currency_type:
      "1":
        "1": "金币"
        "2": "Gold"
        "3": "金幣"
      "2":
        "1": "钻石"
        "2": "Diamond"
        "3": "鑽石"
```

## 🎯 配置说明

### 1. keywords 部分
定义模板中的关键字与映射类型的对应关系：

```yaml
keywords:
  活动名: "activity_name"  # 模板中包含"活动名"的占位符会使用activity_name映射
  游戏名: "game_name"      # 模板中包含"游戏名"的占位符会使用game_name映射
  币种: "currency_type"    # 模板中包含"币种"的占位符会使用currency_type映射
```

### 2. mappings 部分
定义具体的ID到文本的映射关系：

```yaml
mappings:
  activity_name:           # 映射类型
    "18000":              # 参数值（活动ID）
      "1": "每日签到活动"   # 语言ID "1" 对应的中文
      "2": "Daily Check-in" # 语言ID "2" 对应的英文
      "3": "每日簽到活動"   # 语言ID "3" 对应的繁体中文
```

## 📋 完整配置示例

```yaml
special_params:
  keywords:
    活动名: "activity_name"
    游戏名: "game_name"
    币种: "currency_type"
    等级: "level_name"
    道具名: "item_name"
    称号: "title_name"
  
  mappings:
    # 活动名映射
    activity_name:
      "18000":
        "1": "每日签到活动"
        "2": "Daily Check-in"
        "3": "每日簽到活動"
      "19000":
        "1": "充值返利活动"
        "2": "Recharge Bonus"
        "3": "充值返利活動"
      "20000":
        "1": "VIP专属活动"
        "2": "VIP Exclusive"
        "3": "VIP專屬活動"
    
    # 游戏名映射
    game_name:
      "1001":
        "1": "老虎机"
        "2": "Slot Machine"
        "3": "老虎機"
      "1002":
        "1": "百家乐"
        "2": "Baccarat"
        "3": "百家樂"
    
    # 币种映射
    currency_type:
      "1":
        "1": "金币"
        "2": "Gold"
        "3": "金幣"
      "2":
        "1": "钻石"
        "2": "Diamond"
        "3": "鑽石"
    
    # 等级映射
    level_name:
      "1":
        "1": "青铜"
        "2": "Bronze"
        "3": "青銅"
      "2":
        "1": "白银"
        "2": "Silver"
        "3": "白銀"
```

## 🚀 使用示例

### 示例1: 活动名替换
```
模板: "恭喜 {玩家ID1} 在{活动名2} 获得 {币种3}{金额4}"
参数: ["张三", "18000", "1", "100"]
语言: "1" (中文)

处理过程:
1. 检测到占位符 {活动名2}，参数值为 "18000"
2. 查找 activity_name.18000.1 = "每日签到活动"
3. 检测到占位符 {币种3}，参数值为 "1"
4. 查找 currency_type.1.1 = "金币"

最终结果: "恭喜 张三 在每日签到活动 获得 金币100"
```

### 示例2: 英文版本
```
模板: "Congratulations {玩家名1} won {币种2} in {活动名3}"
参数: ["John", "2", "19000"]
语言: "2" (英文)

最终结果: "Congratulations John won Diamond in Recharge Bonus"
```

## 🔧 添加新的映射

### 1. 添加新的关键字
在 `keywords` 部分添加新的关键字：

```yaml
keywords:
  活动名: "activity_name"
  游戏名: "game_name"
  币种: "currency_type"
  道具名: "item_name"     # 新增
  技能名: "skill_name"    # 新增
```

### 2. 添加对应的映射表
在 `mappings` 部分添加对应的映射：

```yaml
mappings:
  # 道具名映射
  item_name:
    "2001":
      "1": "超级武器"
      "2": "Super Weapon"
      "3": "超級武器"
    "2002":
      "1": "防护盾"
      "2": "Shield"
      "3": "防護盾"
  
  # 技能名映射
  skill_name:
    "5001":
      "1": "火球术"
      "2": "Fireball"
      "3": "火球術"
```

### 3. 添加新的活动
在现有映射类型中添加新的ID：

```yaml
activity_name:
  "18000":
    "1": "每日签到活动"
    "2": "Daily Check-in"
    "3": "每日簽到活動"
  "21000":              # 新增活动
    "1": "周年庆典"
    "2": "Anniversary"
    "3": "週年慶典"
```

## 💡 注意事项

1. **配置修改后需要重启服务**才能生效
2. **语言ID必须与notify表中的language字段对应**
3. **未找到映射时会保持原参数值**
4. **关键字匹配是包含匹配**，如 `{活动名2}` 会匹配 "活动名" 关键字
5. **参数值和语言ID都必须是字符串格式**

## 🔍 调试方法

### 查看日志
启用DEBUG日志级别可以看到详细的替换过程：

```yaml
logging:
  level: "DEBUG"
```

### 测试脚本
使用提供的测试脚本验证配置：

```bash
python test_config_driven_params.py
```

### API测试
使用API接口测试特殊参数替换：

```bash
curl -X POST "http://localhost:9005/api/realtime-push/template/preview" \
  -H "Content-Type: application/json" \
  -d '{
    "business_no": "39bac42a",
    "type": "18000",
    "params": ["张三", "18000", "1", "100"]
  }'
```

## 🎉 总结

通过配置文件驱动的特殊参数替换功能，你可以：

- ✅ **灵活配置**：所有映射都在配置文件中，无需修改代码
- ✅ **多语言支持**：同一ID可以配置多种语言的翻译
- ✅ **易于扩展**：添加新的关键字和映射非常简单
- ✅ **动态更新**：修改配置后重启服务即可生效
- ✅ **向后兼容**：未配置的参数会保持原值
